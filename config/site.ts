import {
  TrendingUpIcon,
  HomeIcon,
  BriefcaseBusiness,
  EyeIcon,
  BarChartIcon,
  ShoppingCartIcon,
} from "lucide-react";
export type SiteConfig = typeof siteConfig;

export const siteConfig = {
  name: "Dashboard",
  description: "",
  mainNav: [
    {
      title: "Dashboard",
      href: "/",
      icon: HomeIcon,
    },
    {
      title: "Signals",
      href: "/risk-signals",
      icon: TrendingUpIcon,
    },
    {
      title: "Portfolio",
      href: "/portfolio",
      icon: BriefcaseBusiness,
    },
    {
      title: "Orders",
      href: "/orders",
      icon: ShoppingCartIcon,
    },
    {
      title: "Watchlist",
      href: "/watchlist",
      icon: EyeIcon,
    },
    {
      title: "Analytics",
      href: "/dashboard",
      icon: BarChartIcon,
    },
  ],
  links: {},
};
