import { getTickerListWithChangesBetweenTwoDates } from "./get-ticker-list-with-changes-between-two-dates";
import {
  getArchivesOfRiskSignals,
  getTrendChangesForDate,
  TrendChange,
} from "./trend-change";

// Mock the external dependencies
jest.mock("./trend-change", () => ({
  getArchivesOfRiskSignals: jest.fn(),
  getTrendChangesForDate: jest.fn(),
}));

const mockedGetTrendChangesForDate =
  getTrendChangesForDate as jest.MockedFunction<typeof getTrendChangesForDate>;

// Helper function to create TrendChange objects for testing
const createTrendChange = (
  index: string,
  trend: string,
  date: Date,
): TrendChange => ({
  index,
  trend,
  buyTrade: 0,
  sellTrade: 0,
  previousClose: 0,
  date,
  originalIndex: 0,
  description: undefined,
});

describe("getTickerListWithChangesBetweenTwoDates", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Date validation", () => {
    it("should throw error when dates are the same", async () => {
      const date = new Date("2024-01-01");
      await expect(
        getTickerListWithChangesBetweenTwoDates(date, date),
      ).rejects.toThrow(
        "previousTrendChangeDate and selectedTrendChangeDate cannot be the same date",
      );
    });

    it("should throw error when previousTrendChangeDate is after selectedTrendChangeDate", async () => {
      const selectedDate = new Date("2024-01-01");
      const previousDate = new Date("2024-01-02");
      await expect(
        getTickerListWithChangesBetweenTwoDates(selectedDate, previousDate),
      ).rejects.toThrow(
        "previousTrendChangeDate must be before selectedTrendChangeDate",
      );
    });

    it("should throw error when dates are in different years", async () => {
      const selectedDate = new Date("2024-01-01");
      const previousDate = new Date("2023-12-31");
      await expect(
        getTickerListWithChangesBetweenTwoDates(selectedDate, previousDate),
      ).rejects.toThrow("dates must be in the same year");
    });

    it("should throw error when dates are in different months", async () => {
      const selectedDate = new Date("2024-02-01");
      const previousDate = new Date("2024-01-31");
      await expect(
        getTickerListWithChangesBetweenTwoDates(selectedDate, previousDate),
      ).rejects.toThrow("dates must be in the same month");
    });
  });

  describe("Trend change detection", () => {
    it("should detect BULLISH to BEARISH changes", async () => {
      const testDate = new Date("2024-01-02");
      const prevDate = new Date("2024-01-01");

      mockedGetTrendChangesForDate
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "BEARISH", testDate),
          createTrendChange("GOOGL", "BULLISH", testDate),
        ])
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "BULLISH", prevDate),
          createTrendChange("GOOGL", "BULLISH", prevDate),
        ]);

      const result = await getTickerListWithChangesBetweenTwoDates(testDate);

      expect(result).toHaveLength(1);
      expect(result[0].current.index).toBe("AAPL");
      expect(result[0].current.trend).toBe("BEARISH");
      expect(result[0].previous.trend).toBe("BULLISH");
    });

    it("should detect NEUTRAL to BULLISH/BEARISH changes", async () => {
      const testDate = new Date("2024-01-02");
      const prevDate = new Date("2024-01-01");

      mockedGetTrendChangesForDate
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "BULLISH", testDate),
          createTrendChange("GOOGL", "BEARISH", testDate),
        ])
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "NEUTRAL", prevDate),
          createTrendChange("GOOGL", "NEUTRAL", prevDate),
        ]);

      const result = await getTickerListWithChangesBetweenTwoDates(testDate);

      expect(result).toHaveLength(2);
      expect(result.map((r) => r.current.index)).toEqual(
        expect.arrayContaining(["AAPL", "GOOGL"]),
      );
      expect(
        result.find((r) => r.current.index === "AAPL")?.current.trend,
      ).toBe("BULLISH");
      expect(
        result.find((r) => r.current.index === "GOOGL")?.current.trend,
      ).toBe("BEARISH");
    });

    it("should not detect changes when trend remains the same", async () => {
      const testDate = new Date("2024-01-02");
      const prevDate = new Date("2024-01-01");

      mockedGetTrendChangesForDate
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "BULLISH", testDate),
          createTrendChange("GOOGL", "BEARISH", testDate),
        ])
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "BULLISH", prevDate),
          createTrendChange("GOOGL", "BEARISH", prevDate),
        ]);

      const result = await getTickerListWithChangesBetweenTwoDates(testDate);

      expect(result).toHaveLength(0);
    });

    it("should handle missing tickers in second date", async () => {
      const testDate = new Date("2024-01-02");
      const prevDate = new Date("2024-01-01");

      mockedGetTrendChangesForDate
        .mockResolvedValueOnce([createTrendChange("AAPL", "BEARISH", testDate)])
        .mockResolvedValueOnce([
          createTrendChange("AAPL", "BULLISH", prevDate),
          createTrendChange("GOOGL", "BEARISH", prevDate),
        ]);

      const result = await getTickerListWithChangesBetweenTwoDates(testDate);

      expect(result).toHaveLength(1);
      expect(result[0].current.index).toBe("AAPL");
      expect(result[0].current.trend).toBe("BEARISH");
      expect(result[0].previous.trend).toBe("BULLISH");
    });
  });
});
