"use server";

import prismaDb from "@/lib/prisma";
import { UserProfile } from "@/types/user-profile";

export async function getUserProfile(
  userId: string,
): Promise<UserProfile | null> {
  const userProfile = await prismaDb.userProfile.findUnique({
    where: { user_id: userId },
  });

  if (!userProfile) return null;

  // Map the database fields to our UserProfile type
  return {
    userId: userProfile.user_id,
    firstName: userProfile.first_name || "",
    lastName: userProfile.last_name || "",
    createdAt: userProfile.created_at,
    updatedAt: userProfile.updated_at,
    deletedAt: userProfile.deleted_at,
    settings: userProfile.settings as UserProfile["settings"],
  };
}

export async function updateUserProfile(
  userId: string,
  userProfile: UserProfile,
): Promise<UserProfile> {
  const updatedUserProfile = await prismaDb.userProfile.update({
    where: { user_id: userId },
    data: {
      first_name: userProfile.firstName,
      last_name: userProfile.lastName,
      settings: JSON.parse(JSON.stringify(userProfile.settings)),
      updated_at: new Date(),
    },
  });

  // Map the response back to our UserProfile type
  return {
    userId: updatedUserProfile.user_id,
    firstName: updatedUserProfile.first_name || "",
    lastName: updatedUserProfile.last_name || "",
    createdAt: updatedUserProfile.created_at,
    updatedAt: updatedUserProfile.updated_at,
    deletedAt: updatedUserProfile.deleted_at,
    settings: updatedUserProfile.settings as UserProfile["settings"],
  };
}
