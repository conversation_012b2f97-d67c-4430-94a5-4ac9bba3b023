export interface TrendChange {
  index: string;
  trend: string;
  description?: string;
  buyTrade: number;
  sellTrade: number;
  previousClose: number;
  originalIndex?: number;
  date: Date;
}

export interface Instrument {
  symbol: string;
  isStock: boolean;
}

/**
 * Types for Auto Order System
 */
export interface AutoOrderPrefillTrendChange {
  symbol: string;
  previousTrend: string;
  newTrend: string;
  date: string;
}

export interface AutoOrderPrefillWithMarketPrice
  extends AutoOrderPrefillTrendChange {
  marketQuote: {
    price: number;
    // other quote data...
  };
}
