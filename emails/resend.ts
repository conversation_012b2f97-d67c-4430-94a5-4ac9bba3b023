"use server";

import { Resend } from "resend";

export interface EmailProps {
  body: string;
  to: string[];
  subject: string;
}

export async function sendEmail({
  body,
  to,
  subject,
}: EmailProps): Promise<boolean> {
  console.log("sendEmail()", { body, to, subject });
  const resend = new Resend(process.env.RESEND_API_KEY);
  try {
    const { data, error } = await resend.emails.send({
      from: `Aquarius Technologies <${process.env.RESEND_EMAIL_FROM}>`,
      to: to,
      subject: subject,
      html: body,
    });

    if (error) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
