import {
  Html,
  Head,
  Preview,
  Heading,
  Text,
  Section,
  Container,
  Hr,
  Body,
} from "@react-email/components";

interface TrendChange {
  symbol: string;
  previousTrend: string;
  newTrend: string;
  date: string;
}

interface TrendChangesEmailProps {
  changes: TrendChange[];
  recipientName?: string;
}

const TrendChangesEmail = ({
  changes,
  recipientName,
}: TrendChangesEmailProps) => {
  // Helper function to get color based on trend
  const getTrendColor = (trend: string): string => {
    switch (trend.toLowerCase()) {
      case "bullish":
        return "#22c55e";
      case "bearish":
        return "#ef4444";
      case "neutral":
        return "#f59e0b";
      default:
        return "#6b7280";
    }
  };

  return (
    <Html>
      <Head />
      <Preview>{`Daily Trend Changes Alert - ${changes.length} Updates`}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading as="h1" style={header}>
            Market Trend Changes Alert
          </Heading>

          <Text style={paragraph}>
            {recipientName ? `Hello ${recipientName},` : "Hello,"}
          </Text>

          <Text style={paragraph}>
            The following ticker symbols have experienced trend changes:
          </Text>

          <Section style={section}>
            {changes.map((change, index) => (
              <div key={index} style={changeContainer}>
                <Text style={symbolText}>{change.symbol}</Text>
                <div style={trendChangeContainer}>
                  <Text
                    style={{
                      ...trendText,
                      color: getTrendColor(change.previousTrend),
                    }}
                  >
                    {change.previousTrend}
                  </Text>
                  <Text style={arrowStyle}>→</Text>
                  <Text
                    style={{
                      ...trendText,
                      color: getTrendColor(change.newTrend),
                    }}
                  >
                    {change.newTrend}
                  </Text>
                </div>
                <Text style={dateText}>{change.date}</Text>
                {index < changes.length - 1 && <Hr style={divider} />}
              </div>
            ))}
          </Section>

          <Text style={footer}>
            This is an automated notification. Please do not reply to this
            email.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "40px 20px",
  borderRadius: "5px",
  maxWidth: "600px",
};

const header = {
  color: "#1a1a1a",
  fontSize: "24px",
  lineHeight: "1.3",
  margin: "0 0 20px",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "1.5",
  color: "#4a5568",
  margin: "0 0 20px",
};

const section = {
  margin: "20px 0",
};

const changeContainer = {
  padding: "10px 0",
};

const symbolText = {
  fontSize: "18px",
  fontWeight: "bold",
  margin: "0 0 5px",
  color: "#2d3748",
};

const trendChangeContainer = {
  display: "flex",
  alignItems: "center",
  gap: "8px",
  margin: "5px 0",
};

const trendText = {
  fontSize: "16px",
  fontWeight: "500",
  margin: "0",
};

const arrowStyle = {
  fontSize: "16px",
  color: "#718096",
  margin: "0",
};

const dateText = {
  fontSize: "14px",
  color: "#718096",
  margin: "5px 0 0",
};

const divider = {
  borderTop: "1px solid #e2e8f0",
  margin: "10px 0",
};

const footer = {
  fontSize: "14px",
  color: "#718096",
  margin: "30px 0 0",
  textAlign: "center" as const,
};

export default TrendChangesEmail;
