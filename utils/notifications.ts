"use client";

import { saveSubscription } from "../app/actions/notifications-server";

export async function subscribeToPushNotifications() {
  try {
    if (!("serviceWorker" in navigator) || !("Notification" in window)) {
      throw new Error("Push notifications are not supported");
    }

    // First check if permission is already granted
    if (Notification.permission === "denied") {
      throw new Error(
        "Notifications are blocked. Please enable them in your browser settings.",
      );
    }

    // Explicitly request permission first
    const permissionResult = await Notification.requestPermission();
    if (permissionResult !== "granted") {
      throw new Error("Notification permission was not granted.");
    }

    // Register service worker
    const registration = await navigator.serviceWorker.register(
      "/service-worker.js",
      {
        scope: "/",
      },
    );

    await navigator.serviceWorker.ready;

    // Get existing subscription
    let subscription = await registration.pushManager.getSubscription();
    console.log("Existing subscription:", subscription);

    if (!subscription) {
      try {
        // Get public VAPID key
        console.log("Fetching VAPID key...");
        const response = await fetch("/api/push/vapid-key");
        if (!response.ok) {
          throw new Error(`Failed to fetch VAPID key: ${response.status}`);
        }
        const vapidPublicKey = await response.text();
        console.log("VAPID key received:", vapidPublicKey);

        if (!vapidPublicKey || vapidPublicKey.length === 0) {
          throw new Error("Invalid VAPID key received");
        }

        // Convert VAPID key
        const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);
        console.log("Creating new subscription...");

        // Create new subscription with error handling
        try {
          subscription = await registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: convertedVapidKey,
          });
          console.log("New subscription created:", subscription);
        } catch (subscribeError) {
          console.error("Detailed subscription error:", subscribeError);
          if (
            subscribeError instanceof Error &&
            subscribeError.name === "AbortError"
          ) {
            // Try unsubscribing first if there might be a stale subscription
            const existingSub =
              await registration.pushManager.getSubscription();
            if (existingSub) {
              await existingSub.unsubscribe();
              // Try subscribing again
              subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: convertedVapidKey,
              });
            }
          } else {
            throw subscribeError;
          }
        }
      } catch (subscribeError) {
        console.error("Error during subscription creation:", subscribeError);
        throw subscribeError;
      }
    }

    // Save subscription to backend with error handling
    if (!subscription) {
      throw new Error("Failed to create push subscription");
    }

    try {
      const result = await saveSubscription(subscription);
      if (!result) {
        throw new Error("Failed to save subscription");
      }
      console.log("Subscription saved successfully");
      return subscription;
    } catch (error) {
      // If saving fails, clean up the browser subscription
      await subscription.unsubscribe();
      throw error;
    }
  } catch (error) {
    console.error("Error in subscribeToPushNotifications:", error);
    throw error;
  }
}

// Helper function to convert VAPID key
function urlBase64ToUint8Array(base64String: string) {
  try {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/\-/g, "+")
      .replace(/_/g, "/");

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  } catch (error) {
    console.error("Error in urlBase64ToUint8Array:", error);
    throw error;
  }
}
