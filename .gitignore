# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.env
certificates

bun.lockb

# PWA Autogenerated
**/public/sw.js
**/public/sw.js.map
**/public/workbox-*.js
**/public/workbox-*.js.map
**/public/worker-*.js
**/public/worker-*.js.map
**/public/swe-worker-*.js
**/public/fallback-*.js
public/sw.js

# Puppeteer scripts
apps/web/scripts/mcp-puppeteer/

node_modules
apps/web/node_modules
apps/api/node_modules
packages/shared/node_modules
packages/database/node_modules
tools/eslint-config/node_modules

.next