"use client";

import { Briefcase } from "lucide-react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PortfolioPosition } from "../portfolio/actions/get-portfolio-all-data";

interface HoldingsIndicatorProps {
  symbol: string;
  holdings?: PortfolioPosition[] | null;
}

export function HoldingsIndicator({
  symbol,
  holdings = null,
}: HoldingsIndicatorProps) {
  // console.log(`HoldingsIndicator for ${symbol}:`, holdings);

  if (!holdings) {
    // console.log(`No holdings data for ${symbol}`);
    return null;
  }

  const holding = holdings.find(
    (h) => h.symbol.toUpperCase() === symbol.toUpperCase(),
  );

  if (!holding) {
    // console.log(`No matching holding found for ${symbol}`);
    return null;
  }

  // console.log(`Found holding for ${symbol}:`, holding);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Briefcase className="h-4 w-4 fill-primary text-primary" />
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-size-sm">
            <p>Current Position</p>
            <p className="text-size-xs text-muted-foreground">
              Shares: {holding.shares.toLocaleString()} | Value: $
              {holding.value.toLocaleString()}
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
