"use client";

import { useEffect, useState } from "react";

export function ErudaDebug() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    // Check if we're in the browser and in development
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV === "development"
    ) {
      // Check if eruda is already loaded
      if (!(window as any).eruda) {
        const loadEruda = async () => {
          try {
            const script = document.createElement("script");
            script.src = "https://cdn.jsdelivr.net/npm/eruda";
            script.async = true;

            // Create a promise to handle script loading
            const loadPromise = new Promise((resolve, reject) => {
              script.onload = resolve;
              script.onerror = reject;
            });

            document.body.appendChild(script);

            await loadPromise;

            // Initialize eruda after script is loaded
            if ((window as any).eruda) {
              (window as any).eruda.init({
                tool: ["console", "elements", "network", "resources", "info"],
                useShadowDom: true,
                autoScale: true,
                defaults: {
                  displaySize: 50,
                  transparency: 0.9,
                  theme: "Dark",
                },
              });
            }
          } catch (error) {
            console.error("Failed to load Eruda:", error);
          }
        };

        loadEruda();
      }
    }
  }, [isMounted]);

  // Return null since this is a utility component
  return null;
}

export function ErudaDebugV1() {
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const loadEruda = async () => {
        const script = document.createElement("script");
        script.src = "//cdn.jsdelivr.net/npm/eruda";
        script.onload = () => {
          // @ts-ignore
          window.eruda.init();
        };
        document.body.appendChild(script);
      };
      loadEruda();
    }
  }, []);

  return null;
}
