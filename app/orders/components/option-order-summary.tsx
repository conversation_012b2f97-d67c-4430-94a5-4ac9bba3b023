"use client";
import { TrendChange } from "@/db/trend-change";
import { Button, Dialog, Flex, Inset, Table } from "@radix-ui/themes";
import React, { useEffect, useState, useTransition } from "react";

interface OptionOrderSummaryProps {
  stockPicks: TrendChange[];
}

export default function OptionOrderSummary({
  stockPicks,
}: OptionOrderSummaryProps) {
  const [isPending, startTransition] = useTransition();
  const [ibAccountNumber, setIBAccountNumber] = useState("DU");

  // useEffect(() => {
  //   startTransition(() => {
  //     const func = async () => {
  //       const accounts = await FetchIBAccountInformation();
  //       setIBAccountNumber(JSON.parse(accounts)[0].account);
  //     };
  //     func();
  //   });
  //   return () => {};
  // }, []);

  const dateOnly = new Date();
  dateOnly.setUTCHours(0, 0, 0, 0); // Set time to midnight (UTC)

  return (
    <>
      <Dialog.Root>
        <Dialog.Trigger>
          <Button>Options Order Summary</Button>
        </Dialog.Trigger>
        <Dialog.Content
          style={{ maxWidth: 800 }}
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          <Dialog.Title>Options Order Summary - {ibAccountNumber}</Dialog.Title>
          {/* <Dialog.Description>Review calls and puts</Dialog.Description> */}
          <Dialog.Description>CALL</Dialog.Description>
          <Inset side="x" my="5">
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeaderCell>Index</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Strike</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Expires</Table.ColumnHeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {stockPicks
                  .filter((s) => s.trend === "BULLISH")
                  .map((i) => {
                    return (
                      <Table.Row key={i.index}>
                        <Table.RowHeaderCell>{i.index}</Table.RowHeaderCell>
                        <Table.Cell>{i.buyTrade}</Table.Cell>
                        <Table.Cell>
                          {/* {getNextSecondFriday(dateOnly)?.toDateString()} */}
                        </Table.Cell>
                      </Table.Row>
                    );
                  })}
              </Table.Body>
            </Table.Root>
          </Inset>
          <Dialog.Description>PUT</Dialog.Description>
          <Inset side="x" my="5">
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeaderCell>Index</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Strike</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Expires</Table.ColumnHeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {stockPicks
                  .filter((s) => s.trend === "BEARISH")
                  .map((i) => {
                    return (
                      <Table.Row key={i.index}>
                        <Table.RowHeaderCell>{i.index}</Table.RowHeaderCell>
                        <Table.Cell>{i.sellTrade}</Table.Cell>
                        <Table.Cell>
                          {/* {getNextSecondFriday(dateOnly)?.toDateString()} */}
                        </Table.Cell>
                      </Table.Row>
                    );
                  })}
              </Table.Body>
            </Table.Root>
          </Inset>

          <Flex gap="3" justify="end">
            <Button variant="soft" color="gray">
              Get Option Chain
            </Button>
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Close without saving
              </Button>
            </Dialog.Close>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}

function getNextSecondFriday(date: Date): Date | null {
  const dateOnly = new Date();
  dateOnly.setUTCHours(0, 0, 0, 0); // Set time to midnight (UTC)
  if (date < dateOnly) {
    console.error("The provided date must not be in the past.");
    return null;
  }

  // Clone the date to avoid mutating the original date
  let resultDate = new Date(date.getTime());

  // Find the next Friday from the given date
  resultDate.setDate(
    resultDate.getDate() + ((5 - resultDate.getDay() + 7) % 7),
  );

  // Check if the next Friday is at least 6 days away, otherwise, find the next one
  if ((resultDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24) < 6) {
    resultDate.setDate(resultDate.getDate() + 7);
  }

  // Now resultDate is the first Friday, find the second Friday
  resultDate.setDate(resultDate.getDate() + 7);

  // Ensure the second Friday is within the 6-10 days range from the original date
  const daysUntilExpiration =
    (resultDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24);
  //   if (daysUntilExpiration < 6 || daysUntilExpiration > 10) {
  //     console.error(
  //       "The second Friday does not fall within the 6-10 days range.",
  //     );
  //     return null;
  //   }

  // Adjust for weekends, if the second Friday is less than 10 days away, add additional days to skip the weekend
  if (daysUntilExpiration < 10) {
    resultDate.setDate(resultDate.getDate() + (12 - daysUntilExpiration));
  }

  return resultDate;
}

// Example usage:
const today = new Date(); // This should be the current date when placing the order
const expirationDate = getNextSecondFriday(today);
if (expirationDate) {
  console.log(
    `The expiration date is: ${expirationDate.toISOString().split("T")[0]}`,
  );
}

function getNextSecondWeekFriday(inputDate: Date): Date {
  // Ensure the input date is a valid Date object
  if (
    Object.prototype.toString.call(inputDate) !== "[object Date]" ||
    isNaN(inputDate.getTime())
  ) {
    throw new Error("Invalid input date");
  }

  // Check if the input date is Saturday or Sunday
  const dayOfWeek = inputDate.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    throw new Error("Input date cannot be Saturday or Sunday");
  }

  // Calculate the number of days to add to get to the next second week's Friday
  // If the input day is Monday (1), we add 11 days to get to the second week's Friday
  // If the input day is Tuesday (2), we add 10 days, and so on.
  const daysToAdd = 11 - dayOfWeek;

  // Create a new date object for the output date
  const outputDate = new Date(inputDate);
  outputDate.setDate(inputDate.getDate() + daysToAdd);

  return outputDate;
}

function getNextSecondWeekFriday2(inputDate: Date): Date {
  // Ensure the input date is a valid Date object
  if (
    Object.prototype.toString.call(inputDate) !== "[object Date]" ||
    isNaN(inputDate.getTime())
  ) {
    throw new Error("Invalid input date");
  }

  // Check if the input date is Saturday or Sunday
  const dayOfWeek = inputDate.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    throw new Error("Input date cannot be Saturday or Sunday");
  }

  // Calculate the number of days to add to get to the next second week's Friday
  // If the input day is Monday (1), we add 11 days to get to the second week's Friday
  // If the input day is Tuesday (2), we add 10 days, and so on.
  // For Friday (5), we add 14 days to get to the Friday of the week after next.
  const daysToAdd = dayOfWeek === 5 ? 14 : 11 - dayOfWeek;

  // Create a new date object for the output date
  const outputDate = new Date(inputDate);
  outputDate.setDate(inputDate.getDate() + daysToAdd);

  return outputDate;
}

// Example usage:
const inputDate2 = new Date("2024-03-29"); // A Friday
const outputDate2 = getNextSecondWeekFriday2(inputDate2);
console.log(
  `The Friday of the week after next is: ${outputDate2.toDateString()}`,
);

// Example usage:
const inputDate = new Date("2024-03-26"); // A Tuesday
const outputDate = getNextSecondWeekFriday(inputDate);
console.log(`The Friday of the second week is: ${outputDate.toDateString()}`);
