"use client";
// import FetchIBAccountInformation from "@/app/actions/ibapi/fetch-account-info";
import { TrendChange } from "@/db/trend-change";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import React, { useEffect, useState, useTransition } from "react";
import GetOptionChains from "../server-actions/get-option-chains";
import { ChevronRightIcon } from "@radix-ui/react-icons";
import PlaceOptionLimitOrder from "../server-actions/option-limit-order";
import { fetchPortfolioAllData } from "@/app/portfolio/actions/get-portfolio-all-data";

interface RequestResponse {
  data: string;
  status: number;
}

interface OptionChain {
  median: number;
  ask: number;
  bid: number;
  expiration: Date;
  right: string;
  strike: number;
  contractId: string;
}

interface OptionChainProps {
  stockPick: TrendChange;
  session: any;
}

export default function OptionChainDialog({
  stockPick,
  session,
}: OptionChainProps) {
  const [optionLimitOrderPrice, setOptionLimitOrderPrice] =
    useState<string>("0");
  const [isPending, startTransition] = useTransition();
  const [ibAccountNumber, setIBAccountNumber] = useState("");
  const [isConnectedToIB, setIsConnectedToIB] = useState(false);
  const [optionDetail, setOptionDetail] = useState<OptionChain | undefined>(
    undefined,
  );
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      connectIB();
      setOptionLimitOrderPrice(optionDetail?.median.toString() || "0");
    }
    return () => {};
  }, [open]);

  function connectIB() {
    startTransition(() => {
      const func = async () => {
        try {
          // const accounts = await FetchIBAccountInformation();
          const accounts = await fetchPortfolioAllData(session.user.id);
          if (accounts) {
            setIBAccountNumber(JSON.parse(JSON.stringify(accounts))[0].account);
            setIsConnectedToIB(true);
            getOptionChain();
          } else {
            throw new Error("No account data found");
          }
        } catch (error) {
          setIsConnectedToIB(false);
          console.log(
            `Connection to IB Gateway cannot be established!: ${isConnectedToIB}`,
          );
        }
      };
      func();
    });
  }

  const dateOnly = new Date();
  dateOnly.setUTCHours(0, 0, 0, 0); // Set time to midnight (UTC)

  const handleInputChangeLimitPrice = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const newValue = event.target.value;
    // Check if the new value is either empty or a number
    if (newValue === "" || /^\d*\.?\d*$/.test(newValue)) {
      setOptionLimitOrderPrice(newValue);
    }
  };

  const getOptionChain = () => {
    startTransition(async () => {
      try {
        const options = await GetOptionChains(
          stockPick.index,
          stockPick.buyTrade,
        );
        if (!JSON.parse(options).hasOwnProperty("errorMessage")) {
          const filteredOption: OptionChain | undefined = (
            JSON.parse(options) as unknown as OptionChain[]
          )
            .filter(
              (option: any) =>
                option.right === (stockPick.trend === "BULLISH" ? "C" : "P"),
            ) // Filter by right
            .sort(
              (a: any, b: any) =>
                new Date(a.expiration).getTime() -
                new Date(b.expiration).getTime(),
            ) // Sort by expiry
            .pop(); // Get the last item
          console.log(filteredOption);
          setOptionDetail(filteredOption);
          setIsConnectedToIB(true);
          setOptionLimitOrderPrice(optionDetail?.median!.toString() || "0");
        } else {
          console.log(options);
        }
      } catch (error) {
        console.log(error);
      }
    });
  };

  function placeLimitOrder() {
    if (Number(optionLimitOrderPrice) > 0) {
      startTransition(async () => {
        try {
          const placeLimitOrderResponse = await PlaceOptionLimitOrder(
            stockPick.index,
            Number(optionLimitOrderPrice),
            optionDetail?.strike!,
            optionDetail?.expiration!,
            stockPick.trend === "BULLISH" ? "C" : "P",
            1,
          );
        } catch (error) {
          console.log(error);
        }
      });
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogTitle>Option Chain - {stockPick.index}</DialogTitle>
        {/* Rest of your dialog content */}
      </DialogContent>
    </Dialog>
  );
}

/*
<>
<Dialog.Root onOpenChange={setOpen} open={open}>
  <Dialog.Trigger>
    <Button size="1">Option</Button>
  </Dialog.Trigger>
  <Dialog.Content
    style={{ maxWidth: 800 }}
    onPointerDownOutside={(e) => e.preventDefault()}
  >
    <Dialog.Title>
      {stockPick.trend === "BULLISH" ? "CALL OPTION" : "PUT OPTION"}
    </Dialog.Title>
    {/* <Dialog.Description>Review calls and puts</Dialog.Description> */
// {/* <Dialog.Description>
//   {stockPick.trend === "BULLISH" ? "CALL" : "PUT"}
// </Dialog.Description> */}
//     {!isConnectedToIB && (
//       <Callout.Root>
//         <Callout.Icon>
//           <InfoCircledIcon />
//         </Callout.Icon>
//         <Callout.Text>
//           {!isConnectedToIB && isPending ? (
//             "Please wait, trying to connect to IB Gateway..."
//           ) : (
//             <div className="flex flex-col items-center gap-2">
//               <span>
//                 Connection to IB Gateway cannot be established. Make sure
//                 IB Gateway instance is up and running{" "}
//                 <a
//                   href="http://ibkr.graybeach-bc564721.southeastasia.azurecontainerapps.io"
//                   target="_blank"
//                 >
//                   here
//                 </a>{" "}
//                 then try again. If didn't resolved, restarting the server
//                 may help.
//               </span>
//               <IconButton onClick={connectIB}>
//                 <ReloadIcon width="18" height="18" />
//               </IconButton>
//             </div>
//           )}
//         </Callout.Text>
//       </Callout.Root>
//     )}
//     <Inset side="x" my="5">
//       <Table.Root>
//         <Table.Header>
//           <Table.Row>
//             <Table.ColumnHeaderCell>Index</Table.ColumnHeaderCell>
//             <Table.ColumnHeaderCell>Strike</Table.ColumnHeaderCell>
//             <Table.ColumnHeaderCell>Expiration</Table.ColumnHeaderCell>
//             <Table.ColumnHeaderCell>Limit Price</Table.ColumnHeaderCell>
//           </Table.Row>
//         </Table.Header>
//         <Table.Body>
//           <Table.Row>
//             <Table.RowHeaderCell>{stockPick.index}</Table.RowHeaderCell>
//             <Table.Cell>{optionDetail?.strike}</Table.Cell>
//             <Table.Cell>
//               {optionDetail?.expiration
//                 ? new Date(optionDetail?.expiration).toDateString()
//                 : ""}
//             </Table.Cell>
//             <Table.Cell>
//               <div className="flex flx-row items-center justify-between">
//                 <Flex direction="column" gapY="1">
//                   <TextField.Root
//                     size="2"
//                     placeholder="0"
//                     value={optionLimitOrderPrice}
//                     onChange={handleInputChangeLimitPrice}
//                     variant="classic"
//                     className="pl-2 w-20"
//                   ></TextField.Root>
//                   <div className="flex flx-row gap-2 items-center justify-between">
//                     <p className="text-xs">bid: {optionDetail?.bid}</p>
//                     <p className="text-xs">ask: {optionDetail?.ask}</p>
//                     <p className="text-xs">mid: {optionDetail?.median}</p>
//                   </div>
//                 </Flex>
//               </div>
//             </Table.Cell>
//           </Table.Row>
//         </Table.Body>
//       </Table.Root>
//     </Inset>
//     <Flex justify="between" align="center">
//       <Flex
//         gap="1"
//         justify="start"
//         className="text-sm items-center gap-2 flex flex-row"
//       >
//         {isConnectedToIB && (
//           <>
//             <MaskOnIcon color="green" />
//             Connected to IB
//           </>
//         )}
//       </Flex>
//       <Flex gap="3" justify="end">
//         <Button
//           variant="soft"
//           color="gray"
//           onClick={placeLimitOrder}
//           disabled={
//             Number(optionLimitOrderPrice) <= 0 ||
//             !isConnectedToIB ||
//             session.user.role !== "admin"
//           }
//         >
//           {isPending ? (
//             <span className="flex flex-row items-center gap-1">
//               <Spinner size="1" />
//               Please wait...
//             </span>
//           ) : (
//             "Place Limit Order"
//           )}
//         </Button>
//         <Button
//           variant="soft"
//           color="gray"
//           onClick={getOptionChain}
//           disabled={!isConnectedToIB || isPending}
//         >
//           {isPending ? (
//             <span className="flex flex-row items-center gap-1">
//               <Spinner size="2" />
//               Please wait...
//             </span>
//           ) : (
//             "Option Chain"
//           )}
//         </Button>
//         <Dialog.Close>
//           <Button variant="soft" color="gray" disabled={isPending}>
//             Close
//           </Button>
//         </Dialog.Close>
//       </Flex>
//     </Flex>
//   </Dialog.Content>
// </Dialog.Root>
// </>
