import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import SettingsPanelDrawer from "./settings-panel-drawer";
import { TrendChange } from "@/db/trend-change";
import { Dispatch, SetStateAction, useState, useEffect } from "react";
import { DatePicker } from "./date-picker";
import TrendChangesFromPreviousDate from "./trend-changes-from-previous-date";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";
import { Suspense } from "react";
import { SpinnerBasic } from "@/components/spinner-basic";
import { RiskSignalSettings } from "@/types/user-profile";

interface EntryWindowCardProps {
  showStockPicksOnly: boolean;
  setShowStockPicksOnly: Dispatch<SetStateAction<boolean>>;
  sortStockPicksToTheTop: boolean;
  setSortStockPicksToTheTop: Dispatch<SetStateAction<boolean>>;
  showStocksOnly: boolean;
  setShowStocksOnly: Dispatch<SetStateAction<boolean>>;
  userId: string;
  entryWindow: string;
  handleEntryWindowChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  resultTableTrendChange: TrendChange[];
  stockPicks: TrendChange[];
  selectedTrendChangeDate: Date;
  onDateSelect: (date: Date | undefined) => void;
  initialWatchlistItems: string[];
  holdings: PortfolioPosition[];
  debouncedSaveSettings: (settings: RiskSignalSettings) => void;
}

export default function EntryWindowCard({
  showStockPicksOnly,
  setShowStockPicksOnly,
  sortStockPicksToTheTop,
  setSortStockPicksToTheTop,
  showStocksOnly,
  setShowStocksOnly,
  userId,
  entryWindow,
  handleEntryWindowChange,
  resultTableTrendChange,
  stockPicks,
  selectedTrendChangeDate,
  onDateSelect,
  initialWatchlistItems,
  holdings,
  debouncedSaveSettings,
}: EntryWindowCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        // Any async operations here
      } catch (error) {
        console.error("Error initializing data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  return (
    <Suspense fallback={<SpinnerBasic />}>
      {isLoading ? (
        <SpinnerBasic />
      ) : (
        <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-lg">
          <CardHeader className="pb-6 border-b border-slate-100 dark:border-slate-700">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-blue-600 dark:text-blue-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                    Entry Window
                  </h3>
                  <p className="text-sm text-slate-500 dark:text-slate-400">
                    Configure risk parameters
                  </p>
                </div>
              </div>
              <div className="flex items-center pl-4">
                <SettingsPanelDrawer
                  showStockPicksOnly={showStockPicksOnly}
                  setShowStockPicksOnly={setShowStockPicksOnly}
                  sortStockPicksToTheTop={sortStockPicksToTheTop}
                  setSortStockPicksToTheTop={setSortStockPicksToTheTop}
                  showStocksOnly={showStocksOnly}
                  setShowStocksOnly={setShowStocksOnly}
                  userId={userId}
                  entryWindow={entryWindow}
                  debouncedSaveSettings={debouncedSaveSettings}
                />
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-3">
                    <label
                      htmlFor="entry-window"
                      className="text-sm font-medium text-slate-700 dark:text-slate-300"
                    >
                      Risk Percentage
                    </label>
                    <div className="flex items-center bg-white dark:bg-slate-800 rounded-lg border border-slate-300 dark:border-slate-600 shadow-sm hover:border-blue-400 dark:hover:border-blue-500 transition-colors">
                      <Input
                        id="entry-window"
                        type="number"
                        value={entryWindow}
                        onChange={handleEntryWindowChange}
                        className="w-20 h-11 text-sm border-0 focus-visible:ring-0 focus-visible:ring-offset-0 financial-data font-semibold text-slate-900 dark:text-white"
                        min="0"
                        max="100"
                      />
                      <span className="pr-3 text-sm text-slate-500 dark:text-slate-400 font-medium">
                        %
                      </span>
                    </div>
                  </div>
                  <Badge className="h-9 px-4 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700 font-semibold">
                    Found{" "}
                    <span className="ml-1 font-bold">
                      {
                        (showStockPicksOnly
                          ? resultTableTrendChange.filter((r) =>
                              stockPicks.some((s) => s.index === r.index),
                            )
                          : resultTableTrendChange
                        ).length
                      }
                    </span>
                  </Badge>
                </div>
                <DatePicker
                  date={selectedTrendChangeDate}
                  onSelect={onDateSelect}
                />
              </div>

              <div className="flex justify-end">
                <TrendChangesFromPreviousDate
                  selectedTrendChangeDate={selectedTrendChangeDate}
                  initialWatchlistItems={initialWatchlistItems}
                  userId={userId}
                  holdings={holdings}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </Suspense>
  );
}
