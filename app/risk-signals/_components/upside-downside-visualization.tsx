"use client";

import { But<PERSON> } from "@radix-ui/themes";
import React, { useEffect, useState, useTransition } from "react";
import Tesseract from "tesseract.js";
import {
  UpsideDownsideVisualization,
  getMostRecentUpsideDownsidePotential,
  saveUpsideDownsidePotential,
} from "@/db/upside-downside-potential";
import { extractEquityTableUrlRegEx } from "@/app/actions/get-equity-table";
import FetchRiskSignalPage from "@/app/actions/fetch-risk-signals";
import { Table } from "@radix-ui/themes";
export default function DownsideUpsideVisualization() {
  const [hasDownsideUpsideVisualization, setHasDownsideUpsideVisualization] =
    useState(false);
  const [publishedDate, setPublishedDate] = useState<Date>(new Date());
  const [progress, setProgress] = useState(0);
  const [mostRecentUpDownPotential, setMostRecentUpDownPotential] = useState<
    UpsideDownsideVisualization[]
  >([]);
  const [isPending, startTransition] = useTransition();
  useEffect(() => {
    startTransition(() => {
      getMostRecentUpsideDownsidePotential(publishedDate).then((r) => {
        const result = JSON.parse(
          r,
        ) as unknown as UpsideDownsideVisualization[];
        setMostRecentUpDownPotential(result);
      });
    });
  }, []);

  function handleRetrieveVisualization(): void {
    handleUpsideDownsideVisualization();
  }

  const handleUpsideDownsideVisualization = async () => {
    const htmlResult = await FetchRiskSignalPage();
    if (htmlResult) {
      const datePublished =
        /<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/;
      const datePublishedMatch = htmlResult.match(datePublished);
      const nowInUTC = new Date();
      const nowInUTCTimestamp = nowInUTC.getTime();
      let publishedDateInUTCTimestamp = new Date(nowInUTCTimestamp);
      if (datePublishedMatch) {
        const dateString = datePublishedMatch[1].toString();
        // pubDate = new Date(Date.parse(dateString));
        publishedDateInUTCTimestamp = new Date(
          Date.UTC(
            parseInt(dateString.slice(0, 4)), // Year
            parseInt(dateString.slice(5, 7)) - 1, // Month (0-indexed)
            parseInt(dateString.slice(8, 10)), // Day
            parseInt(dateString.slice(11, 13)), // Hour
            parseInt(dateString.slice(14, 16)), // Minute
            parseInt(dateString.slice(17, 19)), // Second
          ),
        );
        setPublishedDate(publishedDateInUTCTimestamp);
      }
      const equityTablePNGUrl = await extractEquityTableUrlRegEx(htmlResult);
      if (equityTablePNGUrl) {
        const response = await fetch("/api/hedgeye/risk-signal-visualization", {
          method: "POST",
          headers: {
            "Cache-Control": "no-store, max-age=0",
          },
          body: JSON.stringify(equityTablePNGUrl),
        });
        if (
          response.ok &&
          response.headers.get("Content-Type") === "image/png"
        ) {
          const data = await response.blob();
          recognize(data, publishedDateInUTCTimestamp);
        }
      }
    }
  };

  const recognize = async (file: any, pubDate: Date) => {
    Tesseract.recognize(file, "eng", {
      logger: (m) => {
        if (m.status === "recognizing text") {
          setProgress(m.progress);
        }
      },
    }).then(async ({ data: { text } }) => {
      // The first row here is the header. But because we are splitting by whitespace, "Downside Potential" becomes two columns which is wrong. So we will just bypass the first row, since we know its structure and columns names already
      // The last row is empty.
      const rows = text.split("\u000A"); // \n
      const table = rows.map((r) => {
        const cols = r.split(" ");
        return cols;
      });
      const upsideDownsideVisualization: UpsideDownsideVisualization[] = [];
      // Use slice to skip the first and last row
      const tableWithoutFirstAndLast = table.slice(1, -1);
      let oIdx: number = 0;
      tableWithoutFirstAndLast.map((rs) => {
        let rowData: UpsideDownsideVisualization =
          {} as UpsideDownsideVisualization;
        rowData.originalIndex = oIdx;
        rowData.ticker = rs[0];
        rowData.price = Number(rs[1].replace(/,/g, ""));
        rowData.trend = rs[2].toString();
        rowData.downsidePotential = parseFloat(rs[3]);
        rowData.upsidePotential = parseFloat(rs[4]);
        rowData.date = pubDate;
        upsideDownsideVisualization.push(rowData);
        oIdx++;
      });
      await saveUpsideDownsidePotential(upsideDownsideVisualization);
    });
  };

  return (
    <div>
      <h3>Downside vs. Upside Visualization</h3>
      {!hasDownsideUpsideVisualization && (
        <>
          {mostRecentUpDownPotential.length === 0 && "Not yet available!"}
          <Button onClick={handleRetrieveVisualization}>Retrieve</Button>
        </>
      )}
      {!isPending && (
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Ticker</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Price</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Trend</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>
                Downside Potential
              </Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Upside Potential</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {mostRecentUpDownPotential.map((r) => {
              return (
                <Table.Row key={r.ticker}>
                  <Table.RowHeaderCell align="left">
                    {r.ticker}
                  </Table.RowHeaderCell>
                  <Table.Cell align="right">
                    {r.price.toLocaleString("en-US", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </Table.Cell>
                  <Table.Cell
                    align="center"
                    className={`${r.trend === "BULLISH" ? "bg-green-400" : r.trend === "BEARISH" ? "bg-red-400" : "bg-orange-400"}`}
                  >
                    {r.trend}
                  </Table.Cell>
                  <Table.Cell align="right">{r.downsidePotential}%</Table.Cell>
                  <Table.Cell align="right">{r.upsidePotential}%</Table.Cell>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table.Root>
      )}
    </div>
  );
}

// import { RiskSignal } from "@/types/stocks";
// import { TrendChange } from "@/db/trend-change";
// import {
//   TableBody,
//   TableCell,
//   TableColumnHeaderCell,
//   TableHeader,
//   TableRoot,
//   TableRow,
//   TableRowHeaderCell,
// } from "@radix-ui/themes";
// import { useRef, useState } from "react";
// import Tesseract from "tesseract.js";

// export default function UpsideDownsideVisualization() {
//   const [progress, setProgress] = useState(0);
//   const [result, setResult] = useState<RiskSignal[]>([]);
//   const [resultTrendChange, setResultTrendChange] =
//     useState<HTMLTableElement | null>(null);
//   const [resultTableTrendChange, setResultTableTrendChange] = useState<
//     TrendChange[]
//   >([]);

//   const handleGetRiskSignals = async () => {
//     const response = await fetch("/api/hedgeye", {
//       headers: {
//         "Cache-Control": "no-store, max-age=0",
//       },
//     });
//     if (response.ok && response.headers.get("Content-Type") === "image/png") {
//       const data = await response.blob();
//       recognize(data);
//     } else {
//       extractTrendChangeTable((await response.json()).htmlRaw);
//     }
//   };

//   const extractTrendChangeTable = (htmlText: string) => {
//     const parser = new DOMParser();
//     const doc = parser.parseFromString(htmlText, "text/html");
//     const tableTrendChange = doc.querySelector('table[class*="dtr-table"]');
//     setResultTrendChange(tableTrendChange as HTMLTableElement);
//     const tbl = parseTableToTrendChange(tableTrendChange as HTMLTableElement);
//     setResultTableTrendChange(tbl);
//   };

//   // Define a function that takes a table element as an input
//   function parseTableToTrendChange(table: HTMLTableElement): TrendChange[] {
//     // Initialize an empty array to store the results
//     let result: TrendChange[] = [];
//     let highlight: TrendChange[] = [];

//     // Get the table body element
//     let tbody = table.querySelector("tbody");

//     // Loop through each row in the table body
//     for (let row of tbody!.rows) {
//       // Initialize an empty object to store the current row data
//       let rowData: TrendChange = {} as TrendChange;

//       // Get the first cell in the row, which contains the index and the description
//       let firstCell = row.cells[0];

//       // Get the text content of the first cell, which contains the index and the trend in parentheses
//       let firstCellText = firstCell.textContent;

//       // Use a regular expression to extract the index and the trend from the text
//       let match = firstCellText!.match(/(.+) \((.+)\)/);

//       // If the match is successful, assign the index and the trend to the rowData object
//       if (match) {
//         rowData.index = match[1];
//         rowData.trend = match[2];
//       }

//       // Get the em element in the first cell, which contains the description
//       let em = firstCell.querySelector("em");

//       // If the em element exists, assign the description to the rowData object
//       if (em) {
//         rowData.description = em.textContent!;
//       }

//       // Get the second, third, and fourth cells in the row, which contain the buyTrade, sellTrade, and previousClose values
//       let secondCell = row.cells[1];
//       let thirdCell = row.cells[2];
//       let fourthCell = row.cells[3];

//       // Parse the text content of the cells as numbers and assign them to the rowData object
//       rowData.buyTrade = Number(secondCell.textContent!.replace(/,/g, ""));
//       rowData.sellTrade = Number(thirdCell.textContent!.replace(/,/g, ""));
//       rowData.previousClose = Number(fourthCell.textContent!.replace(/,/g, ""));

//       // Push the rowData object to the result array
//       result.push(rowData);

//       // Check if the trend is BULLISH and if the previous close is within 10% of the buyTrade
//       if (rowData.trend.toUpperCase() === "BULLISH") {
//         let tenPercentOfBuyTrade = rowData.buyTrade * 0.1;
//         let difference = Math.abs(rowData.buyTrade - rowData.previousClose);

//         // If the difference is less than or equal to 10% of the buyTrade, add to highlight
//         if (difference <= tenPercentOfBuyTrade) {
//           highlight.push(rowData);
//         }
//       }
//     }

//     // Return the result array
//     return result;
//   }

//   const toHighlight = (rowData: TrendChange) => {
//     if (rowData.trend.toUpperCase() === "BULLISH") {
//       const tenPercentOfBuyTrade = rowData.buyTrade * 0.1;
//       const difference = Math.abs(rowData.buyTrade - rowData.previousClose);

//       // If the difference is less than or equal to 10% of the buyTrade, add to highlight
//       if (difference <= tenPercentOfBuyTrade) {
//         return true;
//       }
//       return false;
//     }
//   };

//   function customNumberFormatter(number: number): string {
//     // Remove commas and check if the number is an integer
//     if (Number(number.toString().replace(/,/g, "")) % 1 === 0) {
//       // If it's an integer, format without decimal places
//       return number.toLocaleString("en-US");
//     } else {
//       // If it's not an integer, format with two decimal places
//       return number.toLocaleString("en-US", {
//         minimumFractionDigits: 2,
//         maximumFractionDigits: 2,
//       });
//     }
//   }

//   return (
//     <div className="mt-5">
//       {/* <InputFile /> */}
//       <div className="flex flex-col items-center gap-2">
//         <button onClick={() => handleGetRiskSignals()} className="border p-2">
//           Get risk range signals
//         </button>
//         <progress value={progress} max={1} />
//         {result.length > 0 && (
//           <TableRoot variant="surface">
//             <TableHeader>
//               <TableRow>
//                 <TableColumnHeaderCell>Ticker</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Price</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Trend</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>
//                   Downside Potential
//                 </TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Upside Potential</TableColumnHeaderCell>
//               </TableRow>
//             </TableHeader>
//             <TableBody>
//               {result.map((r) => {
//                 return (
//                   <TableRow key={r.ticker}>
//                     <TableRowHeaderCell align="left">
//                       {r.ticker}
//                     </TableRowHeaderCell>
//                     <TableCell align="right">
//                       {r.price.toLocaleString("en-US", {
//                         minimumFractionDigits: 2,
//                         maximumFractionDigits: 2,
//                       })}
//                     </TableCell>
//                     <TableCell
//                       align="center"
//                       className={`${r.trend === "BULLISH" ? "bg-green-400" : r.trend === "BEARISH" ? "bg-red-400" : "bg-orange-400"}`}
//                     >
//                       {r.trend}
//                     </TableCell>
//                     <TableCell align="right">{r.downsidePotential}</TableCell>
//                     <TableCell align="right">{r.upsidePotential}</TableCell>
//                   </TableRow>
//                 );
//               })}
//             </TableBody>
//           </TableRoot>
//         )}
//         {/* {resultTrendChange && (
//           <table
//             dangerouslySetInnerHTML={{ __html: resultTrendChange.outerHTML }}
//           />
//         )} */}
//         {resultTableTrendChange.length > 0 && (
//           <TableRoot variant="surface">
//             <TableHeader>
//               <TableRow>
//                 <TableColumnHeaderCell>Index</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Trend</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Buy Trade</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Sell Trade</TableColumnHeaderCell>
//                 <TableColumnHeaderCell>Previous Close</TableColumnHeaderCell>
//               </TableRow>
//             </TableHeader>
//             <TableBody>
//               {resultTableTrendChange.map((r) => {
//                 return (
//                   <TableRow
//                     key={r.index}
//                     className={`${toHighlight(r) ? "bg-green-400" : ""}`}
//                   >
//                     <TableRowHeaderCell align="left">
//                       {r.index}
//                     </TableRowHeaderCell>
//                     <TableCell
//                       align="center"
//                       // className={`${r.trend === "BULLISH" ? "bg-green-400" : r.trend === "BEARISH" ? "bg-red-400" : "bg-orange-400"}`}
//                     >
//                       {r.trend}
//                     </TableCell>
//                     <TableCell align="right">
//                       {customNumberFormatter(r.buyTrade)}
//                     </TableCell>
//                     <TableCell align="right">
//                       {customNumberFormatter(r.sellTrade)}
//                     </TableCell>
//                     <TableCell align="right">
//                       {customNumberFormatter(r.previousClose)}
//                     </TableCell>
//                   </TableRow>
//                 );
//               })}
//             </TableBody>
//           </TableRoot>
//         )}
//       </div>
//     </div>
//   );
// }
