import { SpinnerBasic } from "@/components/spinner-basic";
import { getArchivesOfRiskSignals } from "@/db/trend-change";
import React, { useEffect, useState, useTransition } from "react";

export default function TrendChangeArchive() {
  const [archives, setArchives] = useState<{
    trendChanges: Date[];
    visualization: Date[];
  }>();
  const [isPending, startTransition] = useTransition();
  useEffect(() => {
    startTransition(() => {
      const func = async () => {
        const dates = await getArchivesOfRiskSignals();
        if (dates) {
          setArchives(dates);
        }
      };
      func();
    });
  }, []);
  // if (isPending) return <SpinnerBasic />;
  return (
    <div>
      <h2>Trend Changes Archives</h2>
      <ul>
        {archives?.trendChanges.length &&
          archives?.trendChanges.map((tc) => {
            return (
              <li key={Math.random() + Math.random()}>{tc.toDateString()}</li>
            );
          })}
      </ul>
      <h2>Visualization Archives</h2>
      <ul>
        {archives?.visualization.length &&
          archives?.visualization.map((tc) => {
            return (
              <li key={Math.random() + Math.random()}>{tc.toDateString()}</li>
            );
          })}
      </ul>
    </div>
  );
}
