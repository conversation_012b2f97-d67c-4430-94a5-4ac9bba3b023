import { TrendChange } from "@/db/trend-change";
import { Table, Text, Badge } from "@radix-ui/themes";
import { ArrowUpIcon, ArrowDownIcon, DashIcon } from "@radix-ui/react-icons";
import { customNumberFormatter } from "@/lib/utils-client";
import OptionChainDialog from "./option-chain";
import OrderDialog from "@/app/portfolio/_components/order-dialog";
import WatchlistButton from "./watchlist-button";
import { HoldingsIndicator } from "@/app/_components/holdings-indicator";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";

interface DesktopTableViewProps {
  resultTableTrendChange: TrendChange[];
  showStockPicksOnly: boolean;
  stockPicks: TrendChange[];
  entryWindow: string;
  session: any;
  watchlistItems: string[];
  onToggleWatchlist: (symbol: string) => Promise<void>;
  holdings: PortfolioPosition[] | null;
}

export default function DesktopTableView({
  resultTableTrendChange,
  showStockPicksOnly,
  stockPicks,
  entryWindow,
  session,
  watchlistItems,
  onToggleWatchlist,
  holdings,
}: DesktopTableViewProps) {
  console.log(
    "DesktopTableView() called: resultTableTrendChange.length ->",
    resultTableTrendChange.length,
  );
  console.log("DesktopTableView: showStockPicksOnly", showStockPicksOnly);
  return (
    <div className="hidden md:block h-[calc(100vh-300px)] overflow-auto">
      <Table.Root
        variant="surface"
        className="relative bg-white dark:bg-slate-900"
      >
        <Table.Header className="bg-slate-50 dark:bg-slate-800/50">
          <Table.Row className="border-b border-slate-200 dark:border-slate-700">
            <Table.ColumnHeaderCell className="sticky left-0 z-10 bg-slate-50 dark:bg-slate-800/50 after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:bg-slate-200 dark:after:bg-slate-700 font-semibold text-slate-900 dark:text-white">
              Symbol
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell className="font-semibold text-slate-900 dark:text-white">
              Trend
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell
              align="right"
              className="font-semibold text-slate-900 dark:text-white"
            >
              Buy
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell
              align="right"
              className="font-semibold text-slate-900 dark:text-white"
            >
              Sell
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell
              align="right"
              className="font-semibold text-slate-900 dark:text-white"
            >
              Previous
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell
              align="right"
              className="font-semibold text-slate-900 dark:text-white"
            >
              Window
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell className="font-semibold text-slate-900 dark:text-white">
              Action
            </Table.ColumnHeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {resultTableTrendChange.map((r) => (
            <Table.Row
              key={r.index}
              className={`
                ${
                  stockPicks.some((s) => s.index === r.index)
                    ? "dark:bg-green-900/30 bg-green-50/80 border-green-200 dark:border-green-800"
                    : ""
                }
                hover:dark:bg-slate-800/50 hover:bg-slate-50/80
                transition-all duration-200 border-b border-slate-100 dark:border-slate-800
              `}
            >
              <Table.RowHeaderCell className="sticky left-0 z-10 bg-inherit after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:bg-slate-200 dark:after:bg-slate-800">
                <div className="flex items-center gap-3">
                  <div className="flex flex-col">
                    <Text
                      weight="bold"
                      className="text-foreground font-semibold"
                    >
                      {r.index}
                    </Text>
                    {stockPicks.some((s) => s.index === r.index) && (
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                        Stock Pick
                      </span>
                    )}
                  </div>
                  <HoldingsIndicator symbol={r.index} holdings={holdings} />
                </div>
              </Table.RowHeaderCell>
              <Table.Cell>
                <Badge
                  variant="soft"
                  color={
                    r.trend === "BULLISH"
                      ? "green"
                      : r.trend === "BEARISH"
                        ? "red"
                        : "blue"
                  }
                  className="flex items-center gap-1"
                >
                  {r.trend === "BULLISH" ? (
                    <ArrowUpIcon />
                  ) : r.trend === "BEARISH" ? (
                    <ArrowDownIcon />
                  ) : (
                    <DashIcon />
                  )}
                  {r.trend}
                </Badge>
              </Table.Cell>
              <Table.Cell align="right" className="financial-data font-medium">
                ${customNumberFormatter(r.buyTrade)}
              </Table.Cell>
              <Table.Cell align="right" className="financial-data font-medium">
                ${customNumberFormatter(r.sellTrade)}
              </Table.Cell>
              <Table.Cell
                align="right"
                className="financial-data text-muted-foreground"
              >
                ${customNumberFormatter(r.previousClose)}
              </Table.Cell>
              <Table.Cell align="right" className="financial-data">
                <span className="text-amber-600 dark:text-amber-400 font-semibold">
                  $
                  {customNumberFormatter(
                    (r.sellTrade - r.buyTrade) * (Number(entryWindow) / 100),
                  )}
                </span>
              </Table.Cell>
              <Table.Cell>
                <div className="flex items-center gap-2">
                  {session.user.role === "admin" &&
                    stockPicks.some((s) => s.index === r.index) && (
                      <div className="flex items-center gap-2">
                        <OrderDialog
                          userId={session.user.id}
                          symbol={r.index}
                          price={r.previousClose}
                          action={r.trend === "BULLISH" ? "BUY" : "SELL"}
                        />
                      </div>
                    )}
                  <WatchlistButton
                    symbol={r.index}
                    isInWatchlist={watchlistItems.includes(r.index)}
                    onToggleWatchlist={onToggleWatchlist}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </div>
  );
}
