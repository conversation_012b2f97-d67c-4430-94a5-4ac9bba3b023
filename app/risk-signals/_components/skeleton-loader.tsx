import { Table } from "@radix-ui/themes";
import React from "react";

const SkeletonLoader = () => {
  return (
    <Table.Root variant="surface">
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeaderCell>Index</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Trend</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Buy Trade</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Sell Trade</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Previous Close</Table.ColumnHeaderCell>
        </Table.Row>
      </Table.Header>
      {[].map((r) => {
        return (
          <Table.Row key={r}>
            <Table.RowHeaderCell align="left"></Table.RowHeaderCell>
            <Table.Cell align="center"></Table.Cell>
            <Table.Cell align="right"></Table.Cell>
            <Table.Cell align="right"></Table.Cell>
            <Table.Cell align="right"></Table.Cell>
          </Table.Row>
        );
      })}
    </Table.Root>
  );
};

export default SkeletonLoader;
