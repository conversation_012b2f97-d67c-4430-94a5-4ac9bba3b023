"use client";
import FetchRiskSignalPage, {
  parseTrendChangeLink,
} from "@/app/actions/fetch-risk-signals";
import { extractEquityTableUrlRegEx } from "@/app/actions/get-equity-table";
import {
  TrendChange,
  getLastSyncedTrendChangeDate,
  getTrendChangesByDateOrMostRecent,
  saveTrendChanges,
} from "@/db/trend-change";
import { Text, Container } from "@radix-ui/themes";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  useEffect,
  useState,
  useTransition,
  useCallback,
  useMemo,
} from "react";
import SpinnerBasicWithText, { SpinnerBasic } from "@/components/spinner-basic";
import { calculateStockPicks } from "@/app/actions/stock-picks";
import { Flex } from "@radix-ui/themes";
import { ReloadIcon, InfoCircledIcon } from "@radix-ui/react-icons";
import {
  getSymbols,
  getAllSymbols,
  CachedInstrument,
} from "@/app/models/cache-symbols";
import { Button } from "@/components/ui/button";
import { subscribeToPushNotifications } from "@/utils/notifications";

import { debounce } from "lodash";
import {
  RiskSignalSettings,
  UserProfile,
  UserProfileSettings,
} from "@/types/user-profile";
import MobileCardView from "./mobile-card-view";
import DesktopTableView from "./desktop-table-view";
import EntryWindowCard from "./entry-window-card";
import { upsertUserProfileRiskSignalSettings } from "@/app/actions/upsert-user-profile-settings";
import {
  addToWatchlist,
  getWatchlistItems,
  removeFromWatchlist,
} from "@/app/watchlist/actions";
import { toast } from "@/hooks/use-toast";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";
import { fetchHoldingsData } from "@/app/portfolio/actions/get-holdings";
import { testIBKRConnection } from "@/app/profile/actions/ibkr-test-connection";
import { Suspense } from "react";
import { getCachedInstrumentDetailBatch } from "@/app/actions/get-instrument-detail";
import { CachedSymbol } from "@/app/models/symbols";

interface TrendChangeComponentProps {
  session: any;
  userProfile: UserProfile | null;
  initialWatchlistItems: string[];
  initialHoldings: PortfolioPosition[];
}

export default function TrendChangeComponent({
  session,
  userProfile,
  initialWatchlistItems,
  initialHoldings,
}: TrendChangeComponentProps) {
  console.log("TrendChangeComponent - Initial Props:", {
    hasUserProfile: !!userProfile,
    settings: userProfile?.settings?.riskSignalSettings,
  });

  const [isPending, startTransition] = useTransition();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [resultTableTrendChange, setResultTableTrendChange] = useState<
    TrendChange[]
  >([]);
  // This is the original result table that is fetched from the database with no filtering applied
  const [originalResultTableTrendChange, setOriginalResultTableTrendChange] =
    useState<TrendChange[]>([]);
  const [stockPicks, setStockPicks] = useState<TrendChange[]>([]);
  const [showStockPicksOnly, setShowStockPicksOnly] = useState<boolean>(() => {
    const initialValue =
      userProfile?.settings?.riskSignalSettings?.showStockPicksOnly ?? false;
    console.log("Initializing showStockPicksOnly:", initialValue);
    return initialValue;
  });
  const [showStocksOnly, setShowStocksOnly] = useState<boolean>(
    userProfile?.settings?.riskSignalSettings?.showStockInstrumentsOnly ??
      false,
  );
  const [sortStockPicksToTheTop, setSortStockPicksToTheTop] = useState<boolean>(
    userProfile?.settings?.riskSignalSettings?.sortStockPicksToTop ?? false,
  );
  const [sortWatchlistToTheTop, setSortWatchlistToTheTop] =
    useState<boolean>(true);
  const [watchlistItems, setWatchlistItems] = useState<string[]>(
    initialWatchlistItems,
  );
  const [selectedTrendChangeDate, setSelectedTrendChangeDate] = useState<Date>(
    new Date(),
  );
  const [holdings, setHoldings] =
    useState<PortfolioPosition[]>(initialHoldings);
  // TODO: This is the date of the last trend change that was synced from Hedgeye. We need to retrieve this from the database.
  const [
    trendChangeLastSyncDateFromHedgeye,
    setTrendChangeLastSyncDateFromHedgeye,
  ] = useState<Date>(new Date());
  // State to store the input value as a string to handle backspace correctly
  const [entryWindow, setEntryWindow] = useState<string>("10");
  const [resultCount, setResultCount] = useState<number>(-1);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [isLoadingHoldings, setIsLoadingHoldings] = useState(false);

  // Add a debug effect
  useEffect(() => {
    console.log(
      "showStockPicksOnly Effect - Current Value:",
      showStockPicksOnly,
    );
    console.log(
      "Current userProfile settings:",
      userProfile?.settings?.riskSignalSettings,
    );
  }, [showStockPicksOnly, userProfile]);

  useEffect(() => {
    if (userProfile?.settings?.riskSignalSettings?.defaultEntryWindow) {
      setEntryWindow(
        userProfile.settings.riskSignalSettings.defaultEntryWindow.toString(),
      );
    }
  }, [userProfile]);

  useEffect(() => {
    console.log("showStockPicksOnly changed:", showStockPicksOnly);
  }, [showStockPicksOnly]);

  useEffect(() => {
    getLastSyncedTrendChangeDate().then((date) => {
      setTrendChangeLastSyncDateFromHedgeye(new Date(date));
      setSelectedTrendChangeDate(new Date(date));
    });
  }, []);

  useEffect(() => {
    // Use requestIdleCallback to wait for browser's idle time
    const idleCallback =
      window.requestIdleCallback || ((cb) => setTimeout(cb, 1));

    const fetchHoldings = async () => {
      if (holdings.length === 0) {
        setIsLoadingHoldings(true);
        try {
          const result = await testIBKRConnection(session?.user?.id!);
          if (result) {
            const holdingsData = await fetchHoldingsData(session?.user?.id!);
            console.log("Holdings data ->", holdingsData);
            setHoldings(holdingsData || []);
          }
        } catch (error) {
        } finally {
          setIsLoadingHoldings(false);
        }
      }
    };

    // Schedule the fetch during idle time
    const idleCallbackId = idleCallback(() => {
      // Add to the next macrotask queue
      setTimeout(() => {
        fetchHoldings();
      }, 0);
    });

    // Cleanup
    return () => {
      if (window.cancelIdleCallback) {
        window.cancelIdleCallback(idleCallbackId);
      }
    };
  }, []); // Empty dependency array means this runs once after initial render

  useEffect(() => {
    // Check if notifications are already enabled
    if ("Notification" in window) {
      setNotificationsEnabled(Notification.permission === "granted");
    }
  }, []);

  // 1. First, handle the initial data loading
  async function updateTrendChangeModel(trendChangeList: TrendChange[]) {
    try {
      setLoading(true);
      // Store the original list with indices first
      const indexedList = trendChangeList.map((item, index) => ({
        ...item,
        originalIndex: index,
      }));

      // Set both lists initially
      setOriginalResultTableTrendChange(indexedList);
      setResultTableTrendChange(indexedList);

      // Calculate initial stock picks
      const calculatedStockPicks = await calculateStockPicks(
        indexedList,
        Number(entryWindow),
      );
      setStockPicks(calculatedStockPicks);
      setResultCount(calculatedStockPicks.length);
    } catch (error) {
      console.error("Failed to update trend change model:", error);
    } finally {
      setLoading(false);
    }
  }

  // 2. Handle filtering in a separate function
  const applyFilters = useCallback(async () => {
    console.log("applyFilters called with settings:", {
      showStocksOnly,
      showStockPicksOnly,
      originalTableLength: originalResultTableTrendChange.length,
      stockPicksLength: stockPicks.length,
    });

    if (!originalResultTableTrendChange.length) return;

    // Create a cache key for the current filter settings
    const cacheKey = `filteredList_${showStocksOnly}_${showStockPicksOnly}_${sortStockPicksToTheTop}_${originalResultTableTrendChange.length}_${stockPicks.length}`;

    // Check if we have a cached result
    const cachedResult = sessionStorage.getItem(cacheKey);
    if (cachedResult) {
      try {
        setResultTableTrendChange(JSON.parse(cachedResult));
        return;
      } catch (e) {
        console.error("Failed to parse cached filtered list:", e);
      }
    }

    let filteredList = [...originalResultTableTrendChange];

    try {
      // Calculate stock picks for display regardless of filters
      const currentStockPicks = stockPicks.map((pick) => pick.index);
      console.log("Current stock picks:", {
        count: currentStockPicks.length,
        picks: currentStockPicks,
      });

      // Always update the result count (Found X) based on stock picks
      setResultCount(currentStockPicks.length);

      // Apply filters to the display table
      if (showStockPicksOnly) {
        console.log("Applying stock picks filter");
        filteredList = filteredList.filter((r) =>
          currentStockPicks.includes(r.index),
        );
        console.log("After stock picks filter:", {
          filteredLength: filteredList.length,
        });
      } else if (showStocksOnly) {
        // Only apply stocks filter if stock picks filter is not enabled
        console.log("Applying stocks only filter");

        // Use a cached symbols list if available
        let cachedSymbols: CachedInstrument[] = [];
        const cachedSymbolsStr = sessionStorage.getItem("cachedSymbols");

        if (cachedSymbolsStr) {
          try {
            cachedSymbols = JSON.parse(cachedSymbolsStr);
          } catch (e) {
            console.error("Failed to parse cached symbols:", e);
          }
        }

        if (cachedSymbols.length === 0) {
          cachedSymbols = await getAllSymbols();

          if (cachedSymbols.length === 0) {
            const cachedInstruments = await getCachedInstrumentDetailBatch(
              originalResultTableTrendChange.map((r) => r.index),
            );
            cachedSymbols = Array.from(cachedInstruments.values()).map(
              (cachedInstrument: CachedSymbol) => ({
                ...cachedInstrument,
                isStock:
                  cachedInstrument.securityType2 === "Common Stock" ||
                  cachedInstrument.securityType === "Common Stock",
              }),
            );
          }

          // Cache the symbols
          try {
            sessionStorage.setItem(
              "cachedSymbols",
              JSON.stringify(cachedSymbols),
            );
          } catch (e) {
            console.error("Failed to cache symbols:", e);
          }
        }

        console.log("Filtering stocks:", {
          beforeFilter: filteredList.length,
          totalSymbols: cachedSymbols.length,
        });

        if (cachedSymbols.length > 0) {
          filteredList = filteredList.filter((r) => {
            const matchingSymbol = cachedSymbols.find(
              (s) => s.ticker === r.index && s.isStock && s.ticker !== "GOLD",
            );
            return !!matchingSymbol;
          });
        }

        console.log("After stock filter:", {
          afterFilter: filteredList.length,
        });
      }

      // Cache the filtered list
      try {
        sessionStorage.setItem(cacheKey, JSON.stringify(filteredList));
      } catch (e) {
        console.error("Failed to cache filtered list:", e);
      }

      console.log("Setting final filtered list:", {
        length: filteredList.length,
        showStockPicksOnly,
        showStocksOnly,
        stockPicksCount: currentStockPicks.length,
        resultCount: currentStockPicks.length,
      });

      setResultTableTrendChange(filteredList);
    } catch (error) {
      console.error("Failed to apply filters:", error);
    }
  }, [
    originalResultTableTrendChange,
    showStocksOnly,
    showStockPicksOnly,
    stockPicks,
    sortStockPicksToTheTop,
  ]);

  // 3. Effect to handle filter changes
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // 4. Effect to recalculate stock picks when entry window changes
  useEffect(() => {
    if (originalResultTableTrendChange.length > 0) {
      const recalculateStockPicks = async () => {
        const calculatedStockPicks = await calculateStockPicks(
          originalResultTableTrendChange,
          Number(entryWindow),
        );
        setStockPicks(calculatedStockPicks);
        setResultCount(calculatedStockPicks.length);
      };
      recalculateStockPicks();
    }
  }, [entryWindow, originalResultTableTrendChange]);

  // 5. Sorting logic
  const sortedTrendChanges = useMemo(() => {
    return [...resultTableTrendChange].sort((a, b) => {
      if (sortWatchlistToTheTop) {
        const isAWatchlisted = watchlistItems.includes(a.index);
        const isBWatchlisted = watchlistItems.includes(b.index);
        if (isAWatchlisted !== isBWatchlisted) {
          return isAWatchlisted ? -1 : 1;
        }
      }

      if (sortStockPicksToTheTop) {
        const isAPick = stockPicks.some((pick) => pick.index === a.index);
        const isBPick = stockPicks.some((pick) => pick.index === b.index);
        if (isAPick !== isBPick) {
          return isAPick ? -1 : 1;
        }
      }

      return (a.originalIndex ?? 0) - (b.originalIndex ?? 0);
    });
  }, [
    resultTableTrendChange,
    sortWatchlistToTheTop,
    sortStockPicksToTheTop,
    watchlistItems,
    stockPicks,
  ]);

  // Called and used from the useEffect()
  const getLatestTrendChange = async () => {
    try {
      const date = new Date(selectedTrendChangeDate);
      const trendChangeList = await getTrendChangesByDateOrMostRecent(date);
      return trendChangeList;
    } catch (err) {
      setError(
        err instanceof Error ? err : new Error("Failed to fetch trend changes"),
      );
      return null;
    }
  };

  // Optimize the useEffect that fetches data
  // This is the useEffect that fetches risk signals from hedgeye for trend changes
  useEffect(() => {
    let mounted = true;
    const controller = new AbortController();

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const latestTrendChangeStr = await getLatestTrendChange();

        // Return early if component is unmounted
        if (!mounted) return;

        // If we have data from the database
        if (latestTrendChangeStr) {
          const latestTrendChange = JSON.parse(latestTrendChangeStr);
          if (latestTrendChange && latestTrendChange.length > 0) {
            await updateTrendChangeModel(latestTrendChange);
            setLoading(false);
            return;
          }
        }

        // If no data in database, fetch from external source
        await fetchRiskSignalPage();
      } catch (err) {
        console.error("Failed to fetch trend change:", err);
        setError(
          err instanceof Error
            ? err
            : new Error("Failed to fetch trend changes"),
        );
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    startTransition(() => {
      fetchData();
    });

    return () => {
      mounted = false;
      controller.abort();
    };
  }, [selectedTrendChangeDate]); // Only re-run when date changes

  const makeTrendChangeModel = funcMakeTrendChangeModel();

  const fetchRiskSignalPage = async (manual: boolean = false) => {
    try {
      setLoading(true);
      const html = await FetchRiskSignalPage();
      let resultTrendChanges: TrendChange[] = [];

      const equityTableUrl = await extractEquityTableUrlRegEx(html);

      if (equityTableUrl) {
        const lastTrendChangeLink = await parseTrendChangeLink(html);
        setSelectedTrendChangeDate(lastTrendChangeLink?.date!);

        // Instead of nesting promises, use await
        const trendChangeHtml = await FetchRiskSignalPage(
          lastTrendChangeLink?.href,
        );
        resultTrendChanges = await makeTrendChangeModel(
          trendChangeHtml,
          lastTrendChangeLink?.date!,
        );
        await saveTrendChanges(resultTrendChanges);
        await updateTrendChangeModel(resultTrendChanges);
      } else {
        const datePublished =
          /<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/;
        const datePublishedMatch = html.match(datePublished);

        if (datePublishedMatch) {
          const dateString = datePublishedMatch[1].toString();
          const publishedDateInUTCTimestamp = new Date(
            Date.UTC(
              parseInt(dateString.slice(0, 4)),
              parseInt(dateString.slice(5, 7)) - 1,
              parseInt(dateString.slice(8, 10)),
              parseInt(dateString.slice(11, 13)),
              parseInt(dateString.slice(14, 16)),
              parseInt(dateString.slice(17, 19)),
            ),
          );
          setSelectedTrendChangeDate(publishedDateInUTCTimestamp);
          resultTrendChanges = await makeTrendChangeModel(
            html,
            publishedDateInUTCTimestamp,
          );
          await saveTrendChanges(resultTrendChanges);
          await updateTrendChangeModel(resultTrendChanges);
        }
      }
    } catch (error) {
      console.error("Error in fetchRiskSignalPage:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshData = async () => {
    try {
      // Your existing refresh logic here
      await fetch("/api/hedgeye/risk-range-signals", {
        method: "GET",
      });

      // If notifications aren't enabled yet, request permission
      // if (!notificationsEnabled && "Notification" in window) {
      //   const permission = await Notification.requestPermission();
      //   if (permission === "granted") {
      //     setNotificationsEnabled(true);
      //     await subscribeToPushNotifications();
      //   }
      // }

      // Send notification to all subscribed users
      // await fetch("/api/push/notify", {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify({
      //     message: "Risk signals data has been updated!",
      //   }),
      // });
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  // Create a single debounced save function for all settings
  const debouncedSaveSettings = useMemo(
    () =>
      debounce(async (settings: RiskSignalSettings) => {
        console.log("Debounced settings save triggered:", settings);
        try {
          const userSettings: UserProfileSettings = {
            riskSignalSettings: settings,
          };

          // Immediately update local state before saving
          setShowStockPicksOnly(settings.showStockPicksOnly);
          setSortStockPicksToTheTop(settings.sortStockPicksToTop);
          setShowStocksOnly(settings.showStockInstrumentsOnly);

          // Save to backend
          await upsertUserProfileRiskSignalSettings(
            userSettings,
            session.user.id,
          );

          console.log("Settings saved successfully:", settings);
        } catch (error) {
          console.error("Failed to save settings:", error);
          // Revert local state if save fails
          setShowStockPicksOnly(
            userProfile?.settings?.riskSignalSettings?.showStockPicksOnly ??
              false,
          );
          setSortStockPicksToTheTop(
            userProfile?.settings?.riskSignalSettings?.sortStockPicksToTop ??
              false,
          );
          setShowStocksOnly(
            userProfile?.settings?.riskSignalSettings
              ?.showStockInstrumentsOnly ?? false,
          );
        }
      }, 1000),
    [session.user.id], // Remove applyFilters from dependencies to avoid recreation
  );

  // Add a separate effect to handle filter application when settings change
  useEffect(() => {
    console.log("Settings changed, reapplying filters:", {
      showStocksOnly,
      showStockPicksOnly,
    });

    if (originalResultTableTrendChange.length > 0) {
      applyFilters();
    }
  }, [
    showStocksOnly,
    showStockPicksOnly,
    applyFilters,
    originalResultTableTrendChange.length,
  ]);

  // Clean up the debounced function when component unmounts
  useEffect(() => {
    return () => {
      debouncedSaveSettings.cancel();
    };
  }, [debouncedSaveSettings]);

  // Handle entry window changes
  const handleEntryWindowChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    console.log("Entry window changed to:", value);
    if (value === "" || (Number(value) >= 0 && Number(value) <= 100)) {
      setEntryWindow(value);
      if (value !== "") {
        debouncedSaveSettings({
          defaultEntryWindow: Number(value),
          showStockPicksOnly,
          sortStockPicksToTop: sortStockPicksToTheTop,
          showStockInstrumentsOnly: showStocksOnly,
        });
      }
    }
  };

  // Update the date selection handler
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedTrendChangeDate(date);
      startTransition(() => {
        getLatestTrendChange().then(async (trendChangeList) => {
          if (!trendChangeList) return;

          try {
            const parsedList = JSON.parse(trendChangeList);
            if (parsedList && parsedList.length > 0) {
              await updateTrendChangeModel(parsedList);
            }
          } catch (error) {
            console.error("Error processing trend changes:", error);
            setError(new Error("Failed to process trend changes"));
          }
        });
      });
    }
  };

  // Add these functions to manage watchlist state
  const handleToggleWatchlist = async (symbol: string) => {
    try {
      if (watchlistItems.includes(symbol)) {
        // Get the current watchlist items to find the ID
        const currentItems = await getWatchlistItems(session.user.id);
        const itemToRemove = currentItems.find(
          (item) => item.ticker === symbol,
        );

        if (itemToRemove) {
          // Remove using the watchlist item ID
          await removeFromWatchlist(itemToRemove.id, session.user.id);
          // Update local state after successful removal
          setWatchlistItems((prev) => prev.filter((item) => item !== symbol));
          toast({
            title: "Removed from watchlist",
            description: `${symbol} has been removed from your watchlist`,
          });
        }
      } else {
        // Add to watchlist
        await addToWatchlist(symbol, session.user.id);
        // Update local state after successful addition
        setWatchlistItems((prev) => [...prev, symbol]);
        toast({
          title: "Added to watchlist",
          description: `${symbol} has been added to your watchlist`,
        });
      }
    } catch (error) {
      toast({
        title: "Failed to update watchlist",
        description:
          error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
      // Refresh the watchlist items to ensure UI is in sync
      const items = await getWatchlistItems(session.user.id);
      setWatchlistItems(items.map((item) => item.ticker));
    }
  };

  // In the main component, add a count display component
  const TableCountDisplay = ({ count }: { count: number }) => (
    <div className="flex items-center justify-between px-6 py-4 border-t border-slate-100 dark:border-slate-700 bg-slate-50/50 dark:bg-slate-800/50">
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
          Market Analysis Results
        </span>
      </div>
      <div className="text-sm text-slate-500 dark:text-slate-400">
        Showing{" "}
        <span className="font-semibold text-slate-700 dark:text-slate-300">
          {count}
        </span>{" "}
        {count === 1 ? "instrument" : "instruments"}
      </div>
    </div>
  );

  if (loading || isPending) {
    return <SpinnerBasicWithText text="Loading risk signals...Please wait" />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen text-destructive">
        Error loading risk signals: {error.message}
      </div>
    );
  }

  return (
    <Container className="py-6 px-4 md:px-6">
      {/* Main Content Grid */}
      <div className="space-y-6">
        {/* Risk Range Signal Card */}
        <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-lg">
          <CardHeader className="pb-6 border-b border-slate-100 dark:border-slate-700">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-blue-600 dark:text-blue-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                    Risk Range Signals
                  </h3>
                  <p className="text-sm text-slate-500 dark:text-slate-400">
                    Real-time analysis and insights for market trends powered by
                    Hedgeye Risk Management
                  </p>
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="flex items-start gap-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg mb-4">
              <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                <InfoCircledIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-slate-900 dark:text-white mb-1">
                  Filter Controls Available
                </p>
                <p className="text-sm text-slate-600 dark:text-slate-300">
                  Use the settings panel to adjust view filters and customize
                  your risk analysis display.
                </p>
                {resultTableTrendChange.length > 0 &&
                  (!showStocksOnly || showStockPicksOnly) &&
                  resultTableTrendChange.filter((r) =>
                    stockPicks.some((s) => s.index === r.index),
                  ).length === 0 && (
                    <p className="text-sm text-amber-600 dark:text-amber-400 mt-2 font-medium">
                      {showStocksOnly
                        ? "Stocks only filter is active. Click the gear icon ⚙️ to show all instruments."
                        : showStockPicksOnly
                          ? "Stock picks filter is active. Click the gear icon ⚙️ to show all instruments."
                          : ""}
                    </p>
                  )}
              </div>
            </div>

            {/* Sync Information */}
            <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="text-left">
                <p className="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wide mb-1">
                  Last Sync
                </p>
                <p className="text-sm font-mono text-slate-700 dark:text-slate-300">
                  {new Date(
                    trendChangeLastSyncDateFromHedgeye,
                  ).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}{" "}
                  {new Date(
                    trendChangeLastSyncDateFromHedgeye,
                  ).toLocaleTimeString("en-US", {
                    hour: "numeric",
                    minute: "2-digit",
                    hour12: true,
                  })}
                </p>
              </div>
              {session.user.role === "admin" && (
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-600"
                  onClick={handleRefreshData}
                >
                  <ReloadIcon className="h-4 w-4 transition-transform hover:rotate-180 duration-300" />
                  <span className="sr-only">Refresh data</span>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Entry Window Card */}
        <Suspense fallback={<SpinnerBasic />}>
          <EntryWindowCard
            showStockPicksOnly={showStockPicksOnly}
            setShowStockPicksOnly={setShowStockPicksOnly}
            sortStockPicksToTheTop={sortStockPicksToTheTop}
            setSortStockPicksToTheTop={setSortStockPicksToTheTop}
            showStocksOnly={showStocksOnly}
            setShowStocksOnly={setShowStocksOnly}
            userId={userProfile?.userId || ""}
            entryWindow={entryWindow}
            handleEntryWindowChange={handleEntryWindowChange}
            debouncedSaveSettings={debouncedSaveSettings}
            resultTableTrendChange={resultTableTrendChange}
            stockPicks={stockPicks}
            selectedTrendChangeDate={selectedTrendChangeDate}
            onDateSelect={handleDateSelect}
            initialWatchlistItems={initialWatchlistItems}
            holdings={holdings}
          />
        </Suspense>
        {/* Table Section */}
        <Card className="flex-1 overflow-hidden bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-lg">
          {(showStockPicksOnly
            ? resultTableTrendChange.filter((r) =>
                stockPicks.some((s) => s.index === r.index),
              )
            : resultTableTrendChange
          ).length === 0 && (
            <div className="m-6 p-6 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
              <Flex gap="3" align="center">
                <div className="w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center flex-shrink-0">
                  <InfoCircledIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-900 dark:text-white mb-1">
                    No Data Available
                  </p>
                  <Text size="2" className="text-slate-600 dark:text-slate-300">
                    {showStockPicksOnly
                      ? "No instruments found meeting the current entry criteria. Try adjusting the entry window in settings."
                      : "No instruments found matching the current filters."}
                  </Text>
                </div>
              </Flex>
            </div>
          )}

          {/* Desktop Table View */}
          <div className="hidden md:block">
            <DesktopTableView
              resultTableTrendChange={sortedTrendChanges}
              showStockPicksOnly={showStockPicksOnly}
              stockPicks={stockPicks}
              entryWindow={entryWindow}
              session={session}
              watchlistItems={watchlistItems}
              onToggleWatchlist={handleToggleWatchlist}
              holdings={holdings}
            />
            <TableCountDisplay count={sortedTrendChanges.length} />
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden">
            <MobileCardView
              resultTableTrendChange={sortedTrendChanges}
              showStockPicksOnly={showStockPicksOnly}
              stockPicks={stockPicks}
              entryWindow={entryWindow}
              session={session}
              watchlistItems={watchlistItems}
              onToggleWatchlist={handleToggleWatchlist}
              holdings={holdings}
            />
            <TableCountDisplay count={sortedTrendChanges.length} />
          </div>
        </Card>
      </div>
    </Container>
  );
}

export function funcToModelTrendChange() {
  return (table: HTMLTableElement, date: Date): TrendChange[] => {
    // Initialize an empty array to store the results
    let result: TrendChange[] = [];
    let originalIndex: number = 0;

    // Get the table body element
    let tbody = table.querySelector("tbody");
    // Loop through each row in the table body
    Array.from(tbody!.rows).forEach((row) => {
      // Initialize an empty object to store the current row data
      let rowData: TrendChange = {} as TrendChange;

      // Get the first cell in the row, which contains the index and the description
      let firstCell = row.cells[0];

      // Get the text content of the first cell, which contains the index and the trend in parentheses
      let firstCellText = firstCell.textContent;

      // Use a regular expression to extract the index and the trend from the text
      let match = firstCellText!.match(/(.+) \((.+)\)/);

      // If the match is successful, assign the index and the trend to the rowData object
      if (match) {
        rowData.index = match[1];
        rowData.trend = match[2];
      }

      // Get the em element in the first cell, which contains the description
      let em = firstCell.querySelector("em");

      // If the em element exists, assign the description to the rowData object
      if (em) {
        rowData.description = em.textContent!;
      }

      // Get the second, third, and fourth cells in the row, which contain the buyTrade, sellTrade, and previousClose values
      let secondCell = row.cells[1];
      let thirdCell = row.cells[2];
      let fourthCell = row.cells[3];

      // Parse the text content of the cells as numbers and assign them to the rowData object
      rowData.buyTrade = Number(secondCell.textContent!.replace(/,/g, ""));
      rowData.sellTrade = Number(thirdCell.textContent!.replace(/,/g, ""));
      rowData.previousClose = Number(fourthCell.textContent!.replace(/,/g, ""));

      rowData.originalIndex = originalIndex;
      rowData.date = date;
      // console.log(rowData);
      // Push the rowData object to the result array
      result.push(rowData);
      originalIndex++;
    });
    return result;
  };
}

export function funcExtractTrendChangeTable() {
  return (htmlText: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlText, "text/html");
    const tableTrendChange: HTMLTableElement | null = doc.querySelector(
      'table[class*="dtr-table"]',
    );
    return tableTrendChange;
  };
}

export function funcMakeTrendChangeModel() {
  const extractTrendChangeTable = funcExtractTrendChangeTable();
  const ToModelTrendChange = funcToModelTrendChange();
  return async (html: string, date: Date) => {
    // Get the Trend Change table part
    const extractedTrendChangeTable = extractTrendChangeTable(html);
    // Make model of the table
    const trendChangeList = ToModelTrendChange(
      extractedTrendChangeTable!,
      date,
    );
    return trendChangeList;
  };
}
