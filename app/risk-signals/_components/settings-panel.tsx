"use client";

import React from "react";
import { Switch } from "@/components/ui/switch";
import { Card } from "@/components/ui/card";
import { RiskSignalSettings } from "@/types/user-profile";

interface SettingsPanelProps {
  userId: string;
  onSettingsChange: (settings: RiskSignalSettings) => void;
  temporarySettings: RiskSignalSettings;
}

export default function SettingsPanel({
  onSettingsChange,
  temporarySettings,
}: SettingsPanelProps) {
  const handleSettingChange = (
    key: keyof RiskSignalSettings,
    value: boolean,
  ) => {
    onSettingsChange({
      ...temporarySettings,
      [key]: value,
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full p-4">
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Display Options</h3>
        <div className="flex flex-col gap-3">
          <div className="flex items-center space-x-2">
            <Switch
              checked={temporarySettings.showStockInstrumentsOnly}
              onCheckedChange={(checked) =>
                handleSettingChange("showStockInstrumentsOnly", checked)
              }
              id="stocks-only"
            />
            <label
              htmlFor="stocks-only"
              className="text-sm font-medium leading-none"
            >
              Show stock instruments only
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={temporarySettings.sortStockPicksToTop}
              onCheckedChange={(checked) =>
                handleSettingChange("sortStockPicksToTop", checked)
              }
              id="sort-picks"
            />
            <label
              htmlFor="sort-picks"
              className="text-sm font-medium leading-none"
            >
              Sort stock picks to the top
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={temporarySettings.showStockPicksOnly}
              onCheckedChange={(checked) =>
                handleSettingChange("showStockPicksOnly", checked)
              }
              id="picks-only"
            />
            <label
              htmlFor="picks-only"
              className="text-sm font-medium leading-none"
            >
              Show stock picks only
            </label>
          </div>
        </div>
      </Card>
    </div>
  );
}
