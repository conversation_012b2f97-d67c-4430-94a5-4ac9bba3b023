import React from "react";
import { Popover } from "@radix-ui/themes";
import { Cross2Icon, InfoCircledIcon } from "@radix-ui/react-icons";
import { TrendChange } from "@/db/trend-change";

interface InfoCircledPopoverProps {
  stock: TrendChange;
  windowEntryVariable: number;
}
const InfoCircledPopover = ({
  stock,
  windowEntryVariable,
}: InfoCircledPopoverProps) => (
  <Popover.Root>
    <Popover.Trigger>
      <button className="IconButton" aria-label="Update dimensions">
        <InfoCircledIcon style={{ width: 14, height: 14 }} />
      </button>
    </Popover.Trigger>
    <Popover.Content className="PopoverContent" sideOffset={5}>
      <div style={{ display: "flex", flexDirection: "column", gap: 10 }}>
        <p style={{ marginBottom: 10 }}>
          <strong>Why it is picked?</strong>
        </p>
        <ul>
          <ol>It&apos;s a {stock.trend}</ol>
          {stock.trend === "BULLISH" ? (
            <ol>{`(Buy + Entry Window) > Previous Close`}</ol>
          ) : (
            <ol>{`(Sell - Entry Window) < Previous Close`}</ol>
          )}
        </ul>
      </div>
      {/* <Popover.Close className="PopoverClose" aria-label="Close">
        <Cross2Icon />
      </Popover.Close> */}
      {/* <Popover.Arrow className="PopoverArrow" /> */}
    </Popover.Content>
  </Popover.Root>
);

export default InfoCircledPopover;
