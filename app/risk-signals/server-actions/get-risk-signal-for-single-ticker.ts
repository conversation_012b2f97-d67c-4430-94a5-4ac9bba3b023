"use server";

import prismadb from "@/lib/prisma";
import { TrendChange } from "@/db/trend-change";

export async function getRiskSignalForSingleTicker(
  symbol: string,
): Promise<TrendChange | null> {
  try {
    const signal = await prismadb.trendChange.findFirst({
      where: {
        index: symbol,
      },
      orderBy: {
        date: "desc",
      },
    });

    if (!signal) return null;

    // Convert the database result to match TrendChange type
    return {
      ...signal,
      description: signal.description ?? undefined,
      buyTrade: Number(signal.buyTrade),
      sellTrade: Number(signal.sellTrade),
      previousClose: Number(signal.previousClose),
    };
  } catch (error) {
    console.error("[GET_RISK_SIGNAL_ERROR]", error);
    return null;
  }
}
