"use server";

export default async function PlaceOptionLimitOrder(
  index: string,
  limitPrice: number,
  strikePrice: number,
  expiration: Date,
  right: string,
  quantity: number,
) {
  const url: string = `${process.env.IB_API_URL}/place-option-order`;
  const limitOrder = {
    localSymbol: index,
    expiration: expiration.toString().replaceAll("-", ""),
    strike: strikePrice,
    right: right,
    exchange: "SMART",
    action: "BUY",
    quantity: quantity,
    limitPrice: limitPrice,
  };
  console.log(limitOrder);
  const fetchResponse = await fetch(url, {
    method: "POST",
    body: JSON.stringify(limitOrder),
    headers: {
      "Content-Type": "application/json",
    },
  });
  const fetchData = await fetchResponse.json();
  console.log(fetchData);
  // if (fetchResponse.status != 200) {
  //   return JSON.stringify({
  //     data: fetchData,
  //     status: fetchResponse.status
  //   })
  // }
  return JSON.stringify(fetchData);
}
