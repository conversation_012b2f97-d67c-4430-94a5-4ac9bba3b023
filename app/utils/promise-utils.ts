export class TimeoutError extends Error {
  constructor(message = "Operation timed out") {
    super(message);
    this.name = "TimeoutError";
  }
}

export const DEFAULT_TIMEOUTS = {
  IBKR_CONNECTION: 3000, // 3 seconds for connection checks
  API_CALL: 10000, // 10 seconds for regular API calls
  DATA_FETCH: 15000, // 15 seconds for data-heavy operations
} as const;

export const withTimeout = <T>(
  promise: Promise<T>,
  timeoutMs: number = DEFAULT_TIMEOUTS.API_CALL,
  operationName?: string,
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(
        () =>
          reject(
            new TimeoutError(
              `Operation ${operationName ? `'${operationName}'` : ""} timed out after ${timeoutMs}ms`,
            ),
          ),
        timeoutMs,
      ),
    ),
  ]);
};
