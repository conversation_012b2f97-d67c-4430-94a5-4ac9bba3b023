import { withTimeout, DEFAULT_TIMEOUTS, TimeoutError } from "./promise-utils";

export const withIBKRTimeout = <T>(
  promise: Promise<T>,
  operationName: string,
  timeoutMs = DEFAULT_TIMEOUTS.API_CALL,
): Promise<T> => {
  return withTimeout(promise, timeoutMs, `IBKR ${operationName}`);
};

// Usage example:
export const safeIBKRCall = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  timeoutMs?: number,
): Promise<T> => {
  try {
    return await withIBKRTimeout(
      operation(),
      operationName,
      timeoutMs as 10000 | undefined,
    );
  } catch (error) {
    if (error instanceof TimeoutError) {
      // Handle IBKR specific timeout cleanup if needed
      console.error(`IBKR operation timeout: ${operationName}`);
    }
    throw error;
  }
};
