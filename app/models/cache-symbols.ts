"use server";

import {
  getCachedSymbol,
  setCachedSymbol,
  getAllCachedSymbols,
  type CachedSymbol,
} from "./symbols";

export interface CachedInstrument {
  ticker: string;
  isStock: boolean;
  name?: string;
  securityType?: string;
  securityType2?: string;
  figi?: string;
  exchange?: string;
}

// Move static properties to module-level variables
let cachedSymbols: CachedInstrument[] = [];
let lastFetch: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function getSymbols(): Promise<CachedInstrument[]> {
  const now = Date.now();
  const cachedSymbols = getAllCachedSymbols();
  // If cache is empty or expired, fetch new data
  if (cachedSymbols.size === 0 || now - lastFetch > CACHE_DURATION) {
    console.log("Fetching fresh symbols data");
    try {
      lastFetch = now;
      // cachedSymbols = await getCachedSymbols();
      console.log("Cached symbols updated:", {
        total: cachedSymbols.size,
        stocks: Array.from(cachedSymbols.values()).filter(
          (s) =>
            s.securityType2 === "Common Stock" ||
            s.securityType === "Common Stock",
        ).length,
      });
    } catch (error) {
      console.error("Error fetching symbols:", error);
      // Return existing cache if available, even if expired
      if (cachedSymbols.size > 0) {
        return Array.from(cachedSymbols.values()).map((symbol) => ({
          ...symbol,
          isStock:
            symbol.securityType2 === "Common Stock" ||
            symbol.securityType === "Common Stock",
        }));
      }
      throw error;
    }
  } else {
    console.log("Using cached symbols:", {
      total: cachedSymbols.size,
      stocks: Array.from(cachedSymbols.values()).filter(
        (s) =>
          s.securityType2 === "Common Stock" ||
          s.securityType === "Common Stock",
      ).length,
      cacheAge: Math.round((now - lastFetch) / 1000) + "s",
    });
  }

  return Array.from(cachedSymbols.values()).map((symbol) => ({
    ...symbol,
    isStock:
      symbol.securityType2 === "Common Stock" ||
      symbol.securityType === "Common Stock",
  }));
}

export async function setSymbols(symbols: CachedInstrument[]): Promise<void> {
  for (const symbol of symbols) {
    const cachedSymbol: CachedSymbol = {
      ticker: symbol.ticker,
      name: symbol.name,
      securityType: symbol.securityType ?? "",
      securityType2: symbol.securityType2 ?? "",
      figi: symbol.figi,
      exchange: symbol.exchange,
    };
    setCachedSymbol(symbol.ticker, cachedSymbol);
  }
  cachedSymbols = symbols;
  lastFetch = Date.now();
}

export async function createSymbol(
  symbol: CachedInstrument,
): Promise<CachedInstrument> {
  return symbol;
}

export async function getAllSymbols(): Promise<CachedInstrument[]> {
  return getSymbols();
}
