import { getAllCachedInstruments } from "@/app/actions/get-all-cached-instruments";
import CachedInstrumentsTable from "@/app/admin/instruments/_components/cached-instrument-table";

export default async function InstrumentCachePage() {
  const cachedInstruments = await getAllCachedInstruments();
  console.log(
    "InstrumentCachePage():cachedInstruments->",
    cachedInstruments.length,
  );
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">
        Cached Instrument Details
      </h1>
      <CachedInstrumentsTable instruments={cachedInstruments as any} />
    </div>
  );
}
