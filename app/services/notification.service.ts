class NotificationService {
  static async notifyRiskSignalsUpdate(count: number): Promise<void> {
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/push/notify`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": process.env.INTERNAL_API_KEY ?? "",
      },
      body: JSON.stringify({
        message: "Risk signals data has been updated!",
        count: count,
      }),
    });
  }

  static async notifyRiskSignalsUpdateWithTrendChanges(
    selectedTrendChangeDate: Date,
  ): Promise<void> {
    await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/push/notify-trend-changes`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.INTERNAL_API_KEY ?? "",
        },
        body: JSON.stringify({
          selectedTrendChangeDate: selectedTrendChangeDate,
        }),
      },
    );
  }
}

export async function notifyRiskSignalsUpdate(count: number): Promise<void> {
  await NotificationService.notifyRiskSignalsUpdate(count);
}

export async function notifyRiskSignalsUpdateWithTrendChanges(
  selectedTrendChangeDate: Date,
): Promise<void> {
  await NotificationService.notifyRiskSignalsUpdateWithTrendChanges(
    selectedTrendChangeDate,
  );
}

export default NotificationService;
