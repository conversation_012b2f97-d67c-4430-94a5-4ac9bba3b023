import { TrendChange } from "@/db/trend-change";
import { parseHedgeyeDateString } from "@/utils/date-hedgeye";

class HedgeyeService {
  private static readonly DATE_PUBLISHED_REGEX =
    /<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/;

  static extractPublishedDate(html: string): Date | null {
    const datePublishedMatch = html.match(this.DATE_PUBLISHED_REGEX);
    if (!datePublishedMatch) return null;

    return parseHedgeyeDateString(datePublishedMatch[1]);
  }
}

export async function extractPublishedDate(html: string): Promise<Date | null> {
  return HedgeyeService.extractPublishedDate(html);
}

export default HedgeyeService;
