"use client";

import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  User,
  Shield,
  Clock,
  Building,
} from "lucide-react";
import { PortfolioSummary } from "../actions/get-portfolio-all-data";
import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  description?: string;
}

const StatCard = ({ title, value, icon, description }: StatCardProps) => (
  <Card className="p-4">
    <div className="flex items-center justify-between space-x-4">
      <div>
        <p className="text-size-sm font-weight-medium text-muted-foreground">
          {title}
        </p>
        <h3 className="text-2xl font-weight-bold">{value}</h3>
        {description && (
          <p className="text-size-xs text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="text-muted-foreground">{icon}</div>
    </div>
  </Card>
);

export function PortfolioStats({ data }: { data: PortfolioSummary }) {
  const [currentTime, setCurrentTime] = useState<string>("");

  useEffect(() => {
    // Set initial time
    setCurrentTime(new Date().toLocaleTimeString());
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value);
  };

  const formatPercent = (value: number | undefined | null) => {
    if (value == null) return "0.00%";
    return `${value.toFixed(2)}%`;
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-weight-bold">Portfolio</h1>
        <div className="text-size-sm text-muted-foreground">
          {currentTime && `Last updated: ${currentTime}`}
        </div>
      </div>

      <Card className="my-4 p-6">
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <h2 className="text-size-lg font-weight-semibold mb-4 flex items-center">
              <User className="mr-2" size={20} />
              Account Information
            </h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Account Type</span>
                <span className="font-weight-medium">Margin Account</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Account Status</span>
                <div className="flex items-center">
                  <Shield className="mr-1 h-4 w-4 text-green-500" />
                  <span className="font-weight-medium text-green-500">
                    Active
                  </span>
                </div>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Account Number</span>
                <Link
                  href={`/trading-account/${data.accountNumber}`}
                  className="font-weight-medium hover:underline hover:text-primary transition-colors"
                >
                  {data.accountNumber}
                </Link>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-size-lg font-weight-semibold mb-4 flex items-center">
              <Building className="mr-2" size={20} />
              Broker Details
            </h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Broker Name</span>
                <span className="font-weight-medium">Interactive Brokers</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Connection Status</span>
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-green-500 mr-2" />
                  <span className="font-weight-medium">Connected</span>
                </div>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Last Sync</span>
                <div className="flex items-center">
                  <Clock className="mr-1 h-4 w-4 text-muted-foreground" />
                  <span className="font-weight-medium">{currentTime}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Value"
          value={formatCurrency(data.totalValue)}
          icon={<DollarSign size={24} />}
        />
        <StatCard
          title="Day Change"
          value={formatCurrency(data.dayChange)}
          description={formatPercent(data.dayChangePercent)}
          icon={<TrendingUp size={24} />}
        />
        <StatCard
          title="Total Return"
          value={formatCurrency(data.totalReturn)}
          description={`${formatPercent(data.totalReturnPercent)} YTD`}
          icon={<LineChart size={24} />}
        />
        <StatCard
          title="Cash Balance"
          value={formatCurrency(data.cashBalance)}
          description={`${formatPercent(data.cashBalancePercent)} of portfolio`}
          icon={<Percent size={24} />}
        />
      </div>
    </>
  );
}
