"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card } from "@/components/ui/card";
import { customNumberFormatter } from "@/lib/utils-client";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format } from "date-fns";
import { useEffect, useState, useCallback } from "react";
import { Order, getAllOrders } from "../actions/get-all-orders";
import { SpinnerBasic } from "@/components/spinner-basic";
import { Button } from "@/components/ui/button";
import { XCircleIcon } from "lucide-react";
import { cancelOrder, cancelAllOrders } from "../actions/cancel-order";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogT<PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useSearchParams } from "next/navigation";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Calendar, ArrowUpDown, DollarSign, Clock } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface OrdersListProps {
  data: {
    userId: string;
    accountId: string;
  };
}

const CANCELLABLE_STATUSES = ["PreSubmitted", "Submitted"];

export function OrdersList({ data }: OrdersListProps) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancellingOrderId, setCancellingOrderId] = useState<number | null>(
    null,
  );
  const [isCancellingAll, setIsCancellingAll] = useState(false);
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const activeTab = searchParams.get("tab");
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const fetchOrders = useCallback(async () => {
    try {
      setIsLoading(true);
      const fetchedOrders = await getAllOrders(data.userId);
      const filteredOrders = data.accountId
        ? fetchedOrders.filter((order) => order.accountId === data.accountId)
        : fetchedOrders;
      setOrders(filteredOrders);
    } catch (err) {
      setError("Failed to fetch orders");
      console.error("Error fetching orders:", err);
    } finally {
      setIsLoading(false);
    }
  }, [data.userId, data.accountId]);

  useEffect(() => {
    if (activeTab === "orders") {
      fetchOrders();
    }
  }, [activeTab, fetchOrders]);

  const handleCancelOrder = async (orderId: number, clientId: number) => {
    try {
      setCancellingOrderId(orderId);
      const result = await cancelOrder(data.userId, orderId, clientId);

      if (result) {
        // Multiple fetch attempts to ensure we get the updated state
        const fetchAttempts = async () => {
          for (let i = 0; i < 3; i++) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            await fetchOrders();

            // Check if the order is no longer in the list or has been cancelled
            const order = orders.find((o) => o.orderId === orderId);
            if (!order || order.status === "Cancelled") {
              break;
            }
          }
        };

        await fetchAttempts();

        toast({
          title: "Order cancelled successfully",
          description: `Order #${orderId} has been cancelled.`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Failed to cancel order",
          description: "Please try again or check your connection.",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred.",
      });
    } finally {
      setCancellingOrderId(null);
    }
  };

  const handleCancelAllOrders = async () => {
    try {
      setIsCancellingAll(true);
      const result = await cancelAllOrders(data.userId);

      if (result) {
        // Multiple fetch attempts to ensure we get the updated state
        const fetchAttempts = async () => {
          for (let i = 0; i < 3; i++) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            await fetchOrders();
          }
        };

        await fetchAttempts();

        toast({
          title: "Orders cancelled successfully",
          description: "All pending orders have been cancelled.",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Failed to cancel orders",
          description: "Please try again or check your connection.",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred.",
      });
    } finally {
      setIsCancellingAll(false);
    }
  };

  if (isLoading) {
    return <SpinnerBasic />;
  }

  if (error) {
    return (
      <Card className="p-4">
        <p className="text-destructive text-center">{error}</p>
      </Card>
    );
  }

  if (!orders || orders.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center py-6 text-muted-foreground">
          <ArrowUpDown className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="font-medium mb-1">No Orders</h3>
          <p className="text-sm">You haven't placed any orders yet.</p>
        </div>
      </Card>
    );
  }

  const pendingOrders = orders.filter((order) =>
    CANCELLABLE_STATUSES.includes(order.status),
  );

  return (
    <Card>
      {pendingOrders.length > 0 && (
        <div className="p-4 border-b">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                size="sm"
                disabled={isCancellingAll}
              >
                {isCancellingAll ? (
                  <SpinnerBasic />
                ) : (
                  <>Cancel All Orders ({pendingOrders.length})</>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Cancel All Orders</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to cancel all pending orders? This will
                  cancel {pendingOrders.length} order
                  {pendingOrders.length === 1 ? "" : "s"}. This action cannot be
                  undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleCancelAllOrders}
                  className="bg-destructive hover:bg-destructive/90"
                >
                  Confirm
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      )}

      {isDesktop ? (
        <ScrollArea className="h-[600px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Symbol</TableHead>
                <TableHead>Side</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Price</TableHead>
                <TableHead className="text-right">Total</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.orderId}>
                  <TableCell>
                    {format(new Date(order.timestamp), "MMM dd, yyyy HH:mm")}
                  </TableCell>
                  <TableCell className="font-medium">{order.symbol}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        order.action === "BUY" ? "default" : "destructive"
                      }
                    >
                      {order.action}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    {customNumberFormatter(order.quantity)}
                  </TableCell>
                  <TableCell className="text-right">
                    ${customNumberFormatter(order.price)}
                  </TableCell>
                  <TableCell className="text-right">
                    ${customNumberFormatter(order.quantity * order.price)}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        order.status === "Filled"
                          ? "default"
                          : order.status === "Error"
                            ? "destructive"
                            : "secondary"
                      }
                    >
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {CANCELLABLE_STATUSES.includes(order.status) && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            disabled={cancellingOrderId === order.orderId}
                          >
                            {cancellingOrderId === order.orderId ? (
                              <SpinnerBasic />
                            ) : (
                              <XCircleIcon className="h-4 w-4 text-destructive" />
                            )}
                            <span className="sr-only">Cancel order</span>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Cancel Order</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to cancel this order? This
                              action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() =>
                                handleCancelOrder(order.orderId, order.clientId)
                              }
                              className="bg-destructive hover:bg-destructive/90"
                            >
                              Confirm
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      ) : (
        <ScrollArea className="h-[calc(100vh-300px)]">
          <div className="space-y-2 p-4">
            {orders.map((order) => (
              <Card key={order.orderId} className="p-4">
                {/* Header - Symbol and Status */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-semibold">
                      {order.symbol}
                    </span>
                    <Badge
                      variant={
                        order.status === "Filled"
                          ? "default"
                          : order.status === "Error"
                            ? "destructive"
                            : "secondary"
                      }
                    >
                      {order.status}
                    </Badge>
                  </div>
                  {CANCELLABLE_STATUSES.includes(order.status) && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          disabled={cancellingOrderId === order.orderId}
                        >
                          {cancellingOrderId === order.orderId ? (
                            <SpinnerBasic />
                          ) : (
                            <XCircleIcon className="h-4 w-4 text-destructive" />
                          )}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Cancel Order</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to cancel this order? This
                            action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() =>
                              handleCancelOrder(order.orderId, order.clientId)
                            }
                            className="bg-destructive hover:bg-destructive/90"
                          >
                            Confirm
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>

                {/* Order Details Grid */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                      <ArrowUpDown className="h-3 w-3" /> Action
                    </div>
                    <Badge
                      variant={
                        order.action === "BUY" ? "default" : "destructive"
                      }
                    >
                      {order.action}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                      <DollarSign className="h-3 w-3" /> Price
                    </div>
                    <div className="font-medium">
                      ${customNumberFormatter(order.price)}
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />

                {/* Quantity and Total */}
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">
                      Quantity
                    </div>
                    <div className="font-medium">
                      {customNumberFormatter(order.quantity)}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground mb-1">
                      Total Value
                    </div>
                    <div className="font-semibold">
                      ${customNumberFormatter(order.quantity * order.price)}
                    </div>
                  </div>
                </div>

                {/* Timestamp */}
                <div className="flex items-center gap-1 text-sm text-muted-foreground mt-4 pt-4 border-t">
                  <Clock className="h-3 w-3" />
                  <span>
                    {format(new Date(order.timestamp), "MMM dd, yyyy HH:mm")}
                  </span>
                </div>
              </Card>
            ))}
          </div>
        </ScrollArea>
      )}
    </Card>
  );
}
