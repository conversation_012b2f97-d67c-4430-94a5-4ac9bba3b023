"use client";

import { Card } from "@/components/ui/card";
import { PortfolioSummary } from "../actions/get-portfolio-all-data";
import OrderDialog from "./order-dialog";
import { Session } from "next-auth";
import {
  TrendingDown,
  TrendingUp,
  Loader2,
  DollarSign,
  BarChart2,
  Per<PERSON>,
} from "lucide-react";
import { useEffect, useState } from "react";
import { getRiskSignalForSingleTicker } from "@/app/risk-signals/server-actions/get-risk-signal-for-single-ticker";
import { TrendChange } from "@/db/trend-change";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

export function PositionsList({
  session,
  data,
  defaultAccountId,
}: {
  session: Session;
  data: PortfolioSummary;
  defaultAccountId?: string;
}) {
  const [trendSignals, setTrendSignals] = useState<
    Record<string, TrendChange | null>
  >({});
  const [isLoadingSignals, setIsLoadingSignals] = useState(true);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  useEffect(() => {
    const fetchTrendSignals = async () => {
      setIsLoadingSignals(true);
      try {
        const signals: Record<string, TrendChange | null> = {};

        if (data.positions) {
          await Promise.all(
            data.positions.map(async (position) => {
              const signal = await getRiskSignalForSingleTicker(
                position.symbol,
              );
              signals[position.symbol] = signal;
            }),
          );
        }

        setTrendSignals(signals);
      } catch (error) {
        console.error("Error fetching trend signals:", error);
      } finally {
        setIsLoadingSignals(false);
      }
    };

    if (data.positions && data.positions.length > 0) {
      fetchTrendSignals();
    } else {
      setIsLoadingSignals(false);
    }
  }, [data.positions]);

  if (!data.positions && isLoadingSignals) {
    return (
      <Card className="p-4 flex items-center justify-center">
        <Loader2 className="h-6 w-6 animate-spin" />
      </Card>
    );
  }

  if ((data.positions ?? []).length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center py-6 text-muted-foreground">
          <BarChart2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="font-medium mb-1">No Positions</h3>
          <p className="text-sm">You don't have any open positions yet.</p>
        </div>
      </Card>
    );
  }

  const renderTrendIndicator = (symbol: string) => {
    const signal = trendSignals[symbol];
    if (!signal) return null;

    const isBullish = signal.trend === "BULLISH";
    const isBearish = signal.trend === "BEARISH";

    if (!isBullish && !isBearish) return null;

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            {isBullish ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500" />
            )}
          </TooltipTrigger>
          <TooltipContent>
            <p>Trending {isBullish ? "Bullish" : "Bearish"}</p>
            <p className="text-xs text-muted-foreground">
              Buy: ${signal.buyTrade} | Sell: ${signal.sellTrade}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return isDesktop ? (
    <Card className="p-4">
      <table className="w-full">
        <thead>
          <tr className="text-left text-sm text-muted-foreground">
            <th className="pb-3">Symbol</th>
            <th className="pb-3">Shares</th>
            <th className="pb-3">Price</th>
            <th className="pb-3">Avg Cost</th>
            <th className="pb-3">Value</th>
            <th className="pb-3">Return</th>
            <th className="pb-3">Action</th>
          </tr>
        </thead>
        <tbody>
          {(data.positions ?? []).length === 0 ? (
            <tr>
              <td
                colSpan={7}
                className="text-center py-4 text-muted-foreground"
              >
                No positions found
              </td>
            </tr>
          ) : (
            (data.positions ?? []).map((position) => (
              <tr key={position.symbol} className="border-t">
                <td className="py-3">
                  <div className="flex items-center gap-2">
                    <div className="font-medium">{position.symbol}</div>
                    {renderTrendIndicator(position.symbol)}
                  </div>
                </td>
                <td className="py-3">{position.shares}</td>
                <td className="py-3">${position.price.toFixed(2)}</td>
                <td className="py-3">${position.avgCost.toFixed(2)}</td>
                <td className="py-3">${position.value.toFixed(2)}</td>
                <td
                  className={`py-3 ${
                    position.return >= 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {position.return >= 0 ? "+" : ""}
                  {position.return.toFixed(2)}%
                </td>
                <td className="py-3">
                  <OrderDialog
                    userId={session.user.id}
                    symbol={position.symbol}
                    action="SELL"
                    price={position.price}
                    quantity={position.shares}
                    defaultAccountId={defaultAccountId}
                  />
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </Card>
  ) : (
    <div className="space-y-4">
      {(data.positions ?? []).map((position) => (
        <Card key={position.symbol} className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold">{position.symbol}</span>
              {renderTrendIndicator(position.symbol)}
            </div>
            <OrderDialog
              userId={session.user.id}
              symbol={position.symbol}
              action="SELL"
              price={position.price}
              quantity={position.shares}
              defaultAccountId={defaultAccountId}
            />
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                <DollarSign className="h-3 w-3" /> Current Price
              </div>
              <div className="font-medium">${position.price.toFixed(2)}</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                <BarChart2 className="h-3 w-3" /> Shares
              </div>
              <div className="font-medium">{position.shares}</div>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="flex justify-between items-center">
            <div>
              <div className="text-sm text-muted-foreground mb-1">
                Position Value
              </div>
              <div className="font-semibold">${position.value.toFixed(2)}</div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground mb-1">Return</div>
              <Badge variant={position.return >= 0 ? "success" : "destructive"}>
                <Percent className="h-3 w-3 mr-1" />
                {position.return >= 0 ? "+" : ""}
                {position.return.toFixed(2)}%
              </Badge>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <div className="text-sm text-muted-foreground">Average Cost</div>
            <div className="font-medium">${position.avgCost.toFixed(2)}</div>
          </div>
        </Card>
      ))}
    </div>
  );
}
