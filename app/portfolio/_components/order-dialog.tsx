import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import CreateOrder from "./create-order";
import { PlusCircleIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { getAllAccounts, type Account } from "../actions/get-all-accounts";

interface OrderDialogProps {
  userId: string;
  symbol?: string;
  action?: "BUY" | "SELL";
  price?: number;
  quantity?: number;
  defaultAccountId?: string;
}

export default function OrderDialog({
  userId,
  symbol = "",
  action = "BUY",
  price = 0,
  quantity = 1,
  defaultAccountId,
}: OrderDialogProps) {
  const [open, setOpen] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);

  useEffect(() => {
    const fetchAccounts = async () => {
      const accountsList = await getAllAccounts(userId);
      if (defaultAccountId) {
        accountsList.sort((a, b) => {
          if (a.accountId === defaultAccountId) return -1;
          if (b.accountId === defaultAccountId) return 1;
          return 0;
        });
      }
      setAccounts(accountsList);
    };

    if (open) {
      fetchAccounts();
    }
  }, [userId, open, defaultAccountId]);

  const handleOrderSuccess = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={action === "SELL" ? "destructive" : "ghost"}
          size="sm"
          className="h-8 w-8 p-0"
        >
          <PlusCircleIcon className="h-4 w-4" />
          <span className="sr-only">
            {action === "SELL" ? "Sell position" : "Place order"}
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] p-0">
        <CreateOrder
          userId={userId}
          accounts={accounts}
          defaultSymbol={symbol}
          defaultAction={action}
          defaultPrice={price}
          defaultQuantity={quantity}
          defaultAccountId={defaultAccountId}
          onSuccess={handleOrderSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}
