"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PortfolioStats } from "./portfolio-stats";
import { PositionsList } from "./positions-list";
import { OrdersList } from "./orders-list";
import { Suspense, useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter, useSearchParams } from "next/navigation";
import { PortfolioData } from "../actions/get-portfolio-all-data";
import { AutoOrderPrefillList } from "./auto-order-prefill-list";

export default function PortfolioPageComponent({
  session,
  portfolioData,
}: {
  session: any;
  portfolioData: PortfolioData[];
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const activeTab = searchParams.get("tab") || "holdings";
  const [selectedAccount, setSelectedAccount] = useState<string>("");
  const [currentPortfolio, setCurrentPortfolio] =
    useState<PortfolioData | null>(null);

  // Set initial selected account
  useEffect(() => {
    if (portfolioData.length > 0 && !selectedAccount) {
      setSelectedAccount(portfolioData[0].accountNumber);
      setCurrentPortfolio(portfolioData[0]);
    }
  }, [portfolioData, selectedAccount]);

  // Update current portfolio when account selection changes
  const handleAccountChange = (accountNumber: string) => {
    setSelectedAccount(accountNumber);
    const selected = portfolioData.find(
      (p) => p.accountNumber === accountNumber,
    );
    if (selected) {
      setCurrentPortfolio(selected);
    }
  };

  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("tab", value);
    router.push(`?${params.toString()}`);
  };

  return (
    <div className="space-y-4 py-4 p-4">
      {portfolioData.length > 1 && (
        <div className="w-[240px]">
          <Select value={selectedAccount} onValueChange={handleAccountChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select account" />
            </SelectTrigger>
            <SelectContent>
              {portfolioData.map((account) => (
                <SelectItem
                  key={account.accountNumber}
                  value={account.accountNumber}
                >
                  {account.accountNumber}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div>
        <Suspense fallback={<Skeleton className="h-[200px]" />}>
          {currentPortfolio && <PortfolioStats data={currentPortfolio} />}
        </Suspense>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList>
          <TabsTrigger value="holdings">Holdings</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="auto-prefill">Auto Order Prefill</TabsTrigger>
        </TabsList>
        <TabsContent value="holdings" className="space-y-4">
          <Suspense fallback={<Skeleton className="h-[400px]" />}>
            {currentPortfolio && (
              <PositionsList
                session={session}
                data={currentPortfolio}
                defaultAccountId={selectedAccount}
              />
            )}
          </Suspense>
        </TabsContent>
        <TabsContent value="orders" className="space-y-4">
          <Suspense fallback={<Skeleton className="h-[400px]" />}>
            <OrdersList
              data={{ userId: session.user.id, accountId: selectedAccount }}
            />
          </Suspense>
        </TabsContent>
        <TabsContent value="auto-prefill" className="space-y-4">
          <Suspense fallback={<Skeleton className="h-[400px]" />}>
            <AutoOrderPrefillList userId={session.user.id} />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}
