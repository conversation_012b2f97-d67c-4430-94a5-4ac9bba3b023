"use server";

import { EventName } from "@stoqey/ib";
import { createIBApiConnection } from "@/app/actions/ibapi/connection";
import { fetchQuotes } from "@/utils/yahoo-finance/fetch-quote";
import { testIBKRConnection } from "@/app/profile/actions/ibkr-test-connection";

interface IBKRPosition {
  symbol: string;
  shares: number;
  avgCost: number;
}

export type PositionData = {
  symbol: string;
  shares: number;
  price: number;
  value: number;
  avgCost: number;
  return: number;
};

export async function fetchHoldingsData(
  userId: string,
): Promise<PositionData[] | null> {
  try {
    const isConnected = await testIBKRConnection(userId);
    if (!isConnected) {
      console.log("IBKR not connected");
      return null;
    }

    const ib = await createIBApiConnection(userId);
    const ibkrPositions: IBKRPosition[] = [];

    const positionsPromise = new Promise<PositionData[]>((resolve, reject) => {
      let connectionEstablished = false;

      // Increase timeout to 30 seconds
      const timeout = setTimeout(() => {
        ib.disconnect();
        reject(new Error("Timeout waiting for positions data"));
      }, 10000);

      // Error handler
      ib.on(EventName.error, (err, code, reqId) => {
        console.error(
          `IBKR Error: ${err.message} - code: ${code} - reqId: ${reqId}`,
        );
        if (code === 502) {
          // Connection may be refused
          reject(new Error("Connection refused"));
        }
      });

      // Connected handler
      ib.on(EventName.connected, () => {
        console.log("IBKR Connected successfully");
        connectionEstablished = true;
        ib.reqPositions();
      });

      // Position handler
      ib.on(EventName.position, (account, contract, pos, avgCost) => {
        console.log("Received position:", contract.symbol, pos);
        if (!contract.symbol) return;

        ibkrPositions.push({
          symbol: contract.symbol,
          shares: pos,
          avgCost: avgCost!,
        });
      });

      // Position end handler
      ib.on(EventName.positionEnd, async () => {
        console.log(
          "Position end received. Total IBKR positions:",
          ibkrPositions.length,
        );

        try {
          if (ibkrPositions.length === 0) {
            console.log("No positions found");
            clearTimeout(timeout);
            ib.disconnect();
            resolve([]);
            return;
          }

          // Get all quotes in one batch request
          const symbols = ibkrPositions.map((p) => p.symbol);
          console.log("Fetching quotes for symbols:", symbols);

          const quotes = await fetchQuotes(symbols);
          console.log(
            "Received quotes for",
            Object.keys(quotes).length,
            "symbols",
          );

          // Process all positions with their quotes
          const positions = ibkrPositions.map((position) => {
            const quote = quotes[position.symbol];
            if (!quote) {
              console.warn(`No quote found for symbol: ${position.symbol}`);
              return null;
            }

            const currentPrice = quote.regularMarketPrice;
            const positionValue = position.shares * currentPrice;
            const costBasis = position.shares * position.avgCost;
            const positionReturn =
              ((positionValue - costBasis) / costBasis) * 100;

            return {
              symbol: position.symbol,
              shares: position.shares,
              price: currentPrice,
              value: positionValue,
              avgCost: position.avgCost,
              return: positionReturn,
            };
          });

          const validPositions = positions.filter(
            (p): p is PositionData => p !== null,
          );

          console.log(
            "All positions processed. Total valid positions:",
            validPositions.length,
          );

          clearTimeout(timeout);
          ib.disconnect();
          resolve(validPositions);
        } catch (error) {
          console.error("Error processing positions:", error);
          reject(error);
        }
      });

      // Disconnected handler
      ib.on(EventName.disconnected, () => {
        console.log("IBKR Disconnected");
      });

      // Start the connection
      console.log("Initiating IBKR connection...");
      ib.connect();
    });

    const holdings = await positionsPromise;
    console.log("Retrieved positions:", holdings);
    return holdings;
  } catch (error) {
    console.error("Error fetching positions data:", error);
    return null;
  }
}
