"use server";

import { auth } from "@/app/api/auth/[...nextauth]/auth";
import prismadb from "@/lib/prisma";
import { Session } from "next-auth";

type PushSubscriptionData = {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
};

export async function saveSubscription(subscription: PushSubscription) {
  let session: Session | null = null;

  try {
    // Get and validate session
    session = await auth();
    if (!session?.user?.id) {
      throw new Error("Unauthorized: No valid session found");
    }

    // Safely type and extract subscription data
    const subData = subscription as unknown as PushSubscriptionData;
    if (!subData?.endpoint || !subData?.keys?.p256dh || !subData?.keys?.auth) {
      throw new Error("Invalid subscription data");
    }

    const { endpoint, keys } = subData;
    const { p256dh, auth: authKey } = keys;

    // Check for existing subscription
    const existingSubscription = await prismadb.pushSubscription.findFirst({
      where: {
        endpoint,
        user_id: session.user.id,
        deleted_at: null, // Only find active subscriptions
      },
    });

    if (existingSubscription) {
      return existingSubscription;
    }

    // Create new subscription
    const savedSubscription = await prismadb.pushSubscription.create({
      data: {
        endpoint,
        p256dh,
        auth: authKey,
        user_id: session.user.id,
      },
    });

    return savedSubscription;
  } catch (error) {
    // Log error but throw a sanitized version
    console.error("Error in saveSubscription:", error);
    throw new Error(
      "Failed to save push subscription. Please try again later.",
    );
  }
}
