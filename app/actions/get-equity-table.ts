"use server";

import { tempLoggingBetterStack } from "@/lib/logger";

export async function getRiskSignalUpsideDownsidePotentials(url: string) {
  try {
    const response = await fetch(url, {
      headers: {
        "cache-control": "no-store",
      },
    });
    const blob = await response.blob();
    return blob;
  } catch (error) {
    console.log(error);
  }
}

// If the risk signal page contain equity table as image (png), this will return the url. Otherwise, need to parse the <table> for the trend change.
export async function extractEquityTableUrlRegEx(htmlText: string) {
  const regex =
    /https:\/\/d1yhils6iwh5l5\.cloudfront\.net\/charts\/resized\/(\d+)\/large\/equityTable[^.]*\.png/;
  const totalCount = countRegexMatches(htmlText, regex);
  // console.log(`Total occurrences of '${regex}': ${totalCount}`);
  const match = htmlText.match(regex);
  await tempLoggingBetterStack({
    dt: new Date(),
    message: `extractEquityTableUrlRegEx() - ${match}`,
  });
  return match ? match[0] : undefined;
}

// If the risk signal page contain equity table as image (png), this will return the url. Otherwise, need to parse the <table> for the trend change.
// export async function extractTrendChangeTable(htmlText: string) {
//   const regex =
//     /https:\/\/d1yhils6iwh5l5\.cloudfront\.net\/charts\/resized\/(\d+)\/large\/equityTable\.png/;
//   const match = htmlText.match(regex);
//   await tempLoggingBetterStack({
//     dt: new Date(),
//     message: `extractEquityTableUrlRegEx() - ${match}`,
//   });
//   return match ? match[0] : null;
// }

function countRegexMatches(inputString: string, regexPattern: string | RegExp) {
  const matches = inputString.match(new RegExp(regexPattern, "g"));
  return matches ? matches.length : 0;
}
