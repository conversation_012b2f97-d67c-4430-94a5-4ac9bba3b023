import { getUserProfile } from "@/db/user-profile";
import { <PERSON><PERSON><PERSON>, IBApiNext } from "@stoqey/ib";

export async function createIBApiConnection(userId: string) {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }
    const clientId: number = parseInt(
      userProfile?.settings?.ibkrConnectionDetail?.clientId?.toString() ??
        Math.floor(Math.random() * 100).toString(),
    );
    const ib = new IBApi({
      clientId: clientId,
      host: userProfile.settings.ibkrConnectionDetail.host,
      port: userProfile.settings.ibkrConnectionDetail.port,
    });
    return ib;
  } catch (error) {
    console.error("Error creating IBKR connection:", error);
    throw error;
  }
}

export async function createIBApiNextConnection(userId: string) {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }
    const ibApiNext = new IBApiNext({
      host: userProfile.settings.ibkrConnectionDetail.host,
      port: userProfile.settings.ibkrConnectionDetail.port,
    });
    const clientId: number = parseInt(
      userProfile?.settings?.ibkrConnectionDetail?.clientId?.toString() ??
        Math.floor(Math.random() * 100).toString(),
    );
    return {
      ibApiNext,
      clientId,
    };
  } catch (error) {
    console.error("Error creating IBKR connection:", error);
    throw error;
  }
}
