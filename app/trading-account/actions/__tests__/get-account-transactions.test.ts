import { EventName } from "@stoqey/ib";
import { getAccountTransactions } from "../get-account-transactions";
import { IBConnectionError } from "@/app/utils/ibkr-errors";
import { TimeoutError } from "@/app/utils/promise-utils";

// Define event listener types
type IBEventListener = (...args: any[]) => void;
type IBEventEmitter = {
  on: (event: string, listener: IBEventListener) => void;
  once: (event: string, listener: IBEventListener) => void;
  emit: (event: string, ...args: any[]) => void;
};

// Mock EventEmitter for IBApi
class MockIBApi implements IBEventEmitter {
  private listeners: { [key: string]: IBEventListener[] } = {};

  connect() {}
  disconnect() {}

  on(event: string, listener: IBEventListener) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  once(event: string, listener: IBEventListener) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  emit(event: string, ...args: any[]) {
    if (this.listeners[event]) {
      this.listeners[event].forEach((listener) => listener(...args));
    }
  }

  reqAccountUpdates() {}
}

// Mock the modules
jest.mock("@stoqey/ib", () => ({
  EventName: {
    connected: "connected",
    error: "error",
    updateAccountValue: "updateAccountValue",
    accountDownloadEnd: "accountDownloadEnd",
  },
}));

jest.mock("@/app/actions/ibapi/connection", () => ({
  createIBApiConnection: jest.fn().mockImplementation(() => new MockIBApi()),
}));

// Add mock for ibkr-utils
jest.mock("@/app/utils/ibkr-utils", () => ({
  withIBKRTimeout: jest
    .fn()
    .mockImplementation((promise, operationName, timeout) => {
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(
            new TimeoutError(
              `IBKR ${operationName} timed out after ${timeout}ms`,
            ),
          );
        }, timeout);
        promise
          .then((result: unknown) => {
            clearTimeout(timeoutId);
            resolve(result);
          })
          .catch((error: unknown) => {
            clearTimeout(timeoutId);
            reject(error);
          });
      });
    }),
}));

describe("getAccountTransactions", () => {
  let mockIBApi: MockIBApi;

  beforeEach(() => {
    jest.clearAllMocks();
    mockIBApi = new MockIBApi();
    jest.spyOn(mockIBApi, "connect");
    jest.spyOn(mockIBApi, "disconnect");
    jest.spyOn(mockIBApi, "reqAccountUpdates");
    (
      jest.requireMock("@/app/actions/ibapi/connection") as any
    ).createIBApiConnection.mockReturnValue(mockIBApi);
  });

  it("should successfully retrieve account transactions", async () => {
    // Mock successful connection and updates
    const mockTransactions = [
      { key: "CashBalance", value: "1000", currency: "USD" },
      { key: "CashBalance", value: "1500", currency: "USD" }, // Simulates a deposit of 500
      { key: "Deposits", value: "500", currency: "USD" }, // Direct deposit record
      { key: "CashBalance", value: "1000", currency: "USD" }, // Simulates a withdrawal of 500
      { key: "Transfer", value: "-500", currency: "USD" }, // Direct withdrawal record
    ];

    // Setup mock event handlers
    mockIBApi.connect = jest.fn().mockImplementation(() => {
      setTimeout(() => {
        mockIBApi.emit(EventName.connected);

        // Emit transaction updates in sequence
        mockTransactions.forEach(({ key, value, currency }) => {
          mockIBApi.emit(
            EventName.updateAccountValue,
            key,
            value,
            currency,
            "testAccount123",
          );
        });

        // Emit completion
        mockIBApi.emit(EventName.accountDownloadEnd, "testAccount123");
      }, 100);
    });

    const result = await getAccountTransactions("testAccount123", "user123");

    // We expect 2 unique transactions (one deposit and one withdrawal)
    expect(result).toHaveLength(2);

    // Check the deposit transaction
    expect(result.find((t) => t.type === "deposit")).toMatchObject({
      type: "deposit",
      amount: 500,
      currency: "USD",
      status: "completed",
    });

    // Check the withdrawal transaction
    expect(result.find((t) => t.type === "withdrawal")).toMatchObject({
      type: "withdrawal",
      amount: 500,
      currency: "USD",
      status: "completed",
    });
  });

  it("should handle connection errors", async () => {
    // Mock connection error
    mockIBApi.connect = jest.fn().mockImplementation(() => {
      setTimeout(() => {
        mockIBApi.emit(EventName.error, new Error("Connection failed"), 504, 0);
      }, 100);
    });

    await expect(
      getAccountTransactions("testAccount123", "user123"),
    ).rejects.toThrow(IBConnectionError);
  });

  it("should handle timeout", async () => {
    // Mock timeout by not emitting any events and using a very short timeout
    mockIBApi.connect = jest.fn();

    // Override the timeout value just for this test
    jest
      .requireMock("@/app/utils/ibkr-utils")
      .withIBKRTimeout.mockImplementationOnce(
        (promise: Promise<unknown>, operationName: string) => {
          return new Promise((_, reject) => {
            setTimeout(() => {
              reject(
                new TimeoutError(
                  `IBKR ${operationName} timed out after 10000ms`,
                ),
              );
            }, 100); // Use a shorter timeout for the test
          });
        },
      );

    await expect(
      getAccountTransactions("testAccount123", "user123"),
    ).rejects.toThrow("IBKR Get Account Transactions timed out after 10000ms");
  });

  it("should ignore updates for different accounts", async () => {
    mockIBApi.connect = jest.fn().mockImplementation(() => {
      setTimeout(() => {
        mockIBApi.emit(EventName.connected);

        // Emit update for different account
        mockIBApi.emit(
          EventName.updateAccountValue,
          "CashBalance",
          "1000",
          "USD",
          "differentAccount",
        );

        // Emit completion for target account
        mockIBApi.emit(EventName.accountDownloadEnd, "testAccount123");
      }, 100);
    });

    const result = await getAccountTransactions("testAccount123", "user123");
    expect(result).toHaveLength(0);
  });

  it("should handle multiple updates of the same transaction", async () => {
    const mockTransactions = [
      { key: "CashBalance", value: "1000", currency: "USD" },
      { key: "TotalCashValue", value: "1000", currency: "USD" },
      { key: "Deposits", value: "1000", currency: "USD" },
    ];

    mockIBApi.connect = jest.fn().mockImplementation(() => {
      setTimeout(() => {
        mockIBApi.emit(EventName.connected);

        mockTransactions.forEach(({ key, value, currency }) => {
          mockIBApi.emit(
            EventName.updateAccountValue,
            key,
            value,
            currency,
            "testAccount123",
          );
        });

        mockIBApi.emit(EventName.accountDownloadEnd, "testAccount123");
      }, 100);
    });

    const result = await getAccountTransactions("testAccount123", "user123");
    expect(result).toHaveLength(1); // Should deduplicate the same transaction
  });
});
