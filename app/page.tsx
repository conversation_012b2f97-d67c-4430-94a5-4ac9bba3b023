import { auth, signIn } from "@/app/api/auth/[...nextauth]/auth";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function Home() {
  const session = await auth();
  if (!session?.user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-size-lg mb-4">
          Please log in to view your profile.
        </div>
        <form
          action={async () => {
            "use server";
            await signIn("Google");
          }}
        >
          <button className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90">
            Sign in with Google
          </button>
        </form>
      </div>
    );
  }
  const { user } = session;
  if (session) {
    redirect("/risk-signals");
  }
}
