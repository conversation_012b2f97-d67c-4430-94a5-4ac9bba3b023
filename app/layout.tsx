import type { Metadata } from "next/types";
import { Inter } from "next/font/google";
import "./globals.css";
import "@radix-ui/themes/styles.css";
import { RootLayoutClient } from "./_components/root-layout-client";
import "../utils/yahoo-finance/init";

const inter = Inter({ subsets: ["latin"] });

// Move metadata to a separate constant outside the component
export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: "Lunar Hedge",
  description: "",
  manifest: "/manifest.json",
  icons: {
    apple: [{ url: "/web-app-manifest-512x512.png", sizes: "512x512" }],
  },
};

// Remove "use client" from here since this is a Server Component
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Lunar Hedge" />
        <link rel="apple-touch-icon" href="/icon-512x512.png" sizes="512x512" />
      </head>
      <body className={inter.className}>
        <RootLayoutClient>{children}</RootLayoutClient>
      </body>
    </html>
  );
}
