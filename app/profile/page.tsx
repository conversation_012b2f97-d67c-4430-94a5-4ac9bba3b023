import { auth } from "@/app/api/auth/[...nextauth]/auth";
import { IBAccountDetails } from "./IBAccountDetails";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { NotificationPreferences } from "./NotificationPreferences";
import { IBKRConnectionForm } from "./IBKRConnectionForm";
import { UserRoleDisplay } from "./_components/user-role-display";
import SendEmailButtonTest from "./send-email-button-test";

export default async function Profile() {
  const session = await auth();
  if (!session?.user) {
    return <div>Please log in to view your profile.</div>;
  }
  const { user } = session;

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container max-w-4xl px-4 py-8 md:py-12">
        <div className="space-y-8">
          {/* Profile Header Card */}
          <Card className="p-6 md:p-8">
            <div className="flex flex-col md:flex-row md:items-center md:space-x-8">
              <div className="h-24 w-24 md:h-32 md:w-32 rounded-full overflow-hidden bg-slate-200">
                <Avatar className="h-full w-full object-cover">
                  <AvatarImage
                    src={user?.image || undefined}
                    alt={user?.name || undefined}
                  />
                  <AvatarFallback>{user?.name?.[0]}</AvatarFallback>
                </Avatar>
              </div>
              <div className="mt-4 md:mt-0 flex-grow">
                <h1 className="text-2xl md:text-3xl font-bold text-slate-900">
                  {user.name}
                </h1>
                <p className="text-slate-500 mt-1">{user.email}</p>
              </div>
              <UserRoleDisplay userId={user.id!} />
            </div>
          </Card>

          {/* Account Information */}
          <div className="space-y-4">
            <h2 className="text-xl md:text-2xl font-semibold text-slate-900 px-1">
              Trading Account
            </h2>

            {/* IBKR Connection Settings */}
            <Card className="p-6 md:p-8">
              <div className="space-y-6">
                <div>
                  <h3 className="text-base md:text-lg font-medium text-slate-900 mb-4">
                    IBKR Connection Settings
                  </h3>
                  <IBKRConnectionForm userId={user.id!} />
                </div>
              </div>
            </Card>

            {/* Existing IBKR Account Details Card */}
            <Card className="p-6 md:p-8">
              <div className="space-y-6">
                <div>
                  <h3 className="text-base md:text-lg font-medium text-slate-900 mb-2">
                    Interactive Brokers Account
                  </h3>
                  <div className="bg-slate-50 rounded-lg p-4">
                    <IBAccountDetails />
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Notification Preferences */}
          <div className="space-y-4">
            <h2 className="text-xl md:text-2xl font-semibold text-slate-900 px-1">
              Notification Settings
            </h2>
            <Card className="p-6 md:p-8">
              <NotificationPreferences userId={user.id!} />
            </Card>
          </div>
        </div>
      </div>
      {/* <SendEmailButtonTest /> */}
    </div>
  );
}
