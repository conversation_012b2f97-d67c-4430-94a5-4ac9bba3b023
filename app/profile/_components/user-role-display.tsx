"use client";

import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { upsertUserRole } from "../actions/update-user-role";
import { getUserProfile } from "@/db/user-profile";

interface UserRoleDisplayProps {
  userId: string;
}

export function UserRoleDisplay({ userId }: UserRoleDisplayProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentRole, setCurrentRole] = useState<"user" | "admin">("user");
  useEffect(() => {
    const fetchProfile = async () => {
      const profile = await getUserProfile(userId);
      setCurrentRole(profile?.settings?.role || "user");
    };
    fetchProfile();
  }, [userId]);

  const handleRoleChange = async (newRole: "user" | "admin") => {
    try {
      await upsertUserRole({ role: newRole }, userId);
      setCurrentRole(newRole);
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to update role:", error);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Badge
        variant={currentRole === "admin" ? "default" : "secondary"}
        className="text-sm"
      >
        {currentRole.charAt(0).toUpperCase() + currentRole.slice(1)}
      </Badge>
      {currentRole === "admin" && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              Change Role
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Change User Role</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Select
                value={currentRole}
                onValueChange={(value: "user" | "admin") =>
                  handleRoleChange(value)
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
