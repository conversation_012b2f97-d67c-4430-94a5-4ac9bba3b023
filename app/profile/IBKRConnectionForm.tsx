"use client";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { upsertUserProfileIBKRConnectionDetail } from "@/app/actions/upsert-user-profile-settings";
import { getUserProfile } from "@/db/user-profile";
import { IBKRConnectionDetail } from "@/types/user-profile";

const formSchema = z.object({
  host: z.string().min(1, "Host is required"),
  port: z.number().min(0, "Port must be a positive number"),
  clientId: z.number().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function IBKRConnectionForm({ userId }: { userId: string }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      host: "",
      port: 8888,
      clientId: undefined,
    },
  });

  useEffect(() => {
    async function loadSettings() {
      try {
        const profile = await getUserProfile(userId);
        if (profile?.settings?.ibkrConnectionDetail) {
          const { host, port, clientId } =
            profile.settings.ibkrConnectionDetail;
          form.reset({
            host,
            port,
            clientId,
          });
        }
      } catch (error) {
        console.error("Error loading IBKR settings:", error);
        toast({
          title: "Error",
          description: "Failed to load saved connection details.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }

    loadSettings();
  }, [userId, form]);

  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);
    try {
      await upsertUserProfileIBKRConnectionDetail(
        { ibkrConnectionDetail: values as IBKRConnectionDetail },
        userId,
      );
      toast({
        title: "Settings updated",
        description: "Your IBKR connection details have been saved.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update IBKR connection details.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoading) {
    return <div>Loading saved settings...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid gap-4">
          <FormField
            control={form.control}
            name="host"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Host</FormLabel>
                <FormControl>
                  <Input placeholder="localhost" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="port"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Port</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="7496"
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="clientId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Client ID (Optional)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="1"
                    {...field}
                    value={field.value ?? ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? null : Number(value));
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Save Connection Details"}
        </Button>
      </form>
    </Form>
  );
}
