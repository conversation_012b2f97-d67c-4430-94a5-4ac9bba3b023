"use client";

import { But<PERSON> } from "@radix-ui/themes";
import { EmailProps, sendEmail } from "@/emails/resend";
import { render } from "@react-email/render";
import EmailTemplate from "@/emails/email-template";
import TrendChangesEmail from "@/emails/trend-changes";

export default async function SendEmailButton() {
  const emailProps: EmailProps = {
    body: await render(EmailTemplate({ firstName: "Marvin" })),
    to: ["<EMAIL>"],
    subject: "Hello",
  };

  const handleSendEmail = async () => {
    console.log("handleSendEmail()");
    const resp = await sendEmail(emailProps);
    console.log(resp);
  };

  const changes = [
    {
      symbol: "AAPL",
      newTrend: "BEARISH",
      previousTrend: "BULLISH",
      date: new Date("2024-03-20"),
    },
  ];
  const emailPropsTrendChanges: EmailProps = {
    body: await render(
      TrendChangesEmail({
        changes: changes.map((change) => ({
          symbol: change.symbol,
          previousTrend: change.previousTrend,
          newTrend: change.newTrend,
          date: change.date.toISOString(),
        })),
        recipientName: "Marvin",
      }),
    ),
    to: ["<EMAIL>"],
    subject: "Trend Changes",
  };

  async function handleSendEmailTrendChanges() {
    console.log("handleSendEmailTrendChanges()");
    const resp = await sendEmail(emailPropsTrendChanges);
    console.log(resp);
  }

  return (
    <>
      {/* <Button
        onClick={async () => {
          const resp = await fetch("/api/sendemail", {
            method: "POST",
            body: JSON.stringify(emailProps),
          });
          console.log(await resp.json());
        }}
      >
        Send email using api route
      </Button>
      <Button
        onClick={async () => {
          await handleSendEmail();
        }}
      >
        Send email using server action
      </Button> */}
      <Button
        onClick={async () => {
          await handleSendEmailTrendChanges();
        }}
      >
        Send email using server action
      </Button>
    </>
  );
}
