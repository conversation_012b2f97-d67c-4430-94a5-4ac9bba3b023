"use client";
import React from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Avatar, Flex } from "@radix-ui/themes";
import { User } from "next-auth";

interface ProfileSheetProps {
  user: User;
}

export default function ProfileSheet({ user }: ProfileSheetProps) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Avatar
          src={user.image!}
          fallback={user.email?.charAt(0)!}
          radius="full"
          size="2"
        />
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          {/* <SheetTitle>Edit profile</SheetTitle> */}
          {/* <SheetDescription>
            Make changes to your profile here. Click save when you're done.
          </SheetDescription> */}
        </SheetHeader>
        <SheetFooter>
          <SheetClose asChild>
            {/* <Button type="submit">Save changes</Button> */}
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
