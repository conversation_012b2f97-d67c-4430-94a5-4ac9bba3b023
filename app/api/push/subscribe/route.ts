import { NextResponse } from "next/server";
import * as webpush from "web-push";
import prismadb from "@/lib/prisma";
import { getToken } from "next-auth/jwt";

// Configure web-push with your VAPID keys
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!,
);

export async function POST(request: Request) {
  try {
    // Get the token from the request
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    console.log("POST /api/push/subscribe -> token:", token);

    if (!token?.sub) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { subscription } = await request.json();
    console.log("POST /api/push/subscribe -> subscription:", subscription);

    // Extract necessary fields from the subscription
    const { endpoint, keys } = subscription;
    const { p256dh, auth } = keys;

    // Check for existing subscription
    const existingSubscription = await prismadb.pushSubscription.findFirst({
      where: {
        endpoint: endpoint,
        user_id: token.sub, // Use token.sub as the user ID
      },
    });

    if (existingSubscription) {
      console.log("Subscription already exists");
      return NextResponse.json({
        message: "Subscription already exists",
        subscription: existingSubscription,
      });
    }

    // Store the subscription in your database
    const savedSubscription = await prismadb.pushSubscription.create({
      data: {
        endpoint,
        p256dh,
        auth,
        user_id: token.sub, // Use token.sub as the user ID
      },
    });

    console.log("Subscription saved:", savedSubscription);

    return NextResponse.json({
      message: "Subscription added successfully",
      subscription: savedSubscription,
    });
  } catch (error) {
    console.error("Error in POST /api/push/subscribe:", error);
    return NextResponse.json(
      { error: "Failed to add subscription" },
      { status: 500 },
    );
  }
}
