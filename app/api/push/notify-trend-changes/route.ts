import { NextResponse } from "next/server";
import * as webpush from "web-push";
import prismadb from "@/lib/prisma";
import { auth } from "@/app/api/auth/[...nextauth]/auth";
import { getTickerListWithChangesBetweenTwoDates } from "@/db/get-ticker-list-with-changes-between-two-dates";
import { EmailProps, sendEmail } from "@/emails/resend";
import TrendChangesEmail from "@/emails/trend-changes";
import { render } from "@react-email/render";
import OrderService from "@/app/services/order.service";

// Configure web-push with your VAPID keys
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!,
);

// Add type for subscription
type PushSubscription = {
  id: string;
  user_id: string;
  endpoint: string;
  p256dh: string;
  auth: string;
  deleted_at: Date | null;
};

// Add this helper function at the top of the file
function deduplicateTrendChanges(
  changes: Array<{
    symbol: string;
    previousTrend: string;
    newTrend: string;
    date: string;
  }>,
) {
  const uniqueChanges = new Map();

  changes.forEach((change) => {
    const key = `${change.symbol}-${change.previousTrend}-${change.newTrend}-${change.date}`;
    uniqueChanges.set(key, change);
  });

  return Array.from(uniqueChanges.values());
}

export async function POST(request: Request) {
  try {
    // Check for API key in headers
    const apiKey = request.headers.get("x-api-key");
    const isServerToServer = apiKey === process.env.INTERNAL_API_KEY;

    if (!isServerToServer) {
      // Fall back to session auth for browser requests
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }
    }

    let { selectedTrendChangeDate } = await request.json().catch(() => ({}));

    // Validate selectedTrendChangeDate
    if (!selectedTrendChangeDate) {
      return NextResponse.json(
        { error: "selectedTrendChangeDate is required" },
        { status: 400 },
      );
    }

    const tickersWithTrendChanges =
      await getTickerListWithChangesBetweenTwoDates(selectedTrendChangeDate);

    if (!tickersWithTrendChanges?.length) {
      return NextResponse.json({ message: "No trend changes found" });
    }

    // First, get all active subscriptions
    const subscriptions = await prismadb.pushSubscription.findMany({
      where: {
        deleted_at: null,
      },
    });

    if (!subscriptions.length) {
      return NextResponse.json({ message: "No active subscriptions found" });
    }

    // Group subscriptions by user_id to avoid duplicate emails
    const userSubscriptions: { [key: string]: PushSubscription[] } = {};
    subscriptions.forEach((sub) => {
      if (!userSubscriptions[sub.user_id]) {
        userSubscriptions[sub.user_id] = [];
      }
      userSubscriptions[sub.user_id].push(sub);
    });

    const results = await Promise.allSettled(
      Object.entries(userSubscriptions).map(async ([userId, userSubs]) => {
        try {
          const watchlist = await prismadb.watchlist.findMany({
            where: {
              user_id: userId,
            },
          });

          const tickersInWatchlistWithTrendChanges = watchlist.filter(
            (watchlistItem) =>
              tickersWithTrendChanges.some(
                (tickerWithTrendChange) =>
                  tickerWithTrendChange.current.index === watchlistItem.ticker,
              ),
          );

          // Only proceed if there are matching tickers
          if (tickersInWatchlistWithTrendChanges.length) {
            // Build the changes array from the actual data
            let changes = tickersInWatchlistWithTrendChanges.map(
              (watchlistItem) => {
                const trendChange = tickersWithTrendChanges.find(
                  (t) => t.current.index === watchlistItem.ticker,
                );

                return {
                  symbol: watchlistItem.ticker,
                  previousTrend: trendChange?.previous?.trend || "Unknown",
                  newTrend: trendChange?.current?.trend || "Unknown",
                  date: selectedTrendChangeDate,
                };
              },
            );

            // Deduplicate the changes before sending
            changes = deduplicateTrendChanges(changes);

            // Send push notifications to all user's subscriptions
            await Promise.all(
              userSubs.map(async (subscription) => {
                const notification = {
                  title: "Risk Signal Trend Changes",
                  body: `Trend changes detected for: ${[
                    ...new Set(
                      tickersInWatchlistWithTrendChanges.map(
                        (item) => item.ticker,
                      ),
                    ),
                  ].join(", ")}`,
                  icon: "/icon-512x512.png",
                  badge: "/icon-96x96.png",
                  data: {
                    url: "/risk-signals",
                    tickers: tickersInWatchlistWithTrendChanges.map(
                      (item) => item.ticker,
                    ),
                  },
                };

                if (
                  !subscription.endpoint ||
                  !subscription.p256dh ||
                  !subscription.auth
                ) {
                  throw new Error("Invalid subscription details");
                }

                try {
                  await webpush.sendNotification(
                    {
                      endpoint: subscription.endpoint,
                      keys: {
                        p256dh: subscription.p256dh,
                        auth: subscription.auth,
                      },
                    },
                    JSON.stringify(notification),
                  );
                } catch (error: any) {
                  if (error.statusCode === 410) {
                    await prismadb.pushSubscription.update({
                      where: { id: subscription.id },
                      data: { deleted_at: new Date() },
                    });
                  }
                  throw error;
                }
              }),
            );

            // Get user details and send email (only once per user)
            const user = await prismadb.user.findUnique({
              where: { id: userId },
              select: { name: true, email: true },
            });

            if (!user?.email) {
              throw new Error("User email not found");
            }

            // Send email with the actual changes.
            // Take note the `changes` here means the tickers that have trend changes and it's in the watchlist.
            const emailPropsTrendChanges: EmailProps = {
              body: await render(
                TrendChangesEmail({
                  changes,
                  recipientName: user.name || undefined,
                }),
              ),
              to: [user.email],
              subject: `Trend Changes Alert - ${changes.length} Updates`,
            };

            await sendEmail(emailPropsTrendChanges);

            // This is to automatically order the stocks that have trend changes.
            await OrderService.autoOrder(changes, userId);
          }

          return { success: true, userId };
        } catch (error: any) {
          return {
            success: false,
            userId,
            error: error.message || "Unknown error",
          };
        }
      }),
    );

    return NextResponse.json({ results });
  } catch (error) {
    console.error("Error sending notifications:", error);
    return NextResponse.json(
      { error: "Failed to send notifications" },
      { status: 500 },
    );
  }
}
