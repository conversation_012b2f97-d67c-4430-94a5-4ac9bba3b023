import NextAuth from "next-auth";
import type { NextAuthConfig, Session } from "next-auth";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import prismadb from "../../../../lib/prisma";
import authConfig from "./auth.config";
import { DefaultSession } from "next-auth";
import { UserProfileSettings } from "@/types/user-profile";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      image: string;
      role?: string;
    } & DefaultSession["user"];
    expires: string;
  }
}

async function getUserProfile(userId: string) {
  if (!userId) return null;

  const userProfile = await prismadb.userProfile.findUnique({
    where: {
      user_id: userId,
    },
  });

  return userProfile;
}

export const config = {
  ...authConfig,
  adapter: PrismaAdapter(prismadb),
  basePath: "/api/auth",
  session: { strategy: "jwt" },
  callbacks: {
    async session({ session, token }) {
      const user = await prismadb.user.findUnique({
        where: {
          email: token.email!,
        },
        include: {
          user_profiles: true,
        },
      });

      if (!user) return session;

      const userProfile = user.user_profiles?.settings as UserProfileSettings;

      const sessionWithUserRole = {
        ...session,
        user: {
          ...session.user,
          id: user.id,
          role: userProfile.role ?? "user",
        },
      };

      return sessionWithUserRole;
    },
  },
} satisfies NextAuthConfig;

export const { handlers, auth, signIn, signOut } = NextAuth(config);
