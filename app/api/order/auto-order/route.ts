import { NextResponse } from "next/server";
import { auth } from "../../auth/[...nextauth]/auth";
import { fetchHoldingsData } from "@/app/portfolio/actions/get-holdings";
import { z } from "zod";
import { AutoOrderPrefillTrendChange } from "@/types/trend-change";
import { autoOrderPrefill } from "@/app/services/order.service";

// Define types for better type safety

interface AutoOrderRequest {
  changes: AutoOrderPrefillTrendChange[];
  userId: string;
}

// Input validation schema
const autoOrderSchema = z.object({
  changes: z.array(
    z.object({
      symbol: z.string(),
      previousTrend: z.string(),
      newTrend: z.string(),
      date: z.string(),
    }),
  ),
  userId: z.string(),
});

/**
 * API Route Handler for Auto Orders
 * Endpoint: /api/order/auto-order
 *
 * This endpoint is called when:
 * 1. Risk signals change and auto-order is enabled
 * 2. Manual trigger of auto-order process
 *
 * Security:
 * - Requires either valid session or internal API key
 * - Validates input data using zod schema
 */
export async function POST(request: Request) {
  try {
    // 1. Authentication check
    const apiKey = request.headers.get("x-api-key");
    const isServerToServer = apiKey === process.env.INTERNAL_API_KEY;

    if (!isServerToServer) {
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }
    }

    // 2. Validate request body
    const body = await request.json();
    try {
      autoOrderSchema.parse(body);
    } catch (error) {
      console.error("Validation error:", error);
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const { changes, userId } = body as AutoOrderRequest;

    if (!changes?.length || !userId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    // 3. Get current holdings
    const holdings = await fetchHoldingsData(userId);

    if (!holdings) {
      return NextResponse.json(
        { error: "Failed to fetch holdings" },
        { status: 500 },
      );
    }

    // 4. Process orders
    const tickersWithHoldings = changes.filter((change) =>
      holdings.some((holding) => holding.symbol === change.symbol),
    );

    const sellList = tickersWithHoldings.filter(
      (change) => change.newTrend.toLowerCase() === "bearish",
    );

    const buyList = changes.filter(
      (change) =>
        change.newTrend.toLowerCase() === "bullish" &&
        !holdings.some((holding) => holding.symbol === change.symbol),
    );

    const response = await autoOrderPrefill(buyList, sellList, userId);

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("[AUTO_ORDER_ERROR]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
