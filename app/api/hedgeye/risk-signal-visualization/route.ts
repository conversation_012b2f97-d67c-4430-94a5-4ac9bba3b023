import {
  extractEquityTableUrlRegEx,
  getRiskSignalUpsideDownsidePotentials,
} from "@/app/actions/get-equity-table";
import logger, { tempLoggingBetterStack } from "@/lib/logger";
import { getEnvironmentVariable } from "@/lib/utils-server";
import { unstable_noStore as noStore } from "next/cache";
import { NextRequest, NextResponse } from "next/server";

const postParseVisualizationHandler = async (req: NextRequest) => {
  noStore;
  let equityTableUrl = await req.json();
  const blob = await getRiskSignalUpsideDownsidePotentials(equityTableUrl);
  if (blob) {
    const res = new NextResponse(blob, {
      status: 200,
      headers: new Headers({
        "Content-Type": "image/png",
        "Cache-Control": "no-store",
        "Content-Length": blob.size + "",
      }),
    });
    return res;
  } else {
    await tempLoggingBetterStack({
      dt: new Date(),
      message: "No data - it maybe that the url not found or it parsing error.",
    });
    return NextResponse.json({ message: "No data", status: 204 });
  }
};

export const POST = postParseVisualizationHandler;
