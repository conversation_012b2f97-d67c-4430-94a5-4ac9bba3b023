"use server";

import prismadb from "@/lib/prisma";
import { fetchQuote } from "@/utils/yahoo-finance/fetch-quote";
import { revalidatePath } from "next/cache";

export async function addToWatchlist(ticker: string, userId: string) {
  try {
    if (!ticker || !userId) {
      throw new Error("Missing required fields");
    }

    // Check if stock already exists in watchlist
    const existingStock = await prismadb.watchlist.findFirst({
      where: {
        user_id: userId,
        ticker: ticker.toUpperCase(),
        deleted_at: null,
      },
    });

    if (existingStock) {
      throw new Error("Stock already in watchlist");
    }

    await prismadb.watchlist.create({
      data: {
        user_id: userId,
        ticker: ticker.toUpperCase(),
      },
    });

    revalidatePath("/watchlist");
    return { success: true };
  } catch (error) {
    console.error("[WATCHLIST_ADD_ERROR]", error);
    if (error instanceof Error) {
      throw new Error(error.message);
    }
    throw new Error("Failed to add stock to watchlist");
  }
}

export async function getWatchlistItems(userId: string) {
  try {
    if (!userId) {
      throw new Error("User ID is required");
    }

    const watchlistItems = await prismadb.watchlist.findMany({
      where: {
        user_id: userId,
        deleted_at: null,
      },
      orderBy: {
        created_at: "desc",
      },
    });
    return watchlistItems;
  } catch (error) {
    console.error("[WATCHLIST_GET_ERROR]", error);
    return [];
  }
}

export async function removeFromWatchlist(id: string, userId: string) {
  try {
    if (!id || !userId) {
      throw new Error("Missing required fields");
    }

    // Verify the item belongs to the user before deleting
    const watchlistItem = await prismadb.watchlist.findFirst({
      where: {
        id,
        user_id: userId,
        deleted_at: null,
      },
    });

    if (!watchlistItem) {
      throw new Error("Stock not found in watchlist");
    }

    // Soft delete the item
    await prismadb.watchlist.update({
      where: { id },
      data: { deleted_at: new Date() },
    });

    revalidatePath("/watchlist");
    return { success: true };
  } catch (error) {
    console.error("[WATCHLIST_REMOVE_ERROR]", error);
    if (error instanceof Error) {
      throw new Error(error.message);
    }
    throw new Error("Failed to remove stock from watchlist");
  }
}

export async function getStockQuotes(tickers: string[]) {
  try {
    const quotes: Record<string, any> = {};

    for (const ticker of tickers) {
      try {
        const quote = await fetchQuote(ticker);
        quotes[ticker] = quote;
      } catch (error) {
        console.error(`[QUOTE_FETCH_ERROR] ${ticker}:`, error);
        quotes[ticker] = null;
      }
    }

    return quotes;
  } catch (error) {
    console.error("[QUOTES_FETCH_ERROR]", error);
    return {};
  }
}

export type WatchlistActionError = {
  error: string;
};
