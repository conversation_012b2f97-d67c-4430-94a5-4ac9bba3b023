"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { removeFromWatchlist } from "../actions";
import { useState } from "react";

interface RemoveStockDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stockId: string;
  ticker: string;
  userId: string;
}

export function RemoveStockDialog({
  open,
  onOpenChange,
  stockId,
  ticker,
  userId,
}: RemoveStockDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleRemove = async () => {
    try {
      setLoading(true);
      await removeFromWatchlist(stockId, userId);
      toast({
        title: "Success",
        description: `${ticker} has been removed from your watchlist`,
      });
      onOpenChange(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to remove stock from watchlist";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove Stock</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to remove {ticker} from your watchlist? This
            action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleRemove}
            disabled={loading}
            className="bg-destructive hover:bg-destructive/90"
          >
            {loading ? "Removing..." : "Remove"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
