import { auth } from "../api/auth/[...nextauth]/auth";
import { redirect } from "next/navigation";
import { fetchDashboardAnalytics } from "./actions/fetch-dashboard-analytics";
import DashboardMain from "./_components/dashboard-main";

const DashboardPage = async () => {
  const session = await auth();
  if (!session) {
    redirect("/signin");
  }
  const userId = session.user.id;
  const dashboardData = await fetchDashboardAnalytics(userId);
  return <DashboardMain initialDashboardData={dashboardData} userId={userId} />;
};

export default DashboardPage;
