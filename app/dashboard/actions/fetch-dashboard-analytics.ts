"use server";

import {
  getLastSyncedTrendChangeDate,
  getTrendChangesByDateOrMostRecent,
  TrendChange,
} from "@/db/trend-change";
import { testIBKRConnection } from "@/app/profile/actions/ibkr-test-connection";
import { getUserProfile } from "@/db/user-profile";
import { getWatchlistItems } from "@/app/watchlist/actions";
import { getTickerListWithChangesBetweenTwoDates } from "@/db/get-ticker-list-with-changes-between-two-dates";
import { fetchHoldingsData } from "@/app/portfolio/actions/get-holdings";
import { getAllOrders } from "@/app/portfolio/actions/get-all-orders";
import { fetchAutoOrderPrefill } from "@/app/portfolio/actions/fetch-auto-order-prefill";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";
import { getCachedInstrumentDetailBatch } from "@/app/actions/get-instrument-detail";

// Define types for the dashboard data structure
type PositionPerformance = {
  symbol: string;
  gain?: number;
  loss?: number;
  gain_percentage?: number;
  loss_percentage?: number;
  position_size: number;
};

type TradingData = {
  open_orders: number;
  auto_prefill_orders: number;
  open_positions: number;
  positions_performance: {
    top_gainer: PositionPerformance;
    top_loser: PositionPerformance;
  };
};

type ConnectionStatus = {
  is_connected: boolean;
  account_id: string;
  last_checked: string;
};

export type DashboardAnalytics = {
  stocks_in_watchlist: number;
  symbols_trend_changed_from_previous_date: {
    current: TrendChange;
    previous: TrendChange;
  }[];
  symbols_within_10_percent_window_entry: {
    symbol: string;
    isStock: boolean;
  }[];
  risk_signals_last_updated: string;
  connection_status: ConnectionStatus;
  trading_data: TradingData;
};

// Add this helper function to calculate position performance
function calculatePositionsPerformance(holdings: PortfolioPosition[] | null) {
  if (!holdings || holdings.length === 0) {
    return {
      top_gainer: { symbol: "", position_size: 0 },
      top_loser: { symbol: "", position_size: 0 },
    };
  }

  let topGainer = {
    symbol: "",
    gain: 0,
    gain_percentage: 0,
    position_size: 0,
  };
  let topLoser = {
    symbol: "",
    loss: 0,
    loss_percentage: 0,
    position_size: 0,
  };

  holdings.forEach((position) => {
    // Calculate unrealized P&L using return percentage and position value
    const returnPercentage = position.return; // Already in percentage
    const positionValue = position.value;
    const unrealizedPnL = (positionValue * returnPercentage) / 100;

    if (
      returnPercentage > 0 &&
      returnPercentage > (topGainer.gain_percentage || 0)
    ) {
      topGainer = {
        symbol: position.symbol,
        gain: Number(unrealizedPnL.toFixed(2)),
        gain_percentage: Number(returnPercentage.toFixed(2)),
        position_size: Number(positionValue.toFixed(2)),
      };
    } else if (
      returnPercentage < 0 &&
      Math.abs(returnPercentage) > (topLoser.loss_percentage || 0)
    ) {
      topLoser = {
        symbol: position.symbol,
        loss: Number(Math.abs(unrealizedPnL).toFixed(2)),
        loss_percentage: Number(Math.abs(returnPercentage).toFixed(2)),
        position_size: Number(positionValue.toFixed(2)),
      };
    }
  });

  return {
    top_gainer: topGainer,
    top_loser: topLoser,
  };
}

// Add this utility function for timeout control
const withTimeout = <T>(
  promise: Promise<T>,
  timeoutMs: number = 10000,
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error("Operation timed out")), timeoutMs),
    ),
  ]);
};

// Add this new function to calculate symbols within 10% entry window
async function getSymbolsWithin10PercentWindow(): Promise<
  { symbol: string; isStock: boolean }[]
> {
  try {
    const lastSyncedDate = await getLastSyncedTrendChangeDate();
    const trendChanges = await getTrendChangesByDateOrMostRecent(
      new Date(lastSyncedDate),
    );

    if (!trendChanges) {
      return [];
    }

    const parsedTrendChanges = JSON.parse(trendChanges);
    const symbols = parsedTrendChanges
      .filter((signal: any) => {
        if (signal.trend === "BULLISH") {
          const tenPercentOfBuyTrade = signal.buyTrade * 0.1;
          const difference = signal.buyTrade - signal.previousClose;
          return difference > 0 && difference <= tenPercentOfBuyTrade;
        } else if (signal.trend === "BEARISH") {
          const tenPercentOfSellTrade = signal.sellTrade * 0.1;
          const difference = signal.previousClose - signal.sellTrade;
          return difference > 0 && difference <= tenPercentOfSellTrade;
        }
        return false;
      })
      .map((signal: any) => signal.index);

    // Fetch instrument details for all symbols at once
    const instrumentDetails = await Promise.all(
      symbols.map((symbol: string) =>
        getCachedInstrumentDetailBatch([symbol]).catch(() => null),
      ),
    );

    return instrumentDetails
      .filter((detail): detail is NonNullable<typeof detail> => detail !== null)
      .map((detail) => ({
        symbol: detail.symbol,
        isStock: detail.isStock,
      }));
  } catch (error) {
    console.error("Error calculating symbols within 10% window:", error);
    return [];
  }
}

export const fetchDashboardAnalytics = async (
  userId: string,
): Promise<DashboardAnalytics> => {
  // Default values for when things fail
  const defaultAnalytics: DashboardAnalytics = {
    stocks_in_watchlist: 0,
    symbols_trend_changed_from_previous_date: [],
    symbols_within_10_percent_window_entry: [],
    risk_signals_last_updated: new Date().toISOString(),
    connection_status: {
      is_connected: false,
      account_id: "",
      last_checked: new Date().toISOString(),
    },
    trading_data: {
      open_orders: 0,
      auto_prefill_orders: 0,
      open_positions: 0,
      positions_performance: {
        top_gainer: { symbol: "", position_size: 0 },
        top_loser: { symbol: "", position_size: 0 },
      },
    },
  };

  try {
    // Get user profile with error handling and timeout
    let userProfile;
    try {
      userProfile = await withTimeout(getUserProfile(userId));
    } catch (error) {
      console.error("Error fetching user profile:", error);
      userProfile = null;
    }

    // Check IB connection with improved error handling and timeout
    let connectionStatus = defaultAnalytics.connection_status;
    try {
      const testIBKRConnectionStatus = await withTimeout(
        testIBKRConnection(userId),
        3000, // Shorter timeout for connection check
      ).catch(() => false); // Explicitly handle timeout by returning false

      connectionStatus = {
        is_connected: Boolean(testIBKRConnectionStatus),
        account_id: userProfile?.settings?.defaultIBAccountId || "",
        last_checked: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error checking IB connection:", error);
      // Keep default connection status
    }

    // If not connected, don't try to fetch trading-related data
    if (!connectionStatus.is_connected) {
      return {
        ...defaultAnalytics,
        stocks_in_watchlist: 0, // We'll still try to get these non-IB dependent values
        symbols_trend_changed_from_previous_date: [],
        symbols_within_10_percent_window_entry: [],
        connection_status: connectionStatus,
        risk_signals_last_updated: new Date().toISOString(),
      };
    }

    // Get watchlist items with error handling
    let watchlistItems = [];
    try {
      watchlistItems = await withTimeout(getWatchlistItems(userId));
    } catch (error) {
      console.error("Error fetching watchlist:", error);
    }

    // Get trend changes with error handling
    let lastSyncedTrendChangeDate = new Date().toISOString();
    let trendChanges: { current: TrendChange; previous: TrendChange }[] = [];
    try {
      lastSyncedTrendChangeDate = await withTimeout(
        getLastSyncedTrendChangeDate(),
      );
      const trendChangesResult = await withTimeout(
        getTickerListWithChangesBetweenTwoDates(lastSyncedTrendChangeDate),
      );
      trendChanges = trendChangesResult;
    } catch (error) {
      console.error("Error fetching trend changes:", error);
    }

    // Get holdings data with error handling
    let holdings: PortfolioPosition[] | null = null;
    try {
      holdings = await withTimeout(fetchHoldingsData(userId), 10000); // Longer timeout for holdings
    } catch (error) {
      console.error("Error fetching positions data:", error);
    }

    // Get orders data with error handling
    let openOrders = 0;
    let autoPrefillOrders = 0;
    try {
      const [orders, autoOrders] = await Promise.all([
        withTimeout(getAllOrders(userId)),
        withTimeout(fetchAutoOrderPrefill(userId)),
      ]);
      openOrders = orders.length;
      autoPrefillOrders = autoOrders.length;
    } catch (error) {
      console.error("Error fetching orders:", error);
    }

    const positionsPerformance = calculatePositionsPerformance(holdings);

    // Add this new call to get symbols within 10% window
    let symbolsWithin10PercentWindow: { symbol: string; isStock: boolean }[] =
      [];
    try {
      symbolsWithin10PercentWindow = await withTimeout(
        getSymbolsWithin10PercentWindow(),
      );
    } catch (error) {
      console.error("Error fetching symbols within 10% window:", error);
    }

    // Return combined data with all the error handling in place
    return {
      stocks_in_watchlist: watchlistItems.length,
      symbols_trend_changed_from_previous_date: trendChanges,
      symbols_within_10_percent_window_entry: symbolsWithin10PercentWindow.map(
        ({ symbol, isStock }) => ({
          symbol,
          isStock,
        }),
      ),
      trading_data: {
        open_orders: openOrders,
        auto_prefill_orders: autoPrefillOrders,
        open_positions: holdings?.length || 0,
        positions_performance: positionsPerformance,
      },
      risk_signals_last_updated: lastSyncedTrendChangeDate,
      connection_status: connectionStatus,
    };
  } catch (error) {
    console.error("Error fetching dashboard analytics:", error);
    return defaultAnalytics;
  }
};

export default fetchDashboardAnalytics;
