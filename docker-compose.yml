version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: lunar-hedge-postgres
    environment:
      POSTGRES_DB: lunar_hedge
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # API Backend
  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    container_name: lunar-hedge-api
    environment:
      NODE_ENV: development
      PORT: 3001
      HOST: 0.0.0.0
      DATABASE_URL: ********************************************/lunar_hedge
      ALLOWED_ORIGINS: http://localhost:3000,http://web:3000
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./apps/api:/app/apps/api
      - ./packages:/app/packages
      - /app/node_modules
    command: pnpm --filter api dev

  # Web Frontend
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    container_name: lunar-hedge-web
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001/api
      PG_HEDGE_PRISMA_URL: ********************************************/lunar_hedge
      PG_HEDGE_URL_NON_POOLING: ********************************************/lunar_hedge
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - api
    volumes:
      - ./apps/web:/app/apps/web
      - ./packages:/app/packages
      - /app/node_modules
    command: pnpm --filter web dev

  # IBKR Gateway (from original setup)
  ib-gateway:
    image: ghcr.io/unusualalpha/ib-gateway:latest
    restart: always
    environment:
      TWS_USERID: "meetmpchamp"
      TWS_PASSWORD: "NWNDHbq%b3FZy4"
      TRADING_MODE: "paper"
      VNC_SERVER_PASSWORD: ${VNC_SERVER_PASSWORD:-}
    ports:
      - "127.0.0.1:4001:4001"
      - "127.0.0.1:4002:4002"
      - "127.0.0.1:5900:5900"

volumes:
  postgres_data:
