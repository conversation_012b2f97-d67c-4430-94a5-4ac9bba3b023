{"name": "lunar-hedge-monorepo", "version": "0.1.0", "private": true, "description": "Lunar Hedge Trading Platform - Monorepo", "workspaces": ["apps/*", "packages/*", "tools/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "turbo run test", "clean": "turbo run clean && rm -rf node_modules", "type-check": "turbo run type-check", "web:dev": "pnpm --filter web dev", "web:build": "pnpm --filter web build", "web:start": "pnpm --filter web start", "api:dev": "pnpm --filter api dev", "api:build": "pnpm --filter api build", "api:start": "pnpm --filter api start", "db:generate": "pnpm --filter database generate", "db:migrate": "pnpm --filter database migrate", "db:studio": "pnpm --filter database studio"}, "devDependencies": {"@types/node": "^24.0.1", "prettier": "3.5.3", "turbo": "^2.3.0", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}}