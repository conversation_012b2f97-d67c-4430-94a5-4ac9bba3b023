export * from '@prisma/client';
export { PrismaClient } from '@prisma/client';

// Re-export commonly used types and utilities
import { PrismaClient } from '@prisma/client';

// Create a singleton instance for the database connection
declare global {
  var __prisma: PrismaClient | undefined;
}

export const prisma = globalThis.__prisma || new PrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma;
}
