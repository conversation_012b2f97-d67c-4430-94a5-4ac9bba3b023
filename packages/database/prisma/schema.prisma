generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("PG_HEDGE_PRISMA_URL")
  directUrl = env("PG_HEDGE_URL_NON_POOLING")
}

model Account {
  id                 String  @id @default(cuid())
  userId             String  @map("user_id")
  type               String
  provider           String
  providerAccountId  String  @map("provider_account_id")
  refresh_token      String?
  access_token       String?
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?
  session_state      String?
  oauth_token_secret String?
  oauth_token        String?
  user               User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("account")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model User {
  id                 String             @id @default(cuid())
  name               String?
  email              String?            @unique
  emailVerified      DateTime?          @map("email_verified") @db.Timestamptz(3)
  image              String?
  created_at         DateTime           @default(now()) @db.Timestamptz(3)
  updated_at         DateTime           @default(now()) @db.Timestamptz(3)
  accounts           Account[]
  auto_order_prefill AutoOrderPrefill[]
  subscriptions      PushSubscription[]
  sessions           Session[]
  user_profiles      UserProfile?
  watchlist          Watchlist[]

  @@map("user")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_token")
}

model UserProfile {
  user_id    String    @id
  first_name String?
  last_name  String?
  settings   Json?
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  updated_at DateTime  @updatedAt @db.Timestamptz(3)
  deleted_at DateTime? @db.Timestamptz(3)
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("user_profile")
}

model TrendChange {
  id            String   @id @default(cuid())
  originalIndex Int      @map("original_index")
  index         String   @db.VarChar(20)
  description   String?
  trend         String   @db.VarChar(12)
  buyTrade      Decimal  @map("buy_trade") @db.Decimal(9, 3)
  sellTrade     Decimal  @map("sell_trade") @db.Decimal(9, 3)
  previousClose Decimal  @map("previous_close") @db.Decimal(9, 3)
  date          DateTime @db.Timestamptz(3)

  @@map("trend_change")
}

model UpsideDownsidePotential {
  id                String   @id @default(cuid())
  originalIndex     Int      @map("original_index")
  ticker            String   @db.VarChar(20)
  description       String?
  trend             String   @db.VarChar(12)
  downsidePotential Decimal  @map("downside_potential") @db.Decimal(9, 2)
  upsidePotential   Decimal  @map("upside_potential") @db.Decimal(9, 2)
  date              DateTime @db.Timestamptz(3)
  price             Decimal  @db.Money

  @@map("upside_downside_potential")
}

model PushSubscription {
  id         String    @id @default(cuid())
  endpoint   String
  auth       String
  p256dh     String
  user_id    String
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  updated_at DateTime  @default(now()) @db.Timestamptz(3)
  deleted_at DateTime? @db.Timestamptz(3)
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("push_subscription")
}

model Watchlist {
  id         String    @id @default(cuid())
  user_id    String
  ticker     String
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  updated_at DateTime  @default(now()) @db.Timestamptz(3)
  deleted_at DateTime? @db.Timestamptz(3)
  settings   Json?
  audit_log  Json?
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("watchlist")
}

model AutoOrderPrefill {
  id         String    @id @default(cuid())
  user_id    String
  action     String
  ticker     String
  quantity   Int
  price      Decimal   @db.Money
  order_type String
  account_id String
  created_at DateTime  @default(now()) @db.Timestamptz(3)
  updated_at DateTime  @default(now()) @db.Timestamptz(3)
  deleted_at DateTime? @db.Timestamptz(3)
  metadata   Json?
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("auto_order_prefill")
}
