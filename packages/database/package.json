{"name": "database", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"generate": "prisma generate", "migrate": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "studio": "prisma studio", "seed": "tsx src/seed.ts", "reset": "prisma migrate reset --force"}, "dependencies": {"@prisma/client": "6.9.0"}, "devDependencies": {"prisma": "^6.9.0", "tsx": "^4.7.0", "typescript": "^5.8.3"}}