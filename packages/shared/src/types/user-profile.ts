export interface RiskSignalSettings {
  showStockPicksOnly: boolean;
  sortStockPicksToTop: boolean;
  showStockInstrumentsOnly: boolean;
  defaultEntryWindow: number;
}

export interface IBKRConnectionDetail {
  host: string;
  port: number;
  clientId?: number;
}

export interface UserProfileSettings {
  riskSignalSettings?: RiskSignalSettings;
  ibkrConnectionDetail?: IBKRConnectionDetail;
  role?: "admin" | "user";
  defaultIBAccountId?: string;
  enableAutoOrderPrefill?: boolean;
}

export interface UserProfile {
  userId: string;
  firstName: string;
  lastName: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  settings: UserProfileSettings;
}
