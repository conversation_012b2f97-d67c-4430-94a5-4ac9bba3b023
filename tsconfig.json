{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"]}, "types": ["jest", "node", "@testing-library/jest-dom", "next"]}, "include": ["**/*.ts", "**/*.tsx", "jest.config.ts", "jest.setup.ts", ".next/types/**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules", "scripts/mcp-puppeteer"]}