name: Build and Deploy

on:
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write #This is required for requesting the OIDC JWT Token
      contents: read #Required when GH token is used to authenticate with private repo

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2.4.0
        with:
          version: 8

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20.x"
          cache: "pnpm"
          cache-dependency-path: pnpm-lock.yaml

      - name: Install dependencies & build application
        run: |
          pnpm install --ignore-scripts
          npx prisma generate
          pnpm build

      - name: Login via Azure CLI
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.LUNARHEDGEWEBAPP_AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.LUNARHEDGEWEBAPP_AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.LUNARHEDGEWEBAPP_AZURE_SUBSCRIPTION_ID }}

      - name: Build and push image
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.REGISTRY_LOGIN_SERVER }}
          username: ${{ secrets.LUNARHEDGEWEBAPP_REGISTRY_USERNAME }}
          password: ${{ secrets.LUNARHEDGEWEBAPP_REGISTRY_PASSWORD }}

      - run: |
          docker build . -t ${{ secrets.REGISTRY_LOGIN_SERVER }}/lunar-hedge-webapp:${{ github.sha }} -f ci/Dockerfile
          docker push ${{ secrets.REGISTRY_LOGIN_SERVER }}/lunar-hedge-webapp:${{ github.sha }}

      - name: Deploy Container App
        uses: azure/container-apps-deploy-action@v1
        with:
          containerAppName: lunar-hedge-webapp
          resourceGroup: lunar-hedge
          imageToDeploy: ${{ secrets.REGISTRY_LOGIN_SERVER }}/lunar-hedge-webapp:${{ github.sha }}
          targetPort: "3000"
        env:
          NODE_ENV: "production"
