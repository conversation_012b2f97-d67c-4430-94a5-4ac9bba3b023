{"name": "api", "version": "0.1.0", "private": true, "main": "./src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/rate-limit": "^10.1.1", "@fastify/swagger": "^9.3.0", "@fastify/swagger-ui": "^5.0.1", "@fastify/type-provider-typebox": "^5.0.0", "@sinclair/typebox": "^0.34.0", "database": "workspace:*", "fastify": "^5.2.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "shared": "workspace:*"}, "devDependencies": {"@types/node": "^24.0.1", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.28.0", "jest": "^30.0.0", "tsx": "^4.7.0", "typescript": "^5.8.3"}}