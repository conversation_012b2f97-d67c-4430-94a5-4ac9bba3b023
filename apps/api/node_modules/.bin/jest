#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/jest@30.0.0_@types+node@24.0.1_ts-node@10.9.2_@types+node@24.0.1_typescript@5.8.3_/node_modules/jest/bin/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/jest@30.0.0_@types+node@24.0.1_ts-node@10.9.2_@types+node@24.0.1_typescript@5.8.3_/node_modules/jest/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/jest@30.0.0_@types+node@24.0.1_ts-node@10.9.2_@types+node@24.0.1_typescript@5.8.3_/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/jest@30.0.0_@types+node@24.0.1_ts-node@10.9.2_@types+node@24.0.1_typescript@5.8.3_/node_modules/jest/bin/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/jest@30.0.0_@types+node@24.0.1_ts-node@10.9.2_@types+node@24.0.1_typescript@5.8.3_/node_modules/jest/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/jest@30.0.0_@types+node@24.0.1_ts-node@10.9.2_@types+node@24.0.1_typescript@5.8.3_/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jest/bin/jest.js" "$@"
else
  exec node  "$basedir/../jest/bin/jest.js" "$@"
fi
