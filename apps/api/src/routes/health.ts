import { FastifyPluginAsync } from 'fastify';
import { Type } from '@sinclair/typebox';
import { HealthCheckResponse } from 'shared';

const healthRoutes: FastifyPluginAsync = async (fastify) => {
  // Health check endpoint
  fastify.get('/health', {
    schema: {
      description: 'Health check endpoint',
      tags: ['Health'],
      response: {
        200: Type.Object({
          status: Type.Union([Type.Literal('ok'), Type.Literal('error')]),
          timestamp: Type.String(),
          uptime: Type.Number(),
          version: Type.String(),
          environment: Type.String(),
        }),
      },
    },
  }, async (request, reply) => {
    const startTime = process.hrtime();
    
    try {
      // You can add additional health checks here
      // For example: database connectivity, external services, etc.
      
      const response = {
        status: 'ok' as const,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV || 'development',
      };

      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = seconds * 1000 + nanoseconds / 1000000;

      reply.header('X-Response-Time', `${responseTime.toFixed(2)}ms`);
      
      return response;
    } catch (error) {
      fastify.log.error('Health check failed:', error);
      
      reply.status(503);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV || 'development',
        error: 'Service unavailable',
      };
    }
  });

  // Detailed health check with more information
  fastify.get('/health/detailed', {
    schema: {
      description: 'Detailed health check with system information',
      tags: ['Health'],
      response: {
        200: Type.Object({
          status: Type.Union([Type.Literal('ok'), Type.Literal('error')]),
          timestamp: Type.String(),
          uptime: Type.Number(),
          version: Type.String(),
          environment: Type.String(),
          system: Type.Object({
            nodeVersion: Type.String(),
            platform: Type.String(),
            arch: Type.String(),
            memory: Type.Object({
              used: Type.Number(),
              total: Type.Number(),
              free: Type.Number(),
            }),
          }),
        }),
      },
    },
  }, async (request, reply) => {
    try {
      const memoryUsage = process.memoryUsage();
      
      const response = {
        status: 'ok' as const,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV || 'development',
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
            free: Math.round((memoryUsage.heapTotal - memoryUsage.heapUsed) / 1024 / 1024), // MB
          },
        },
      };

      return response;
    } catch (error) {
      fastify.log.error('Detailed health check failed:', error);
      
      reply.status(503);
      return {
        status: 'error' as const,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV || 'development',
        error: 'Service unavailable',
      };
    }
  });
};

export default healthRoutes;
