<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="*************" clover="3.2.0">
  <project timestamp="*************" name="All files">
    <metrics statements="176" coveredstatements="151" conditionals="33" coveredconditionals="29" methods="5" coveredmethods="4" elements="214" coveredelements="184" complexity="0" loc="176" ncloc="176" packages="2" files="3" classes="3"/>
    <package name="trading-account.actions">
      <metrics statements="137" coveredstatements="131" conditionals="31" coveredconditionals="27" methods="2" coveredmethods="2"/>
      <file name="get-account-transactions.ts" path="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/app/trading-account/actions/get-account-transactions.ts">
        <metrics statements="137" coveredstatements="131" conditionals="31" coveredconditionals="27" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="18" count="5" type="stmt"/>
        <line num="19" count="5" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="21" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="23" count="5" type="stmt"/>
        <line num="24" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="26" count="5" type="stmt"/>
        <line num="27" count="5" type="stmt"/>
        <line num="28" count="5" type="stmt"/>
        <line num="29" count="5" type="stmt"/>
        <line num="30" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="3" type="stmt"/>
        <line num="32" count="5" type="stmt"/>
        <line num="33" count="5" type="stmt"/>
        <line num="34" count="5" type="stmt"/>
        <line num="35" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="5" type="stmt"/>
        <line num="41" count="5" type="stmt"/>
        <line num="42" count="5" type="stmt"/>
        <line num="43" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="44" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="45" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="46" count="8" type="stmt"/>
        <line num="47" count="8" type="stmt"/>
        <line num="48" count="8" type="stmt"/>
        <line num="49" count="8" type="stmt"/>
        <line num="50" count="8" type="stmt"/>
        <line num="51" count="8" type="stmt"/>
        <line num="52" count="8" type="stmt"/>
        <line num="53" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="55" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="56" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="57" count="5" type="stmt"/>
        <line num="58" count="5" type="stmt"/>
        <line num="59" count="5" type="stmt"/>
        <line num="60" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="5" type="stmt"/>
        <line num="72" count="5" type="stmt"/>
        <line num="73" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="76" count="5" type="stmt"/>
        <line num="77" count="9" type="stmt"/>
        <line num="78" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="79" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="80" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="81" count="3" type="stmt"/>
        <line num="82" count="3" type="stmt"/>
        <line num="83" count="3" type="stmt"/>
        <line num="84" count="3" type="stmt"/>
        <line num="85" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="86" count="3" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="88" count="3" type="stmt"/>
        <line num="89" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="90" count="3" type="stmt"/>
        <line num="91" count="3" type="stmt"/>
        <line num="92" count="3" type="stmt"/>
        <line num="93" count="9" type="stmt"/>
        <line num="94" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="95" count="5" type="stmt"/>
        <line num="96" count="5" type="stmt"/>
        <line num="97" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="98" count="3" type="stmt"/>
        <line num="99" count="3" type="stmt"/>
        <line num="100" count="3" type="stmt"/>
        <line num="101" count="3" type="stmt"/>
        <line num="102" count="3" type="stmt"/>
        <line num="103" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="104" count="4" type="stmt"/>
        <line num="105" count="4" type="stmt"/>
        <line num="106" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="107" count="6" type="stmt"/>
        <line num="108" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="109" count="4" type="stmt"/>
        <line num="110" count="4" type="stmt"/>
        <line num="111" count="4" type="stmt"/>
        <line num="112" count="4" type="stmt"/>
        <line num="113" count="3" type="stmt"/>
        <line num="114" count="3" type="stmt"/>
        <line num="115" count="3" type="stmt"/>
        <line num="116" count="3" type="stmt"/>
        <line num="117" count="3" type="stmt"/>
        <line num="118" count="5" type="stmt"/>
        <line num="119" count="5" type="stmt"/>
        <line num="120" count="5" type="stmt"/>
        <line num="121" count="5" type="stmt"/>
        <line num="122" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="5" type="stmt"/>
        <line num="130" count="5" type="stmt"/>
        <line num="131" count="5" type="stmt"/>
        <line num="132" count="5" type="stmt"/>
        <line num="133" count="5" type="stmt"/>
        <line num="134" count="5" type="stmt"/>
        <line num="135" count="5" type="stmt"/>
        <line num="136" count="5" type="stmt"/>
        <line num="137" count="5" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="39" coveredstatements="20" conditionals="2" coveredconditionals="2" methods="3" coveredmethods="2"/>
      <file name="ibkr-errors.ts" path="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/app/utils/ibkr-errors.ts">
        <metrics statements="6" coveredstatements="6" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
      </file>
      <file name="promise-utils.ts" path="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/app/utils/promise-utils.ts">
        <metrics statements="33" coveredstatements="14" conditionals="1" coveredconditionals="1" methods="2" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
