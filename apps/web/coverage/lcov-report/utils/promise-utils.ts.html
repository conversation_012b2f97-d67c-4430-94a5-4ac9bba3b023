<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for utils/promise-utils.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1>
          <a href="../index.html">All files</a> /
          <a href="index.html">utils</a> promise-utils.ts
        </h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">42.42% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">14/33</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">100% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">1/1</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">50% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">1/2</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">42.42% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">14/33</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block,
          <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">export class TimeoutError extends Error {
  constructor(message = "Operation timed out") {
    super(message);
    this.name = "TimeoutError";
  }
}
&nbsp;
export const DEFAULT_TIMEOUTS = {
  IBKR_CONNECTION: 3000, // 3 seconds for connection checks
  API_CALL: 10000, // 10 seconds for regular API calls
  DATA_FETCH: 15000, // 15 seconds for data-heavy operations
} as const;
&nbsp;
export const withTimeout = <span class="fstat-no" title="function not covered" >&lt;T&gt;(</span>
<span class="cstat-no" title="statement not covered" >  promise: Promise&lt;T&gt;,</span>
<span class="cstat-no" title="statement not covered" >  timeoutMs: number = DEFAULT_TIMEOUTS.API_CALL,</span>
<span class="cstat-no" title="statement not covered" >  operationName?: string,</span>
<span class="cstat-no" title="statement not covered" >): Promise&lt;T&gt; =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return Promise.race([</span>
<span class="cstat-no" title="statement not covered" >    promise,</span>
<span class="cstat-no" title="statement not covered" >    new Promise&lt;T&gt;((_, reject) =&gt;</span>
<span class="cstat-no" title="statement not covered" >      setTimeout(</span>
<span class="cstat-no" title="statement not covered" >        () =&gt;</span>
<span class="cstat-no" title="statement not covered" >          reject(</span>
<span class="cstat-no" title="statement not covered" >            new TimeoutError(</span>
<span class="cstat-no" title="statement not covered" >              `Operation ${operationName ? `'${operationName}'` : ""} timed out after ${timeoutMs}ms`,</span>
<span class="cstat-no" title="statement not covered" >            ),</span>
<span class="cstat-no" title="statement not covered" >          ),</span>
<span class="cstat-no" title="statement not covered" >        timeoutMs,</span>
<span class="cstat-no" title="statement not covered" >      ),</span>
<span class="cstat-no" title="statement not covered" >    ),</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;</pre></td></tr></table></pre>

      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a
        href="https://istanbul.js.org/"
        target="_blank"
        rel="noopener noreferrer"
        >istanbul</a
      >
      at 2025-01-05T15:30:03.875Z
    </div>
    <script src="../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../sorter.js"></script>
    <script src="../block-navigation.js"></script>
  </body>
</html>
