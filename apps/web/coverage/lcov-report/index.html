<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1>All files</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">85.79% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">151/176</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">87.87% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">29/33</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">80% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">4/5</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">85.79% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">151/176</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block,
          <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line high"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">
                File
              </th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th
                data-col="statements"
                data-type="number"
                data-fmt="pct"
                class="pct"
              >
                Statements
              </th>
              <th
                data-col="statements_raw"
                data-type="number"
                data-fmt="html"
                class="abs"
              ></th>
              <th
                data-col="branches"
                data-type="number"
                data-fmt="pct"
                class="pct"
              >
                Branches
              </th>
              <th
                data-col="branches_raw"
                data-type="number"
                data-fmt="html"
                class="abs"
              ></th>
              <th
                data-col="functions"
                data-type="number"
                data-fmt="pct"
                class="pct"
              >
                Functions
              </th>
              <th
                data-col="functions_raw"
                data-type="number"
                data-fmt="html"
                class="abs"
              ></th>
              <th
                data-col="lines"
                data-type="number"
                data-fmt="pct"
                class="pct"
              >
                Lines
              </th>
              <th
                data-col="lines_raw"
                data-type="number"
                data-fmt="html"
                class="abs"
              ></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file high" data-value="trading-account/actions">
                <a href="trading-account/actions/index.html"
                  >trading-account/actions</a
                >
              </td>
              <td data-value="95.62" class="pic high">
                <div class="chart">
                  <div class="cover-fill" style="width: 95%"></div>
                  <div class="cover-empty" style="width: 5%"></div>
                </div>
              </td>
              <td data-value="95.62" class="pct high">95.62%</td>
              <td data-value="137" class="abs high">131/137</td>
              <td data-value="87.09" class="pct high">87.09%</td>
              <td data-value="31" class="abs high">27/31</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="2" class="abs high">2/2</td>
              <td data-value="95.62" class="pct high">95.62%</td>
              <td data-value="137" class="abs high">131/137</td>
            </tr>

            <tr>
              <td class="file medium" data-value="utils">
                <a href="utils/index.html">utils</a>
              </td>
              <td data-value="51.28" class="pic medium">
                <div class="chart">
                  <div class="cover-fill" style="width: 51%"></div>
                  <div class="cover-empty" style="width: 49%"></div>
                </div>
              </td>
              <td data-value="51.28" class="pct medium">51.28%</td>
              <td data-value="39" class="abs medium">20/39</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="2" class="abs high">2/2</td>
              <td data-value="66.66" class="pct medium">66.66%</td>
              <td data-value="3" class="abs medium">2/3</td>
              <td data-value="51.28" class="pct medium">51.28%</td>
              <td data-value="39" class="abs medium">20/39</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a
        href="https://istanbul.js.org/"
        target="_blank"
        rel="noopener noreferrer"
        >istanbul</a
      >
      at 2025-01-05T15:30:03.875Z
    </div>
    <script src="prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="sorter.js"></script>
    <script src="block-navigation.js"></script>
  </body>
</html>
