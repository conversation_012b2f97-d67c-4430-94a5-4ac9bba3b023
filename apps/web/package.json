{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:https": "next dev --experimental-https --turbo", "build": "next build", "start": "next start", "lint": "npx eslint .", "format": "prettier --write .", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf .next", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "1.0.7", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "@react-email/components": "^0.0.42", "@react-email/render": "^1.1.2", "@stoqey/ib": "^1.5.1", "@tanstack/react-virtual": "^3.13.10", "babel-loader": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "database": "workspace:*", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "5.0.0-beta.25", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "pino": "^9.7.0", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-email": "^4.0.16", "react-hook-form": "^7.57.0", "resend": "^4.5.2", "rxjs": "^7.8.2", "shared": "workspace:*", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.1", "vaul": "^1.1.2", "web-push": "^3.6.7", "web-vitals": "^5.0.3", "yahoo-finance2": "^2.13.3", "zod": "^3.25.63"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.17", "@types/next": "^9.0.0", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-day-picker": "^5.3.0", "@types/react-dom": "^19.1.6", "@types/web-push": "^3.6.4", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "autoprefixer": "^10.4.21", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "10.1.5", "eslint-plugin-check-file": "3.3.0", "eslint-plugin-prettier": "^5.4.1", "jest": "^30.0.0", "jsdom": "^26.1.0", "pino-pretty": "^13.0.0", "postcss": "^8.5.5", "prettier": "3.5.3", "tailwindcss": "^4.1.10", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "8.34.0"}, "pnpm": {"overrides": {"react-day-picker": "8.10.1"}}}