version: "3.8"

services:
  # ibkr:
  #   image: ghcr.io/extrange/ibkr # latest, stable, 10.21, 10.21.1p etc
  #   shm_size: "2gb" # Sets the shared memory size
  #   platform: linux/amd64 # Sets the platform
  #   ports:
  #     - "127.0.0.1:6080:6080" # noVNC browser access
  #     - "127.0.0.1:8888:8888" # API access
  #   ulimits:
  #     nofile: 10000 # See FAQ
  #   environment:
  #     USERNAME: "meetmpchamp"
  #     PASSWORD: "NWNDHbq%b3FZy4"
  #     # TWOFA_TIMEOUT_ACTION: restart
  #     # GATEWAY_OR_TWS: gateway
  #     #
  #     # Variables prefixed with IBC_ override IBCAlpha`s config.ini:
  #     # IBC_TradingMode: live
  #     # IBC_ReadOnlyApi: yes
  #     # ...
  #     # See below for more details
  ib-gateway:
    image: ghcr.io/unusualalpha/ib-gateway:latest
    restart: always
    environment:
      TWS_USERID: "meetmpchamp"
      TWS_PASSWORD: "NWNDHbq%b3FZy4"
      TRADING_MODE: "paper"
      VNC_SERVER_PASSWORD: ${VNC_SERVER_PASSWORD:-}
    ports:
      - "127.0.0.1:4001:4001"
      - "127.0.0.1:4002:4002"
      - "127.0.0.1:5900:5900"
  webapp:
    build:
      context: .
      dockerfile: Dockerfile
