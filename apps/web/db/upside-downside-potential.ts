"use server";

import prismadb from "@/lib/prisma";
import UpsideDownsidePotential from "@prisma/client";

const getDownsideUpsidePotentialsForDate = async (targetDate: Date) => {
  if (isNaN(targetDate.getTime())) {
    console.log("isNaN(targetDate.getTime())", isNaN(targetDate.getTime()));
    return [];
  }
  const dateString = targetDate.toISOString().split("T")[0];
  const result =
    await prismadb.$queryRaw`SELECT id, original_index as "originalIndex", ticker, description, trend, downside_potential as "downsidePotential", upside_potential as "upsidePotential", price, date FROM "upside_downside_potential" WHERE CAST(date AS DATE) = CAST(${dateString} AS DATE);`;
  return result as unknown as UpsideDownsideVisualization[];
};

export async function saveUpsideDownsidePotential(
  upsideDownsidePotentials: UpsideDownsideVisualization[],
): Promise<number> {
  const existing = await getDownsideUpsidePotentialsForDate(
    upsideDownsidePotentials[0].date,
  );
  // console.log(existing && (JSON.parse(existing) as []).length !== 0);
  if (existing && existing.length > 0) {
    console.log(
      "Existing up/down potential already in the database for this date: ",
      upsideDownsidePotentials[0].date,
    );
    return 0;
  }
  const saveUpsideDownsidePotentials =
    await prismadb.upsideDownsidePotential.createMany({
      data: upsideDownsidePotentials,
    });
  return saveUpsideDownsidePotentials.count;
}

export async function getMostRecentUpsideDownsidePotential(
  date: Date | undefined,
): Promise<string> {
  const dateOnly = date ? new Date(date) : new Date();
  dateOnly.setUTCHours(0, 0, 0, 0); // Set time to midnight (UTC)

  const downsideUpsidePotentials =
    await getDownsideUpsidePotentialsForDate(dateOnly);
  if (downsideUpsidePotentials.length === 0) {
    const mostRecentDate = await prismadb.upsideDownsidePotential.findFirst({
      orderBy: {
        date: "desc", // Order by date in descending order
      },
    });
    const mostRecentUpsideDownsidePotential =
      await getDownsideUpsidePotentialsForDate(mostRecentDate?.date!);
    return JSON.stringify(mostRecentUpsideDownsidePotential);
  }
  return JSON.stringify(downsideUpsidePotentials);
}

export interface UpsideDownsideVisualization {
  id: string;
  originalIndex: number;
  ticker: string;
  price: number;
  trend: string;
  downsidePotential: number;
  upsidePotential: number;
  date: Date;
}
