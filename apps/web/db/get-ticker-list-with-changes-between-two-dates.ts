"use server";

import {
  getArchivesOfRiskSignals,
  getTrendChangesForDate,
  TrendChange,
} from "./trend-change";

// Returns a list of tickers that have changed between two dates
// The change can be from BULLISH to BEARISH or BEARISH to BULLISH or NEUTRAL to BULLISH or NEUTRAL to BEARISH
// For example, if the trend changes are [{"ticker": "AAPL", "date": "2024-01-01", "trend": "BULLISH"}, {"ticker": "AAPL", "date": "2024-01-02", "trend": "BEARISH"}]
// Then the ticker list with changes between the two dates will be ["AAPL"]
// If date1 has no records, then get the prior dates until there is a record
// If date2 has no records, then get the next dates until there is a record
// date1 and date2 cannot be the same date
// date1 and date2 must be in the same year
// date1 and date2 must be in the same month
// date1 should be before date2

function stripTime(date: Date | string): Date {
  // Ensure we have a Date object
  const dateObj = date instanceof Date ? date : new Date(date);

  // Validate the date
  if (isNaN(dateObj.getTime())) {
    throw new Error("Invalid date provided");
  }

  // Create date in UTC to avoid timezone offset issues
  const year = dateObj.getFullYear();
  const month = dateObj.getMonth();
  const day = dateObj.getDate();

  // Create new UTC date
  return new Date(Date.UTC(year, month, day, 0, 0, 0));
}

export async function getTickerListWithChangesBetweenTwoDates(
  selectedTrendChangeDate: Date | string,
  previousTrendChangeDate?: Date | string,
): Promise<{ current: TrendChange; previous: TrendChange }[]> {
  // Ensure we have valid Date objects
  const selectedDate = stripTime(selectedTrendChangeDate);
  // If previousTrendChangeDate is not provided, use the previous date
  let previousDate: Date;
  if (!previousTrendChangeDate) {
    previousDate = new Date(selectedDate);
    previousDate.setDate(previousDate.getDate() - 1);
    previousDate = stripTime(previousDate);
  } else {
    previousDate = stripTime(previousTrendChangeDate);
  }
  // Validate that dates are not the same
  if (previousDate.getTime() === selectedDate.getTime()) {
    throw new Error(
      "previousTrendChangeDate and selectedTrendChangeDate cannot be the same date",
    );
  }

  // Validate that previousTrendChangeDate is before selectedTrendChangeDate
  if (previousDate > selectedDate) {
    throw new Error(
      "previousTrendChangeDate must be before selectedTrendChangeDate",
    );
  }

  // Validate that dates are in the same year
  if (previousDate.getFullYear() !== selectedDate.getFullYear()) {
    throw new Error("dates must be in the same year");
  }

  // Validate that dates are in the same month
  if (previousDate.getMonth() !== selectedDate.getMonth()) {
    throw new Error("dates must be in the same month");
  }

  //   // Find the closest date with records for date1
  //   const closestDate1 = await findClosestDateWithRecords(previousDate, "prior");
  //   if (!closestDate1) {
  //     throw new Error("No records found for or before previousTrendChangeDate");
  //   }

  //   // Find the closest date with records for date2
  //   const closestDate2 = await findClosestDateWithRecords(selectedDate, "next");
  //   if (!closestDate2) {
  //     throw new Error("No records found for or after selectedTrendChangeDate");
  //   }

  const trendsDate1 = await getTrendChangesForDate(selectedDate);
  const trendsDate2 = await getTrendChangesForDate(previousDate);

  // Create maps for easier comparison and lookup
  const date1TrendMap = new Map(trendsDate1.map((tc) => [tc.index, tc]));
  const date2TrendMap = new Map(trendsDate2.map((tc) => [tc.index, tc]));

  // Find tickers that have changed trends
  const changedTickers: { current: TrendChange; previous: TrendChange }[] = [];
  const processedTickers = new Set<string>();

  // Check tickers that exist in both dates
  for (const [ticker, current] of date1TrendMap) {
    // Skip if we've already processed this ticker
    if (processedTickers.has(ticker)) continue;

    const previous = date2TrendMap.get(ticker);
    if (previous && current.trend !== previous.trend) {
      // Only add if the trend actually changed
      if (
        (current.trend === "BULLISH" && previous.trend === "BEARISH") ||
        (current.trend === "BEARISH" && previous.trend === "BULLISH") ||
        (current.trend === "NEUTRAL" &&
          (previous.trend === "BULLISH" || previous.trend === "BEARISH")) ||
        ((current.trend === "BULLISH" || current.trend === "BEARISH") &&
          previous.trend === "NEUTRAL")
      ) {
        changedTickers.push({
          current,
          previous,
        });
        processedTickers.add(ticker);
      }
    }
  }

  return changedTickers;
}

async function findClosestDateWithRecords(
  date: Date | string,
  direction: "prior" | "next",
): Promise<Date | null> {
  const { trendChanges } = await getArchivesOfRiskSignals();
  if (trendChanges.length === 0) return null;

  // Strip time for consistent comparison
  const targetDate = stripTime(date);
  const normalizedDates = trendChanges.map((d) => stripTime(d));

  // Sort dates based on direction
  const sortedDates = [...normalizedDates].sort((a, b) =>
    direction === "prior"
      ? b.getTime() - a.getTime()
      : a.getTime() - b.getTime(),
  );

  if (direction === "prior") {
    return sortedDates.find((d) => d.getTime() <= targetDate.getTime()) || null;
  } else {
    return sortedDates.find((d) => d.getTime() >= targetDate.getTime()) || null;
  }
}
