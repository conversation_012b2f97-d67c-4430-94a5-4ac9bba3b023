import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// export function customNumberFormatter(number: number): string {
//   if (number) {
//     // Remove commas and check if the number is an integer
//     if (Number(number.toString().replace(/,/g, "")) % 1 === 0) {
//       // If it's an integer, format without decimal places
//       return number.toLocaleString("en-US");
//     } else {
//       // If it's not an integer, format with two decimal places
//       return number.toLocaleString("en-US", {
//         minimumFractionDigits: 2,
//         maximumFractionDigits: 2,
//       });
//     }
//   }
//   return "";
// }

export function customNumberFormatter(number: number): string {
  if (number) {
    // Remove commas and check if the number is an integer
    const numberStr = number.toString().replace(/,/g, "");
    if (parseFloat(numberStr) % 1 === 0) {
      // If it's an integer, format without decimal places
      return number.toLocaleString("en-US");
    } else {
      // If it's not an integer, format with three decimal places, ignoring trailing zeros
      const formattedNumber = number.toLocaleString("en-US", {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3,
      });
      return formattedNumber.replace(/\.?0+$/, ""); // Remove trailing zeros
    }
  }
  return "";
}
