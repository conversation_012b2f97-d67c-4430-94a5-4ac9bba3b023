import pino from "pino";
import pretty from "pino-pretty";
import { getEnvironmentVariable } from "./utils-server";

const levels = {
  emerg: 80,
  alert: 70,
  crit: 60,
  error: 50,
  warn: 40,
  notice: 30,
  info: 20,
  debug: 10,
};
const stream = pretty({
  colorize: true,
});
// create pino logger
const logger = pino(
  {
    level: process.env.PINO_LOG_LEVEL || "info",
    customLevels: levels,
    useOnlyCustomLevels: true,
    base: {
      env: process.env.NODE_ENV,
    },
    formatters: {
      level: (label: string) => {
        return { level: label.toUpperCase() };
      },
    },
  },
  stream,
);

export default logger;

// This is just a temporary logging to help debugging the production.
export async function tempLoggingBetterStack(data: {
  dt: Date;
  message: string;
}) {
  const betterstackApiKey =
    getEnvironmentVariable("NODE_ENV") === "production"
      ? "UJt6jTT1Tj8MmbNQgn1WemXW"
      : "uboJNurXKDEm5FSZcTfqfT51";
  await fetch("https://in.logs.betterstack.com", {
    method: "POST",
    body: JSON.stringify(data),
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${betterstackApiKey}`,
    },
  });
}

export async function testToLogEnvVarIfCanBeRead() {
  const data = {
    dt: new Date(),
    message: `Reading HEDGEYE_SESSION_COOKIE env var: ${process.env.HEDGEYE_SESSION_COOKIE} AND NEXT_PUBLIC_SITE_URL: ${process.env.NEXT_PUBLIC_SITE_URL}`,
  };

  await tempLoggingBetterStack(data);
}
