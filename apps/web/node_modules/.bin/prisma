#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules/prisma/build/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules/prisma/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules/prisma/build/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules/prisma/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules/prisma/build/index.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/prisma@6.9.0_typescript@5.8.3/node_modules/prisma/build/index.js" "$@"
fi
