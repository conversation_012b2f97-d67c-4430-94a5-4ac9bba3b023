#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.4_@jest+transform@30.0.0_@jest+types@30.0.0_babel-jest@30.0.0_samyh7dvut3y3zoghlmhi35c4m/node_modules/ts-jest/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.4_@jest+transform@30.0.0_@jest+types@30.0.0_babel-jest@30.0.0_samyh7dvut3y3zoghlmhi35c4m/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.4_@jest+transform@30.0.0_@jest+types@30.0.0_babel-jest@30.0.0_samyh7dvut3y3zoghlmhi35c4m/node_modules/ts-jest/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.4_@jest+transform@30.0.0_@jest+types@30.0.0_babel-jest@30.0.0_samyh7dvut3y3zoghlmhi35c4m/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
