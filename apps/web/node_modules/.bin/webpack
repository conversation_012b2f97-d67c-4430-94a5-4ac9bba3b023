#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/webpack@5.99.9/node_modules/webpack/bin/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/webpack@5.99.9/node_modules/webpack/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/webpack@5.99.9/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/webpack@5.99.9/node_modules/webpack/bin/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/webpack@5.99.9/node_modules/webpack/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/webpack@5.99.9/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/webpack@5.99.9/node_modules/webpack/bin/webpack.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/webpack@5.99.9/node_modules/webpack/bin/webpack.js" "$@"
fi
