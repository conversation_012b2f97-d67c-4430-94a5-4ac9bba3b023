#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-email/dist/cli/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-email/dist/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-email/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-email/dist/cli/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-email/dist/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-email/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/react-email@4.0.16_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules:/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../react-email/dist/cli/index.mjs" "$@"
else
  exec node  "$basedir/../react-email/dist/cli/index.mjs" "$@"
fi
