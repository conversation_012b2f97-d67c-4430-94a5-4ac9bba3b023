const puppeteer = require("puppeteer");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const puppeteerExtra = require("puppeteer-extra");

// Add stealth plugin
puppeteerExtra.use(StealthPlugin());

async function takeScreenshot() {
  const browser = await puppeteerExtra.launch({
    headless: false,
    args: [
      "--window-size=1366,768",
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-infobars",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--disable-gpu",
      "--lang=en-US,en",
    ],
  });

  const page = await browser.newPage();

  // Set a more common user agent
  await page.setUserAgent(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
  );

  // Set viewport
  await page.setViewport({ width: 1366, height: 768 });

  // Navigate to your app
  await page.goto("https://your-app-url.com");

  // Take screenshot
  await page.screenshot({ path: "screenshot.png" });

  await browser.close();
}

takeScreenshot();
