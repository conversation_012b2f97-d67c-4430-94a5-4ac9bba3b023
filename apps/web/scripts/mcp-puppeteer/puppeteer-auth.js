const puppeteer = require("puppeteer");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const puppeteerExtra = require("puppeteer-extra");

// Add stealth plugin
puppeteerExtra.use(StealthPlugin());

async function takeScreenshotWithAuth() {
  // Launch browser with stealth plugin and additional arguments
  const browser = await puppeteerExtra.launch({
    headless: false,
    args: [
      "--window-size=1366,768",
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-infobars",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--disable-gpu",
      "--lang=en-US,en",
      "--disable-features=IsolateOrigins,site-per-process",
      "--disable-web-security",
    ],
  });

  const page = await browser.newPage();

  // Set a more common user agent
  await page.setUserAgent(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  );

  // Set viewport
  await page.setViewport({ width: 1366, height: 768 });

  // Set cookies from a previous authenticated session
  const cookies = [
    // Replace with your actual session cookie
    {
      name: "next-auth.session-token",
      value: "your-session-token-here",
      domain: "localhost",
      path: "/",
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
    },
  ];

  await page.setCookie(...cookies);

  // Navigate to your protected page
  await page.goto("http://localhost:3000/risk-signals", {
    waitUntil: "networkidle2",
  });

  // Wait for content to load
  await page.waitForSelector("body");

  console.log("Taking screenshot...");

  // Take screenshot
  await page.screenshot({
    path: "authenticated-page.png",
    fullPage: true,
  });

  console.log("Screenshot saved to authenticated-page.png");

  await browser.close();
}

takeScreenshotWithAuth().catch((err) => {
  console.error("Error taking screenshot:", err);
  process.exit(1);
});
