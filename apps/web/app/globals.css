@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Darker+Grotesque&display=swap");

@import "tailwindcss";
@config "../tailwind.config.ts";

/* TailwindCSS v4 Theme Configuration */
@theme {
  /* Breakpoints */
  --breakpoint-xs: 375px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Border Radius */
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  /* Colors - Light Mode (Default) - Bright Professional Theme */
  --background: 0 0% 99%;
  --foreground: 220 9% 15%;
  --card: 0 0% 100%;
  --card-foreground: 220 9% 15%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 9% 15%;
  --primary: 221 83% 53%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 40% 98%;
  --secondary-foreground: 220 9% 15%;
  --muted: 210 40% 96%;
  --muted-foreground: 215 16% 47%;
  --accent: 210 40% 95%;
  --accent-foreground: 220 9% 15%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: 221 83% 53%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;

  /* Base radius value */
  --radius: 0.5rem;
}

.dark {
  /* Colors - Dark Mode */
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    margin: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  main {
    flex: 1;
  }

  /* Typography Classes */
  .text-heading {
    color: hsl(var(--foreground));
    font-weight: 600;
  }

  .text-subheading {
    color: hsl(var(--foreground));
    font-weight: 500;
  }

  .text-body {
    color: hsl(var(--foreground));
  }

  .text-muted {
    color: hsl(var(--muted-foreground));
  }

  .text-subtle {
    color: hsl(var(--muted-foreground));
    opacity: 0.8;
  }

  /* Interactive text colors */
  .text-interactive {
    color: hsl(var(--muted-foreground));
    transition: color 0.2s ease;
  }

  .text-interactive:hover {
    color: hsl(var(--foreground));
  }

  /* Size classes that respect theme */
  .text-size-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .text-size-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-size-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .text-size-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .text-size-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Font weight classes */
  .font-weight-normal {
    font-weight: 400;
  }

  .font-weight-medium {
    font-weight: 500;
  }

  .font-weight-semibold {
    font-weight: 600;
  }

  .font-weight-bold {
    font-weight: 700;
  }
}

.text-gradient {
  background: linear-gradient(to right, #00dc82, #36e4da);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (prefers-color-scheme: light) {
  .text-gradient {
    background: linear-gradient(to right, #00dc82, #36e4da);
  }
}

/* Simple enhancements for financial trading platform */

/* Professional scrollbar styling */
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Financial data styling */
.financial-data {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.025em;
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-1px);
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Professional trading platform enhancements */
.trading-header {
  background: linear-gradient(
    135deg,
    hsl(var(--background)) 0%,
    hsl(var(--muted)) 100%
  );
  border-bottom: 1px solid hsl(var(--border));
}

.trading-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 0.1),
    0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.trading-card:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Trading status indicators - optimized for bright light theme */
.status-bullish {
  color: #047857;
  background-color: #ecfdf5;
  border: 1px solid #a7f3d0;
}

.dark .status-bullish {
  color: #34d399;
  background-color: #064e3b;
  border: 1px solid #065f46;
}

.status-bearish {
  color: #dc2626;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
}

.dark .status-bearish {
  color: #f87171;
  background-color: #7f1d1d;
  border: 1px solid #991b1b;
}

.status-neutral {
  color: #1d4ed8;
  background-color: #eff6ff;
  border: 1px solid #93c5fd;
}

.dark .status-neutral {
  color: #60a5fa;
  background-color: #1e3a8a;
  border: 1px solid #1e40af;
}

/* Fix Sheet overlay to be theme-aware */
[data-radix-dialog-overlay] {
  background-color: hsl(var(--background) / 0.8) !important;
}
