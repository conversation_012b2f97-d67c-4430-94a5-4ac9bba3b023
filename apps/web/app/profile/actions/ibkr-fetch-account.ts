"use server";
import { getUserProfile } from "@/db/user-profile";
import { EventName, IBApi } from "@stoqey/ib";
import { createIBApiConnection } from "@/app/actions/ibapi/connection";
type IBKRConfig = {
  host: string;
  port: number;
  clientId: number;
};

export async function getIBKRConfig(): Promise<IBKRConfig> {
  const host = process.env.IBKR_HOST;
  const port = process.env.IBKR_PORT;
  const clientId = process.env.IBKR_CLIENT_ID
    ? parseInt(process.env.IBKR_CLIENT_ID)
    : Math.floor(Math.random() * 9999) + 1;

  if (!host || !port || !clientId) {
    throw new Error(
      "Missing IBKR configuration. Please set IBKR_HOST, IBKR_PORT, and IBKR_CLIENT_ID environment variables.",
    );
  }

  return {
    host,
    port: parseInt(port),
    clientId,
  };
}

export type AccountInfo = {
  accountId: string;
  totalCashValue: number;
  availableFunds: number;
  netLiquidation: number;
};

export async function fetchIBKRAccountInfo(
  userId: string,
): Promise<AccountInfo | null> {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }
    const ib = await createIBApiConnection(userId);
    let accountInfo: AccountInfo = {
      accountId: "",
      totalCashValue: 0,
      availableFunds: 0,
      netLiquidation: 0,
    };
    // Use Promises to wait for account updates
    const accountInfoPromise = new Promise<AccountInfo>((resolve, reject) => {
      // register event handlers
      ib.on(EventName.error, (err, code, reqId) => {
        console.error(
          `Error: ${err.message} - code: ${code} - reqId: ${reqId}`,
        );
        reject(err);
      });

      ib.on(
        EventName.updateAccountValue,
        (key, value, currency, accountName) => {
          if (key === "AccountCode") {
            console.log(`Account Number: ${value}`);
            accountInfo.accountId = value;
          } else if (key === "AvailableFunds") {
            console.log(`Available Cash Balance: ${value} ${currency}`);
            accountInfo.availableFunds = parseFloat(value);
          } else if (key === "NetLiquidation") {
            console.log(`Net Liquidation: ${value} ${currency}`);
            accountInfo.netLiquidation = parseFloat(value);
          } else if (key === "TotalCashValue") {
            console.log(`Total Cash Value: ${value} ${currency}`);
            accountInfo.totalCashValue = parseFloat(value);
          }

          // Resolve the promise when all required information is received
          if (
            accountInfo.accountId &&
            accountInfo.availableFunds &&
            accountInfo.netLiquidation &&
            accountInfo.totalCashValue
          ) {
            ib.disconnect();
            resolve(accountInfo);
          }
        },
      );

      // connect and request account updates
      ib.connect();
      ib.reqAccountUpdates(true, "");
    });

    return await accountInfoPromise;
  } catch (error) {
    console.error("Error fetching account info:", error);
    return null;
  }
}
