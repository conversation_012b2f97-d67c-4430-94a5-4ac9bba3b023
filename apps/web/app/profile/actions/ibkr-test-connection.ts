"use server";

import { createIBApiNextConnection } from "@/app/actions/ibapi/connection";

export async function testIBKRConnection(userId: string): Promise<boolean> {
  try {
    const { ibApiNext, clientId } = await createIBApiNextConnection(userId);

    return new Promise((resolve, reject) => {
      const connectionTimeout = setTimeout(
        () => reject(new Error("Connection timeout")),
        3000,
      );
      const subscription = ibApiNext
        .connect()
        .getAccountSummary("All", "NetLiquidation")
        .subscribe({
          next: () => {
            clearTimeout(connectionTimeout);
            resolve(true);
            ibApiNext.disconnect();
          },
          error: (error: Error) => {
            clearTimeout(connectionTimeout);
            reject(error);
            ibApiNext.disconnect();
          },
        });

      // Ensure disconnection on promise resolution/rejection
      subscription.add(() => {
        if (ibApiNext.isConnected) {
          ibApiNext.disconnect();
          console.log("Disconnected from IBKR");
        }
      });
    });
  } catch (error) {
    console.error("Error testing IBKR connection:", error);
    return false;
  }
}
