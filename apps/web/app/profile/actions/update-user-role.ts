"use server";

import { getUserProfile } from "@/db/user-profile";
import { UserProfileSettings } from "@/types/user-profile";
import prismadb from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export async function updateUserRole(userId: string, role: "user" | "admin") {
  try {
    // Here you would implement the actual database update
    // For example, using your existing db utilities:
    // await updateUserProfile(userId, { settings: { role } });

    revalidatePath("/profile");
    return { success: true };
  } catch (error) {
    console.error("Failed to update user role:", error);
    throw new Error("Failed to update user role");
  }
}

export const upsertUserRole = async (
  settings: UserProfileSettings,
  userId: string,
) => {
  try {
    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      throw new Error("User profile not found");
    }

    const userProfileSettings = JSON.parse(
      JSON.stringify({
        ...userProfile.settings,
        role: settings.role,
      }),
    );

    const updatedUserProfile = await prismadb.userProfile.update({
      where: { user_id: userId },
      data: {
        settings: userProfileSettings,
      },
    });
    revalidatePath("/profile");
    return updatedUserProfile;
  } catch (error) {
    console.error("Error upserting user profile settings", error);
    throw error;
  }
};
