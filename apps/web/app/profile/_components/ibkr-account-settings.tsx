"use client";

import { useEffect, useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { getUserProfile } from "@/db/user-profile";
import { useSession } from "next-auth/react";
import { toast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getAllAccounts } from "@/app/portfolio/actions/get-all-accounts";
import { upsertUserProfileIBAccountSettings } from "@/app/actions/upsert-user-profile-settings";

interface Account {
  accountId: string;
  accountNumber: string;
}

export function IBKRAccountSettings() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [settings, setSettings] = useState({
    defaultIBAccountId: "",
    enableAutoOrderPrefill: false,
  });
  const [initialSettings, setInitialSettings] = useState({
    defaultIBAccountId: "",
    enableAutoOrderPrefill: false,
  });
  const [hasChanges, setHasChanges] = useState(false);

  // Load accounts and initial settings
  useEffect(() => {
    const loadData = async () => {
      if (session?.user?.id) {
        // Load accounts
        const accountsList = await getAllAccounts(session.user.id);
        const formattedAccounts = accountsList.map((account) => ({
          accountId: account.accountId,
          accountNumber: account.accountId, // Using accountId as accountNumber since it's required
        }));
        setAccounts(formattedAccounts);

        // Load user profile settings
        const profile = await getUserProfile(session.user.id);
        const currentSettings = {
          defaultIBAccountId: profile?.settings?.defaultIBAccountId || "",
          enableAutoOrderPrefill:
            profile?.settings?.enableAutoOrderPrefill || false,
        };
        setSettings(currentSettings);
        setInitialSettings(currentSettings);
      }
    };

    loadData();
  }, [session?.user?.id]);

  // Check for changes
  useEffect(() => {
    const changed =
      settings.defaultIBAccountId !== initialSettings.defaultIBAccountId ||
      settings.enableAutoOrderPrefill !==
        initialSettings.enableAutoOrderPrefill;
    setHasChanges(changed);
  }, [settings, initialSettings]);

  const handleSaveSettings = async () => {
    if (!session?.user?.id) return;

    try {
      setIsLoading(true);
      await upsertUserProfileIBAccountSettings(
        {
          defaultIBAccountId: settings.defaultIBAccountId,
          enableAutoOrderPrefill: settings.enableAutoOrderPrefill,
        },
        session.user.id,
      );
      setInitialSettings(settings);
      toast({
        title: "Settings saved",
        description: "Your IBKR account settings have been updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="defaultIBAccountId">Default Account</Label>
          <Select
            value={settings.defaultIBAccountId}
            onValueChange={(value) =>
              setSettings({ ...settings, defaultIBAccountId: value })
            }
          >
            <SelectTrigger className="w-[240px]">
              <SelectValue placeholder="Select default account" />
            </SelectTrigger>
            <SelectContent>
              {accounts.map((account) => (
                <SelectItem
                  key={account.accountNumber}
                  value={account.accountNumber}
                >
                  {account.accountNumber}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            This account will be used as the default for all trading operations
          </p>
        </div>

        <div className="flex items-center justify-between space-x-2">
          <div className="space-y-0.5">
            <Label htmlFor="autoOrderPrefill">Auto Order Prefill</Label>
            <p className="text-sm text-muted-foreground">
              Automatically prefill order details based on risk signals
            </p>
          </div>
          <Switch
            id="autoOrderPrefill"
            checked={settings.enableAutoOrderPrefill}
            onCheckedChange={(checked) =>
              setSettings({ ...settings, enableAutoOrderPrefill: checked })
            }
          />
        </div>
      </div>

      <Button
        onClick={handleSaveSettings}
        disabled={isLoading || !hasChanges}
        className="w-full sm:w-auto"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Saving...
          </>
        ) : (
          "Save Settings"
        )}
      </Button>
    </div>
  );
}
