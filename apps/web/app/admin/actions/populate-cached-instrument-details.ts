"use server";

import { getCachedInstrumentDetailBatch } from "@/app/actions/get-instrument-detail";
import { CachedSymbol, getAllCachedSymbols } from "@/app/models/symbols";
import {
  getLastSyncedTrendChangeDate,
  getTrendChangesByDateOrMostRecent,
} from "@/db/trend-change";
import { TrendChange } from "@/types/trend-change";

export async function populateCachedInstrumentDetails(
  symbols: string[],
  symbolsFromRiskSignals: boolean = false,
) {
  if (symbols.length === 0 && symbolsFromRiskSignals) {
    const lastSyncedTrendChangeDate = await getLastSyncedTrendChangeDate();
    const riskSignals = JSON.parse(
      await getTrendChangesByDateOrMostRecent(
        new Date(lastSyncedTrendChangeDate),
      ),
    ) as TrendChange[];
    symbols = riskSignals.map((riskSignal) => riskSignal.index);
  }
  console.log("populateCachedInstrumentDetails():symbols->", symbols);
  const results = await getCachedInstrumentDetailBatch(symbols);
  // const validResults = Array.from(results.values())
  //   .filter(
  //     (result): result is CachedSymbol =>
  //       result !== null && typeof result === "object" && "symbol" in result,
  //   )
  //   .map((result) => result);

  // console.log(
  //   `Batch processed ${symbols.length} symbols, got ${validResults.length} valid results`,
  // );
  return {
    processed: symbols.length,
    valid: results.size,
    cache: results,
  };
}
