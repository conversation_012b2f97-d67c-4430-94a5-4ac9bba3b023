"use client";

import { useEffect } from "react";
import { useState } from "react";
import { populateCachedInstrumentDetails } from "@/app/admin/actions/populate-cached-instrument-details";

type InstrumentDetail = {
  ticker: string;
  name: string;
  exchange: string;
  currency: string;
  securityType: string;
  securityType2: string;
  isStock: boolean;
};

interface CachedInstrumentsTableProps {
  instruments: InstrumentDetail[];
}

export default function CachedInstrumentsTable({
  instruments,
}: CachedInstrumentsTableProps) {
  const [search, setSearch] = useState("");
  const [filterStocksOnly, setFilterStocksOnly] = useState(false);
  const [isPopulating, setIsPopulating] = useState(false);
  const [localInstruments, setLocalInstruments] = useState<InstrumentDetail[]>(
    Array.isArray(instruments) ? instruments.filter(Boolean) : [],
  );
  console.log("Render phase - localInstruments:", localInstruments.length);
  const fetchInstruments = async () => {
    try {
      setIsPopulating(true);
      const updatedInstruments = await populateCachedInstrumentDetails(
        [],
        true,
      );

      if (!updatedInstruments?.cache) {
        console.error("No cache data received");
        return;
      }

      const instrumentsArray = Array.from(updatedInstruments.cache.values())
        .filter(Boolean)
        .map((item) => ({
          ticker: item.ticker || "",
          name: item.name || "",
          exchange: item.exchange || "",
          currency: "USD",
          securityType: item.securityType || "",
          securityType2: item.securityType2 || "",
          isStock: Boolean(item.securityType === "Common Stock"),
        }));

      setLocalInstruments(instrumentsArray);
    } catch (error) {
      console.error("Error populating cache:", error);
    } finally {
      setIsPopulating(false);
    }
  };

  useEffect(() => {
    const shouldFetch = !isPopulating && localInstruments.length === 0;
    if (shouldFetch) {
      fetchInstruments();
    }
  }, [isPopulating]);

  const filteredInstruments = localInstruments
    .filter(Boolean)
    .filter((instrument) =>
      instrument.ticker?.toLowerCase().includes(search.toLowerCase() || ""),
    )
    .filter((instrument) => !filterStocksOnly || instrument.isStock);

  return (
    <div className="bg-card rounded-lg shadow">
      <div className="p-4 border-b">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search symbols..."
                className="w-full sm:w-64 px-4 py-2 border rounded-lg"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="form-checkbox h-5 w-5 text-primary"
                  checked={filterStocksOnly}
                  onChange={(e) => setFilterStocksOnly(e.target.checked)}
                />
                <span className="text-body">Stocks only</span>
              </label>
            </div>
          </div>
          <button
            onClick={fetchInstruments}
            disabled={isPopulating}
            className={`px-4 py-2 rounded-lg text-white ${
              isPopulating
                ? "bg-blue-400 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700"
            } transition-colors duration-200`}
          >
            {isPopulating ? (
              <span className="flex items-center gap-2">
                <svg
                  className="animate-spin h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Populating...
              </span>
            ) : (
              "Populate Cache"
            )}
          </button>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Symbol
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Exchange
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Currency
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Is Stock
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredInstruments.map((instrument) => (
              <tr key={instrument.ticker} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {instrument.ticker}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {instrument.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {instrument.exchange}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {instrument.currency}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {instrument.securityType}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      instrument.isStock
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {instrument.isStock ? "Yes" : "No"}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {filteredInstruments.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No instruments found
          </div>
        )}
      </div>
      <div className="px-6 py-4 border-t border-gray-200">
        <div className="text-sm text-gray-500">
          Total cached instruments: {localInstruments.length}
        </div>
      </div>
    </div>
  );
}
