// Initialize the cache as a Map if it doesn't exist
declare global {
  var __cachedSymbols: Map<string, CachedSymbol>;
}

if (!global.__cachedSymbols) {
  global.__cachedSymbols = new Map();
}

export interface CachedSymbol {
  ticker: string;
  name?: string;
  securityType?: string;
  securityType2?: string;
  figi?: string;
  exchange?: string;
  lastUpdated?: Date;
}

export function getCachedSymbol(symbol: string): CachedSymbol | undefined {
  return global.__cachedSymbols.get(symbol);
}

export function setCachedSymbol(symbol: string, data: CachedSymbol): void {
  global.__cachedSymbols.set(symbol, {
    ...data,
    lastUpdated: new Date(),
  });
}

export function getAllCachedSymbols(): Map<string, CachedSymbol> {
  return global.__cachedSymbols;
}

// Optional: Add a function to clear the cache if needed
export function clearSymbolCache(): void {
  global.__cachedSymbols.clear();
}

export interface Instrument {
  symbol: string | Instrument;
  isStock: boolean;
}

const SYMBOLS_KEY = "symbols";

class Instruments {
  private static instance: Instruments;

  private symbols: Instrument[];

  private constructor() {
    this.symbols = [];
  }

  public static getInstance(): Instruments {
    if (!Instruments.instance) {
      Instruments.instance = new Instruments();
    }
    return Instruments.instance;
  }

  public async getSymbols(): Promise<Instrument[]> {
    const storedSymbols = localStorage.getItem(SYMBOLS_KEY);
    if (storedSymbols) {
      return JSON.parse(storedSymbols);
    } else {
      // If SYMBOLS_KEY is not found, create an empty array
      localStorage.setItem(SYMBOLS_KEY, JSON.stringify([]));
      return this.symbols;
    }
  }

  public async setSymbols(symbols: Instrument[]): Promise<void> {
    this.symbols = symbols;
    localStorage.setItem(SYMBOLS_KEY, JSON.stringify(symbols));
  }

  public static createSymbol(symbol: Instrument): Instrument {
    return symbol;
  }
}

export default Instruments;
