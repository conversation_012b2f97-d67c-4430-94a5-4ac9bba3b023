import { Skeleton } from "@/components/ui/skeleton";
import { auth } from "../api/auth/[...nextauth]/auth";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { AutoOrderPrefillList } from "../portfolio/_components/auto-order-prefill-list";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertTriangle, Wifi, ShoppingCart } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default async function Orders() {
  const session = await auth();

  if (!session) {
    redirect("/auth");
  }

  return (
    <div className="space-y-4 py-4 p-4">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold tracking-tight">Orders</h1>
        <p className="text-muted-foreground">
          View and manage your trading orders and auto order prefills.
        </p>
      </div>

      <Alert className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800">
        <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        <AlertTitle className="text-amber-800 dark:text-amber-400">
          IBKR Connection Required
        </AlertTitle>
        <AlertDescription className="text-amber-700 dark:text-amber-300">
          Active orders will appear here when connected to your IBKR account.
          Currently displaying auto order prefills only.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            <span>Auto Order Prefills</span>
          </CardTitle>
          <CardDescription>
            These prefilled orders are ready to be submitted when you connect to
            your IBKR account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px]" />}>
            <AutoOrderPrefillList userId={session.user.id} />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="h-5 w-5" />
            <span>Active Orders</span>
          </CardTitle>
          <CardDescription>
            Connect to IBKR to view and manage your active orders.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="rounded-full bg-muted p-3 mb-4">
              <Wifi className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="font-medium mb-1">No Connection to IBKR</h3>
            <p className="text-sm text-muted-foreground max-w-md">
              Connect to your Interactive Brokers account to view your active
              orders. You can set up the connection in your profile settings.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
