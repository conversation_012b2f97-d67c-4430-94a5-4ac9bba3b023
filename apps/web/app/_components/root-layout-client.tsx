"use client";

import { Theme } from "@radix-ui/themes";
import { NextThemeProvider } from "@/providers/next-theme-provider";
import { NextAuthSessionProvider } from "@/providers/session-provider";
import Header from "@/components/header";
import { FooterNav } from "@/components/footer-nav";
import dynamic from "next/dynamic";
import React from "react";
import { useTheme } from "next-themes";

// Dynamically import Eruda with no SSR
const ErudaDebug = dynamic(
  () => import("./eruda-debug").then((mod) => mod.ErudaDebug),
  { ssr: false },
);

function ThemeWrapper({ children }: { children: React.ReactNode }) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  // Ensure component is mounted before accessing theme
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Use a fallback theme during SSR and initial hydration
  const currentTheme = mounted ? resolvedTheme : "light";

  return (
    <Theme
      appearance={currentTheme as "light" | "dark"}
      accentColor="mint"
      radius="large"
      scaling="110%"
      suppressHydrationWarning
    >
      {children}
    </Theme>
  );
}

export function RootLayoutClient({ children }: { children: React.ReactNode }) {
  return (
    <NextThemeProvider>
      <NextAuthSessionProvider>
        <ThemeWrapper>
          {process.env.NODE_ENV === "development" && (
            <React.Suspense fallback={null}>
              <ErudaDebug />
            </React.Suspense>
          )}
          <div className="flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <FooterNav />
          </div>
        </ThemeWrapper>
      </NextAuthSessionProvider>
    </NextThemeProvider>
  );
}
