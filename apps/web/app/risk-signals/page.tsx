import { Suspense } from "react";
import ErrorBoundary from "../../components/error-boundary";
import TrendChangeComponent from "./_components/trend-change";
import { auth } from "../api/auth/[...nextauth]/auth";
import { SpinnerBasicWithText } from "@/components/spinner-basic";
import { getUserProfile } from "@/db/user-profile";
import { getWatchlistItems } from "../watchlist/actions";
import { fetchHoldingsData } from "../portfolio/actions/get-holdings";

export default async function RiskSignalsPage() {
  const session = await auth();
  const [userProfile, watchlistItems, holdings] = await Promise.all([
    getUserProfile(session?.user?.id!),
    getWatchlistItems(session?.user?.id!),
    [],
  ]);

  console.log("Page Load - User Profile Settings:", {
    id: session?.user?.id,
    settings: userProfile?.settings?.riskSignalSettings,
  });

  return (
    <Suspense
      fallback={
        <SpinnerBasicWithText text="Loading risk signals...Please wait" />
      }
    >
      <ErrorBoundary>
        <TrendChangeComponent
          session={session}
          userProfile={userProfile}
          initialWatchlistItems={watchlistItems.map((item) => item.ticker)}
          initialHoldings={holdings || []}
        />
      </ErrorBoundary>
    </Suspense>
  );
}
