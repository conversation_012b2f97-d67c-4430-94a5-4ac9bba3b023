import { TrendChange } from "@/db/trend-change";
import React from "react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface StockPickSettingsProps {
  populateStockPicks: (
    trendChangeList: TrendChange[],
    entryWindowVariable: number,
  ) => Promise<TrendChange[]>;
  stockTrends: TrendChange[];
  entryWindow: string;
  setEntryWindow: React.Dispatch<React.SetStateAction<string>>;
  resultCount: number;
  setResultCount: React.Dispatch<React.SetStateAction<number>>;
  stockPicks: TrendChange[];
}

export default function StockPickSettings({
  populateStockPicks,
  stockTrends,
  entryWindow,
  setEntryWindow,
  resultCount,
  setResultCount,
  stockPicks,
}: StockPickSettingsProps) {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <h3 className="text-sm font-medium">Entry Window</h3>
        <div className="relative">
          <Input
            type="number"
            value={entryWindow}
            onChange={(e) => setEntryWindow(e.target.value)}
            min={0}
            max={100}
            className="pr-8"
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
            %
          </span>
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <h3 className="text-sm font-medium">Results</h3>
        <Badge variant="secondary">
          {resultCount === -1 ? "calculating..." : `${resultCount} matches`}
        </Badge>
      </div>
    </div>
  );
}
