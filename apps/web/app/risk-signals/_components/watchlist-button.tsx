"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Star } from "lucide-react";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

interface WatchlistButtonProps {
  symbol: string;
  isInWatchlist: boolean;
  onToggleWatchlist: (symbol: string) => Promise<void>;
}

export default function WatchlistButton({
  symbol,
  isInWatchlist,
  onToggleWatchlist,
}: WatchlistButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleToggleWatchlist = async () => {
    try {
      setIsLoading(true);
      await onToggleWatchlist(symbol);
    } catch (error) {
      toast({
        title:
          error instanceof Error ? error.message : "Failed to update watchlist",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      className="h-8 w-8 p-0"
      onClick={handleToggleWatchlist}
      disabled={isLoading}
    >
      <Star
        className={`h-4 w-4 ${
          isInWatchlist
            ? "fill-yellow-400 text-yellow-400"
            : "fill-none text-muted-foreground hover:text-foreground"
        }`}
      />
    </Button>
  );
}
