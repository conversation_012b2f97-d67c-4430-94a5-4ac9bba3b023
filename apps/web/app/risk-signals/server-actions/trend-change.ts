import prismadb from "@/lib/prisma";
// Add pagination support to getTrendChangesByDateOrMostRecent
export async function getTrendChangesByDateOrMostRecent(
  date: Date,
  page: number = 1,
  pageSize: number = 20,
): Promise<string> {
  try {
    const skip = (page - 1) * pageSize;

    // First try to get trend changes for the specific date
    let trendChanges = await prismadb.trendChange.findMany({
      where: {
        date: {
          gte: new Date(date.setHours(0, 0, 0, 0)),
          lt: new Date(date.setHours(23, 59, 59, 999)),
        },
      },
      skip,
      take: pageSize,
      orderBy: {
        index: "asc",
      },
    });

    // If no trend changes found for the date, get the most recent ones
    if (trendChanges.length === 0) {
      const mostRecentDate = await prismadb.trendChange.findFirst({
        select: {
          date: true,
        },
        orderBy: {
          date: "desc",
        },
      });

      if (mostRecentDate) {
        trendChanges = await prismadb.trendChange.findMany({
          where: {
            date: mostRecentDate.date,
          },
          skip,
          take: pageSize,
          orderBy: {
            index: "asc",
          },
        });
      }
    }

    // Convert BigInt to string for JSON serialization
    const serializedTrendChanges = trendChanges.map((tc) => ({
      ...tc,
      buyTrade: Number(tc.buyTrade),
      sellTrade: Number(tc.sellTrade),
      previousClose: Number(tc.previousClose),
    }));

    return JSON.stringify(serializedTrendChanges);
  } catch (error) {
    console.error("Error in getTrendChangesByDateOrMostRecent:", error);
    throw error;
  }
}
