"use server";

export default async function GetOptionChains(
  index: string,
  strikePrice: number,
) {
  const url: string = `${process.env.IB_API_URL}/option-chain`;
  try {
    const fetchResponse = await fetch(url, {
      method: "POST",
      body: JSON.stringify({
        localSymbol: index,
        exchange: "SMART",
        strikePrice: strikePrice,
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });
    const fetchData = await fetchResponse.json();
    return JSON.stringify(fetchData);
  } catch (error) {
    return JSON.stringify({ errorMessage: error });
  }
}
