"use server";

interface OpenFIGI {
  idType: string;
  idValue: string;
}

async function FetchOpenFIGIBatch(newSymbols: OpenFIGI[]) {
  try {
    const response = await fetch(`https://api.openfigi.com/v3/mapping`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-OPENFIGI-APIKEY": "563d9fd2-f93d-4d39-998d-5fbf54baae06",
      },
      body: JSON.stringify(newSymbols),
    });
    const fetchData = await response.json();
    return JSON.stringify(fetchData);
  } catch (error) {
    return JSON.stringify({ errorMessage: error });
  }
}

async function FetchOpenFIGISingle(symbol: OpenFIGI) {
  try {
    const response = await fetch(`https://api.openfigi.com/v3/mapping`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-OPENFIGI-APIKEY": "563d9fd2-f93d-4d39-998d-5fbf54baae06",
      },
      body: JSON.stringify([symbol]),
    });
    const fetchData = await response.json();
    return JSON.stringify(fetchData);
  } catch (error) {
    return JSON.stringify({ errorMessage: error });
  }
}

export { FetchOpenFIGIBatch, FetchOpenFIGISingle };
