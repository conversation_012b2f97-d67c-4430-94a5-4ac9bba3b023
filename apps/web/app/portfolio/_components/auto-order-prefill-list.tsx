"use client";

import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Loader2,
  DollarSign,
  Calendar,
  ArrowUpDown,
  ListFilter,
} from "lucide-react";
import {
  fetchAutoOrderPrefill,
  type AutoOrderPrefillWithDefaults,
} from "../actions/fetch-auto-order-prefill";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Separator } from "@/components/ui/separator";

interface AutoOrderPrefillListProps {
  userId: string;
}

export function AutoOrderPrefillList({ userId }: AutoOrderPrefillListProps) {
  const [prefills, setPrefills] = useState<AutoOrderPrefillWithDefaults[]>([]);
  const [loading, setLoading] = useState(true);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  useEffect(() => {
    const loadPrefills = async () => {
      try {
        const data = await fetchAutoOrderPrefill(userId);
        setPrefills(data);
      } catch (error) {
        console.error("Failed to load auto order prefills:", error);
      } finally {
        setLoading(false);
      }
    };

    loadPrefills();
  }, [userId]);

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Card>
    );
  }

  if (!prefills.length) {
    return (
      <Card className="p-6">
        <div className="text-center py-6 text-muted-foreground">
          <ListFilter className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="font-medium mb-1">No Auto Order Prefills</h3>
          <p className="text-sm">
            Enable auto order prefill in your account settings to use this
            feature
          </p>
        </div>
      </Card>
    );
  }

  return isDesktop ? (
    <Card>
      <ScrollArea className="h-[calc(100vh-400px)]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Symbol</TableHead>
              <TableHead>Action</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {prefills.map((prefill) => (
              <TableRow key={prefill.id}>
                <TableCell className="font-medium">{prefill.ticker}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      prefill.action === "BUY" ? "default" : "destructive"
                    }
                  >
                    {prefill.action}
                  </Badge>
                </TableCell>
                <TableCell>{prefill.quantity}</TableCell>
                <TableCell>${prefill.price.toFixed(2)}</TableCell>
                <TableCell>
                  <Badge variant={prefill.deleted_at ? "secondary" : "success"}>
                    {prefill.deleted_at ? "Deleted" : "Pending"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(prefill.created_at).toLocaleDateString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
    </Card>
  ) : (
    <ScrollArea className="h-[calc(100vh-400px)]">
      <div className="space-y-4 p-4">
        {prefills.map((prefill) => (
          <Card key={prefill.id} className="p-4">
            {/* Header - Symbol and Action */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <span className="text-lg font-semibold">{prefill.ticker}</span>
                <Badge
                  variant={prefill.action === "BUY" ? "default" : "destructive"}
                >
                  {prefill.action}
                </Badge>
              </div>
              <Badge variant={prefill.deleted_at ? "secondary" : "success"}>
                {prefill.deleted_at ? "Deleted" : "Pending"}
              </Badge>
            </div>

            {/* Order Details Grid */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <ArrowUpDown className="h-3 w-3" /> Quantity
                </div>
                <div className="font-medium">{prefill.quantity}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <DollarSign className="h-3 w-3" /> Price
                </div>
                <div className="font-medium">${prefill.price.toFixed(2)}</div>
              </div>
            </div>

            <Separator className="my-4" />

            {/* Created Date */}
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="h-3 w-3" />
              <span>
                Created: {new Date(prefill.created_at).toLocaleDateString()}
              </span>
            </div>
          </Card>
        ))}
      </div>
    </ScrollArea>
  );
}
