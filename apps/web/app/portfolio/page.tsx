import { redirect } from "next/navigation";
import { auth } from "../api/auth/[...nextauth]/auth";
import PortfolioPageComponent from "./_components/portfolio-page";
import { fetchPortfolioAllData } from "./actions/get-portfolio-all-data";
import { AlertTriangle, Wifi } from "lucide-react";

export default async function PortfolioPageServer() {
  const session = await auth();

  if (!session) {
    redirect("/auth");
  }

  const [portfolioDataArray] = await Promise.all([
    fetchPortfolioAllData(session.user.id),
  ]);

  if (!portfolioDataArray || portfolioDataArray.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full space-y-6 text-center border border-gray-200">
          <div className="flex justify-center space-x-4">
            <AlertTriangle className="h-12 w-12 text-yellow-500" />
            <Wifi className="h-12 w-12 text-gray-400" />
          </div>
          <h2 className="text-2xl font-semibold text-gray-800">
            Unable to Load Portfolio Data
          </h2>
          <p className="text-gray-600">
            We're having trouble connecting to the server. This might be due to:
          </p>
          <ul className="text-gray-600 text-left list-disc list-inside">
            <li>A temporary connection issue</li>
            <li>Server maintenance</li>
            <li>No portfolio items created yet</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <PortfolioPageComponent
      session={session}
      portfolioData={portfolioDataArray}
    />
  );
}
