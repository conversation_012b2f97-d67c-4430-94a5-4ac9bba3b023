"use server";

import { IBApiNext } from "@stoqey/ib";
import { getUserProfile } from "@/db/user-profile";

export async function cancelOrder(
  userId: string,
  orderId: number,
  clientId: number,
): Promise<boolean> {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }
    const ibApiNext = new IBApiNext({
      host: userProfile.settings.ibkrConnectionDetail.host,
      port: userProfile.settings.ibkrConnectionDetail.port,
    });

    return new Promise((resolve, reject) => {
      try {
        ibApiNext.connect(clientId);
        ibApiNext.cancelOrder(orderId);

        // Wait for order cancellation to be processed
        setTimeout(() => {
          if (ibApiNext.isConnected) {
            ibApiNext.disconnect();
          }
          resolve(true);
        }, 2000);
      } catch (error) {
        if (ibApiNext.isConnected) {
          ibApiNext.disconnect();
        }
        reject(error);
      }
    });
  } catch (error) {
    console.error("Error canceling order:", error);
    return false;
  }
}

export async function cancelAllOrders(userId: string): Promise<boolean> {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }
    const ibApiNext = new IBApiNext({
      host: userProfile.settings.ibkrConnectionDetail.host,
      port: userProfile.settings.ibkrConnectionDetail.port,
    });

    return new Promise((resolve, reject) => {
      try {
        ibApiNext.connect();
        ibApiNext.cancelAllOrders();

        // Wait for order cancellation to be processed
        setTimeout(() => {
          if (ibApiNext.isConnected) {
            ibApiNext.disconnect();
          }
          resolve(true);
        }, 2000);
      } catch (error) {
        if (ibApiNext.isConnected) {
          ibApiNext.disconnect();
        }
        reject(error);
      }
    });
  } catch (error) {
    console.error("Error canceling all orders:", error);
    return false;
  }
}
