"use server";

import {
  IBApiNext,
  Contract,
  Order,
  OrderAction,
  OrderType,
  SecType,
} from "@stoqey/ib";
import { getUserProfile } from "@/db/user-profile";

export async function placeOrder(
  userId: string,
  symbol: string,
  action: "BUY" | "SELL",
  quantity: number,
  price: number,
  selectedAccountId: string,
): Promise<boolean> {
  console.log("placeOrder(): -> ", { userId, symbol, action, quantity, price });
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }
    console.log("User profile retrieved:", userProfile);

    const ibApiNext = new IBApiNext({
      host: userProfile.settings.ibkrConnectionDetail.host,
      port: userProfile.settings.ibkrConnectionDetail.port,
    });

    return new Promise(async (resolve, reject) => {
      try {
        ibApiNext.connect(
          userProfile.settings.ibkrConnectionDetail!.clientId ?? undefined,
        );

        const orderId = await ibApiNext.getNextValidOrderId();
        console.log("Next valid order ID:", orderId);

        const contract: Contract = {
          symbol,
          exchange: "SMART",
          currency: "USD",
          secType: SecType.STK,
        };

        const order: Order = {
          orderId,
          action: action as OrderAction,
          totalQuantity: quantity,
          orderType: OrderType.LMT,
          lmtPrice: price,
          account: selectedAccountId,
          transmit: true,
        };

        // console.log("Placing order with details:", { contract, order });
        ibApiNext.placeOrder(orderId, contract, order);
        console.log("Order placed successfully");
        resolve(true);
      } catch (error) {
        console.error("Error placing order:", error);
        reject(error);
      } finally {
        ibApiNext.disconnect();
        console.log("Disconnected from IB API");
      }
    });
  } catch (error) {
    console.error("Error placing order:", error);
    return false;
  }
}
