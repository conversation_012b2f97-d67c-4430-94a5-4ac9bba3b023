"use server";

import { getUserProfile } from "@/db/user-profile";
import prismaDb from "@/lib/prisma";
import { AutoOrderPrefillWithMarketPrice } from "@/types/trend-change";

/**
 * Server Action: Creates auto-order prefill records
 *
 * This action:
 * 1. Validates user has auto-order enabled
 * 2. Creates prefill records for both buy and sell orders
 * 3. Associates orders with user's default account
 *
 * @param userId - User ID for authentication and settings
 * @param buyList - List of symbols to buy with market prices
 * @param sellList - List of symbols to sell with market prices
 */
export async function createAutoOrderPrefill(
  userId: string,
  buyList: AutoOrderPrefillWithMarketPrice[],
  sellList: AutoOrderPrefillWithMarketPrice[],
) {
  if (!userId) {
    throw new Error("User ID is required");
  }

  try {
    // First check if auto order prefill is enabled for the user
    const userProfile = await getUserProfile(userId);
    const isEnabled = userProfile?.settings?.enableAutoOrderPrefill ?? false;
    const defaultAccountId = userProfile?.settings?.defaultIBAccountId;

    // If not enabled, return empty array with proper typing
    if (!isEnabled || !defaultAccountId) {
      return [];
    }

    const buyAutoOrderPrefill = buyList.map((change) => ({
      user_id: userId,
      account_id: defaultAccountId,
      order_type: "MARKET",
      action: "BUY",
      ticker: change.symbol,
      quantity: 1,
      price: change.marketQuote.price,
    }));

    const sellAutoOrderPrefill = sellList.map((change) => ({
      user_id: userId,
      account_id: defaultAccountId,
      order_type: "MARKET",
      action: "SELL",
      ticker: change.symbol,
      quantity: 1,
      price: change.marketQuote.price,
    }));

    const autoOrderPrefill = [...buyAutoOrderPrefill, ...sellAutoOrderPrefill];

    const result = await prismaDb.autoOrderPrefill.createMany({
      data: autoOrderPrefill,
    });

    return result;
  } catch (error) {
    console.error("[CREATE_AUTO_ORDER_PREFILL_ERROR]", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to create auto order prefill record",
    );
  }
}
