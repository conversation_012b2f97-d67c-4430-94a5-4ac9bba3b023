"use server";

import { IBApiNext } from "@stoqey/ib";
import { getUserProfile } from "@/db/user-profile";

export type Account = {
  accountId: string;
  accountCode: string;
  name?: string;
};

export async function getAllAccounts(userId: string): Promise<Account[]> {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.settings?.ibkrConnectionDetail) {
      throw new Error("IBKR connection details not found");
    }

    const ibApiNext = new IBApiNext({
      host: userProfile.settings.ibkrConnectionDetail.host,
      port: userProfile.settings.ibkrConnectionDetail.port,
    });

    await ibApiNext.connect(
      userProfile.settings.ibkrConnectionDetail!.clientId ?? undefined,
    );
    console.log("Connected to IB API");

    const accountIds = await ibApiNext.getManagedAccounts();
    const accounts: Account[] = accountIds
      .filter((accountId) => accountId && accountId.trim() !== "")
      .map((accountId) => ({
        accountId,
        accountCode: accountId,
      }));

    console.log(accounts);
    ibApiNext.disconnect();
    console.log("Disconnected from IB API");

    return accounts;
  } catch (error) {
    console.error("Error fetching all accounts:", error);
    return [];
  }
}
