"use server";

import { IBApiNext } from "@stoqey/ib";
import { getUserProfile } from "@/db/user-profile";

export interface Order {
  orderId: number;
  orderType: string;
  permId: number;
  symbol: string;
  action: "BUY" | "SELL";
  quantity: number;
  price: number;
  status: "Submitted" | "Filled" | "Cancelled" | "Error";
  timestamp: Date;
  clientId: number;
  accountId: string; // Add this field
}

export async function getAllOrders(userId: string): Promise<Order[] | []> {
  const userProfile = await getUserProfile(userId);
  if (!userProfile?.settings?.ibkrConnectionDetail) {
    throw new Error("IBKR connection details not found");
  }
  const ibApiNext = new IBApiNext({
    host: userProfile.settings.ibkrConnectionDetail.host,
    port: userProfile.settings.ibkrConnectionDetail.port,
  });
  try {
    ibApiNext.connect(
      userProfile.settings.ibkrConnectionDetail!.clientId ?? undefined,
    );
    const orders = await ibApiNext.getAllOpenOrders();
    // console.log("getAllOrders() -> orders", orders);
    const formattedOrders: Order[] = (orders as any[]).map((order: any) => ({
      orderId: order.order.orderId,
      orderType: order.order.orderType,
      permId: order.order.permId,
      symbol: order.contract.symbol,
      action: order.order.action,
      quantity: order.order.totalQuantity,
      price: order.order.lmtPrice || order.order.auxPrice,
      status: order.orderState.status,
      timestamp: new Date(order.orderState.timestamp || Date.now()),
      clientId: order.order.clientId,
      accountId: order.order.account, // Assuming orders contain account information
    }));

    // Group orders by account
    // TODO: remove this if not needed
    const groupedOrders: { [account: string]: Order[] } = {};
    formattedOrders.forEach((order) => {
      if (!groupedOrders[order.accountId]) {
        groupedOrders[order.accountId] = [];
      }
      groupedOrders[order.accountId].push(order);
    });

    return formattedOrders;
  } catch (error) {
    console.error("Error fetching all orders:", error);
    return [];
  } finally {
    ibApiNext.disconnect();
  }
}
