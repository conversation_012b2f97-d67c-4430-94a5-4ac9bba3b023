"use server";

import { I<PERSON>pi, EventName } from "@stoqey/ib";
import { createIBApiConnection } from "@/app/actions/ibapi/connection";

type PortfolioData = {
  totalValue: number;
  dayChange: number;
  dayChangePercent: number;
  totalReturn: number;
  totalReturnPercent: number;
  cashBalance: number;
  cashBalancePercent: number;
};

export async function fetchPortfolioData(
  userId: string,
): Promise<PortfolioData | null> {
  try {
    const ib = await createIBApiConnection(userId);
    let portfolioData: PortfolioData = {
      totalValue: 0,
      dayChange: 0,
      dayChangePercent: 0,
      totalReturn: 0,
      totalReturnPercent: 0,
      cashBalance: 0,
      cashBalancePercent: 0,
    };

    const accountSummaryPromise = new Promise<PortfolioData>(
      (resolve, reject) => {
        ib.on(EventName.error, (err, code, reqId) => {
          console.error(
            `Error: ${err.message} - code: ${code} - reqId: ${reqId}`,
          );
          reject(err);
        });

        ib.on(
          EventName.accountSummary,
          (reqId, account, tag, value, currency) => {
            switch (tag) {
              case "NetLiquidation":
                portfolioData.totalValue = parseFloat(value);
                break;
              case "AvailableFunds":
                portfolioData.cashBalance = parseFloat(value);
                break;
              case "UnrealizedPnL":
                portfolioData.totalReturn = parseFloat(value);
                break;
              case "PctChange":
                portfolioData.dayChangePercent = parseFloat(value);
                break;
              case "DayTradesRemaining":
                portfolioData.dayChange = parseFloat(value);
                break;
            }
          },
        );

        ib.on(EventName.accountSummaryEnd, (reqId) => {
          portfolioData.totalReturnPercent =
            (portfolioData.totalReturn / portfolioData.totalValue) * 100;
          portfolioData.cashBalancePercent =
            (portfolioData.cashBalance / portfolioData.totalValue) * 100;
          ib.disconnect();
          resolve(portfolioData);
        });

        ib.connect();
        ib.reqAccountSummary(
          1,
          "All",
          "NetLiquidation,AvailableFunds,UnrealizedPnL,PctChange,DayTradesRemaining",
        );
      },
    );

    return await accountSummaryPromise;
  } catch (error) {
    console.error("Error fetching portfolio data:", error);
    return null;
  }
}
