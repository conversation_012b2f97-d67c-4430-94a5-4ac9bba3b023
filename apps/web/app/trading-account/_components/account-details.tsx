"use client";

import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useMediaQuery } from "@/hooks/use-media-query";
import { getAccountTransactions } from "../actions/get-account-transactions";
import { useEffect, useState } from "react";
import { AccountTransaction } from "../actions/get-account-transactions";
import { IBConnectionError } from "@/app/utils/ibkr-errors";
import { AlertCircle, ArrowLeft, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Link from "next/link";

interface AccountDetailsProps {
  accountId: string;
  userId: string;
}

const AccountDetails = ({ accountId, userId }: AccountDetailsProps) => {
  const [transactions, setTransactions] = useState<AccountTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getAccountTransactions(accountId, userId);
        setTransactions(data);
      } catch (err) {
        let errorMessage = "Failed to fetch transactions";
        if (err instanceof IBConnectionError) {
          errorMessage = `IB Connection Error: ${err.message}`;
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [accountId, userId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No transactions found for this account.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Link
              href="/portfolio"
              className="flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Portfolio
            </Link>
          </div>
          <h2 className="text-2xl font-bold">Transaction History</h2>
        </div>
      </div>

      {isDesktop ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.map((transaction) => (
              <TableRow key={transaction.id}>
                <TableCell>
                  {new Date(transaction.date).toLocaleDateString()}
                </TableCell>
                <TableCell className="capitalize">{transaction.type}</TableCell>
                <TableCell>
                  <span
                    className={
                      transaction.type === "withdrawal"
                        ? "text-red-500"
                        : "text-green-500"
                    }
                  >
                    {transaction.type === "withdrawal" ? "-" : "+"}$
                    {transaction.amount.toFixed(2)}
                  </span>
                </TableCell>
                <TableCell>{transaction.currency}</TableCell>
                <TableCell className="capitalize">
                  {transaction.status}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <Card key={transaction.id} className="p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="capitalize font-medium">
                  {transaction.type}
                </span>
                <span
                  className={`font-bold ${
                    transaction.type === "withdrawal"
                      ? "text-red-500"
                      : "text-green-500"
                  }`}
                >
                  {transaction.type === "withdrawal" ? "-" : "+"}$
                  {transaction.amount.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{new Date(transaction.date).toLocaleDateString()}</span>
                <span>{transaction.currency}</span>
                <span className="capitalize">{transaction.status}</span>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AccountDetails;
