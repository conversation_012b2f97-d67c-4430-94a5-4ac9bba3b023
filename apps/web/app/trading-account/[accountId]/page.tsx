import { redirect } from "next/navigation";
import AccountDetails from "../_components/account-details";
import { auth } from "@/app/api/auth/[...nextauth]/auth";
import { Card } from "@/components/ui/card";

type Props = {
  params: Promise<{ accountId: string }>;
};

export default async function TradingAccountPage({ params }: Props) {
  const { accountId } = await params;
  const session = await auth();
  if (!session) {
    return redirect("/");
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Trading Account</h1>
          <div className="text-sm text-muted-foreground">
            Account ID: {accountId}
          </div>
        </div>

        <Card className="p-6">
          <AccountDetails accountId={accountId} userId={session.user.id} />
        </Card>
      </div>
    </div>
  );
}
