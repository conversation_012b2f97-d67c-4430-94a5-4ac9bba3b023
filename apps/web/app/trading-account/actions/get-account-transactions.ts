"use server";

import { createIBApiConnection } from "@/app/actions/ibapi/connection";
import { EventName } from "@stoqey/ib";
import { withIBKRTimeout } from "@/app/utils/ibkr-utils";
import { IBConnectionError } from "@/app/utils/ibkr-errors";

export type AccountTransaction = {
  id: string;
  type: "deposit" | "withdrawal";
  amount: number;
  date: string;
  status: "completed" | "pending" | "failed";
  currency: string;
};

export async function getAccountTransactions(
  accountId: string,
  userId: string,
): Promise<AccountTransaction[]> {
  const executeTransaction = async () => {
    return new Promise<AccountTransaction[]>(async (resolve, reject) => {
      const transactions: AccountTransaction[] = [];
      let initialBalance = 0;

      try {
        const ib = await createIBApiConnection(userId);

        // Handle connection
        ib.once(EventName.connected, () => {
          ib.reqAccountUpdates(true, accountId);
        });

        // Handle errors
        ib.on(EventName.error, (error, code, reqId) => {
          ib.disconnect();
          reject(
            new IBConnectionError(`IB API Error ${code}: ${error.message}`),
          );
        });

        // Handle account updates
        ib.on(EventName.updateAccountValue, (key, value, currency, account) => {
          if (account !== accountId) return;

          // Log all keys to debug
          console.log(
            `Received key: ${key}, value: ${value}, currency: ${currency}`,
          );

          // Check for various cash-related keys
          switch (key) {
            case "CashBalance":
            case "AvailableFunds":
            case "NetLiquidation":
            case "TotalCashValue":
              const currentValue = parseFloat(value);

              // If we have an initial balance, we can detect transactions
              if (initialBalance && currentValue !== initialBalance) {
                const amount = currentValue - initialBalance;
                transactions.push({
                  id: `${Date.now()}-${transactions.length}`,
                  type: amount >= 0 ? "deposit" : "withdrawal",
                  amount: Math.abs(amount),
                  date: new Date().toISOString(),
                  status: "completed",
                  currency: currency || "USD",
                });
              }

              // Update initial balance if not set
              if (!initialBalance) {
                initialBalance = currentValue;
              }
              break;

            case "Deposits":
            case "Payment":
            case "Transfer":
              const transferAmount = parseFloat(value);
              if (!isNaN(transferAmount) && transferAmount !== 0) {
                transactions.push({
                  id: `${Date.now()}-${transactions.length}`,
                  type: transferAmount >= 0 ? "deposit" : "withdrawal",
                  amount: Math.abs(transferAmount),
                  date: new Date().toISOString(),
                  status: "completed",
                  currency: currency || "USD",
                });
              }
              break;
          }
        });

        // Handle completion
        ib.on(EventName.accountDownloadEnd, (account) => {
          if (account === accountId) {
            ib.disconnect();

            // Remove duplicates based on amount and type
            const uniqueTransactions = transactions.filter(
              (transaction, index, self) =>
                index ===
                self.findIndex(
                  (t) =>
                    t.amount === transaction.amount &&
                    t.type === transaction.type &&
                    Math.abs(
                      new Date(t.date).getTime() -
                        new Date(transaction.date).getTime(),
                    ) < 1000,
                ),
            );

            resolve(uniqueTransactions);
          }
        });

        // Connect to IB
        ib.connect();
      } catch (error) {
        reject(
          new IBConnectionError(
            error instanceof Error ? error.message : "Unknown error occurred",
          ),
        );
      }
    });
  };

  return withIBKRTimeout(
    executeTransaction(),
    "Get Account Transactions",
    10000,
  );
}
