import { getUserProfile } from "@/db/user-profile";
import {
  AutoOrderPrefillTrendChange,
  AutoOrderPrefillWithMarketPrice,
} from "@/types/trend-change";
import { fetchQuote } from "@/utils/yahoo-finance/fetch-quote";
import { createAutoOrderPrefill } from "../portfolio/actions/create-auto-order-prefill";

/**
 * Service class handling order-related operations
 */
class OrderService {
  /**
   * Initiates an automatic order through the API endpoint
   * This is called when risk signals change and auto-order is enabled
   * @param changes - Array of trend changes that trigger orders
   * @param userId - User ID for authentication
   */
  public static async autoOrder(changes: any, userId: string): Promise<void> {
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/order/auto-order`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": process.env.INTERNAL_API_KEY ?? "", // Internal API key for server-to-server auth
      },
      body: JSON.stringify({ changes, userId }),
    });
  }
}

/**
 * Creates auto-order prefills based on trend changes
 * This function is called when new risk signals are detected
 *
 * Flow:
 * 1. Checks if auto-order prefill is enabled for user
 * 2. Fetches current market prices for all symbols
 * 3. Creates prefill records in database
 *
 * @param buyList - List of symbols with bullish signals
 * @param sellList - List of symbols with bearish signals
 * @param userId - User ID for authentication and settings
 */
export async function autoOrderPrefill(
  buyList: AutoOrderPrefillTrendChange[],
  sellList: AutoOrderPrefillTrendChange[],
  userId: string,
): Promise<string> {
  // Check user settings and permissions
  const userProfile = await getUserProfile(userId);
  const isEnabled = userProfile?.settings?.enableAutoOrderPrefill ?? false;
  const defaultIBAccountId = userProfile?.settings?.defaultIBAccountId ?? "";

  if (!isEnabled || !defaultIBAccountId) {
    return JSON.stringify({
      status: "error",
      message: "Auto-order prefill is not enabled",
    });
  }

  // Fetch current market prices for buy signals
  const buyListWithMarketPrice: AutoOrderPrefillWithMarketPrice[] =
    await Promise.all(
      buyList.map(async (change) => ({
        ...change,
        marketQuote: await fetchQuote(change.symbol),
      })) as unknown as AutoOrderPrefillWithMarketPrice[],
    );

  // Fetch current market prices for sell signals
  const sellListWithMarketPrice: AutoOrderPrefillWithMarketPrice[] =
    await Promise.all(
      sellList.map(async (change) => ({
        ...change,
        marketQuote: await fetchQuote(change.symbol),
      })) as unknown as AutoOrderPrefillWithMarketPrice[],
    );

  // Create prefill records in database
  const result = await createAutoOrderPrefill(
    userId,
    buyListWithMarketPrice,
    sellListWithMarketPrice,
  );

  return JSON.stringify({
    status: "success",
    message: "Auto-order prefill is enabled",
  });
}

export default OrderService;
