"use client";

import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { AddStockDialog } from "./AddStockDialog";

export default function WatchlistHeader({ userId }: { userId: string }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold">Watchlist</h1>
        <p className="text-sm text-muted-foreground">
          Track your favorite stocks
        </p>
      </div>

      <Button
        onClick={() => setIsDialogOpen(true)}
        className="w-full sm:w-auto"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Stock
      </Button>

      <AddStockDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        userId={userId}
      />
    </div>
  );
}
