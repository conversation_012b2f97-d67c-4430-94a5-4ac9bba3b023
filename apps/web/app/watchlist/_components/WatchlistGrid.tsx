"use client";

import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { RemoveStockDialog } from "./RemoveStockDialog";
import { getStockQuotes } from "../actions";
import { HoldingsIndicator } from "@/app/_components/holdings-indicator";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";

interface WatchlistGridProps {
  items: any[];
  userId: string;
  holdings: PortfolioPosition[] | null;
}

export default function WatchlistGrid({
  items,
  userId,
  holdings,
}: WatchlistGridProps) {
  const [stockData, setStockData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [stockToRemove, setStockToRemove] = useState<{
    id: string;
    ticker: string;
  } | null>(null);

  useEffect(() => {
    const fetchStockData = async () => {
      try {
        const tickers = items.map((item) => item.ticker);
        const quotes = await getStockQuotes(tickers);
        setStockData(quotes);
      } catch (error) {
        console.error("Error fetching stock data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (items.length > 0) {
      fetchStockData();
    } else {
      setLoading(false);
    }
  }, [items]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-4">
            <Skeleton className="h-4 w-20 mb-3" />
            <Skeleton className="h-6 w-24 mb-2" />
            <Skeleton className="h-4 w-full" />
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      {items.map((item) => {
        const quote = stockData[item.ticker];
        const priceChange = quote?.regularMarketChange || 0;
        const priceChangePercent = quote?.regularMarketChangePercent || 0;

        function formatCurrency(value: number): string {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
          }).format(value);
        }

        return (
          <Card
            key={item.id}
            className="p-4 hover:shadow-lg transition-shadow relative"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <h3 className="text-base font-semibold">{item.ticker}</h3>
                <HoldingsIndicator symbol={item.ticker} holdings={holdings} />
              </div>
              <span className="text-xs text-muted-foreground truncate ml-2 max-w-[150px]">
                {quote?.shortName}
              </span>
            </div>

            <div className="space-y-1.5">
              <div className="text-size-xl font-weight-bold">
                {formatCurrency(quote?.regularMarketPrice || 0)}
              </div>

              <div
                className={`text-size-xs ${
                  priceChange >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                <span className="inline-flex items-center gap-1">
                  <span>
                    {priceChange >= 0 ? "+" : ""}
                    {formatCurrency(priceChange)}
                  </span>
                  <span>({priceChangePercent.toFixed(2)}%)</span>
                </span>
              </div>
            </div>

            <div className="absolute bottom-3 right-3">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-muted-foreground hover:text-destructive transition-colors"
                onClick={() =>
                  setStockToRemove({ id: item.id, ticker: item.ticker })
                }
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </Card>
        );
      })}

      {stockToRemove && (
        <RemoveStockDialog
          open={!!stockToRemove}
          onOpenChange={(open) => !open && setStockToRemove(null)}
          stockId={stockToRemove.id}
          ticker={stockToRemove.ticker}
          userId={userId}
        />
      )}
    </div>
  );
}
