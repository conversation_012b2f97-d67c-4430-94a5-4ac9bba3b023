"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { addToWatchlist } from "@/app/watchlist/actions";
import { useToast } from "@/hooks/use-toast";

interface AddStockDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
}

export function AddStockDialog({
  open,
  onOpenChange,
  userId,
}: AddStockDialogProps) {
  const [ticker, setTicker] = useState("");
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await addToWatchlist(ticker.toUpperCase(), userId);
      onOpenChange(false);
      setTicker("");
      toast({
        title: "Success",
        description: `${ticker.toUpperCase()} has been added to your watchlist`,
      });
    } catch (error) {
      // More specific error messages based on the error
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to add stock to watchlist";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Stock to Watchlist</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            placeholder="Enter stock ticker (e.g., AAPL)"
            value={ticker}
            onChange={(e) => setTicker(e.target.value.trim())}
            className="uppercase"
            minLength={1}
            maxLength={5}
            required
          />

          <Button
            type="submit"
            className="w-full"
            disabled={!ticker.trim() || loading}
          >
            {loading ? "Adding..." : "Add to Watchlist"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
