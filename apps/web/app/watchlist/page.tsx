import { redirect } from "next/navigation";
import { getWatchlistItems } from "./actions";
import { auth } from "../api/auth/[...nextauth]/auth";
import WatchlistHeader from "./_components/WatchlistHeader";
import WatchlistGrid from "./_components/WatchlistGrid";
import { EmptyWatchlist } from "./_components/EmptyWatchlist";
import { fetchHoldingsData } from "../portfolio/actions/get-holdings";

export default async function WatchlistPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/api/auth/signin");
  }

  const [watchlistItems, holdings] = await Promise.all([
    getWatchlistItems(session.user.id),
    fetchHoldingsData(session.user.id),
  ]);

  // Sort watchlist items - items with holdings go to the top
  const sortedWatchlistItems = [...watchlistItems].sort((a, b) => {
    const aHasHoldings = holdings?.some((h) => h.symbol === a.ticker);
    const bHasHoldings = holdings?.some((h) => h.symbol === b.ticker);

    if (aHasHoldings && !bHasHoldings) return -1;
    if (!aHasHoldings && bHasHoldings) return 1;
    return 0;
  });

  return (
    <div className="container max-w-7xl mx-auto px-4 py-4 sm:py-6 md:py-8">
      <WatchlistHeader userId={session.user.id} />

      {sortedWatchlistItems.length === 0 ? (
        <EmptyWatchlist />
      ) : (
        <WatchlistGrid
          items={sortedWatchlistItems}
          userId={session.user.id}
          holdings={holdings}
        />
      )}
    </div>
  );
}
