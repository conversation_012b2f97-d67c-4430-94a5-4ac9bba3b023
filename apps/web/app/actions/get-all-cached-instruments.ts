"use server";

import { unstable_cache } from "next/cache";

// This type should match your InstrumentDetail type
type InstrumentDetail = {
  symbol: string;
  name: string;
  exchange: string;
  currency: string;
  type: string;
  isStock: boolean;
};

export const getAllCachedInstruments = unstable_cache(
  async (): Promise<InstrumentDetail[]> => {
    // Since Next.js doesn't provide direct cache access, we'll need to maintain a list of cached symbols
    // This is a workaround and might need to be adjusted based on your needs
    const cachedSymbols = (global as any).__cachedSymbols || [];
    console.log(
      "getAllCachedInstruments():cachedSymbols->",
      cachedSymbols.length,
    );
    return cachedSymbols;
  },
  ["all-instrument-details"],
  {
    revalidate: 24 * 60 * 60,
    tags: ["all-instrument-details"],
  },
);
