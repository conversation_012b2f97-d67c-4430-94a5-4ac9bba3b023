"use server";

import { TrendChange } from "@/db/trend-change";

export interface SelectStockParams {
  stock: TrendChange;
  entryWindowVariable: number;
}

// - ⁠Calculate the range (Sell Side - Buy Side) and multiple by 10% (this is a variable that we can change) to find entry window
// - ⁠Is Closing price within entry window for equity?
// - ⁠- For BULLISH equity, is Buy Side + entry window > Closing? If true, then highlight equity
// - ⁠- For BEARISH equity, is Sell Side - entry window < Closing? If true, then highlight equity
export async function selectStockPicks({
  stock,
  entryWindowVariable,
}: SelectStockParams): Promise<boolean> {
  // Ignore the neutral
  if (stock.trend === "NEUTRAL") return false;
  // Also ignore treasury bonds
  if (stock.description?.toLowerCase().includes("treasury")) return false;
  // Get the entry window
  const entryWindow =
    ((Number(stock.sellTrade) - Number(stock.buyTrade)) *
      Number(entryWindowVariable)) /
    100;
  // console.log(
  //   `${stock.index} : ${Number(stock.sellTrade)} - ${Number(stock.buyTrade)} * ${Number(entryWindowVariable) / 100} = ${entryWindow}`,
  // );
  // This next line of code below is not use currently. It just check if closing price within bullish and bearish (both), instead of individiual check
  // const isWithinEntryWindow =
  //   stock.buyTrade + entryWindow >= stock.previousClose &&
  //   stock.sellTrade - entryWindow <= stock.previousClose;
  // Simply check if previous closing price is less than buy + entry window
  if (stock.trend === "BULLISH") {
    return Number(stock.buyTrade) + entryWindow > Number(stock.previousClose);
  }
  // Simply check if previous closing price is greather than sell - entry window
  if (stock.trend === "BEARISH") {
    return Number(stock.sellTrade) - entryWindow < Number(stock.previousClose);
  }
  // default is false
  return false;
}

export async function calculateStockPicks(
  trendChangeList: TrendChange[],
  entryWindowVariable: number,
): Promise<TrendChange[]> {
  const stocksToPickPromises = trendChangeList.map((stock) =>
    selectStockPicks({ stock, entryWindowVariable }),
  );
  const stocksToPickResults = await Promise.all(stocksToPickPromises);
  const stocksToPick = trendChangeList.filter(
    (_, index) => stocksToPickResults[index],
  );
  return stocksToPick;
}
