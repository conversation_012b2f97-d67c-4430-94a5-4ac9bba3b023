"use server";

import { TrendChange } from "@/db/trend-change";
import { tempLoggingBetterStack } from "@/lib/logger";
import { getEnvironmentVariable } from "@/lib/utils-server";
import { TrendChangeLink } from "@/types/stocks";
import { JSD<PERSON> } from "jsdom";

const setHeader = () => {
  const headers = new Headers();
  headers.append("Cookie", getEnvironmentVariable("HEDGEYE_SESSION_COOKIE"));
  headers.append("cache-control", "no-store");
  return headers;
};

export default async function FetchRiskSignalPage(
  url: string = "https://app.hedgeye.com/feed_items/all?page=1&with_category=33-risk-range-signals",
) {
  const result: string = await fetch(url, {
    method: "GET",
    headers: setHeader(),
  })
    .then(async (response) => {
      // console.log(response);
      await tempLoggingBetterStack({
        dt: new Date(),
        message: response.ok
          ? "Fetch risk range signal page url successful."
          : "Fetch risk range signal result is undefined.",
      });
      if (!response.ok) {
        throw new Error("Fetching risk signal returns nothing");
      }
      // Return the raw HTML string
      return await response.text();
    })
    .catch(async (error) => {
      await tempLoggingBetterStack({
        dt: new Date(),
        message: error as string,
      });
      throw new Error(error);
    });
  // console.log(result);
  return result;
}

export async function parseTrendChangeLink(
  html: string,
): Promise<TrendChangeLink | null> {
  const dateRegex = /<p class='med-article__meta__date'>(.*?)<\/p>/;
  const hrefRegex = /<a class='med-article__title-link' href='(.*?)'>/;

  const dateMatch = html.match(dateRegex);
  const hrefMatch = html.match(hrefRegex);

  if (dateMatch && hrefMatch) {
    const dateString = dateMatch[1];
    const hrefString = hrefMatch[1];

    // Check if dateString exists
    if (!dateString) return null;

    // Parse the date string into a Date object
    const dateParts = dateString.split(" ");
    if (!dateParts || dateParts.length !== 2) return null;
    const [month, day, year] = dateParts[0].split("/");
    const timeParts = dateParts[1].split(":");
    if (!timeParts || timeParts.length !== 2) return null;
    const hours = timeParts[0];
    const minutes = timeParts[1].substring(0, 2);
    const amPm = timeParts[1].substring(3, 5);
    const date = new Date(
      `${month}/${day}/${year} ${hours}:${minutes} ${amPm} EST`,
    );

    return {
      date: date,
      href: `https://app.hedgeye.com${hrefString}`,
    };
  }

  return null;
}

export async function actionExtractTrendChangeTable() {
  return (htmlText: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlText, "text/html");
    const tableTrendChange: HTMLTableElement | null = doc.querySelector(
      'table[class*="dtr-table"]',
    );
    return tableTrendChange;
  };
}

export async function actionToModelTrendChange() {
  return (table: HTMLTableElement, date: Date): TrendChange[] => {
    // Initialize an empty array to store the results
    let result: TrendChange[] = [];
    let originalIndex: number = 0;

    // Get the table body element
    let tbody = table.querySelector("tbody");

    // Loop through each row in the table body
    Array.from(tbody!.rows).forEach((row) => {
      // Initialize an empty object to store the current row data
      let rowData: TrendChange = {} as TrendChange;

      // Get the first cell in the row, which contains the index and the description
      let firstCell = row.cells[0];

      // Get the text content of the first cell, which contains the index and the trend in parentheses
      let firstCellText = firstCell.textContent;

      // Use a regular expression to extract the index and the trend from the text
      let match = firstCellText!.match(/(.+) \((.+)\)/);

      // If the match is successful, assign the index and the trend to the rowData object
      if (match) {
        rowData.index = match[1];
        rowData.trend = match[2];
      }

      // Get the em element in the first cell, which contains the description
      let em = firstCell.querySelector("em");

      // If the em element exists, assign the description to the rowData object
      if (em) {
        rowData.description = em.textContent!;
      }

      // Get the second, third, and fourth cells in the row, which contain the buyTrade, sellTrade, and previousClose values
      let secondCell = row.cells[1];
      let thirdCell = row.cells[2];
      let fourthCell = row.cells[3];

      // Parse the text content of the cells as numbers and assign them to the rowData object
      rowData.buyTrade = Number(secondCell.textContent!.replace(/,/g, ""));
      rowData.sellTrade = Number(thirdCell.textContent!.replace(/,/g, ""));
      rowData.previousClose = Number(fourthCell.textContent!.replace(/,/g, ""));

      rowData.originalIndex = originalIndex;
      rowData.date = date;
      // console.log(rowData);
      // Push the rowData object to the result array
      result.push(rowData);
      originalIndex++;
    });
    return result;
  };
}

export async function actionMakeTrendChangeModel() {
  const extractTrendChangeTable = await actionExtractTrendChangeTable();
  const toModelTrendChange = await actionToModelTrendChange();
  return async (html: string, date: Date) => {
    // Get the Trend Change table part
    const extractedTrendChangeTable = extractTrendChangeTable(html);
    // Make model of the table
    const trendChangeList = toModelTrendChange(
      extractedTrendChangeTable!,
      date,
    );
    return trendChangeList;
  };
}

class DOMParser {
  parseFromString(s: any, contentType = "text/html") {
    return new JSDOM(s, { contentType }).window.document;
  }
}
