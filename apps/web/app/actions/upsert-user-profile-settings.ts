"use server";

import prismadb from "@/lib/prisma";
import { UserProfileSettings } from "@/types/user-profile";
import { getUserProfile } from "@/db/user-profile";

export const upsertUserProfileIBKRConnectionDetail = async (
  settings: UserProfileSettings,
  userId: string,
) => {
  try {
    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      return await prismadb.userProfile.create({
        data: {
          user_id: userId,
          settings: JSON.parse(
            JSON.stringify({
              ibkrConnectionDetail: settings.ibkrConnectionDetail,
            }),
          ),
        },
      });
    }

    const userProfileSettings = JSON.parse(
      JSON.stringify({
        ...userProfile.settings,
        ibkrConnectionDetail: settings.ibkrConnectionDetail,
      }),
    );

    const updatedUserProfile = await prismadb.userProfile.update({
      where: { user_id: userId },
      data: {
        settings: userProfileSettings,
      },
    });

    return updatedUserProfile;
  } catch (error) {
    console.error("Error upserting user profile settings", error);
    throw error;
  }
};

export const upsertUserProfileRiskSignalSettings = async (
  settings: UserProfileSettings,
  userId: string,
) => {
  try {
    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      throw new Error("User profile not found");
    }

    const userProfileSettings = JSON.parse(
      JSON.stringify({
        ...userProfile.settings,
        riskSignalSettings: settings.riskSignalSettings,
      }),
    );

    const updatedUserProfile = await prismadb.userProfile.update({
      where: { user_id: userId },
      data: {
        settings: userProfileSettings,
      },
    });

    return updatedUserProfile;
  } catch (error) {
    console.error("Error upserting user profile settings", error);
    throw error;
  }
};

export const upsertUserProfileIBAccountSettings = async (
  settings: UserProfileSettings,
  userId: string,
) => {
  try {
    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      throw new Error("User profile not found");
    }

    const userProfileSettings = JSON.parse(
      JSON.stringify({
        ...userProfile.settings,
        defaultIBAccountId: settings.defaultIBAccountId,
        enableAutoOrderPrefill: settings.enableAutoOrderPrefill,
      }),
    );

    const updatedUserProfile = await prismadb.userProfile.update({
      where: { user_id: userId },
      data: {
        settings: userProfileSettings,
      },
    });

    return updatedUserProfile;
  } catch (error) {
    console.error("Error upserting user profile settings", error);
    throw error;
  }
};
