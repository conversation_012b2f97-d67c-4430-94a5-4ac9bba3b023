"use server";

import {
  getLastSyncedTrendChangeDate,
  getTrendChangesByDateOrMostRecent,
  TrendChange,
} from "@/db/trend-change";
import {
  getAllCachedSymbols,
  getCachedSymbol,
  setCachedSymbol,
  type CachedSymbol,
} from "../models/symbols";
import {
  FetchOpenFIGISingle,
  FetchOpenFIGIBatch,
} from "../risk-signals/server-actions/fetch-openfigi";

// export async function getCachedInstrumentDetail(symbol: string) {
//   try {
//     // Check cache first
//     const cached = getCachedSymbol(symbol);

//     if (cached) {
//       return cached;
//     }
//     // If not in cache, fetch from OpenFIGI
//     const figiData = await FetchOpenFIGISingle({
//       idValue: symbol,
//       idType: "TICKER",
//     });

//     if (figiData) {
//       const instrumentData: CachedSymbol = {
//         symbol,
//         name: String(figiData),
//         type: String(figiData),
//         figi: String(figiData),
//         exchange: String(figiData),
//       };

//       // Cache the result
//       setCachedSymbol(symbol, instrumentData);

//       return instrumentData;
//     }

//     // If no FIGI data, cache minimal info
//     const minimal: CachedSymbol = { symbol };
//     setCachedSymbol(symbol, minimal);
//     return minimal;
//   } catch (error) {
//     console.error(`Error fetching instrument detail for ${symbol}:`, error);
//     return { symbol }; // Return minimal data on error
//   }
// }

export async function getCachedInstrumentDetailBatch(symbols: string[]) {
  console.log("getCachedInstrumentDetailBatch():symbols->", symbols);
  if (symbols.length === 0) {
    const lastSyncedTrendChangeDate = await getLastSyncedTrendChangeDate();
    const riskSignals = JSON.parse(
      await getTrendChangesByDateOrMostRecent(
        new Date(lastSyncedTrendChangeDate),
      ),
    ) as TrendChange[];
    symbols = riskSignals.map((riskSignal) => riskSignal.index);
  }
  // Step 1: Filter symbols not yet cached
  const cachedSymbols = symbols.filter((symbol) => !getCachedSymbol(symbol));
  console.log(
    "getCachedInstrumentDetailBatch():cachedSymbols->",
    cachedSymbols,
  );
  // Step 2: Fetch OpenFIGI for the symbols that are not cached
  const results = JSON.parse(
    await FetchOpenFIGIBatch(
      cachedSymbols.map((symbol) => ({ idValue: symbol, idType: "TICKER" })),
    ),
  );
  // Step 3: Cache the results
  if (Array.isArray(results)) {
    results.forEach((result: any) => {
      // console.log("results.forEach((result: any)->", result);
      if (result.data && result.data.length > 0) {
        const instrumentData: CachedSymbol = {
          ticker: result.data[0].ticker,
          name: result.data[0].name,
          securityType: result.data[0].securityType,
          securityType2: result.data[0].securityType2,
          figi: result.data[0].figi,
          exchange: result.data[0].exchCode,
        };
        // Cache the result
        setCachedSymbol(instrumentData.ticker, instrumentData);
      }
    });
  }
  const finalCachedSymbols = getAllCachedSymbols();
  console.log("getCachedInstrumentDetailBatch():results->", finalCachedSymbols);
  return finalCachedSymbols;
}
