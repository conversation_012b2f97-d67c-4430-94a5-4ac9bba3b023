import FetchRiskSignalPage, {
  actionMakeTrendChangeModel,
} from "@/app/actions/fetch-risk-signals";
import { saveTrendChanges } from "@/db/trend-change";
import { extractPublishedDate } from "../../../services/hedgeye.service";
import {
  notifyRiskSignalsUpdate,
  notifyRiskSignalsUpdateWithTrendChanges,
} from "../../../services/notification.service";
import { unstable_noStore as noStore } from "next/cache";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

const getLatestRiskRangeSignalsHandler = async (req: NextRequest) => {
  console.info("getLatestRiskRangeSignalsHandler() started ...");
  try {
    noStore();

    const html = await FetchRiskSignalPage();
    if (!html) {
      throw new Error("Failed to fetch risk signal page");
    }

    const publishedDate = await extractPublishedDate(html);
    if (!publishedDate) {
      throw new Error("Could not extract published date from HTML");
    }

    const makeTrendChangeModel = actionMakeTrendChangeModel();
    if (!makeTrendChangeModel) {
      throw new Error("Failed to create trend change model");
    }

    const resultTrendChanges = await (
      await makeTrendChangeModel
    )(html, publishedDate);
    if (!resultTrendChanges || !Array.isArray(resultTrendChanges)) {
      throw new Error("Invalid trend changes result");
    }

    if (resultTrendChanges.length > 0) {
      // Save the new trend changes to the database
      console.info("Saving trend changes to the database ...");
      const saveResult = await saveTrendChanges(resultTrendChanges);
      console.info(
        "Trend changes saved to the database ... saveResult ->",
        saveResult,
      );
      if (saveResult && saveResult > 0) {
        // Notify users who subscribed to risk signals updates
        await notifyRiskSignalsUpdate(resultTrendChanges.length);
        // Notify users who subscribed to risk signals updates with trend changes
        await notifyRiskSignalsUpdateWithTrendChanges(publishedDate);
      }
    }

    // TODO: Compare the new trend changes with the the previous day's trend changes. Make a list of stocks that have changed. The change can be from BULLISH to BEARISH or BEARISH to BULLISH or NEUTRAL to BULLISH or NEUTRAL to BEARISH.
    // TODO: If the trend changes, for example from BULLISH to BEARISH, we will also notify the user who subscribed to notifications and they added the stock to their watchlist.

    return NextResponse.json({
      message: publishedDate.toISOString(),
      timestamp: new Date(),
      status: 200,
      count: resultTrendChanges.length,
    });
  } catch (error) {
    console.error("Error in risk range signals handler:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    const statusCode =
      error instanceof Error && error.message.includes("Failed to fetch")
        ? 503
        : 500;

    return NextResponse.json({
      message: errorMessage,
      timestamp: new Date(),
      status: statusCode,
    });
  }
};

export const GET = getLatestRiskRangeSignalsHandler;
export const POST = getLatestRiskRangeSignalsHandler;
