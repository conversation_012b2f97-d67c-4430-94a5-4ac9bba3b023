import {
  extractEquityTableUrlRegEx,
  getRiskSignalUpsideDownsidePotentials,
} from "@/app/actions/get-equity-table";
import logger, { tempLoggingBetterStack } from "@/lib/logger";
import { getEnvironmentVariable } from "@/lib/utils-server";
import { unstable_noStore as noStore } from "next/cache";
import { NextResponse } from "next/server";

const getRiskSignalHandler = async () => {
  noStore;
  let equityTableUrl: string | undefined = undefined;
  try {
    const headers = new Headers();
    headers.append("Cookie", getEnvironmentVariable("HEDGEYE_SESSION_COOKIE"));
    headers.append("cache-control", "no-store");
    const htmlResult: string = await fetch(
      "https://app.hedgeye.com/feed_items/all?page=1&with_category=33-risk-range-signals",
      {
        method: "GET",
        headers: headers,
      },
    )
      .then(async (response) => {
        await tempLoggingBetterStack({
          dt: new Date(),
          message: response.ok
            ? "Fetch risk range signal page url successful."
            : "Fetch risk range signal result is undefined.",
        });
        if (!response.ok) {
          throw new Error("Fetching risk signal returns nothing");
        }
        return await response.text();
      })
      .catch(async (error) => {
        await tempLoggingBetterStack({
          dt: new Date(),
          message: error as string,
        });
        throw new Error(error);
      });
    equityTableUrl = await extractEquityTableUrlRegEx(htmlResult);
    const datePublished =
      /<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/;
    const datePublishedMatch = htmlResult.match(datePublished);
    if (datePublishedMatch && datePublishedMatch[1]) {
      console.log(datePublishedMatch[1].toString());
    }
    if (equityTableUrl) {
      const data = await getRiskSignalUpsideDownsidePotentials(equityTableUrl);
      if (data) {
        const res = new NextResponse(data, {
          status: 200,
          headers: new Headers({
            "Content-Type": "image/png",
            "Cache-Control": "no-store",
            "Content-Length": data + "",
          }),
        });
        return res;
      } else {
        await tempLoggingBetterStack({
          dt: new Date(),
          message:
            "No data - it maybe that the url not found or it parsing error.",
        });
        return NextResponse.json({ message: "No data", status: 204 });
      }
    } else {
      return NextResponse.json({ htmlRaw: equityTableUrl!, status: 200 });
    }
  } catch (error) {
    await tempLoggingBetterStack({ dt: new Date(), message: error as string });
    logger.error(error);
    return NextResponse.json({ message: error, status: 500 });
  }
};

export const GET = getRiskSignalHandler;
