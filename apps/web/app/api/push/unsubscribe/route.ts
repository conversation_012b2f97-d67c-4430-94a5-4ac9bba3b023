import { NextResponse } from "next/server";
import prismadb from "@/lib/prisma";
import { auth } from "@/app/api/auth/[...nextauth]/auth";

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { endpoint } = await request.json();
    console.log("Unsubscribing endpoint:", endpoint);

    // Find and delete the subscription
    const subscription = await prismadb.pushSubscription.findFirst({
      where: {
        endpoint: endpoint,
        user_id: session.user.id,
      },
    });

    if (subscription) {
      await prismadb.pushSubscription.delete({
        where: {
          id: subscription.id,
        },
      });
      console.log("Subscription deleted from database");
    } else {
      console.log("No subscription found to delete");
    }

    return NextResponse.json({ message: "Unsubscribed successfully" });
  } catch (error) {
    console.error("Error in unsubscribe:", error);
    return NextResponse.json(
      { error: "Failed to unsubscribe" },
      { status: 500 },
    );
  }
}
