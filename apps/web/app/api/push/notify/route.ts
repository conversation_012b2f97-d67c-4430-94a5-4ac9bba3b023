import { NextResponse } from "next/server";
import * as webpush from "web-push";
import prismadb from "@/lib/prisma";
import { auth } from "@/app/api/auth/[...nextauth]/auth";
import { EmailProps, sendEmail } from "@/emails/resend";
import RiskSignalUpdateNotification from "@/emails/risk-signal-update-notification";
import { render } from "@react-email/render";

// Configure web-push with your VAPID keys
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!,
);

// Add type for subscription
type PushSubscription = {
  id: string;
  user_id: string;
  endpoint: string;
  p256dh: string;
  auth: string;
  deleted_at: Date | null;
};

export async function POST(request: Request) {
  try {
    // Check for API key in headers
    const apiKey = request.headers.get("x-api-key");
    const isServerToServer = apiKey === process.env.INTERNAL_API_KEY;
    const { message, count } = await request.json();

    if (!isServerToServer) {
      // Fall back to session auth for browser requests
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }
    }

    // Get all active subscriptions
    const subscriptions = await prismadb.pushSubscription.findMany({
      where: {
        deleted_at: null,
      },
    });

    if (!subscriptions.length) {
      return NextResponse.json({ message: "No active subscriptions found" });
    }

    // Group subscriptions by user_id to avoid duplicate emails
    const userSubscriptions: { [key: string]: PushSubscription[] } = {};
    subscriptions.forEach((sub) => {
      if (!userSubscriptions[sub.user_id]) {
        userSubscriptions[sub.user_id] = [];
      }
      userSubscriptions[sub.user_id].push(sub);
    });

    const notification = {
      title: "Risk Signal Update",
      body: message,
      icon: "/icon-512x512.png",
      badge: "/icon-96x96.png",
      data: {
        url: "/risk-signals",
      },
    };

    const results = await Promise.allSettled(
      Object.entries(userSubscriptions).map(async ([userId, userSubs]) => {
        try {
          // Send push notifications to all user's subscriptions
          await Promise.all(
            userSubs.map(async (subscription) => {
              if (
                !subscription.endpoint ||
                !subscription.p256dh ||
                !subscription.auth
              ) {
                throw new Error("Invalid subscription details");
              }

              try {
                await webpush.sendNotification(
                  {
                    endpoint: subscription.endpoint,
                    keys: {
                      p256dh: subscription.p256dh,
                      auth: subscription.auth,
                    },
                  },
                  JSON.stringify(notification),
                );
              } catch (error: any) {
                if (error.statusCode === 410) {
                  await prismadb.pushSubscription.update({
                    where: { id: subscription.id },
                    data: { deleted_at: new Date() },
                  });
                }
                throw error;
              }
            }),
          );

          // Get user details and send email (only once per user)
          const user = await prismadb.user.findUnique({
            where: { id: userId },
            select: { name: true, email: true },
          });

          if (!user?.email) {
            throw new Error("User email not found");
          }

          // Send email using the risk signal update template
          const emailProps: EmailProps = {
            body: await render(
              RiskSignalUpdateNotification({
                recipientName: user.name || undefined,
                updateDate: new Date().toLocaleDateString(),
                totalSignals: count,
                dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/risk-signals`,
              }),
            ),
            to: [user.email],
            subject: "Risk Range Signals Have Been Updated",
          };

          await sendEmail(emailProps);

          return { success: true, userId };
        } catch (error: any) {
          return {
            success: false,
            userId,
            error: error.message || "Unknown error",
          };
        }
      }),
    );

    return NextResponse.json({ results });
  } catch (error) {
    console.error("Error sending notifications:", error);
    return NextResponse.json(
      { error: "Failed to send notifications" },
      { status: 500 },
    );
  }
}
