import { NextRequest, NextResponse } from "next/server";
import { sendEmail } from "@/emails/resend";

const sendEmailHandler = async (req: NextRequest) => {
  try {
    const emailProps = await req.json();
    const resp = await sendEmail(emailProps);
    if (!resp) {
      return NextResponse.json("Ok", { status: 400 });
    }
    return NextResponse.json(resp);
  } catch (error) {
    return NextResponse.json(error, { status: 400 });
  }
};

export const POST = sendEmailHandler;
