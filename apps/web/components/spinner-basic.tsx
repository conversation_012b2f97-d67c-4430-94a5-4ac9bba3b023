"use client";

export function SpinnerBasic() {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white/50 dark:bg-black/50">
      <div
        className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
        role="status"
      />
    </div>
  );
}

export function SpinnerBasicWithText({ text }: { text: string }) {
  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center bg-white/50 dark:bg-black/50">
      <div
        className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
        role="status"
      />
      <div className="mt-2">{text}</div>
    </div>
  );
}

export default SpinnerBasicWithText;
