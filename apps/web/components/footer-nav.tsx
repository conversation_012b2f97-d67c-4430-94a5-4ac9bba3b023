"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Home, LineChart, BarChart2, List, Globe } from "lucide-react";

interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  href: string;
  isActive: boolean;
}

const NavItem = ({ icon, label, href, isActive }: NavItemProps) => {
  return (
    <Link
      href={href}
      className={cn(
        "flex flex-col items-center justify-center space-y-1 text-xs",
        isActive ? "text-primary" : "text-muted-foreground",
      )}
    >
      {icon}
      <span>{label}</span>
    </Link>
  );
};

export function FooterNav() {
  const pathname = usePathname();

  const navItems = [
    {
      icon: <Home size={20} />,
      label: "Home",
      href: "/",
    },
    {
      icon: <LineChart size={20} />,
      label: "Portfolio",
      href: "/portfolio",
    },
    {
      icon: <List size={20} />,
      label: "Watchlist",
      href: "/watchlist",
    },
    {
      icon: <BarChart2 size={20} />,
      label: "Analytics",
      href: "/dashboard",
    },
  ];

  return (
    <div className="sticky bottom-0 left-0 right-0 border-t bg-background md:hidden safe-area-pb">
      <nav className="flex w-full items-center justify-between px-4 py-2">
        {navItems.map((item) => (
          <NavItem
            key={item.href}
            {...item}
            isActive={pathname === item.href}
          />
        ))}
      </nav>
    </div>
  );
}

export default FooterNav;
