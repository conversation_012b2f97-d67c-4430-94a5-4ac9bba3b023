import {
  Html,
  Head,
  Preview,
  Heading,
  Text,
  Section,
  Container,
  Body,
  Button,
  Link,
} from "@react-email/components";

interface RiskSignalUpdateProps {
  recipientName?: string;
  updateDate: string;
  totalSignals: number;
  dashboardUrl: string;
}

const RiskSignalUpdateNotification = ({
  recipientName,
  updateDate,
  totalSignals,
  dashboardUrl,
}: RiskSignalUpdateProps) => {
  return (
    <Html>
      <Head />
      <Preview>Risk Range Signals Have Been Updated</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading as="h1" style={header}>
            Risk Range Signals Update
          </Heading>

          <Text style={paragraph}>
            {recipientName ? `Hello ${recipientName},` : "Hello,"}
          </Text>

          <Section style={section}>
            <Text style={paragraph}>
              We've successfully updated our Risk Range Signals database. Here's
              a quick summary:
            </Text>

            <div style={summaryBox}>
              <Text style={summaryText}>
                ✨ <strong>Update Completed:</strong> {updateDate}
              </Text>
              <Text style={summaryText}>
                📊 <strong>Total Signals Processed:</strong> {totalSignals}
              </Text>
            </div>

            <Text style={paragraph}>
              You can view the latest signals and analysis on your dashboard:
            </Text>

            <Button
              style={{
                ...button,
                padding: "12px 20px",
              }}
              href={dashboardUrl}
            >
              View Dashboard
            </Button>
          </Section>

          <Text style={noteText}>
            Note: This data is updated automatically to ensure you have the most
            current market insights.
          </Text>

          <Text style={footer}>
            This is an automated notification. Please do not reply to this
            email.
          </Text>

          <Text style={disclaimer}>
            The information provided is for informational purposes only and
            should not be considered as investment advice.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "40px 20px",
  borderRadius: "5px",
  maxWidth: "600px",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
};

const header = {
  color: "#1a1a1a",
  fontSize: "24px",
  lineHeight: "1.3",
  margin: "0 0 20px",
  textAlign: "center" as const,
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "1.5",
  color: "#4a5568",
  margin: "0 0 20px",
};

const section = {
  margin: "20px 0",
};

const summaryBox = {
  backgroundColor: "#f8fafc",
  padding: "20px",
  borderRadius: "8px",
  border: "1px solid #e2e8f0",
  margin: "20px 0",
};

const summaryText = {
  fontSize: "16px",
  color: "#2d3748",
  margin: "10px 0",
  lineHeight: "1.5",
};

const button = {
  backgroundColor: "#3182ce",
  borderRadius: "5px",
  color: "#fff",
  fontSize: "16px",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "100%",
  maxWidth: "200px",
  margin: "20px auto",
};

const noteText = {
  fontSize: "14px",
  color: "#718096",
  fontStyle: "italic",
  margin: "20px 0",
  padding: "10px",
  backgroundColor: "#f7fafc",
  borderRadius: "5px",
};

const footer = {
  fontSize: "14px",
  color: "#718096",
  margin: "30px 0 10px",
  textAlign: "center" as const,
};

const disclaimer = {
  fontSize: "12px",
  color: "#a0aec0",
  margin: "10px 0 0",
  textAlign: "center" as const,
};

export default RiskSignalUpdateNotification;
