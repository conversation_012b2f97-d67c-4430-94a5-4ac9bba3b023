import { unstable_noStore as noStore } from "next/cache";
import yahooFinance from "yahoo-finance2";

// Suppress the survey notice
yahooFinance.suppressNotices(["yahooSurvey"]);

/**
 * Maps common ticker symbols to their Yahoo Finance equivalents
 */
const TICKER_MAPPING: Record<string, string> = {
  SPX: "^GSPC", // S&P 500
  VIX: "^VIX", // Volatility Index
  DJI: "^DJI", // Dow Jones Industrial Average
  IXIC: "^IXIC", // NASDAQ Composite
  // Add more mappings as needed
};

interface QuoteResponse {
  regularMarketPrice: number;
  regularMarketChange: number;
  regularMarketChangePercent: number;
  regularMarketTime: Date;
}

interface BatchQuoteResponse {
  [symbol: string]: QuoteResponse;
}

/**
 * Fetches real-time quote data from Yahoo Finance
 * Includes error handling and retries for reliability
 */
export async function fetchQuote(
  ticker: string,
  retryCount = 2,
): Promise<QuoteResponse> {
  noStore();
  try {
    const yahooTicker = TICKER_MAPPING[ticker] || ticker;

    for (let i = 0; i <= retryCount; i++) {
      try {
        // Add exponential backoff delay
        if (i > 0) {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, i) * 1000),
          );
        }

        const response = await yahooFinance.quote(yahooTicker, {
          fields: [
            "regularMarketPrice",
            "regularMarketChange",
            "regularMarketChangePercent",
            "regularMarketTime",
          ],
        });

        if (response && typeof response.regularMarketPrice === "number") {
          return {
            regularMarketPrice: response.regularMarketPrice,
            regularMarketChange: response.regularMarketChange ?? 0,
            regularMarketChangePercent:
              response.regularMarketChangePercent ?? 0,
            regularMarketTime: new Date(
              response.regularMarketTime ?? Date.now(),
            ),
          };
        }
        throw new Error("Invalid response format");
      } catch (error) {
        console.warn(`Attempt ${i + 1} failed for ${ticker}:`, error);
        if (i === retryCount) throw error;
      }
    }
    throw new Error("All retry attempts failed");
  } catch (error) {
    console.error(`Failed to fetch quote for ${ticker}:`, error);
    return {
      regularMarketPrice: 0,
      regularMarketChange: 0,
      regularMarketChangePercent: 0,
      regularMarketTime: new Date(),
    };
  }
}

/**
 * Fetches quotes for multiple symbols in a single request
 * @param tickers Array of ticker symbols
 * @param retryCount Number of retry attempts
 */
export async function fetchQuotes(
  tickers: string[],
  retryCount = 2,
): Promise<BatchQuoteResponse> {
  noStore();
  try {
    // Map tickers to Yahoo Finance format
    const yahooTickers = tickers.map(
      (ticker) => TICKER_MAPPING[ticker] || ticker,
    );

    // Try to fetch with retries
    for (let i = 0; i <= retryCount; i++) {
      try {
        const response = await yahooFinance.quote(yahooTickers, {
          fields: [
            "regularMarketPrice",
            "regularMarketChange",
            "regularMarketChangePercent",
            "regularMarketTime",
          ],
        });

        // Process batch response
        const result: BatchQuoteResponse = {};

        // Handle both array and single responses
        const quotes = Array.isArray(response) ? response : [response];

        quotes.forEach((quote, index) => {
          const symbol = tickers[index];
          if (quote && typeof quote.regularMarketPrice === "number") {
            result[symbol] = {
              regularMarketPrice: quote.regularMarketPrice,
              regularMarketChange: quote.regularMarketChange ?? 0,
              regularMarketChangePercent: quote.regularMarketChangePercent ?? 0,
              regularMarketTime: new Date(
                quote.regularMarketTime ?? Date.now(),
              ),
            };
          } else {
            // Provide default values for failed quotes
            result[symbol] = {
              regularMarketPrice: 0,
              regularMarketChange: 0,
              regularMarketChangePercent: 0,
              regularMarketTime: new Date(),
            };
          }
        });

        return result;
      } catch (error) {
        if (i === retryCount) throw error;
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
    throw new Error("All retry attempts failed");
  } catch (error) {
    console.error(`Failed to fetch quotes for ${tickers.join(", ")}:`, error);
    // Return default values for all requested symbols
    return tickers.reduce((acc, symbol) => {
      acc[symbol] = {
        regularMarketPrice: 0,
        regularMarketChange: 0,
        regularMarketChangePercent: 0,
        regularMarketTime: new Date(),
      };
      return acc;
    }, {} as BatchQuoteResponse);
  }
}

// Update initialization to optimize for batch requests
export function initializeYahooFinance() {
  yahooFinance.setGlobalConfig({
    queue: {
      concurrency: 2, // Reduced concurrency since we're batching
      timeout: 5000,
    },
    validation: {
      logErrors: true,
    },
  });
}
