// NYSE trading hours are 9:30 AM to 4:00 PM Eastern Time, Monday to Friday
// excluding holidays

export function isNYSEOpen(): boolean {
  // Get current date in NY timezone
  const nyTime = new Date().toLocaleString("en-US", {
    timeZone: "America/New_York",
  });
  const nyDate = new Date(nyTime);

  // Get day of week (0 = Sunday, 6 = Saturday)
  const day = nyDate.getDay();

  // Check if it's weekend
  if (day === 0 || day === 6) return false;

  // Get current hour and minute in NY
  const hour = nyDate.getHours();
  const minute = nyDate.getMinutes();
  const currentTime = hour * 100 + minute; // Convert to military time format (e.g., 1430 for 2:30 PM)

  // Market opens at 9:30 AM (930) and closes at 4:00 PM (1600)
  return currentTime >= 930 && currentTime < 1600;
}

// Optional: More detailed market status
export function getNYSEStatus(): {
  isOpen: boolean;
  status: "pre-market" | "open" | "closed" | "after-hours";
} {
  const nyTime = new Date().toLocaleString("en-US", {
    timeZone: "America/New_York",
  });
  const nyDate = new Date(nyTime);
  const day = nyDate.getDay();
  const hour = nyDate.getHours();
  const minute = nyDate.getMinutes();
  const currentTime = hour * 100 + minute;

  // Check if it's weekend
  if (day === 0 || day === 6) {
    return { isOpen: false, status: "closed" };
  }

  // Pre-market: 4:00 AM - 9:30 AM
  if (currentTime >= 400 && currentTime < 930) {
    return { isOpen: false, status: "pre-market" };
  }
  // Regular market hours: 9:30 AM - 4:00 PM
  else if (currentTime >= 930 && currentTime < 1600) {
    return { isOpen: true, status: "open" };
  }
  // After-hours: 4:00 PM - 8:00 PM
  else if (currentTime >= 1600 && currentTime < 2000) {
    return { isOpen: false, status: "after-hours" };
  }
  // Closed
  else {
    return { isOpen: false, status: "closed" };
  }
}

// Usage example:
// const marketOpen = isNYSEOpen();
// console.log(`Market is ${marketOpen ? 'open' : 'closed'}`);

// const marketStatus = getNYSEStatus();
// console.log(`Market status: ${marketStatus.status}`);
