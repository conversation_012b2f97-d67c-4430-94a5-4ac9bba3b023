{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "mPQRIel-NKU6JLAAcNs1D", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "3588c447b0cd63861bbd2f039e280a4a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "32f81b24cbcb3e5e54145a7d995e1f71031200be467a35e3859ae3a0d3bcaaf6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9dd99f1f19f6897e5389a2d540fa29b9e4228db1d65dc76ab7bdb725a2af001a"}}}, "functions": {}, "sortedMiddleware": ["/"]}