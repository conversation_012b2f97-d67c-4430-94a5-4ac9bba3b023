{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "HfDiSJI_wmhujeABJ5TI-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "84e783fb7640cf3c52c875698ccefb4f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "13eaeb4cfc70270191f23b04d3d6b1268741a414573a045400c58e50f1f7f214", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4370107f071618d710b4daa9df4081dcf7509be9fca4c4e6fe7f6f22812d989c"}}}, "functions": {}, "sortedMiddleware": ["/"]}