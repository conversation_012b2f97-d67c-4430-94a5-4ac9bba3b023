{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "YGV3FGJuyRQm-ZcLQshAH", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "bb3cc8457ab09d5f7ad675e636d7f9b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4c496312cc7b5dedb5449f494d3fb4fdeea460d3c7f311ba8af7046b532c52b1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c81cce653106a749b6be882ad219841bb8cbb1210bcc7cbd48f2d573d98cfde7"}}}, "functions": {}, "sortedMiddleware": ["/"]}