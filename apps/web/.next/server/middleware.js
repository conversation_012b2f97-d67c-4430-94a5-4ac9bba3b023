(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{64:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>a});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},74:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},99:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,a=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),f=Symbol.iterator,g=Object.prototype.hasOwnProperty,y=Object.assign;function m(e,t,r,n,i,o){return{$$typeof:a,type:e,key:t,ref:void 0!==(r=o.ref)?r:null,props:o}}function b(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var w=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function _(){}function E(e,t,r){if(null==e)return e;var s=[],c=0;return!function e(t,r,s,c,l){var u,d,p,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var y=!1;if(null===t)y=!0;else switch(g){case"bigint":case"string":case"number":y=!0;break;case"object":switch(t.$$typeof){case a:case o:y=!0;break;case h:return e((y=t._init)(t._payload),r,s,c,l)}}if(y)return l=l(t),y=""===c?"."+v(t,0):c,i(l)?(s="",null!=y&&(s=y.replace(w,"$&/")+"/"),e(l,r,s,"",function(e){return e})):null!=l&&(b(l)&&(u=l,d=s+(null==l.key||t&&t.key===l.key?"":(""+l.key).replace(w,"$&/")+"/")+y,l=m(u.type,d,void 0,void 0,void 0,u.props)),r.push(l)),1;y=0;var E=""===c?".":c+":";if(i(t))for(var S=0;S<t.length;S++)g=E+v(c=t[S],S),y+=e(c,r,s,g,l);else if("function"==typeof(S=null===(p=t)||"object"!=typeof p?null:"function"==typeof(p=f&&p[f]||p["@@iterator"])?p:null))for(t=S.call(t),S=0;!(c=t.next()).done;)g=E+v(c=c.value,S++),y+=e(c,r,s,g,l);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(_,_):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,s,c,l);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return y}(e,s,"","",function(e){return t.call(r,e,c++)}),s}function S(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function k(){return new WeakMap}function x(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:E,forEach:function(e,t,r){E(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return E(e,function(){t++}),t},toArray:function(e){return E(e,function(e){return e})||[]},only:function(e){if(!b(e))throw Error(n(143));return e}},t.Fragment=s,t.Profiler=l,t.StrictMode=c,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(k);void 0===(t=n.get(e))&&(t=x(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var a=arguments[n];if("function"==typeof a||"object"==typeof a&&null!==a){var o=t.o;null===o&&(t.o=o=new WeakMap),void 0===(t=o.get(a))&&(t=x(),o.set(a,t))}else null===(o=t.p)&&(t.p=o=new Map),void 0===(t=o.get(a))&&(t=x(),o.set(a,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(n=t).s=1,n.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=y({},e.props),a=e.key,o=void 0;if(null!=t)for(s in void 0!==t.ref&&(o=void 0),void 0!==t.key&&(a=""+t.key),t)g.call(t,s)&&"key"!==s&&"__self"!==s&&"__source"!==s&&("ref"!==s||void 0!==t.ref)&&(i[s]=t[s]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var c=Array(s),l=0;l<s;l++)c[l]=arguments[l+2];i.children=c}return m(e.type,a,void 0,void 0,o,i)},t.createElement=function(e,t,r){var n,i={},a=null;if(null!=t)for(n in void 0!==t.key&&(a=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];i.children=s}if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===i[n]&&(i[n]=o[n]);return m(e,a,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=b,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:S}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},120:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(320).xl)()},125:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(320).xl)()},193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return o},withRequest:function(){return a}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function a(e,t,r){let a=i(e,t);return a?n.run(a,r):r()}function o(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},223:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>o,oJ:()=>i,zB:()=>a});var n=r(509);let i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.Q}},260:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),a=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),a=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:a.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,o.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:a.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),a=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),a=r(194),o=r(277),s=r(369),c=r(930),l="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),a=r(139),o=r(607),s=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(i)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),a=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}function a(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),a=r(130),o=i.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var a;let o=c[s]=null!=(a=c[s])?a:{version:i.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,a.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||a.major!==s.major)return o(e);if(0===a.major)return a.minor===s.minor&&a.patch<=s.patch?(t.add(e),!0):o(e);return a.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class a extends n{add(e,t){}}t.NoopUpDownCounterMetric=a;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class l extends s{}t.NoopObservableGaugeMetric=l;class u extends s{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),a=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new a.NonRecordingSpan;let c=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new a.NonRecordingSpan(c):new a.NonRecordingSpan}startActiveSpan(e,t,r,n){let a,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(a=t,c=r):(a=t,o=r,c=n);let l=null!=o?o:s.active(),u=this.startSpan(e,a,l),d=(0,i.setSpan)(l,u);return s.with(d,c,void 0,u)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class a{getTracer(e,t,r){var i;return null!=(i=this.getDelegateTracer(e,t,r))?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=a},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),a=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(a.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let a=r.slice(0,i),o=r.slice(i+1,t.length);(0,n.validateKey)(a)&&(0,n.validateValue)(o)&&e.set(a,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,a=RegExp(`^(?:${n}|${i})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),a=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return a.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}},o=!0;try{t[e].call(a.exports,a,a.exports,i),o=!1}finally{o&&delete n[e]}return a.exports}i.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var o=i(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=i(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=i(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=i(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var u=i(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var p=i(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var h=i(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return h.SpanStatusCode}});var f=i(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=i(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var y=i(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return y.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return y.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return y.isValidSpanId}});var m=i(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let b=i(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return b.context}});let w=i(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return w.diag}});let v=i(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return v.metrics}});let _=i(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return _.propagation}});let E=i(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return E.trace}}),a.default={context:b.context,diag:w.diag,metrics:v.metrics,propagation:_.propagation,trace:E.trace}})(),e.exports=a})()},313:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return a},wrapRequestHandler:function(){return o}});let n=r(193),i=r(840);function a(){return(0,i.interceptFetch)(r.g.fetch)}function o(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},320:(e,t,r)=>{"use strict";r.d(t,{xl:()=>o});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return a?new a:new i}},349:(e,t,r)=>{"use strict";r.d(t,{t3:()=>c,I3:()=>d,Ui:()=>l,xI:()=>o,Pk:()=>s});var n=r(407),i=r(64);r(74),r(981),r(125),r(785);let a="function"==typeof n.unstable_postpone;function o(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function s(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function c(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=h(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw h(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function l(e,t,r){(function(){if(!a)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(u(e,t))}function u(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&p(e.message)}function p(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===p(u("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function h(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},352:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(o,s){"use strict";var c="function",l="undefined",u="object",d="string",p="major",h="model",f="name",g="type",y="vendor",m="version",b="architecture",w="console",v="mobile",_="tablet",E="smarttv",S="wearable",k="embedded",x="Amazon",A="Apple",T="ASUS",R="BlackBerry",P="Browser",O="Chrome",C="Firefox",I="Google",N="Huawei",U="Microsoft",j="Motorola",D="Opera",M="Samsung",$="Sharp",L="Sony",H="Xiaomi",W="Zebra",K="Facebook",q="Chromium OS",B="Mac OS",J=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},V=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},z=function(e,t){return typeof e===d&&-1!==F(t).indexOf(F(e))},F=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,a,o,l,d=0;d<t.length&&!o;){var p=t[d],h=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(i=0;i<h.length;i++)l=o[++n],typeof(a=h[i])===u&&a.length>0?2===a.length?typeof a[1]==c?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==c||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):void 0:this[a[0]]=l?a[1].call(this,l,a[2]):void 0:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):s):this[a]=l||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(z(t[r][n],e))return"?"===r?s:r}else if(z(t[r],e))return"?"===r?s:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,m],[/opios[\/ ]+([\w\.]+)/i],[m,[f,D+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[f,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[f,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+P],m],[/\bfocus\/([\w\.]+)/i],[m,[f,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[f,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[f,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[f,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[m,[f,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+P]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+P],m],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,K],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[f,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,O+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[f,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[f,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,m],[/(cobalt)\/([\w\.]+)/i],[f,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,F]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",F]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,F]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[y,M],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[y,M],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[y,A],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[y,A],[g,_]],[/(macintosh);/i],[h,[y,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[y,$],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[y,N],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[y,N],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[y,H],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[y,H],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[y,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[y,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[y,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[y,j],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[y,j],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[y,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[y,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[y,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[y,"Nokia"],[g,v]],[/(pixel c)\b/i],[h,[y,I],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[y,I],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[y,L],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[y,L],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[y,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[y,x],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[y,x],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[h,y,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[y,R],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[y,T],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[y,T],[g,v]],[/(nexus 9)/i],[h,[y,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[h,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[y,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[y,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,h,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,h,[g,_]],[/(surface duo)/i],[h,[y,U],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[y,"Fairphone"],[g,v]],[/(u304aa)/i],[h,[y,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[h,[y,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[h,[y,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[h,[y,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[h,[y,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[y,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[h,[y,"NuVision"],[g,_]],[/\b(k88) b/i],[h,[y,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[h,[y,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[h,[y,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[h,[y,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[h,[y,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],h,[g,_]],[/\b(ns-?\w{0,9}) b/i],[h,[y,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[y,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],h,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],h,[g,v]],[/\b(ph-1) /i],[h,[y,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[y,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[h,[y,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[h,[y,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[h,[y,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[y,h,[g,v]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[y,U],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[y,W],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[y,W],[g,v]],[/smart-tv.+(samsung)/i],[y,[g,E]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[y,M],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[g,E]],[/(apple) ?tv/i],[y,[h,A+" TV"],[g,E]],[/crkey/i],[[h,O+"cast"],[y,I],[g,E]],[/droid.+aft(\w)( bui|\))/i],[h,[y,x],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[y,$],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[h,[y,L],[g,E]],[/(mitv-\w{5}) bui/i],[h,[y,H],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[y,h,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,G],[h,G],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,h,[g,w]],[/droid.+; (shield) bui/i],[h,[y,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[h,[y,L],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[y,U],[g,w]],[/((pebble))app/i],[y,h,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[y,A],[g,S]],[/droid.+; (glass) \d/i],[h,[y,I],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[h,[y,W],[g,S]],[/(quest( 2| pro)?)/i],[h,[y,K],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[g,k]],[/(aeobc)\b/i],[h,[y,x],[g,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[m,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[m,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,B],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,m],[/\(bb(10);/i],[m,[f,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[f,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[f,O+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,q],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,m],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,m]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==l&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,a=t?J(Z,t):Z,w=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=s,t[m]=s,X.call(t,n,a.browser),t[p]=typeof(e=t[m])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[b]=s,X.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[h]=s,e[g]=s,X.call(e,n,a.device),w&&!e[g]&&i&&i.mobile&&(e[g]=v),w&&"Macintosh"==e[h]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=s,e[m]=s,X.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[f]=s,e[m]=s,X.call(e,n,a.os),w&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,q).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=V([f,m,p]),ee.CPU=V([b]),ee.DEVICE=V([h,y,g,w,v,E,_,S,k]),ee.ENGINE=ee.OS=V([f,m]),typeof a!==l?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==l&&(o.UAParser=ee);var et=typeof o!==l&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete a[e]}return r.exports}o.ab="//",e.exports=o(226)})()},356:e=>{"use strict";e.exports=require("node:buffer")},407:(e,t,r)=>{"use strict";e.exports=r(99)},431:(e,t,r)=>{"use strict";r.d(t,{RM:()=>a,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},440:(e,t)=>{"use strict";t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},n=e.length;if(n<2)return r;var i=t&&t.decode||l,a=0,o=0,u=0;do{if(-1===(o=e.indexOf("=",a)))break;if(-1===(u=e.indexOf(";",a)))u=n;else if(o>u){a=e.lastIndexOf(";",o-1)+1;continue}var d=s(e,a,o),p=c(e,o,d),h=e.slice(d,p);if(!r.hasOwnProperty(h)){var f=s(e,o+1,u),g=c(e,u,f);34===e.charCodeAt(f)&&34===e.charCodeAt(g-1)&&(f++,g--);var y=e.slice(f,g);r[h]=function(e,t){try{return t(e)}catch(t){return e}}(y,i)}a=u+1}while(a<n);return r},t.l=function(e,t,s){var c=s&&s.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var l=c(t);if(!i.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(!s)return u;if(null!=s.maxAge){var d=Math.floor(s.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(s.domain){if(!a.test(s.domain))throw TypeError("option domain is invalid");u+="; Domain="+s.domain}if(s.path){if(!o.test(s.path))throw TypeError("option path is invalid");u+="; Path="+s.path}if(s.expires){var p,h=s.expires;if(p=h,"[object Date]"!==r.call(p)||isNaN(h.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+h.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():s.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,n=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/;function s(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function l(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},447:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(431),i=r(223);function a(e){return(0,i.nJ)(e)||(0,n.RM)(e)}},491:(e,t,r)=>{"use strict";let n,i,a,o,s,c,l,u;r.r(t),r.d(t,{default:()=>cP});var d={};async function p(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(d),r.d(d,{config:()=>cx,default:()=>ck});let h=null;async function f(){if("phase-production-build"===process.env.NEXT_PHASE)return;h||(h=p());let e=await h;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function g(...e){let t=await p();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let y=null;function m(){return y||(y=f()),y}function b(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),m();class w extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class v extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class _ extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let E="_N_T_",S={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function k(e){var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function x(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...k(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function A(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...S,GROUP:{builtinReact:[S.reactServerComponents,S.actionBrowser],serverOnly:[S.reactServerComponents,S.actionBrowser,S.instrument,S.middleware],neutralTarget:[S.apiNode,S.apiEdge],clientOnly:[S.serverSideRendering,S.appPagesBrowser],bundled:[S.reactServerComponents,S.actionBrowser,S.serverSideRendering,S.appPagesBrowser,S.shared,S.instrument,S.middleware],appPages:[S.reactServerComponents,S.serverSideRendering,S.appPagesBrowser,S.actionBrowser]}});let T=Symbol("response"),R=Symbol("passThrough"),P=Symbol("waitUntil");class O{constructor(e,t){this[R]=!1,this[P]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[T]||(this[T]=Promise.resolve(e))}passThroughOnException(){this[R]=!0}waitUntil(e){if("external"===this[P].kind)return(0,this[P].function)(e);this[P].promises.push(e)}}class C extends O{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function I(e){return e.replace(/\/$/,"")||"/"}function N(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function U(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=N(e);return""+t+r+n+i}function j(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=N(e);return""+r+t+n+i}function D(e,t){if("string"!=typeof e)return!1;let{pathname:r}=N(e);return r===t||r.startsWith(t+"/")}let M=new WeakMap;function $(e,t){let r;if(!t)return{pathname:e};let n=M.get(t);n||(n=t.map(e=>e.toLowerCase()),M.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let a=i[1].toLowerCase(),o=n.indexOf(a);return o<0?{pathname:e}:(r=t[o],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let L=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function H(e,t){return new URL(String(e).replace(L,"localhost"),t&&String(t).replace(L,"localhost"))}let W=Symbol("NextURLInternal");class K{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[W]={url:H(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let a=function(e,t){var r,n;let{basePath:i,i18n:a,trailingSlash:o}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):o};i&&D(s.pathname,i)&&(s.pathname=function(e,t){if(!D(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,i),s.basePath=i);let c=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){let e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");s.buildId=e[0],c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=c)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):$(s.pathname,a.locales);s.locale=e.detectedLocale,s.pathname=null!=(n=e.pathname)?n:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):$(c,a.locales)).detectedLocale&&(s.locale=e.detectedLocale)}return s}(this[W].url.pathname,{nextConfig:this[W].options.nextConfig,parseData:!0,i18nProvider:this[W].options.i18nProvider}),o=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[W].url,this[W].options.headers);this[W].domainLocale=this[W].options.i18nProvider?this[W].options.i18nProvider.detectDomainLocale(o):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(i=a.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[W].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,o);let s=(null==(r=this[W].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[W].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[W].url.pathname=a.pathname,this[W].defaultLocale=s,this[W].basePath=a.basePath??"",this[W].buildId=a.buildId,this[W].locale=a.locale??s,this[W].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(D(i,"/api")||D(i,"/"+t.toLowerCase()))?e:U(e,"/"+t)}((e={basePath:this[W].basePath,buildId:this[W].buildId,defaultLocale:this[W].options.forceLocale?void 0:this[W].defaultLocale,locale:this[W].locale,pathname:this[W].url.pathname,trailingSlash:this[W].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=I(t)),e.buildId&&(t=j(U(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=U(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:j(t,"/"):I(t)}formatSearch(){return this[W].url.search}get buildId(){return this[W].buildId}set buildId(e){this[W].buildId=e}get locale(){return this[W].locale??""}set locale(e){var t,r;if(!this[W].locale||!(null==(r=this[W].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[W].locale=e}get defaultLocale(){return this[W].defaultLocale}get domainLocale(){return this[W].domainLocale}get searchParams(){return this[W].url.searchParams}get host(){return this[W].url.host}set host(e){this[W].url.host=e}get hostname(){return this[W].url.hostname}set hostname(e){this[W].url.hostname=e}get port(){return this[W].url.port}set port(e){this[W].url.port=e}get protocol(){return this[W].url.protocol}set protocol(e){this[W].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[W].url=H(e),this.analyze()}get origin(){return this[W].url.origin}get pathname(){return this[W].url.pathname}set pathname(e){this[W].url.pathname=e}get hash(){return this[W].url.hash}set hash(e){this[W].url.hash=e}get search(){return this[W].url.search}set search(e){this[W].url.search=e}get password(){return this[W].url.password}set password(e){this[W].url.password=e}get username(){return this[W].url.username}set username(e){this[W].url.username=e}get basePath(){return this[W].basePath}set basePath(e){this[W].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new K(String(this),this[W].options)}}var q=r(660);let B=Symbol("internal request");class J extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);A(r),e instanceof Request?super(e,t):super(r,t);let n=new K(r,{headers:x(this.headers),nextConfig:t.nextConfig});this[B]={cookies:new q.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[B].cookies}get nextUrl(){return this[B].nextUrl}get page(){throw new v}get ua(){throw new _}get url(){return this[B].url}}class V{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let z=Symbol("internal response"),F=new Set([301,302,303,307,308]);function G(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class X extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new q.ResponseCookies(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let a=Reflect.apply(e[n],e,i),o=new Headers(r);return a instanceof q.ResponseCookies&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,q.stringifyCookie)(e)).join(",")),G(t,o),a};default:return V.get(e,n,i)}}});this[z]={cookies:n,url:t.url?new K(t.url,{headers:x(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[z].cookies}static json(e,t){let r=Response.json(e,t);return new X(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!F.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",A(e)),new X(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",A(e)),G(t,r),new X(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),G(e,t),new X(null,{...e,headers:t})}}function Y(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let Q="Next-Router-Prefetch",Z=["RSC","Next-Router-State-Tree",Q,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],ee="_rsc";class et extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new et}}class er extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return V.get(t,r,n);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return V.get(t,a,n)},set(t,r,n,i){if("symbol"==typeof r)return V.set(t,r,n,i);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return V.set(t,o??r,n,i)},has(t,r){if("symbol"==typeof r)return V.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&V.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return V.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||V.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return et.callable;default:return V.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new er(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var en=r(125),ei=r(981);class ea extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ea}}class eo{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ea.callable;default:return V.get(e,t,r)}}})}}let es=Symbol.for("next.mutated.cookies");class ec{static wrap(e,t){let r=new q.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,a=()=>{let e=en.J.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new q.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},o=new Proxy(r,{get(e,t,r){switch(t){case es:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),o}finally{a()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),o}finally{a()}};default:return V.get(e,t,r)}}});return o}}function el(e){return"action"===e.phase}function eu(e){if(!el((0,ei.XN)(e)))throw new ea}var ed=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(ed||{}),ep=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ep||{}),eh=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(eh||{}),ef=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ef||{}),eg=function(e){return e.startServer="startServer.startServer",e}(eg||{}),ey=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(ey||{}),em=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(em||{}),eb=function(e){return e.executeRoute="Router.executeRoute",e}(eb||{}),ew=function(e){return e.runHandler="Node.runHandler",e}(ew||{}),ev=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(ev||{}),e_=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(e_||{}),eE=function(e){return e.execute="Middleware.execute",e}(eE||{});let eS=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ek=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function ex(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eA,propagation:eT,trace:eR,SpanStatusCode:eP,SpanKind:eO,ROOT_CONTEXT:eC}=n=r(260);class eI extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eN=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eI})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eP.ERROR,message:null==t?void 0:t.message})),e.end()},eU=new Map,ej=n.createContextKey("next.rootSpanId"),eD=0,eM=()=>eD++,e$={set(e,t,r){e.push({key:t,value:r})}};class eL{getTracerInstance(){return eR.getTracer("next.js","0.0.1")}getContext(){return eA}getTracePropagationData(){let e=eA.active(),t=[];return eT.inject(e,t,e$),t}getActiveScopeSpan(){return eR.getSpan(null==eA?void 0:eA.active())}withPropagatedContext(e,t,r){let n=eA.active();if(eR.getSpanContext(n))return t();let i=eT.extract(n,e,r);return eA.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:a,options:o}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},s=o.spanName??r;if(!eS.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||o.hideSpan)return a();let c=this.getSpanContext((null==o?void 0:o.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=eR.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==eA?void 0:eA.active())??eC,l=!0);let u=eM();return o.attributes={"next.span_name":s,"next.span_type":r,...o.attributes},eA.with(c.setValue(ej,u),()=>this.getTracerInstance().startActiveSpan(s,o,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{eU.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ek.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&eU.set(u,new Map(Object.entries(o.attributes??{})));try{if(a.length>1)return a(e,t=>eN(e,t));let t=a(e);if(ex(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eN(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eN(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return eS.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,o=arguments[a];if("function"!=typeof o)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(eA.active(),o);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eR.setSpan(eA.active(),e):void 0}getRootSpanAttributes(){let e=eA.active().getValue(ej);return eU.get(e)}setRootSpanAttribute(e,t){let r=eA.active().getValue(ej),n=eU.get(r);n&&n.set(e,t)}}let eH=(()=>{let e=new eL;return()=>e})(),eW="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eW);class eK{constructor(e,t,r,n){var i;let a=e&&function(e,t){let r=er.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,o=null==(i=r.get(eW))?void 0:i.value;this._isEnabled=!!(!a&&o&&e&&o===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eW,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eW,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eq(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of k(r))n.append("set-cookie",e);for(let e of new q.ResponseCookies(n).getAll())t.set(e)}}var eB=r(970),eJ=r.n(eB);class eV extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class ez{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new ez(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let eF=Symbol.for("@next/cache-handlers-map"),eG=Symbol.for("@next/cache-handlers-set"),eX=globalThis;function eY(){if(eX[eF])return eX[eF].entries()}async function eQ(e,t){if(!e)return t();let r=eZ(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eZ(e));await e1(e,t)}}function eZ(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e0(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eX[eG])return eX[eG].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function e1(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([e0(r,e.incrementalCache),...Object.values(n),...i])}let e2=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class e3{disable(){throw e2}getStore(){}run(){throw e2}exit(){throw e2}enterWith(){throw e2}static bind(e){return e}}let e5="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,e4=e5?new e5:new e3;class e6{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eJ()),this.callbackQueue.pause()}after(e){if(ex(e))this.waitUntil||e8(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||e8();let r=ei.FP.getStore();r&&this.workUnitStores.add(r);let n=e4.getStore(),i=n?n.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let a=(t=async()=>{try{await e4.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},e5?e5.bind(t):e3.bind(t));this.callbackQueue.add(a)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=en.J.getStore();if(!e)throw Object.defineProperty(new eV("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eQ(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eV("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function e8(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function e9(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class e7{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function te(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let tt=Symbol.for("@next/request-context"),tr=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function tn(e,t,r){let n=[],i=r&&r.size>0;for(let t of tr(e))t=`${E}${t}`,n.push(t);if(t.pathname&&!i){let e=`${E}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eY();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,e9(async()=>i.getExpiration(...e)));return t}(n)}}class ti extends J{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let ta={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},to=(e,t)=>eH().withPropagatedContext(e.headers,t,ta),ts=!1;async function tc(e){var t;let n,i;if(!ts&&(ts=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(313);e(),to=t(to)}await m();let a=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let o=new K(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...o.searchParams.keys()]){let t=o.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(o.searchParams.delete(r),t))o.searchParams.append(r,e);o.searchParams.delete(e)}}let s=o.buildId;o.buildId="";let c=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),l=c.has("x-nextjs-data"),u="1"===c.get("RSC");l&&"/index"===o.pathname&&(o.pathname="/");let d=new Map;if(!a)for(let e of Z){let t=e.toLowerCase(),r=c.get(t);null!==r&&(d.set(t,r),c.delete(t))}let p=new ti({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(ee),t?r.toString():r})(o).toString(),init:{body:e.request.body,headers:c,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});l&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:te()})}));let h=e.request.waitUntil??(null==(t=function(){let e=globalThis[tt];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new C({request:p,page:e.page,context:h?{waitUntil:h}:void 0});if((n=await to(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),r=new e7;return eH().trace(eE.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,a,o,c,l,u;let d=te(),h=await tn("/",p.nextUrl,null),g=(l=p.nextUrl,u=e=>{i=e},function(e,t,r,n,i,a,o,s,c,l,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let p={};return{type:"request",phase:e,implicitTags:a,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return p.headers||(p.headers=function(e){let t=er.from(e);for(let e of Z)t.delete(e.toLowerCase());return er.seal(t)}(t.headers)),p.headers},get cookies(){if(!p.cookies){let e=new q.RequestCookies(er.from(t.headers));eq(t,e),p.cookies=eo.seal(e)}return p.cookies},set cookies(value){p.cookies=value},get mutableCookies(){if(!p.mutableCookies){let e=function(e,t){let r=new q.RequestCookies(er.from(e));return ec.wrap(r,t)}(t.headers,o||(r?d:void 0));eq(t,e),p.mutableCookies=e}return p.mutableCookies},get userspaceMutableCookies(){return p.userspaceMutableCookies||(p.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return eu("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return eu("cookies().set"),e.set(...r),t};default:return V.get(e,r,n)}}});return t}(this.mutableCookies)),p.userspaceMutableCookies},get draftMode(){return p.draftMode||(p.draftMode=new eK(c,t,this.cookies,this.mutableCookies)),p.draftMode},renderResumeDataCache:s??null,isHmrRefresh:l,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",p,void 0,l,{},h,u,void 0,d,!1,void 0)),y=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:a,previouslyRevalidatedTags:o}){var s;let c={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(s=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?s:"/"+s,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:a,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new e6({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:o,refreshTagsByCacheKind:function(){let e=new Map,t=eY();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,e9(async()=>n.refreshTags()));return e}()};return r.store=c,c}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(a=e.request.nextConfig)||null==(n=a.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(c=e.request.nextConfig)||null==(o=c.experimental)?void 0:o.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(Q),buildId:s??"",previouslyRevalidatedTags:[]});return await en.J.run(y,()=>ei.FP.run(g,e.handler,p,f))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,f)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let g=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&g&&(u||!a)){let t=new K(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});a||t.host!==p.nextUrl.host||(t.buildId=s||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=Y(t.toString(),o.toString());!a&&l&&n.headers.set("x-nextjs-rewrite",r),u&&i&&(o.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),o.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let y=null==n?void 0:n.headers.get("Location");if(n&&y&&!a){let t=new K(y,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===o.host&&(t.buildId=s||t.buildId,n.headers.set("Location",t.toString())),l&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",Y(t.toString(),o.toString()).url))}let b=n||X.next(),w=b.headers.get("x-middleware-override-headers"),v=[];if(w){for(let[e,t]of d)b.headers.set(`x-middleware-request-${e}`,t),v.push(e);v.length>0&&b.headers.set("x-middleware-override-headers",w+","+v.join(","))}return{response:b,waitUntil:("internal"===f[P].kind?Promise.all(f[P].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}var tl=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},tu=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function td(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class tp{constructor(e,t,r){if(is.add(this),ic.set(this,{}),il.set(this,void 0),iu.set(this,void 0),tl(this,iu,r,"f"),tl(this,il,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(tu(this,ic,"f")[e]=r)}get value(){return Object.keys(tu(this,ic,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>tu(this,ic,"f")[e]).join("")}chunk(e,t){let r=tu(this,is,"m",ip).call(this);for(let n of tu(this,is,"m",id).call(this,{name:tu(this,il,"f").name,value:e,options:{...tu(this,il,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(tu(this,is,"m",ip).call(this))}}ic=new WeakMap,il=new WeakMap,iu=new WeakMap,is=new WeakSet,id=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return tu(this,ic,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),tu(this,ic,"f")[t]=i}return tu(this,iu,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},ip=function(){let e={};for(let t in tu(this,ic,"f"))delete tu(this,ic,"f")?.[t],e[t]={name:t,value:"",options:{...tu(this,il,"f").options,maxAge:0}};return e};class th extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class tf extends th{}tf.kind="signIn";class tg extends th{}tg.type="AdapterError";class ty extends th{}ty.type="AccessDenied";class tm extends th{}tm.type="CallbackRouteError";class tb extends th{}tb.type="ErrorPageLoop";class tw extends th{}tw.type="EventError";class tv extends th{}tv.type="InvalidCallbackUrl";class t_ extends tf{constructor(){super(...arguments),this.code="credentials"}}t_.type="CredentialsSignin";class tE extends th{}tE.type="InvalidEndpoints";class tS extends th{}tS.type="InvalidCheck";class tk extends th{}tk.type="JWTSessionError";class tx extends th{}tx.type="MissingAdapter";class tA extends th{}tA.type="MissingAdapterMethods";class tT extends th{}tT.type="MissingAuthorize";class tR extends th{}tR.type="MissingSecret";class tP extends tf{}tP.type="OAuthAccountNotLinked";class tO extends tf{}tO.type="OAuthCallbackError";class tC extends th{}tC.type="OAuthProfileParseError";class tI extends th{}tI.type="SessionTokenError";class tN extends tf{}tN.type="OAuthSignInError";class tU extends tf{}tU.type="EmailSignInError";class tj extends th{}tj.type="SignOutError";class tD extends th{}tD.type="UnknownAction";class tM extends th{}tM.type="UnsupportedStrategy";class t$ extends th{}t$.type="InvalidProvider";class tL extends th{}tL.type="UntrustedHost";class tH extends th{}tH.type="Verification";class tW extends tf{}tW.type="MissingCSRF";let tK=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class tq extends th{}tq.type="DuplicateConditionalUI";class tB extends th{}tB.type="MissingWebAuthnAutocomplete";class tJ extends th{}tJ.type="WebAuthnVerificationError";class tV extends tf{}tV.type="AccountNotLinked";class tz extends th{}tz.type="ExperimentalFeatureNotEnabled";let tF=!1;function tG(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let tX=!1,tY=!1,tQ=!1,tZ=["createVerificationToken","useVerificationToken","getUserByEmail"],t0=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],t1=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],t2=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},t3=async(e,t,r,n,i)=>{let{crypto:{subtle:a}}=t2();return new Uint8Array(await a.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:n},await a.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function t5(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function t4(e,t,r,n,i){return t3(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=t5(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),t5(r,"salt"),function(e){let t=t5(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let t6=crypto,t8=e=>e instanceof CryptoKey,t9=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await t6.subtle.digest(r,t))},t7=new TextEncoder,re=new TextDecoder;function rt(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function rr(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function rn(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return rr(r,t,0),rr(r,e%0x100000000,4),r}function ri(e){let t=new Uint8Array(4);return rr(t,e),t}function ra(e){return rt(ri(e.length),e)}async function ro(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(ri(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await t9("sha256",n),32*t)}return i.slice(0,t>>3)}let rs=e=>{let t=e;"string"==typeof t&&(t=t7.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},rc=e=>rs(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),rl=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},ru=e=>{let t=e;t instanceof Uint8Array&&(t=re.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return rl(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}};class rd extends Error{constructor(e,t){super(e,t),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}rd.code="ERR_JOSE_GENERIC";class rp extends rd{constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=r,this.reason=n,this.payload=t}}rp.code="ERR_JWT_CLAIM_VALIDATION_FAILED";class rh extends rd{constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.code="ERR_JWT_EXPIRED",this.claim=r,this.reason=n,this.payload=t}}rh.code="ERR_JWT_EXPIRED";class rf extends rd{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}}rf.code="ERR_JOSE_ALG_NOT_ALLOWED";class rg extends rd{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}}rg.code="ERR_JOSE_NOT_SUPPORTED";class ry extends rd{constructor(e="decryption operation failed",t){super(e,t),this.code="ERR_JWE_DECRYPTION_FAILED"}}ry.code="ERR_JWE_DECRYPTION_FAILED";class rm extends rd{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}}rm.code="ERR_JWE_INVALID";class rb extends rd{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}}rb.code="ERR_JWS_INVALID";class rw extends rd{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}}rw.code="ERR_JWT_INVALID";class rv extends rd{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}}rv.code="ERR_JWK_INVALID";class r_ extends rd{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}}r_.code="ERR_JWKS_INVALID";class rE extends rd{constructor(e="no applicable key found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_NO_MATCHING_KEY"}}rE.code="ERR_JWKS_NO_MATCHING_KEY";class rS extends rd{constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator,rS.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";class rk extends rd{constructor(e="request timed out",t){super(e,t),this.code="ERR_JWKS_TIMEOUT"}}rk.code="ERR_JWKS_TIMEOUT";class rx extends rd{constructor(e="signature verification failed",t){super(e,t),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}function rA(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}rx.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";let rT=(e,t)=>{if("string"!=typeof e||!e)throw new rv(`${t} missing or invalid`)};async function rR(e,t){let r;if(!rA(e))throw TypeError("JWK must be an object");if(t??(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":rT(e.crv,'"crv" (Curve) Parameter'),rT(e.x,'"x" (X Coordinate) Parameter'),rT(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":rT(e.crv,'"crv" (Subtype of Key Pair) Parameter'),rT(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":rT(e.e,'"e" (Exponent) Parameter'),rT(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":rT(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new rg('"kty" (Key Type) Parameter missing or unsupported')}let n=t7.encode(JSON.stringify(r));return rc(await t9(t,n))}let rP=Symbol(),rO=t6.getRandomValues.bind(t6);function rC(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new rg(`Unsupported JWE Algorithm: ${e}`)}}let rI=e=>rO(new Uint8Array(rC(e)>>3)),rN=(e,t)=>{if(t.length<<3!==rC(e))throw new rm("Invalid Initialization Vector length")},rU=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new rm(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function rj(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function rD(e,t){return e.name===t}function rM(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!rD(e.algorithm,"AES-GCM"))throw rj("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rj(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!rD(e.algorithm,"AES-KW"))throw rj("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rj(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw rj("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!rD(e.algorithm,"PBKDF2"))throw rj("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!rD(e.algorithm,"RSA-OAEP"))throw rj("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw rj(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i.length&&!i.some(e=>n.usages.includes(e))){let e="CryptoKey does not support this operation, its usages must include ";if(i.length>2){let t=i.pop();e+=`one of ${i.join(", ")}, or ${t}.`}else 2===i.length?e+=`one of ${i[0]} or ${i[1]}.`:e+=`${i[0]}.`;throw TypeError(e)}}function r$(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let rL=(e,...t)=>r$("Key must be ",e,...t);function rH(e,t,...r){return r$(`Key for the ${e} algorithm must be `,t,...r)}let rW=e=>!!t8(e)||e?.[Symbol.toStringTag]==="KeyObject",rK=["CryptoKey"];async function rq(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(rL(r,"Uint8Array"));let a=parseInt(e.slice(1,4),10),o=await t6.subtle.importKey("raw",r.subarray(a>>3),"AES-CBC",!1,["encrypt"]),s=await t6.subtle.importKey("raw",r.subarray(0,a>>3),{hash:`SHA-${a<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await t6.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=rt(i,n,c,rn(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await t6.subtle.sign("HMAC",s,l)).slice(0,a>>3)),iv:n}}async function rB(e,t,r,n,i){let a;r instanceof Uint8Array?a=await t6.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(rM(r,e,"encrypt"),a=r);let o=new Uint8Array(await t6.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},a,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let rJ=async(e,t,r,n,i)=>{if(!t8(r)&&!(r instanceof Uint8Array))throw TypeError(rL(r,...rK,"Uint8Array"));switch(n?rN(e,n):n=rI(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&rU(r,parseInt(e.slice(-3),10)),rq(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&rU(r,parseInt(e.slice(1,4),10)),rB(e,t,r,n,i);default:throw new rg("Unsupported JWE Content Encryption Algorithm")}},rV=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]];function rz(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function rF(e,t,r){if(t8(e))return rM(e,t,r),e;if(e instanceof Uint8Array)return t6.subtle.importKey("raw",e,"AES-KW",!0,[r]);throw TypeError(rL(e,...rK,"Uint8Array"))}let rG=async(e,t,r)=>{let n=await rF(t,e,"wrapKey");rz(n,e);let i=await t6.subtle.importKey("raw",r,...rV);return new Uint8Array(await t6.subtle.wrapKey("raw",i,n,"AES-KW"))},rX=async(e,t,r)=>{let n=await rF(t,e,"unwrapKey");rz(n,e);let i=await t6.subtle.unwrapKey("raw",r,n,"AES-KW",...rV);return new Uint8Array(await t6.subtle.exportKey("raw",i))};async function rY(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let o;if(!t8(e))throw TypeError(rL(e,...rK));if(rM(e,"ECDH"),!t8(t))throw TypeError(rL(t,...rK));rM(t,"ECDH","deriveBits");let s=rt(ra(t7.encode(r)),ra(i),ra(a),ri(n));return o="X25519"===e.algorithm.name?256:"X448"===e.algorithm.name?448:Math.ceil(parseInt(e.algorithm.namedCurve.substr(-3),10)/8)<<3,ro(new Uint8Array(await t6.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}async function rQ(e){if(!t8(e))throw TypeError(rL(e,...rK));return t6.subtle.generateKey(e.algorithm,!0,["deriveBits"])}function rZ(e){if(!t8(e))throw TypeError(rL(e,...rK));return["P-256","P-384","P-521"].includes(e.algorithm.namedCurve)||"X25519"===e.algorithm.name||"X448"===e.algorithm.name}async function r0(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new rm("PBES2 Salt Input must be 8 or more octets");let i=rt(t7.encode(t),new Uint8Array([0]),e),a=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},s=await function(e,t){if(e instanceof Uint8Array)return t6.subtle.importKey("raw",e,"PBKDF2",!1,["deriveBits"]);if(t8(e))return rM(e,t,"deriveBits","deriveKey"),e;throw TypeError(rL(e,...rK,"Uint8Array"))}(n,t);if(s.usages.includes("deriveBits"))return new Uint8Array(await t6.subtle.deriveBits(o,s,a));if(s.usages.includes("deriveKey"))return t6.subtle.deriveKey(o,s,{length:a,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let r1=async(e,t,r,n=2048,i=rO(new Uint8Array(16)))=>{let a=await r0(i,e,n,t);return{encryptedKey:await rG(e.slice(-6),a,r),p2c:n,p2s:rc(i)}},r2=async(e,t,r,n,i)=>{let a=await r0(i,e,n,t);return rX(e.slice(-6),a,r)};function r3(e){switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new rg(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let r5=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},r4=async(e,t,r)=>{if(!t8(t))throw TypeError(rL(t,...rK));if(rM(t,e,"encrypt","wrapKey"),r5(e,t),t.usages.includes("encrypt"))return new Uint8Array(await t6.subtle.encrypt(r3(e),t,r));if(t.usages.includes("wrapKey")){let n=await t6.subtle.importKey("raw",r,...rV);return new Uint8Array(await t6.subtle.wrapKey("raw",n,t,r3(e)))}throw TypeError('RSA-OAEP key "usages" must include "encrypt" or "wrapKey" for this operation')},r6=async(e,t,r)=>{if(!t8(t))throw TypeError(rL(t,...rK));if(rM(t,e,"decrypt","unwrapKey"),r5(e,t),t.usages.includes("decrypt"))return new Uint8Array(await t6.subtle.decrypt(r3(e),t,r));if(t.usages.includes("unwrapKey")){let n=await t6.subtle.unwrapKey("raw",r,t,r3(e),...rV);return new Uint8Array(await t6.subtle.exportKey("raw",n))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function r8(e){return rA(e)&&"string"==typeof e.kty}let r9=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new rg('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new rg('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new rg('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new rg('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n=[t,e.ext??!1,e.key_ops??r],i={...e};return delete i.alg,delete i.use,t6.subtle.importKey("jwk",i,...n)},r7=e=>ru(e),ne=e=>e?.[Symbol.toStringTag]==="KeyObject",nt=async(e,t,r,n,i=!1)=>{let a=e.get(t);if(a?.[n])return a[n];let o=await r9({...r,alg:n});return i&&Object.freeze(t),a?a[n]=o:e.set(t,{[n]:o}),o},nr={normalizePublicKey:(e,t)=>{if(ne(e)){let r=e.export({format:"jwk"});return(delete r.d,delete r.dp,delete r.dq,delete r.p,delete r.q,delete r.qi,r.k)?r7(r.k):(a||(a=new WeakMap),nt(a,e,r,t))}return r8(e)?e.k?ru(e.k):(a||(a=new WeakMap),nt(a,e,e,t,!0)):e},normalizePrivateKey:(e,t)=>{if(ne(e)){let r=e.export({format:"jwk"});return r.k?r7(r.k):(i||(i=new WeakMap),nt(i,e,r,t))}return r8(e)?e.k?ru(e.k):(i||(i=new WeakMap),nt(i,e,e,t,!0)):e}};function nn(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new rg(`Unsupported JWE Algorithm: ${e}`)}}let ni=e=>rO(new Uint8Array(nn(e)>>3)),na=async e=>{if(e instanceof Uint8Array)return{kty:"oct",k:rc(e)};if(!t8(e))throw TypeError(rL(e,...rK,"Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...a}=await t6.subtle.exportKey("jwk",e);return a};async function no(e){return na(e)}let ns=e=>e?.[Symbol.toStringTag],nc=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},nl=(e,t,r,n)=>{if(!(t instanceof Uint8Array)){if(n&&r8(t)){if(function(e){return r8(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&nc(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!rW(t))throw TypeError(rH(e,t,...rK,"Uint8Array",n?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${ns(t)} instances for symmetric algorithms must be of type "secret"`)}},nu=(e,t,r,n)=>{if(n&&r8(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&nc(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&nc(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!rW(t))throw TypeError(rH(e,t,...rK,n?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${ns(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${ns(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${ns(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${ns(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${ns(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function nd(e,t,r,n){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?nl(t,r,n,e):nu(t,r,n,e)}let np=nd.bind(void 0,!1);nd.bind(void 0,!0);let nh=(e,t)=>{if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(e.length!==t.length)throw TypeError("Input buffers must have the same length");let r=e.length,n=0,i=-1;for(;++i<r;)n|=e[i]^t[i];return 0===n};async function nf(e,t,r,n,i,a){let o,s;if(!(t instanceof Uint8Array))throw TypeError(rL(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await t6.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await t6.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=rt(a,n,r,rn(a.length<<3)),p=new Uint8Array((await t6.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{o=nh(i,p)}catch{}if(!o)throw new ry;try{s=new Uint8Array(await t6.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new ry;return s}async function ng(e,t,r,n,i,a){let o;t instanceof Uint8Array?o=await t6.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(rM(t,e,"decrypt"),o=t);try{return new Uint8Array(await t6.subtle.decrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,rt(r,i)))}catch{throw new ry}}let ny=async(e,t,r,n,i,a)=>{if(!t8(t)&&!(t instanceof Uint8Array))throw TypeError(rL(t,...rK,"Uint8Array"));if(!n)throw new rm("JWE Initialization Vector missing");if(!i)throw new rm("JWE Authentication Tag missing");switch(rN(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&rU(t,parseInt(e.slice(-3),10)),nf(e,t,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&rU(t,parseInt(e.slice(1,4),10)),ng(e,t,r,n,i,a);default:throw new rg("Unsupported JWE Content Encryption Algorithm")}};async function nm(e,t,r,n){let i=e.slice(0,7),a=await rJ(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:rc(a.iv),tag:rc(a.tag)}}async function nb(e,t,r,n,i){return ny(e.slice(0,7),t,r,n,i,new Uint8Array(0))}async function nw(e,t,r,n,i={}){let a,o,s;switch(np(e,r,"encrypt"),r=await nr.normalizePublicKey?.(r,e)||r,e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!rZ(r))throw new rg("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:l}=i,{epk:u}=i;u||(u=(await rQ(r)).privateKey);let{x:d,y:p,crv:h,kty:f}=await no(u),g=await rY(r,u,"ECDH-ES"===e?t:e,"ECDH-ES"===e?nn(t):parseInt(e.slice(-5,-2),10),c,l);if(o={epk:{x:d,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),c&&(o.apu=rc(c)),l&&(o.apv=rc(l)),"ECDH-ES"===e){s=g;break}s=n||ni(t);let y=e.slice(-6);a=await rG(y,g,s);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||ni(t),a=await r4(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||ni(t);let{p2c:c,p2s:l}=i;({encryptedKey:a,...o}=await r1(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||ni(t),a=await rG(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||ni(t);let{iv:c}=i;({encryptedKey:a,...o}=await nm(e,r,s,c));break}default:throw new rg('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:a,parameters:o}}let nv=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},n_=function(e,t,r,n,i){let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(o))throw new rg(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)};class nE{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,n,i,a,o;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new rm("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!nv(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new rm("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(n_(rm,new Map,t?.crit,this._protectedHeader,s),void 0!==s.zip)throw new rg('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new rm('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new rm('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);{let i;({cek:n,encryptedKey:r,parameters:i}=await nw(c,l,e,this._cek,this._keyManagementParameters)),i&&(t&&rP in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...i}:this.setUnprotectedHeader(i):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...i}:this.setProtectedHeader(i))}a=this._protectedHeader?t7.encode(rc(JSON.stringify(this._protectedHeader))):t7.encode(""),this._aad?(o=rc(this._aad),i=rt(a,t7.encode("."),t7.encode(o))):i=a;let{ciphertext:u,tag:d,iv:p}=await rJ(l,this._plaintext,n,this._iv,i),h={ciphertext:rc(u)};return p&&(h.iv=rc(p)),d&&(h.tag=rc(d)),r&&(h.encrypted_key=rc(r)),o&&(h.aad=o),this._protectedHeader&&(h.protected=re.decode(a)),this._sharedUnprotectedHeader&&(h.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(h.header=this._unprotectedHeader),h}}class nS{constructor(e){this._flattened=new nE(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let nk=e=>Math.floor(e.getTime()/1e3),nx=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,nA=e=>{let t,r=nx.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function nT(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class nR{constructor(e={}){if(!rA(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:nT("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:nT("setNotBefore",nk(e))}:this._payload={...this._payload,nbf:nk(new Date)+nA(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:nT("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:nT("setExpirationTime",nk(e))}:this._payload={...this._payload,exp:nk(new Date)+nA(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:nk(new Date)}:e instanceof Date?this._payload={...this._payload,iat:nT("setIssuedAt",nk(e))}:"string"==typeof e?this._payload={...this._payload,iat:nT("setIssuedAt",nk(new Date)+nA(e))}:this._payload={...this._payload,iat:nT("setIssuedAt",e)},this}}class nP extends nR{setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new nS(t7.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}async function nO(e,t){if(!rA(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return ru(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new rg('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return r9({...e,alg:t});default:throw new rg('Unsupported "kty" (Key Type) Parameter value')}}async function nC(e,t,r,n,i){switch(np(e,t,"decrypt"),t=await nr.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new rm("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new rm("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!rA(n.epk))throw new rm('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!rZ(t))throw new rg("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await nO(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new rm('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=ru(n.apu)}catch{throw new rm("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new rm('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=ru(n.apv)}catch{throw new rm("Failed to base64url decode the apv")}}let s=await rY(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?nn(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return s;if(void 0===r)throw new rm("JWE Encrypted Key missing");return rX(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new rm("JWE Encrypted Key missing");return r6(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new rm("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new rm('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(n.p2c>o)throw new rm('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new rm('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=ru(n.p2s)}catch{throw new rm("Failed to base64url decode the p2s")}return r2(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new rm("JWE Encrypted Key missing");return rX(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new rm("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new rm('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new rm('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=ru(n.iv)}catch{throw new rm("Failed to base64url decode the iv")}try{a=ru(n.tag)}catch{throw new rm("Failed to base64url decode the tag")}return nb(e,t,r,i,a)}default:throw new rg('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let nI=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function nN(e,t,r){let n,i,a,o,s,c,l;if(!rA(e))throw new rm("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new rm("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new rm("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new rm("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new rm("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new rm("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new rm("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new rm("JWE AAD incorrect type");if(void 0!==e.header&&!rA(e.header))throw new rm("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!rA(e.unprotected))throw new rm("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=ru(e.protected);n=JSON.parse(re.decode(t))}catch{throw new rm("JWE Protected Header is invalid")}if(!nv(n,e.header,e.unprotected))throw new rm("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(n_(rm,new Map,r?.crit,n,u),void 0!==u.zip)throw new rg('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new rm("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new rm("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&nI("keyManagementAlgorithms",r.keyManagementAlgorithms),f=r&&nI("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new rf('"alg" (Algorithm) Header Parameter value not allowed');if(f&&!f.has(p))throw new rf('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=ru(e.encrypted_key)}catch{throw new rm("Failed to base64url decode the encrypted_key")}let g=!1;"function"==typeof t&&(t=await t(n,e),g=!0);try{a=await nC(d,t,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof rm||e instanceof rg)throw e;a=ni(p)}if(void 0!==e.iv)try{o=ru(e.iv)}catch{throw new rm("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=ru(e.tag)}catch{throw new rm("Failed to base64url decode the tag")}let y=t7.encode(e.protected??"");c=void 0!==e.aad?rt(y,t7.encode("."),t7.encode(e.aad)):y;try{l=ru(e.ciphertext)}catch{throw new rm("Failed to base64url decode the ciphertext")}let m={plaintext:await ny(p,a,l,o,s,c)};if(void 0!==e.protected&&(m.protectedHeader=n),void 0!==e.aad)try{m.additionalAuthenticatedData=ru(e.aad)}catch{throw new rm("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(m.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(m.unprotectedHeader=e.header),g)?{...m,key:t}:m}async function nU(e,t,r){if(e instanceof Uint8Array&&(e=re.decode(e)),"string"!=typeof e)throw new rm("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new rm("Invalid Compact JWE");let l=await nN({ciphertext:o,iv:a||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}let nj=e=>e.toLowerCase().replace(/^application\//,""),nD=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),nM=(e,t,r={})=>{let n,i;try{n=JSON.parse(re.decode(t))}catch{}if(!rA(n))throw new rw("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||nj(e.typ)!==nj(a)))throw new rp('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new rp(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new rp('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new rp('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!nD(n.aud,"string"==typeof l?[l]:l))throw new rp('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=nA(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=nk(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new rp('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new rp('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>h+i)throw new rp('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new rp('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=h-i)throw new rh('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=h-n.iat;if(e-i>("number"==typeof u?u:nA(u)))throw new rh('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new rp('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n};async function n$(e,t,r){let n=await nU(e,t,r),i=nM(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new rp('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new rp('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new rp('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:a};return"function"==typeof t?{...o,key:n.key}:o}var nL=r(440);let nH=()=>Date.now()/1e3|0,nW="A256CBC-HS512";async function nK(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],o=await nB(nW,a[0],i),s=await rR({kty:"oct",k:rc(o)},`sha${o.byteLength<<3}`);return await new nP(t).setProtectedHeader({alg:"dir",enc:nW,kid:s}).setIssuedAt().setExpirationTime(nH()+n).setJti(crypto.randomUUID()).encrypt(o)}async function nq(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await n$(t,async({kid:e,enc:t})=>{for(let r of i){let i=await nB(t,r,n);if(void 0===e||e===await rR({kty:"oct",k:rc(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[nW,"A256GCM"]});return a}async function nB(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await t4("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function nJ({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let nV="\x1b[31m",nz="\x1b[0m",nF={error(e){let t=e instanceof th?e.type:e.name;if(console.error(`${nV}[auth][error]${nz} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${nV}[auth][cause]${nz}:`,t.stack),r&&console.error(`${nV}[auth][details]${nz}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){let t=`https://warnings.authjs.dev#${e}`;console.warn(`\x1b[33m[auth][warn][${e}]${nz}`,`Read more: ${t}`)},debug(e,t){console.log(`\x1b[90m[auth][debug]:${nz} ${e}`,JSON.stringify(t,null,2))}};function nG(e){let t={...nF};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let nX=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function nY(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function nQ(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new tD("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new tD(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new tD(`Cannot parse action at ${e}`);let[i,a]=n;if(!nX.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new tD(`Cannot parse action at ${e}`);return{action:i,providerId:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await nY(e):void 0,cookies:(0,nL.q)(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=nG(t);r.error(n),r.debug("request",e)}}function nZ(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=(0,nL.l)(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function n0(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function n1(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function n2({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await n0(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=n1(32),a=await n0(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function n3(e,t){if(!t)throw new tW(`CSRF token was missing during an action ${e}`)}function n5(e){return null!==e&&"object"==typeof e}function n4(e,...t){if(!t.length)return e;let r=t.shift();if(n5(e)&&n5(r))for(let t in r)n5(r[t])?(n5(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),n4(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return n4(e,...t)}let n6=Symbol("skip-csrf-check"),n8=Symbol("return-type-raw"),n9=Symbol("custom-fetch"),n7=Symbol("conform-internal"),ie=e=>ir({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),it=e=>ir({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function ir(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function ii(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let ia={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function io({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:o,csrfDisabled:s,isPost:c}){var l,u;let d=nG(e),{providers:p,provider:h}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,o=i?.id??a.id,s=n4(a,i,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ii(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ii(e.token,e.issuer),n=ii(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??ie,account:e.account??it}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[n9]??(e[n9]=i?.[n9]),e}return s});return{providers:i,provider:i.find(({id:e})=>e===t)}}({url:n,providerId:t,config:e}),f=!1;if((h?.type==="oauth"||h?.type==="oidc")&&h.redirectProxyUrl)try{f=new URL(h.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${h.redirectProxyUrl}`)}let g={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:h,cookies:n4(td(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:nK,decode:nq,...e.jwt},events:(l=e.events??{},u=d,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new tw(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new tg(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...ia,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:f,experimental:{...e.experimental}},y=[];if(s)g.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await n2({options:g,cookieValue:i?.[g.cookies.csrfToken.name],isPost:c,bodyValue:o});g.csrfToken=e,g.csrfTokenVerified=r,t&&y.push({name:g.cookies.csrfToken.name,value:t,options:g.cookies.csrfToken.options})}let{callbackUrl:m,callbackUrlCookie:b}=await nJ({options:g,cookieValue:i?.[g.cookies.callbackUrl.name],paramValue:a});return g.callbackUrl=m,b&&y.push({name:g.cookies.callbackUrl.name,value:b,options:g.cookies.callbackUrl.options}),{options:g,cookies:y}}var is,ic,il,iu,id,ip,ih,ig,iy,im,ib,iw={},iv=[],i_=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function iE(e,t){for(var r in t)e[r]=t[r];return e}function iS(e){var t=e.parentNode;t&&t.removeChild(e)}function ik(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==i?++iy:i};return null==i&&null!=ig.vnode&&ig.vnode(a),a}function ix(e){return e.children}function iA(e,t){this.props=e,this.context=t}function iT(e,t){if(null==t)return e.__?iT(e.__,e.__.__k.indexOf(e)+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?iT(e):null}function iR(e){(!e.__d&&(e.__d=!0)&&im.push(e)&&!iP.__r++||ib!==ig.debounceRendering)&&((ib=ig.debounceRendering)||setTimeout)(iP)}function iP(){for(var e;iP.__r=im.length;)e=im.sort(function(e,t){return e.__v.__b-t.__v.__b}),im=[],e.some(function(e){var t,r,n,i,a;e.__d&&(i=(n=e.__v).__e,(a=e.__P)&&(t=[],(r=iE({},n)).__v=n.__v+1,iD(a,n,r,e.__n,void 0!==a.ownerSVGElement,null!=n.__h?[i]:null,t,null==i?iT(n):i,n.__h),iM(t,n),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)))})}function iO(e,t,r,n,i,a,o,s,c,l){var u,d,p,h,f,g,y,m=n&&n.__k||iv,b=m.length;for(r.__k=[],u=0;u<t.length;u++)if(null!=(h=r.__k[u]=null==(h=t[u])||"boolean"==typeof h?null:"string"==typeof h||"number"==typeof h||"bigint"==typeof h?ik(null,h,null,null,h):Array.isArray(h)?ik(ix,{children:h},null,null,null):h.__b>0?ik(h.type,h.props,h.key,h.ref?h.ref:null,h.__v):h)){if(h.__=r,h.__b=r.__b+1,null===(p=m[u])||p&&h.key==p.key&&h.type===p.type)m[u]=void 0;else for(d=0;d<b;d++){if((p=m[d])&&h.key==p.key&&h.type===p.type){m[d]=void 0;break}p=null}iD(e,h,p=p||iw,i,a,o,s,c,l),f=h.__e,(d=h.ref)&&p.ref!=d&&(y||(y=[]),p.ref&&y.push(p.ref,null,h),y.push(d,h.__c||f,h)),null!=f?(null==g&&(g=f),"function"==typeof h.type&&h.__k===p.__k?h.__d=c=function e(t,r,n){for(var i,a=t.__k,o=0;a&&o<a.length;o++)(i=a[o])&&(i.__=t,r="function"==typeof i.type?e(i,r,n):iC(n,i,i,a,i.__e,r));return r}(h,c,e):c=iC(e,h,p,m,f,c),"function"==typeof r.type&&(r.__d=c)):c&&p.__e==c&&c.parentNode!=e&&(c=iT(p))}for(r.__e=g,u=b;u--;)null!=m[u]&&function e(t,r,n){var i,a;if(ig.unmount&&ig.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||i$(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){ig.__e(e,r)}i.base=i.__P=null,t.__c=void 0}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||null==t.__e||iS(t.__e),t.__=t.__e=t.__d=void 0}(m[u],m[u]);if(y)for(u=0;u<y.length;u++)i$(y[u],y[++u],y[++u])}function iC(e,t,r,n,i,a){var o,s,c;if(void 0!==t.__d)o=t.__d,t.__d=void 0;else if(null==r||i!=a||null==i.parentNode)e:if(null==a||a.parentNode!==e)e.appendChild(i),o=null;else{for(s=a,c=0;(s=s.nextSibling)&&c<n.length;c+=1)if(s==i)break e;e.insertBefore(i,a),o=a}return void 0!==o?o:i.nextSibling}function iI(e,t,r){"-"===t[0]?e.setProperty(t,r):e[t]=null==r?"":"number"!=typeof r||i_.test(t)?r:r+"px"}function iN(e,t,r,n,i){var a;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||iI(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||iI(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n||e.addEventListener(t,a?ij:iU,a):e.removeEventListener(t,a?ij:iU,a);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,r))}}function iU(e){this.l[e.type+!1](ig.event?ig.event(e):e)}function ij(e){this.l[e.type+!0](ig.event?ig.event(e):e)}function iD(e,t,r,n,i,a,o,s,c){var l,u,d,p,h,f,g,y,m,b,w,v,_,E,S,k=t.type;if(void 0!==t.constructor)return null;null!=r.__h&&(c=r.__h,s=t.__e=r.__e,t.__h=null,a=[s]),(l=ig.__b)&&l(t);try{e:if("function"==typeof k){if(y=t.props,m=(l=k.contextType)&&n[l.__c],b=l?m?m.props.value:l.__:n,r.__c?g=(u=t.__c=r.__c).__=u.__E:("prototype"in k&&k.prototype.render?t.__c=u=new k(y,b):(t.__c=u=new iA(y,b),u.constructor=k,u.render=iL),m&&m.sub(u),u.props=y,u.state||(u.state={}),u.context=b,u.__n=n,d=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=k.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=iE({},u.__s)),iE(u.__s,k.getDerivedStateFromProps(y,u.__s))),p=u.props,h=u.state,d)null==k.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==k.getDerivedStateFromProps&&y!==p&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(y,b),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(y,u.__s,b)||t.__v===r.__v){for(u.props=y,u.state=u.__s,t.__v!==r.__v&&(u.__d=!1),u.__v=t,t.__e=r.__e,t.__k=r.__k,t.__k.forEach(function(e){e&&(e.__=t)}),w=0;w<u._sb.length;w++)u.__h.push(u._sb[w]);u._sb=[],u.__h.length&&o.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(y,u.__s,b),null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(p,h,f)})}if(u.context=b,u.props=y,u.__v=t,u.__P=e,v=ig.__r,_=0,"prototype"in k&&k.prototype.render){for(u.state=u.__s,u.__d=!1,v&&v(t),l=u.render(u.props,u.state,u.context),E=0;E<u._sb.length;E++)u.__h.push(u._sb[E]);u._sb=[]}else do u.__d=!1,v&&v(t),l=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++_<25);u.state=u.__s,null!=u.getChildContext&&(n=iE(iE({},n),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(f=u.getSnapshotBeforeUpdate(p,h)),S=null!=l&&l.type===ix&&null==l.key?l.props.children:l,iO(e,Array.isArray(S)?S:[S],t,r,n,i,a,o,s,c),u.base=t.__e,t.__h=null,u.__h.length&&o.push(u),g&&(u.__E=u.__=null),u.__e=!1}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,o,s){var c,l,u,d=r.props,p=t.props,h=t.type,f=0;if("svg"===h&&(i=!0),null!=a){for(;f<a.length;f++)if((c=a[f])&&"setAttribute"in c==!!h&&(h?c.localName===h:3===c.nodeType)){e=c,a[f]=null;break}}if(null==e){if(null===h)return document.createTextNode(p);e=i?document.createElementNS("http://www.w3.org/2000/svg",h):document.createElement(h,p.is&&p),a=null,s=!1}if(null===h)d===p||s&&e.data===p||(e.data=p);else{if(a=a&&ih.call(e.childNodes),l=(d=r.props||iw).dangerouslySetInnerHTML,u=p.dangerouslySetInnerHTML,!s){if(null!=a)for(d={},f=0;f<e.attributes.length;f++)d[e.attributes[f].name]=e.attributes[f].value;(u||l)&&(u&&(l&&u.__html==l.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,r,n,i){var a;for(a in r)"children"===a||"key"===a||a in t||iN(e,a,null,r[a],n);for(a in t)i&&"function"!=typeof t[a]||"children"===a||"key"===a||"value"===a||"checked"===a||r[a]===t[a]||iN(e,a,t[a],r[a],n)}(e,p,d,i,s),u)t.__k=[];else if(iO(e,Array.isArray(f=t.props.children)?f:[f],t,r,n,i&&"foreignObject"!==h,a,o,a?a[0]:r.__k&&iT(r,0),s),null!=a)for(f=a.length;f--;)null!=a[f]&&iS(a[f]);s||("value"in p&&void 0!==(f=p.value)&&(f!==e.value||"progress"===h&&!f||"option"===h&&f!==d.value)&&iN(e,"value",f,d.value,!1),"checked"in p&&void 0!==(f=p.checked)&&f!==e.checked&&iN(e,"checked",f,d.checked,!1))}return e}(r.__e,t,r,n,i,a,o,c);(l=ig.diffed)&&l(t)}catch(e){t.__v=null,(c||null!=a)&&(t.__e=s,t.__h=!!c,a[a.indexOf(s)]=null),ig.__e(e,t,r)}}function iM(e,t){ig.__c&&ig.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){ig.__e(e,t.__v)}})}function i$(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){ig.__e(e,r)}}function iL(e,t,r){return this.constructor(e,r)}function iH(e,t){var r,n,i,a;r=e,ig.__&&ig.__(r,t),i=(n="function"==typeof iH)?null:iH&&iH.__k||t.__k,a=[],iD(t,r=(!n&&iH||t).__k=function(e,t,r){var n,i,a,o={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?ih.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return ik(e,o,n,i,null)}(ix,null,[r]),i||iw,iw,void 0!==t.ownerSVGElement,!n&&iH?[iH]:i?null:t.firstChild?ih.call(t.childNodes):null,a,!n&&iH?iH:i?i.__e:t.firstChild,n),iM(a,r)}ih=iv.slice,ig={__e:function(e,t,r,n){for(var i,a,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},iy=0,iA.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=iE({},this.state),"function"==typeof e&&(e=e(iE({},r),this.props)),e&&iE(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),iR(this))},iA.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),iR(this))},iA.prototype.render=ix,im=[],iP.__r=0;var iW=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,iK=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,iq=/[\s\n\\/='"\0<>]/,iB=/^xlink:?./,iJ=/["&<]/;function iV(e){if(!1===iJ.test(e+=""))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var iz=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},iF=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},iG={},iX=/([A-Z])/g;function iY(e){var t="";for(var r in e){var n=e[r];null!=n&&""!==n&&(t&&(t+=" "),t+="-"==r[0]?r:iG[r]||(iG[r]=r.replace(iX,"-$1").toLowerCase()),t="number"==typeof n&&!1===iW.test(r)?t+": "+n+"px;":t+": "+n+";")}return t||void 0}function iQ(e,t){return Array.isArray(t)?t.reduce(iQ,e):null!=t&&!1!==t&&e.push(t),e}function iZ(){this.__d=!0}function i0(e,t){return{__v:e,context:t,props:e.props,setState:iZ,forceUpdate:iZ,__d:!0,__h:[]}}function i1(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var i2=[],i3={shallow:!0};i4.render=i4;var i5=[];function i4(e,t,r){t=t||{};var n,i=ig.__s;return ig.__s=!0,n=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?function e(t,r,n,i,a,o){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return iV(t);var s=n.pretty,c=s&&"string"==typeof s?s:"	";if(Array.isArray(t)){for(var l="",u=0;u<t.length;u++)s&&u>0&&(l+="\n"),l+=e(t[u],r,n,i,a,o);return l}var d,p=t.type,h=t.props,f=!1;if("function"==typeof p){if(f=!0,!n.shallow||!i&&!1!==n.renderRootComponent){if(p===ix){var g=[];return iQ(g,t.props.children),e(g,r,n,!1!==n.shallowHighOrder,a,o)}var y,m=t.__c=i0(t,r);ig.__b&&ig.__b(t);var b=ig.__r;if(p.prototype&&"function"==typeof p.prototype.render){var w=i1(p,r);(m=t.__c=new p(h,w)).__v=t,m._dirty=m.__d=!0,m.props=h,null==m.state&&(m.state={}),null==m._nextState&&null==m.__s&&(m._nextState=m.__s=m.state),m.context=w,p.getDerivedStateFromProps?m.state=Object.assign({},m.state,p.getDerivedStateFromProps(m.props,m.state)):m.componentWillMount&&(m.componentWillMount(),m.state=m._nextState!==m.state?m._nextState:m.__s!==m.state?m.__s:m.state),b&&b(t),y=m.render(m.props,m.state,m.context)}else for(var v=i1(p,r),_=0;m.__d&&_++<25;)m.__d=!1,b&&b(t),y=p.call(t.__c,h,v);return m.getChildContext&&(r=Object.assign({},r,m.getChildContext())),ig.diffed&&ig.diffed(t),e(y,r,n,!1!==n.shallowHighOrder,a,o)}p=(d=p).displayName||d!==Function&&d.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=i2.length;n--;)if(i2[n]===e){r=n;break}r<0&&(r=i2.push(e)-1),t="UnnamedComponent"+r}return t}(d)}var E,S,k="<"+p;if(h){var x=Object.keys(h);n&&!0===n.sortAttributes&&x.sort();for(var A=0;A<x.length;A++){var T=x[A],R=h[T];if("children"!==T){if(!iq.test(T)&&(n&&n.allAttributes||"key"!==T&&"ref"!==T&&"__self"!==T&&"__source"!==T)){if("defaultValue"===T)T="value";else if("defaultChecked"===T)T="checked";else if("defaultSelected"===T)T="selected";else if("className"===T){if(void 0!==h.class)continue;T="class"}else a&&iB.test(T)&&(T=T.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===T){if(h.for)continue;T="for"}"style"===T&&R&&"object"==typeof R&&(R=iY(R)),"a"===T[0]&&"r"===T[1]&&"boolean"==typeof R&&(R=String(R));var P=n.attributeHook&&n.attributeHook(T,R,r,n,f);if(P||""===P)k+=P;else if("dangerouslySetInnerHTML"===T)S=R&&R.__html;else if("textarea"===p&&"value"===T)E=R;else if((R||0===R||""===R)&&"function"!=typeof R){if(!(!0!==R&&""!==R||(R=T,n&&n.xml))){k=k+" "+T;continue}if("value"===T){if("select"===p){o=R;continue}"option"===p&&o==R&&void 0===h.selected&&(k+=" selected")}k=k+" "+T+'="'+iV(R)+'"'}}}else E=R}}if(s){var O=k.replace(/\n\s*/," ");O===k||~O.indexOf("\n")?s&&~k.indexOf("\n")&&(k+="\n"):k=O}if(k+=">",iq.test(p))throw Error(p+" is not a valid HTML tag name in "+k);var C,I=iK.test(p)||n.voidElements&&n.voidElements.test(p),N=[];if(S)s&&iF(S)&&(S="\n"+c+iz(S,c)),k+=S;else if(null!=E&&iQ(C=[],E).length){for(var U=s&&~k.indexOf("\n"),j=!1,D=0;D<C.length;D++){var M=C[D];if(null!=M&&!1!==M){var $=e(M,r,n,!0,"svg"===p||"foreignObject"!==p&&a,o);if(s&&!U&&iF($)&&(U=!0),$)if(s){var L=$.length>0&&"<"!=$[0];j&&L?N[N.length-1]+=$:N.push($),j=L}else N.push($)}}if(s&&U)for(var H=N.length;H--;)N[H]="\n"+c+iz(N[H],c)}if(N.length||S)k+=N.join("");else if(n&&n.xml)return k.substring(0,k.length-1)+" />";return!I||C||S?(s&&~k.indexOf("\n")&&(k+="\n"),k=k+"</"+p+">"):k=k.replace(/>$/," />"),k}(e,t,r):function e(t,r,n,i){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return iV(t);if(i6(t)){for(var a="",o=0;o<t.length;o++)a+=e(t[o],r,n,i);return a}ig.__b&&ig.__b(t);var s=t.type,c=t.props;if("function"==typeof s){if(s===ix)return e(t.props.children,r,n,i);var l,u,d,p,h,f=s.prototype&&"function"==typeof s.prototype.render?(l=r,d=i1(u=t.type,l),p=new u(t.props,d),t.__c=p,p.__v=t,p.__d=!0,p.props=t.props,null==p.state&&(p.state={}),null==p.__s&&(p.__s=p.state),p.context=d,u.getDerivedStateFromProps?p.state=i8({},p.state,u.getDerivedStateFromProps(p.props,p.state)):p.componentWillMount&&(p.componentWillMount(),p.state=p.__s!==p.state?p.__s:p.state),(h=ig.__r)&&h(t),p.render(p.props,p.state,p.context)):function(e,t){var r,n=i0(e,t),i=i1(e.type,t);e.__c=n;for(var a=ig.__r,o=0;n.__d&&o++<25;)n.__d=!1,a&&a(e),r=e.type.call(n,e.props,i);return r}(t,r),g=t.__c;g.getChildContext&&(r=i8({},r,g.getChildContext()));var y=e(f,r,n,i);return ig.diffed&&ig.diffed(t),y}var m,b,w="<";if(w+=s,c)for(var v in m=c.children,c){var _,E,S,k=c[v];if(!("key"===v||"ref"===v||"__self"===v||"__source"===v||"children"===v||"className"===v&&"class"in c||"htmlFor"===v&&"for"in c||iq.test(v))){if(E=v="className"===(_=v)?"class":"htmlFor"===_?"for":"defaultValue"===_?"value":"defaultChecked"===_?"checked":"defaultSelected"===_?"selected":n&&iB.test(_)?_.toLowerCase().replace(/^xlink:?/,"xlink:"):_,S=k,k="style"===E&&null!=S&&"object"==typeof S?iY(S):"a"===E[0]&&"r"===E[1]&&"boolean"==typeof S?String(S):S,"dangerouslySetInnerHTML"===v)b=k&&k.__html;else if("textarea"===s&&"value"===v)m=k;else if((k||0===k||""===k)&&"function"!=typeof k){if(!0===k||""===k){k=v,w=w+" "+v;continue}if("value"===v){if("select"===s){i=k;continue}"option"!==s||i!=k||"selected"in c||(w+=" selected")}w=w+" "+v+'="'+iV(k)+'"'}}}var x=w;if(w+=">",iq.test(s))throw Error(s+" is not a valid HTML tag name in "+w);var A="",T=!1;if(b)A+=b,T=!0;else if("string"==typeof m)A+=iV(m),T=!0;else if(i6(m))for(var R=0;R<m.length;R++){var P=m[R];if(null!=P&&!1!==P){var O=e(P,r,"svg"===s||"foreignObject"!==s&&n,i);O&&(A+=O,T=!0)}}else if(null!=m&&!1!==m&&!0!==m){var C=e(m,r,"svg"===s||"foreignObject"!==s&&n,i);C&&(A+=C,T=!0)}if(ig.diffed&&ig.diffed(t),T)w+=A;else if(iK.test(s))return x+" />";return w+"</"+s+">"}(e,t,!1,void 0),ig.__c&&ig.__c(e,i5),ig.__s=i,i5.length=0,n}var i6=Array.isArray,i8=Object.assign;i4.shallowRender=function(e,t){return i4(e,t,i3)};var i9=0;function i7(e,t,r,n,i){var a,o,s={};for(o in t)"ref"==o?a=t[o]:s[o]=t[o];var c={type:e,props:s,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--i9,__source:i,__self:n};if("function"==typeof e&&(a=e.defaultProps))for(o in a)void 0===s[o]&&(s[o]=a[o]);return ig.vnode&&ig.vnode(c),c}async function ae(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let at={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},ar=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function an({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${ar}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${i4(e)}</div></body></html>`}}function ai(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,s){if(t)throw new tD("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return an({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:o}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=o&&(at[o]??at.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return i7("div",{className:"signin",children:[i?.brandColor&&i7("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&i7("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),i7("div",{className:"card",children:[s&&i7("div",{className:"error",children:i7("p",{children:s})}),i?.logo&&i7("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return i7("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?i7("form",{action:e.signinUrl,method:"POST",children:[i7("input",{type:"hidden",name:"csrfToken",value:t}),n&&i7("input",{type:"hidden",name:"callbackUrl",value:n}),i7("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${l} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${l} 30%, #000)`},tabIndex:0,children:[i7("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&i7("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&i7("hr",{}),"email"===e.type&&i7("form",{action:e.signinUrl,method:"POST",children:[i7("input",{type:"hidden",name:"csrfToken",value:t}),i7("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),i7("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),i7("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&i7("form",{action:e.callbackUrl,method:"POST",children:[i7("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>i7("div",{children:[i7("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),i7("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),i7("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&i7("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[i7("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>i7("div",{children:[i7("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),i7("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),i7("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&i7("hr",{})]},e.id)})]}),c&&i7(ix,{children:i7("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${ae})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:an({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return i7("div",{className:"signout",children:[n?.brandColor&&i7("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&i7("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),i7("div",{className:"card",children:[n?.logo&&i7("img",{src:n.logo,alt:"Logo",className:"logo"}),i7("h1",{children:"Signout"}),i7("p",{children:"Are you sure you want to sign out?"}),i7("form",{action:t?.toString(),method:"POST",children:[i7("input",{type:"hidden",name:"csrfToken",value:r}),i7("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:a.verifyRequest,cookies:i}:an({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return i7("div",{className:"verify-request",children:[r.brandColor&&i7("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),i7("div",{className:"card",children:[r.logo&&i7("img",{src:r.logo,alt:"Logo",className:"logo"}),i7("h1",{children:"Check your email"}),i7("p",{children:"A sign in link has been sent to your email address."}),i7("p",{children:i7("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:an({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:i7("p",{children:i7("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:i7("div",{children:[i7("p",{children:"There is a problem with the server configuration."}),i7("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:i7("div",{children:[i7("p",{children:"You do not have permission to sign in."}),i7("p",{children:i7("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:i7("div",{children:[i7("p",{children:"The sign in link is no longer valid."}),i7("p",{children:"It may have been used already or it may have expired."})]}),signin:i7("a",{className:"button",href:i,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=a[r]??a.default;return{status:o,html:i7("div",{className:"error",children:[n?.brandColor&&i7("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),i7("div",{className:"card",children:[n?.logo&&i7("img",{src:n?.logo,alt:"Logo",className:"logo"}),i7("h1",{children:s}),i7("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function aa(e,t=Date.now()){return new Date(t+1e3*e)}async function ao(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:h,getUserByEmail:f,linkAccount:g,createSession:y,getSessionAndUser:m,deleteSession:b}=i,w=null,v=null,_=!1,E="jwt"===s;if(e)if(E)try{let t=n.cookies.sessionToken.name;(w=await a.decode({...a,token:e,salt:t}))&&"sub"in w&&w.sub&&(v=await p(w.sub))}catch{}else{let t=await m(e);t&&(w=t.session,v=t.user)}if("email"===l.type){let r=await f(t.email);return r?(v?.id!==r.id&&!E&&e&&await b(e),v=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:v})):(v=await u({...t,emailVerified:new Date}),await o.createUser?.({user:v}),_=!0),{session:w=E?{}:await y({sessionToken:c(),userId:v.id,expires:aa(n.session.maxAge)}),user:v,isNewUser:_}}if("webauthn"===l.type){let e=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(v){if(e.id===v.id){let e={...l,userId:v.id};return{session:w,user:v,isNewUser:_,account:e}}throw new tV("The account is already associated with another user",{provider:l.provider})}w=E?{}:await y({sessionToken:c(),userId:e.id,expires:aa(n.session.maxAge)});let t={...l,userId:e.id};return{session:w,user:e,isNewUser:_,account:t}}{if(v){await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:_,account:e}}if(t.email?await f(t.email):null)throw new tV("Another account already exists with the same e-mail address",{provider:l.provider});v=await u({...t}),await o.createUser?.({user:v}),await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),w=E?{}:await y({sessionToken:c(),userId:v.id,expires:aa(n.session.maxAge)});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:!0,account:e}}}let S=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(S){if(v){if(S.id===v.id)return{session:w,user:v,isNewUser:_};throw new tP("The account is already associated with another user",{provider:l.provider})}return{session:w=E?{}:await y({sessionToken:c(),userId:S.id,expires:aa(n.session.maxAge)}),user:S,isNewUser:_}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:s}),v)return await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:w,user:v,isNewUser:_};let p=t.email?await f(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)v=p,_=!1;else throw new tP("Another account already exists with the same e-mail address",{provider:l.provider})}else v=await u({...t,emailVerified:null}),_=!0;return await o.createUser?.({user:v}),await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:w=E?{}:await y({sessionToken:c(),userId:v.id,expires:aa(n.session.maxAge)}),user:v,isNewUser:_}}}function as(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(o="oauth4webapi/v3.5.2");let ac="ERR_INVALID_ARG_VALUE",al="ERR_INVALID_ARG_TYPE";function au(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let ad=Symbol(),ap=Symbol(),ah=Symbol(),af=Symbol(),ag=Symbol(),ay=Symbol(),am=Symbol(),ab=new TextEncoder,aw=new TextDecoder;function av(e){return"string"==typeof e?ab.encode(e):aw.decode(e)}function a_(e){return"string"==typeof e?c(e):s(e)}s=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},c=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw au("The input to be decoded is not correctly encoded.",ac,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw au("The input to be decoded is not correctly encoded.",ac,e)}};class aE extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oC,Error.captureStackTrace?.(this,this.constructor)}}class aS extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function ak(e,t,r){return new aS(e,{code:t,cause:r})}function ax(e,t){if(!(e instanceof CryptoKey))throw au(`${t} must be a CryptoKey`,al)}function aA(e,t){if(ax(e,t),"private"!==e.type)throw au(`${t} must be a private CryptoKey`,ac)}function aT(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function aR(e){as(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(o&&!t.has("user-agent")&&t.set("user-agent",o),t.has("authorization"))throw au('"options.headers" must not include the "authorization" header name',ac);return t}function aP(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw au('"options.signal" must return or be an instance of AbortSignal',al);return e}function aO(e){return e.includes("//")?e.replace("//","/"):e}async function aC(e,t,r,n){if(!(e instanceof URL))throw au(`"${t}" must be an instance of URL`,al);aY(e,n?.[ad]!==!0);let i=r(new URL(e.href)),a=aR(n?.headers);return a.set("accept","application/json"),(n?.[af]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:n?.signal?aP(n.signal):void 0})}async function aI(e,t){return aC(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=aO(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=aO(`${n}/${r.pathname}`);break;default:throw au('"options.algorithm" must be "oidc" (default), or "oauth2"',ac)}return e},t)}function aN(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw au(`${r} must be a number`,al,i);if(e>0)return;if(t){if(0!==e)throw au(`${r} must be a non-negative number`,ac,i);return}throw au(`${r} must be a positive number`,ac,i)}catch(e){if(n)throw ak(e.message,n,i);throw e}}function aU(e,t,r,n){try{if("string"!=typeof e)throw au(`${t} must be a string`,al,n);if(0===e.length)throw au(`${t} must not be empty`,ac,n)}catch(e){if(r)throw ak(e.message,r,n);throw e}}async function aj(e,t){if(!(e instanceof URL)&&e!==sa)throw au('"expectedIssuerIdentifier" must be an instance of URL',al);if(!as(t,Response))throw au('"response" must be an instance of Response',al);if(200!==t.status)throw ak('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',o$,t);oF(t);let r=await sn(t);if(aU(r.issuer,'"response" body "issuer" property',oj,{body:r}),e!==sa&&new URL(r.issuer).href!==e.href)throw ak('"response" body "issuer" property does not match the expected value',oq,{expected:e.href,body:r,attribute:"issuer"});return r}function aD(e){var t=e,r="application/json";if(os(t)!==r)throw aM(t,r)}function aM(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return ak(r,oM,e)}function a$(){return a_(crypto.getRandomValues(new Uint8Array(32)))}async function aL(e){return aU(e,"codeVerifier"),a_(await crypto.subtle.digest("SHA-256",av(e)))}function aH(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new aE("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new aE("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new aE("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new aE("unsupported CryptoKey algorithm name",{cause:e})}}function aW(e){let t=e?.[ap];return"number"==typeof t&&Number.isFinite(t)?t:0}function aK(e){let t=e?.[ah];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function aq(){return Math.floor(Date.now()/1e3)}function aB(e){if("object"!=typeof e||null===e)throw au('"as" must be an object',al);aU(e.issuer,'"as.issuer"')}function aJ(e){if("object"!=typeof e||null===e)throw au('"client" must be an object',al);aU(e.client_id,'"client.client_id"')}function aV(e,t){let r=aq()+aW(t);return{jti:a$(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function az(e,t,r){if(!r.usages.includes("sign"))throw au('CryptoKey instances used for signing assertions must include "sign" in their "usages"',ac);let n=`${a_(av(JSON.stringify(e)))}.${a_(av(JSON.stringify(t)))}`,i=a_(await crypto.subtle.sign(oZ(r),r,av(n)));return`${n}.${i}`}async function aF(e){let{kty:t,e:r,n,x:i,y:a,crv:o}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:i,y:a,crv:o};return l.set(e,s),s}async function aG(e){return(l||=new WeakMap).get(e)||aF(e)}let aX=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function aY(e,t){if(t&&"https:"!==e.protocol)throw ak("only requests to HTTPS are allowed",oL,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw ak("only HTTP and HTTPS requests are allowed",oH,e)}function aQ(e,t,r,n){let i;if("string"!=typeof e||!(i=aX(e)))throw ak(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?oJ:oV,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return aY(i,n),i}function aZ(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?aQ(e.mtls_endpoint_aliases[t],t,r,n):aQ(e[t],t,r,n)}class a0 extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oO,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class a1 extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oI,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class a2 extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oP,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let a3="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",a5=RegExp("^[,\\s]*("+a3+")\\s(.*)"),a4=RegExp("^[,\\s]*("+a3+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),a6=RegExp("^[,\\s]*"+("("+a3+")\\s*=\\s*(")+a3+")[,\\s]*(.*)"),a8=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function a9(e){if(e.status>399&&e.status<500){oF(e),aD(e);try{let t=await e.clone().json();if(aT(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function a7(e,t,r){if(e.status!==t){let t;if(t=await a9(e))throw await e.body?.cancel(),new a0("server responded with an error in the response body",{cause:t,response:e});throw ak(`"response" is not a conform ${r} response (unexpected HTTP status code)`,o$,e)}}function oe(e){if(!ov.has(e))throw au('"options.DPoP" is not a valid DPoPHandle',ac)}async function ot(e,t,r,n,i,a){if(aU(e,'"accessToken"'),!(r instanceof URL))throw au('"url" must be an instance of URL',al);aY(r,a?.[ad]!==!0),n=aR(n),a?.DPoP&&(oe(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (a?.[af]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:a?.signal?aP(a.signal):void 0});return a?.DPoP?.cacheNonce(o),o}async function or(e,t,r,n){aB(e),aJ(t);let i=aZ(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[ad]!==!0),a=aR(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),ot(r,"GET",i,a,null,{...n,[ap]:aW(t)})}function on(e,t,r,n){(u||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return aq()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function oi(e,t){u?.delete(e),delete t?.jwks,delete t?.uat}async function oa(e,t,r){var n;let i,a,o,{alg:s,kid:c}=r;if(function(e){if(!oY(e.alg))throw new aE('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!u?.has(e)&&!("object"!=typeof(n=t?.[am])||null===n||!("uat"in n)||"number"!=typeof n.uat||aq()-n.uat>=300)&&"jwks"in n&&aT(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,aT)&&on(e,t?.[am].jwks,t?.[am].uat),u?.has(e)){if({jwks:i,age:a}=u.get(e),a>=300)return oi(e,t?.[am]),oa(e,t,r)}else i=await oG(e,t).then(oX),a=0,on(e,i,aq(),t?.[am]);switch(s.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new aE("unsupported JWS algorithm",{cause:{alg:s}})}let l=i.keys.filter(e=>{if(e.kty!==o||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=l;if(!p){if(a>=60)return oi(e,t?.[am]),oa(e,t,r);throw ak("error when selecting a JWT verification key, no applicable keys found",oB,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw ak('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',oB,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)});return st(s,d)}let oo=Symbol();function os(e){return e.headers.get("content-type")?.split(";")[0]}async function oc(e,t,r,n,i){let a;if(aB(e),aJ(t),!as(n,Response))throw au('"response" must be an instance of Response',al);if(og(n),200!==n.status)throw ak('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',o$,n);if(oF(n),"application/jwt"===os(n)){let{claims:r,jwt:o}=await o1(await n.text(),o6.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),aW(t),aK(t),i?.[ay]).then(oy.bind(void 0,t.client_id)).then(ob.bind(void 0,e));op.set(n,o),a=r}else{if(t.userinfo_signed_response_alg)throw ak("JWT UserInfo Response expected",oN,n);a=await sn(n)}if(aU(a.sub,'"response" body "sub" property',oj,{body:a}),r===oo);else if(aU(r,'"expectedSubject"'),a.sub!==r)throw ak('unexpected "response" body "sub" property value',oq,{expected:r,body:a,attribute:"sub"});return a}async function ol(e,t,r,n,i,a,o){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[af]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:o?.signal?aP(o.signal):void 0})}async function ou(e,t,r,n,i,a){let o=aZ(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[ad]!==!0);i.set("grant_type",n);let s=aR(a?.headers);s.set("accept","application/json"),a?.DPoP!==void 0&&(oe(a.DPoP),await a.DPoP.addProof(o,s,"POST"));let c=await ol(e,t,r,o,i,s,a);return a?.DPoP?.cacheNonce(c),c}let od=new WeakMap,op=new WeakMap;function oh(e){if(!e.id_token)return;let t=od.get(e);if(!t)throw au('"ref" was already garbage collected or did not resolve from the proper sources',ac);return t}async function of(e,t,r,n,i){if(aB(e),aJ(t),!as(r,Response))throw au('"response" must be an instance of Response',al);og(r),await a7(r,200,"Token Endpoint"),oF(r);let a=await sn(r);if(aU(a.access_token,'"response" body "access_token" property',oj,{body:a}),aU(a.token_type,'"response" body "token_type" property',oj,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new aE("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;aN(e,!1,'"response" body "expires_in" property',oj,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&aU(a.refresh_token,'"response" body "refresh_token" property',oj,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw ak('"response" body "scope" property must be a string',oj,{body:a});if(void 0!==a.id_token){aU(a.id_token,'"response" body "id_token" property',oj,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(aN(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await o1(a.id_token,o6.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),aW(t),aK(t),i?.[ay]).then(oS.bind(void 0,o)).then(ow.bind(void 0,e)).then(om.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw ak('ID Token "aud" (audience) claim includes additional untrusted audiences',oK,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw ak('unexpected ID Token "azp" (authorized party) claim value',oK,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&aN(s.auth_time,!1,'ID Token "auth_time" (authentication time)',oj,{claims:s}),op.set(r,c),od.set(a,s)}return a}function og(e){let t;if(t=function(e){if(!as(e,Response))throw au('"response" must be an instance of Response',al);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(a5),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(a4)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(a6)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(a8)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let o={scheme:i,parameters:a};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new a2("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function oy(e,t){return void 0!==t.claims.aud?om(e,t):t}function om(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw ak('unexpected JWT "aud" (audience) claim value',oK,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw ak('unexpected JWT "aud" (audience) claim value',oK,{expected:e,claims:t.claims,claim:"aud"});return t}function ob(e,t){return void 0!==t.claims.iss?ow(e,t):t}function ow(e,t){let r=e[so]?.(t)??e.issuer;if(t.claims.iss!==r)throw ak('unexpected JWT "iss" (issuer) claim value',oK,{expected:r,claims:t.claims,claim:"iss"});return t}let ov=new WeakSet;async function o_(e,t,r,n,i,a,o){if(aB(e),aJ(t),!ov.has(n))throw au('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',ac);aU(i,'"redirectUri"');let s=o8(n,"code");if(!s)throw ak('no authorization code in "callbackParameters"',oj);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),a!==si&&(aU(a,'"codeVerifier"'),c.set("code_verifier",a)),ou(e,t,r,"authorization_code",c,o)}let oE={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function oS(e,t){for(let r of e)if(void 0===t.claims[r])throw ak(`JWT "${r}" (${oE[r]}) claim missing`,oj,{claims:t.claims});return t}let ok=Symbol(),ox=Symbol();async function oA(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?oT(e,t,r,n.expectedNonce,n.maxAge,{[ay]:n[ay]}):oR(e,t,r,n)}async function oT(e,t,r,n,i,a){let o=[];switch(n){case void 0:n=ok;break;case ok:break;default:aU(n,'"expectedNonce" argument'),o.push("nonce")}switch(i??=t.default_max_age){case void 0:i=ox;break;case ox:break;default:aN(i,!1,'"maxAge" argument'),o.push("auth_time")}let s=await of(e,t,r,o,a);aU(s.id_token,'"response" body "id_token" property',oj,{body:s});let c=oh(s);if(i!==ox){let e=aq()+aW(t),r=aK(t);if(c.auth_time+i<e-r)throw ak("too much time has elapsed since the last End-User authentication",oW,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===ok){if(void 0!==c.nonce)throw ak('unexpected ID Token "nonce" claim value',oK,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw ak('unexpected ID Token "nonce" claim value',oK,{expected:n,claims:c,claim:"nonce"});return s}async function oR(e,t,r,n){let i=await of(e,t,r,void 0,n),a=oh(i);if(a){if(void 0!==t.default_max_age){aN(t.default_max_age,!1,'"client.default_max_age"');let e=aq()+aW(t),r=aK(t);if(a.auth_time+t.default_max_age<e-r)throw ak("too much time has elapsed since the last End-User authentication",oW,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw ak('unexpected ID Token "nonce" claim value',oK,{expected:void 0,claims:a,claim:"nonce"})}return i}let oP="OAUTH_WWW_AUTHENTICATE_CHALLENGE",oO="OAUTH_RESPONSE_BODY_ERROR",oC="OAUTH_UNSUPPORTED_OPERATION",oI="OAUTH_AUTHORIZATION_RESPONSE_ERROR",oN="OAUTH_JWT_USERINFO_EXPECTED",oU="OAUTH_PARSE_ERROR",oj="OAUTH_INVALID_RESPONSE",oD="OAUTH_INVALID_REQUEST",oM="OAUTH_RESPONSE_IS_NOT_JSON",o$="OAUTH_RESPONSE_IS_NOT_CONFORM",oL="OAUTH_HTTP_REQUEST_FORBIDDEN",oH="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",oW="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",oK="OAUTH_JWT_CLAIM_COMPARISON_FAILED",oq="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",oB="OAUTH_KEY_SELECTION_FAILED",oJ="OAUTH_MISSING_SERVER_METADATA",oV="OAUTH_INVALID_SERVER_METADATA";function oz(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw ak('unexpected JWT "typ" header parameter value',oj,{header:t.header});return t}function oF(e){if(e.bodyUsed)throw au('"response" body has been used already',ac)}async function oG(e,t){aB(e);let r=aZ(e,"jwks_uri",!1,t?.[ad]!==!0),n=aR(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[af]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?aP(t.signal):void 0})}async function oX(e){if(!as(e,Response))throw au('"response" must be an instance of Response',al);if(200!==e.status)throw ak('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',o$,e);oF(e);let t=await sn(e,e=>(function(e,...t){if(!t.includes(os(e)))throw aM(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw ak('"response" body "keys" property must be an array',oj,{body:t});if(!Array.prototype.every.call(t.keys,aT))throw ak('"response" body "keys" property members must be JWK formatted objects',oj,{body:t});return t}function oY(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function oQ(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new aE(`unsupported ${t.name} modulusLength`,{cause:e})}function oZ(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new aE("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(oQ(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new aE("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return oQ(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new aE("unsupported CryptoKey algorithm name",{cause:e})}async function o0(e,t,r,n){let i=av(`${e}.${t}`),a=oZ(r);if(!await crypto.subtle.verify(a,r,n,i))throw ak("JWT signature verification failed",oj,{key:r,data:i,signature:n,algorithm:a})}async function o1(e,t,r,n,i){let a,o,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new aE("JWE decryption is not configured",{cause:e});if(3!==l)throw ak("Invalid JWT",oj,e);try{a=JSON.parse(av(a_(s)))}catch(e){throw ak("failed to parse JWT Header body as base64url encoded JSON",oU,e)}if(!aT(a))throw ak("JWT Header must be a top level object",oj,e);if(t(a),void 0!==a.crit)throw new aE('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{o=JSON.parse(av(a_(c)))}catch(e){throw ak("failed to parse JWT Payload body as base64url encoded JSON",oU,e)}if(!aT(o))throw ak("JWT Payload must be a top level object",oj,e);let u=aq()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw ak('unexpected JWT "exp" (expiration time) claim type',oj,{claims:o});if(o.exp<=u-n)throw ak('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',oW,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw ak('unexpected JWT "iat" (issued at) claim type',oj,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw ak('unexpected JWT "iss" (issuer) claim type',oj,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw ak('unexpected JWT "nbf" (not before) claim type',oj,{claims:o});if(o.nbf>u+n)throw ak('unexpected JWT "nbf" (not before) claim value',oW,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw ak('unexpected JWT "aud" (audience) claim type',oj,{claims:o});return{header:a,claims:o,jwt:e}}async function o2(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new aE(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,av(e));return a_(i.slice(0,i.byteLength/2))}async function o3(e,t,r,n){return t===await o2(e,r,n)}async function o5(e){if(e.bodyUsed)throw au("form_post Request instances must contain a readable body",ac,{cause:e});return e.text()}async function o4(e){if("POST"!==e.method)throw au("form_post responses are expected to use the POST method",ac,{cause:e});if("application/x-www-form-urlencoded"!==os(e))throw au("form_post responses are expected to use the application/x-www-form-urlencoded content-type",ac,{cause:e});return o5(e)}function o6(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw ak('unexpected JWT "alg" header parameter',oj,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw ak('unexpected JWT "alg" header parameter',oj,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw ak('unexpected JWT "alg" header parameter',oj,{header:n,expected:r,reason:"default value"});return}throw ak('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function o8(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw ak(`"${t}" parameter must be provided only once`,oj);return r}let o9=Symbol(),o7=Symbol();function se(e,t,r,n){var i;if(aB(e),aJ(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw au('"parameters" must be an instance of URLSearchParams, or URL',al);if(o8(r,"response"))throw ak('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',oj,{parameters:r});let a=o8(r,"iss"),o=o8(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw ak('response parameter "iss" (issuer) missing',oj,{parameters:r});if(a&&a!==e.issuer)throw ak('unexpected "iss" (issuer) response parameter value',oj,{expected:e.issuer,parameters:r});switch(n){case void 0:case o7:if(void 0!==o)throw ak('unexpected "state" response parameter encountered',oj,{expected:void 0,parameters:r});break;case o9:break;default:if(aU(n,'"expectedState" argument'),o!==n)throw ak(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',oj,{expected:n,parameters:r})}if(o8(r,"error"))throw new a1("authorization response from the server is an error",{cause:r});let s=o8(r,"id_token"),c=o8(r,"token");if(void 0!==s||void 0!==c)throw new aE("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),ov.add(i),i}async function st(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new aE("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function sr(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function sn(e,t=aD){let r;try{r=await e.json()}catch(r){throw t(e),ak('failed to parse "response" body as JSON',oU,r)}if(!aT(r))throw ak('"response" body must be a top level object',oj,{body:r});return r}let si=Symbol(),sa=Symbol(),so=Symbol();async function ss(e,t,r){let{cookies:n,logger:i}=r,a=n[e],o=new Date;o.setTime(o.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:o});let s=await nK({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:o};return{name:a.name,value:s,options:c}}async function sc(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new tS(`${e} cookie was missing`);let o=await nq({...a,token:t,salt:i[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new tS(`${e} value could not be parsed`,{cause:t})}}function sl(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function su(e,t){return async function(r,n,i){let{provider:a,logger:o}=i;if(!a?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await sc(t,s,i);return sl(t,i,n),c}}let sd={async create(e){let t=a$(),r=await aL(t);return{cookie:await ss("pkceCodeVerifier",t,e),value:r}},use:su("pkce","pkceCodeVerifier")},sp="encodedState",sh={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new tS("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:a$()},i=await nK({secret:e.jwt.secret,token:n,salt:sp,maxAge:900});return{cookie:await ss("state",i,e),value:i}},use:su("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await nq({secret:t.jwt.secret,token:e,salt:sp});if(r)return r;throw Error("Invalid state")}catch(e){throw new tS("State could not be decoded",{cause:e})}}},sf={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=a$();return{cookie:await ss("nonce",t,e),value:t}},use:su("nonce","nonce")},sg="encodedWebauthnChallenge",sy={create:async(e,t,r)=>({cookie:await ss("webauthnChallenge",await nK({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:sg,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await sc("webauthnChallenge",n,e),a=await nq({secret:e.jwt.secret,token:i,salt:sg});if(sl("webauthnChallenge",e,r),!a)throw new tS("WebAuthn challenge was missing");return a}};function sm(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function sb(e,t,r){let n,i,a,{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await aI(e,{[ad]:!0,[af]:s[n9]});if(!(n=await aj(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=sm(e),n=sm(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;aU(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;aU(e,'"clientSecret"');let n=void 0;return async(t,i,a,o)=>{r||=await crypto.subtle.importKey("raw",av(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=aV(t,i);n?.(s,c);let l=`${a_(av(JSON.stringify(s)))}.${a_(av(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,av(l));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${l}.${a_(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){var r;let{key:n,kid:i}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&aU(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return aA(n,'"clientPrivateKey.key"'),async(e,r,a,o)=>{let s={alg:aH(n),kid:i},c=aV(e,r);t?.[ag]?.(s,c),a.set("client_id",r.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await az(s,c,n))}}(s.token.clientPrivateKey,{[ag](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],h=await sh.use(t,p,r);try{a=se(n,u,new URLSearchParams(e),s.checks.includes("state")?h:o9)}catch(e){if(e instanceof a1){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new tO("OAuth Provider returned an error",t)}throw e}let f=await sd.use(t,p,r),g=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(g=s.redirectProxyUrl);let y=await o_(n,u,i,a,g,f??"decoy",{[ad]:!0,[af]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[n9]??fetch)(...e))});s.token?.conform&&(y=await s.token.conform(y.clone())??y);let m={},b="oidc"===s.type;if(s[n7])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let{tid:e}=function(e){let t,r;if("string"!=typeof e)throw new rw("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new rw("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new rw("Invalid JWT");if(!n)throw new rw("JWTs must contain a payload");try{t=ru(n)}catch{throw new rw("Failed to base64url decode the payload")}try{r=JSON.parse(re.decode(t))}catch{throw new rw("Failed to parse the decoded payload as JSON")}if(!rA(r))throw new rw("Invalid JWT Claims Set");return r}((await y.clone().json()).id_token);if("string"==typeof e){let t=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(t,e)),i=await aI(r,{[af]:s[n9]});n=await aj(r,i)}}}let w=await oA(n,u,y,{expectedNonce:await sf.use(t,p,r),requireIdToken:b});if(b){let t=oh(w);if(m=t,s[n7]&&"apple"===s.id)try{m.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await or(n,u,w.access_token,{[af]:s[n9],[ad]:!0});m=await oc(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:w,provider:s});e instanceof Object&&(m=e)}else if(l?.url){let e=await or(n,u,w.access_token,{[af]:s[n9]});m=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await sw(m,s,w,o),profile:m,cookies:p}}async function sw(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new tC(r,{provider:t.id}))}}var sv=r(356).Buffer;async function s_(e,t,r,n){let i=await sA(e,t,r),{cookie:a}=await sy.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function sE(e,t,r,n){let i=await sx(e,t,r),{cookie:a}=await sy.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function sS(e,t,r){let n,{adapter:i,provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new th("Invalid WebAuthn Authentication response");let s=sP(sR(o.id)),c=await i.getAuthenticator(s);if(!c)throw new th(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await sy.use(e,t.cookies,r);try{var u;let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:sO(u.transports),credentialID:sR(u.credentialID),credentialPublicKey:sR(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tJ(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new tJ("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new tg(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let h=await i.getAccount(c.providerAccountId,a.id);if(!h)throw new th(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let f=await i.getUser(h.userId);if(!f)throw new th(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:h.userId})}`);return{account:h,user:f}}async function sk(e,t,r){var n;let i,{provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new th("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await sy.use(e,t.cookies,r);if(!c)throw new th("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tJ(e)}if(!i.verified||!i.registrationInfo)throw new tJ("WebAuthn registration response could not be verified");let l={providerAccountId:sP(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:sP(i.registrationInfo.credentialID),credentialPublicKey:sP(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function sx(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:a?.map(e=>({id:sR(e.credentialID),type:"public-key",transports:sO(e.transports)}))})}async function sA(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n1(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:a?.map(e=>({id:sR(e.credentialID),type:"public-key",transports:sO(e.transports)}))})}function sT(e){let{provider:t,adapter:r}=e;if(!r)throw new tx("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new t$("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function sR(e){return new Uint8Array(sv.from(e,"base64"))}function sP(e){return sv.from(e).toString("base64")}function sO(e){return e?e.split(","):void 0}async function sC(e,t,r,n){if(!t.provider)throw new t$("Callback route called without provider");let{query:i,body:a,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:h,events:f,callbacks:g,session:{strategy:y,maxAge:m},logger:b}=t,w="jwt"===y;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&s?.state){let e=await sh.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return b.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let y=await sb(s,e.cookies,t);y.cookies.length&&n.push(...y.cookies),b.debug("authorization result",y);let{user:v,account:_,profile:E}=y;if(!v||!_||!E)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:_.providerAccountId,provider:c.id})}let S=await sI({user:o??v,account:_,profile:E},t);if(S)return{redirect:S,cookies:n};let{user:k,session:x,isNewUser:A}=await ao(r.value,v,_,t);if(w){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},i=await g.jwt({token:e,user:k,account:_,profile:E,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:x.sessionToken,options:{...t.cookies.sessionToken.options,expires:x.expires}});if(await f.signIn?.({user:k,account:_,profile:E,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:a,token:await n0(`${e}${o}`)}),u=!!s,y=u&&s.expires.valueOf()<Date.now();if(!u||y||a&&s.identifier!==a)throw new tH({hasInvite:u,expired:y});let{identifier:b}=s,v=await l.getUserByEmail(b)??{id:crypto.randomUUID(),email:b,emailVerified:null},_={providerAccountId:v.email,userId:v.id,type:"email",provider:c.id},E=await sI({user:v,account:_},t);if(E)return{redirect:E,cookies:n};let{user:S,session:k,isNewUser:x}=await ao(r.value,v,_,t);if(w){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await g.jwt({token:e,user:S,account:_,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:k.sessionToken,options:{...t.cookies.sessionToken.options,expires:k.expires}});if(await f.signIn?.({user:S,account:_,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(a)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new t_;let p={providerAccountId:l.id,type:"credentials",provider:c.id},y=await sI({user:l,account:p,credentials:e},t);if(y)return{redirect:y,cookies:n};let b={name:l.name,email:l.email,picture:l.image,sub:l.id},w=await g.jwt({token:b,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:w,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*m);let o=r.chunk(i,{expires:a});n.push(...o)}return await f.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let i,a,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new th("Invalid action parameter");let c=sT(t);switch(s){case"authenticate":{let t=await sS(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await sk(t,e,n);i=r.user,a=r.account,o=r.authenticator}}await sI({user:i,account:a},t);let{user:l,isNewUser:u,session:y,account:b}=await ao(r.value,i,a,t);if(!b)throw new th("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),w){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await g.jwt({token:e,user:l,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:y.sessionToken,options:{...t.cookies.sessionToken.options,expires:y.expires}});if(await f.signIn?.({user:l,account:b,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new t$(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof th)throw t;let e=new tm(t,{provider:c.id});throw b.debug("callback route error details",{method:o,query:i,body:a}),e}}async function sI(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof th)throw e;throw new ty(e)}if(!r)throw new ty("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function sN(e,t,r,n,i){let{adapter:a,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json"},cookies:r},h=t.value;if(!h)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,a=await o.decode({...o,token:h,salt:r});if(!a)throw Error("Invalid JWT");let l=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),u=aa(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let i=await o.encode({...o,token:l,salt:r}),a=t.chunk(i,{expires:u});p.cookies?.push(...a),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new tk(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=a,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(h),u=null),u){let{user:t,session:r}=u,a=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*a,f=aa(d);o<=Date.now()&&await l({sessionToken:h,expires:f});let g=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=g,p.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:f}}),await s.session?.({session:g})}else h&&p.cookies?.push(...t.clean())}catch(e){l.error(new tI(e))}return p}async function sU(e,t){let r,n,{logger:i,provider:a}=t,o=a.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(a.issuer),t=await aI(e,{[af]:a[n9],[ad]:!0}),r=await aj(e,t);if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await sh.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),a.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await sd.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let p=await sf.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==a.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:o,cookies:u,provider:a}),{redirect:o.toString(),cookies:u}}async function sj(e,t){let r,{body:n}=e,{provider:i,callbacks:a,adapter:o}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:i.id};try{r=await a.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new ty(e)}if(!r)throw new ty("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,h=await i.generateVerificationToken?.()??n1(32),f=new Date(Date.now()+(i.maxAge??86400)*1e3),g=i.secret??t.secret,y=new URL(t.basePath,t.url.origin),m=i.sendVerificationRequest({identifier:s,token:h,expires:f,url:`${y}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:h,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),b=o.createVerificationToken?.({identifier:s,token:await n0(`${h}${g}`),expires:f});return await Promise.all([m,b]),{redirect:`${y}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function sD(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await sU(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await sj(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function sM(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){o.error(new tj(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function s$(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function sL(e,t,r,n){let i=sT(t),{provider:a}=i,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await s$(t,r),c=s?{user:s,exists:!0}:await a.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return sE(i,e,l,n);case"register":if("string"==typeof l?.email)return s_(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function sH(e,t){let{action:r,providerId:n,error:i,method:a}=e,o=t.skipCSRFCheck===n6,{options:s,cookies:c}=await io({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:o}),l=new tp(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===a){let t=ai({...s,query:e.query,cookies:c});switch(r){case"callback":return await sC(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await sN(s,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await sL(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&n3(r,t),await sC(e,s,l,c);case"session":return n3(r,t),await sN(s,l,c,!0,e.body?.data);case"signin":return n3(r,t),await sD(e,c,s);case"signout":return n3(r,t),await sM(c,l,s)}}throw new tD(`Cannot handle action: ${r}`)}function sW(e,t,r,n,i){let a,o=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)a=new URL(s),o&&"/"!==o&&"/"!==a.pathname&&(a.pathname!==o&&nG(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function sK(e,t){let r=nG(t),n=await nQ(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!tF&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new tL(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new tR("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!tG(i,r.origin))return new tv(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=td(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(o&&!tG(o,r.origin))return new tv(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new tE(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)tX=!0;else if("email"===t.type)tY=!0;else if("webauthn"===t.type){var c;if(tQ=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new th(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new tq("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new tB(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(tX){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new tM("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new tT("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(tY||u?.strategy==="database"||!u?.strategy&&l)if(tY){if(!l)return new tx("Email login requires an adapter");d.push(...tZ)}else{if(!l)return new tx("Database session requires an adapter");d.push(...t0)}if(tQ){if(!t.experimental?.enableWebAuthn)return new tz("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new tx("WebAuthn requires an adapter");d.push(...t1)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new tA(`Required adapter methods were missing: ${e.join(", ")}`)}return tF||(tF=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new tb(`The error page ${e?.error} should not require authentication`)),nZ(ai({theme:a}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let a=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===n8;try{let e=await sH(n,t);if(o)return e;let r=nZ(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof th;if(i&&o&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof th&&tK.has(d.type)?d.type:"Configuration"});d instanceof t_&&s.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(a)return Response.json({url:u});return Response.redirect(u)}}r(352),"undefined"==typeof URLPattern||URLPattern;var sq=r(349),sB=r(74),sJ=r(785);function sV(){let e=e4.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}function sz(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new J(n.replace(i,r),e)}function sF(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||nG(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}new WeakMap;var sG=r(407);let sX={current:null},sY="function"==typeof sG.cache?sG.cache:e=>e,sQ=console.warn;function sZ(e){return function(...t){sQ(e(...t))}}function s0(){let e="cookies",t=en.J.getStore(),r=ei.FP.getStore();if(t){if(r&&"after"===r.phase&&!sV())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return s2(eo.seal(new q.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new sB.f(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var n=t.route,i=r;let e=s1.get(i);if(e)return e;let a=(0,sJ.W)(i.renderSignal,"`cookies()`");return s1.set(i,a),Object.defineProperties(a,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=s4(n,e);(0,sq.t3)(n,e,t,i)}},size:{get(){let e="`cookies().size`",t=s4(n,e);(0,sq.t3)(n,e,t,i)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${s3(arguments[0])})\``;let t=s4(n,e);(0,sq.t3)(n,e,t,i)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${s3(arguments[0])})\``;let t=s4(n,e);(0,sq.t3)(n,e,t,i)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${s3(arguments[0])})\``;let t=s4(n,e);(0,sq.t3)(n,e,t,i)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${s3(t)}, ...)\``:"`cookies().set(...)`"}let t=s4(n,e);(0,sq.t3)(n,e,t,i)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${s3(arguments[0])})\``:`\`cookies().delete(${s3(arguments[0])}, ...)\``;let t=s4(n,e);(0,sq.t3)(n,e,t,i)}},clear:{value:function(){let e="`cookies().clear()`",t=s4(n,e);(0,sq.t3)(n,e,t,i)}},toString:{value:function(){let e="`cookies().toString()`",t=s4(n,e);(0,sq.t3)(n,e,t,i)}}}),a}else"prerender-ppr"===r.type?(0,sq.Ui)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,sq.xI)(e,t,r);(0,sq.Pk)(t,r)}let a=(0,ei.XN)(e);return s2(el(a)?a.userspaceMutableCookies:a.cookies)}sY(e=>{try{sQ(sX.current)}finally{sX.current=null}});let s1=new WeakMap;function s2(e){let t=s1.get(e);if(t)return t;let r=Promise.resolve(e);return s1.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):s6.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):s8.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function s3(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let s5=sZ(s4);function s4(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function s6(){return this.getAll().map(e=>[e.name,e]).values()}function s8(e){for(let e of this.getAll())this.delete(e.name);return e}function s9(){let e=en.J.getStore(),t=ei.FP.getStore();if(e){if(t&&"after"===t.phase&&!sV())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return ce(er.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new sB.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,n=t;let i=s7.get(n);if(i)return i;let a=(0,sJ.W)(n.renderSignal,"`headers()`");return s7.set(n,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${ct(arguments[0])}, ...)\``,t=cn(r,e);(0,sq.t3)(r,e,t,n)}},delete:{value:function(){let e=`\`headers().delete(${ct(arguments[0])})\``,t=cn(r,e);(0,sq.t3)(r,e,t,n)}},get:{value:function(){let e=`\`headers().get(${ct(arguments[0])})\``,t=cn(r,e);(0,sq.t3)(r,e,t,n)}},has:{value:function(){let e=`\`headers().has(${ct(arguments[0])})\``,t=cn(r,e);(0,sq.t3)(r,e,t,n)}},set:{value:function(){let e=`\`headers().set(${ct(arguments[0])}, ...)\``,t=cn(r,e);(0,sq.t3)(r,e,t,n)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=cn(r,e);(0,sq.t3)(r,e,t,n)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=cn(r,e);(0,sq.t3)(r,e,t,n)}},keys:{value:function(){let e="`headers().keys()`",t=cn(r,e);(0,sq.t3)(r,e,t,n)}},values:{value:function(){let e="`headers().values()`",t=cn(r,e);(0,sq.t3)(r,e,t,n)}},entries:{value:function(){let e="`headers().entries()`",t=cn(r,e);(0,sq.t3)(r,e,t,n)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=cn(r,e);(0,sq.t3)(r,e,t,n)}}}),a}else"prerender-ppr"===t.type?(0,sq.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,sq.xI)("headers",e,t);(0,sq.Pk)(e,t)}return ce((0,ei.XN)("headers").headers)}let s7=new WeakMap;function ce(e){let t=s7.get(e);if(t)return t;let r=Promise.resolve(e);return s7.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function ct(e){return"string"==typeof e?`'${e}'`:"..."}let cr=sZ(cn);function cn(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function ci(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return ca(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return ca(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return cs(null);default:return t}}function ca(e,t){let r,n=co.get(ci);return n||(r=cs(e),co.set(e,r),r)}r(64);let co=new WeakMap;function cs(e){let t=new cc(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class cc{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){cu("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){cu("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let cl=sZ(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function cu(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}async function cd(e,t){return sK(new Request(sW("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function cp(e){return"function"==typeof e}function ch(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await s9(),n=await e(void 0);return t?.(n),cd(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),cf([n,i],a)}if(cp(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),cf(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),cd(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve(s9()).then(t=>cd(t,e).then(e=>e.json()));if(t[0]instanceof Request)return cf([t[0],t[1]],e);if(cp(t[0])){let r=t[0];return async(...t)=>cf(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return cd(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function cf(e,t,r){let n=sz(e[0]),i=await cd(n.headers,t),a=await i.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:a}));let s=X.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(cg.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=a,s=await r(n,e[1])??X.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=X.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let cg=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var cy=r(509),cm=r(223);let cb=r(120).s;function cw(e,t){var r;throw null!=t||(t=(null==cb||null==(r=cb.getStore())?void 0:r.isAction)?cm.zB.push:cm.zB.replace),function(e,t,r){void 0===r&&(r=cy.Q.TemporaryRedirect);let n=Object.defineProperty(Error(cm.oJ),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=cm.oJ+";"+t+";"+e+";"+r+";",n}(e,t,cy.Q.TemporaryRedirect)}var cv=r(431);async function c_(e,t={},r,n){let i=new Headers(await s9()),{redirect:a=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??i.get("Referer")??"/",l=sW("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),a&&cw(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return a&&cw(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),h=await sK(p,{...n,raw:n8,skipCSRFCheck:n6}),f=await s0();for(let e of h?.cookies??[])f.set(e.name,e.value,e.options);let g=(h instanceof Response?h.headers.get("Location"):h.redirect)??u;return a?cw(g):g}async function cE(e,t){let r=new Headers(await s9());r.set("Content-Type","application/x-www-form-urlencoded");let n=sW("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),o=await sK(a,{...t,raw:n8,skipCSRFCheck:n6}),s=await s0();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?cw(o.redirect):o}async function cS(e,t){let r=new Headers(await s9());r.set("Content-Type","application/json");let n=new Request(sW("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await sK(n,{...t,raw:n8,skipCSRFCheck:n6}),a=await s0();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}cv.s8,cv.s8,cv.s8,r(992).X;let ck=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return sF(r),sK(sz(t),r)};return{handlers:{GET:t,POST:t},auth:ch(e,e=>sF(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return sF(i),c_(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return sF(r),cE(t,r)},unstable_update:async t=>{let r=await e(void 0);return sF(r),cS(t,r)}}}sF(e);let t=t=>sK(sz(t),e);return{handlers:{GET:t,POST:t},auth:ch(e),signIn:(t,r,n)=>c_(t,r,n,e),signOut:t=>cE(t,e),unstable_update:t=>cS(t,e)}}({secret:process.env.AUTH_SECRET,providers:[function(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}}],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}}).auth,cx={matcher:["/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"]};r(447);let cA={...d},cT=cA.middleware||cA.default,cR="/middleware";if("function"!=typeof cT)throw Object.defineProperty(Error(`The Middleware "${cR}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function cP(e){return tc({...e,page:cR,handler:async(...e)=>{try{return await cT(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await g(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},509:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},521:e=>{"use strict";e.exports=require("node:async_hooks")},660:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function c(e){if(!e)return;let[[t,r],...n]=s(e),{domain:i,expires:a,httponly:o,maxage:c,path:d,samesite:p,secure:h,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var y,m,b={name:t,value:decodeURIComponent(r),domain:i,...a&&{expires:new Date(a)},...o&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:d,...p&&{sameSite:l.includes(y=(y=p).toLowerCase())?y:void 0},...h&&{secure:!0},...g&&{priority:u.includes(m=(m=g).toLowerCase())?m:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,a,o,s)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let c of n(a))i.call(e,c)||c===o||t(e,c,{get:()=>a[c],enumerable:!(s=r(a,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),a);var l=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(i)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},785:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>s});let i="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let o=new WeakMap;function s(e,t){if(e.aborted)return Promise.reject(new a(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new a(t)),s=o.get(e);if(s)s.push(i);else{let t=[i];o.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(c),r}}function c(){}},840:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return c},reader:function(){return a}});let i=r(193),a={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function o(e,t){let{url:r,method:i,headers:a,body:o,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(a),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:o?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}}}async function s(e,t){let r=(0,i.getTestReqInfo)(t,a);if(!r)return e(t);let{testData:s,proxyPort:c}=r,l=await o(s,t),u=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:p}=d;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:h,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:h,headers:new Headers(f)})}function c(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},930:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var c=a[s],l=c.indexOf("=");if(!(l<0)){var u=c.substr(0,l).trim(),d=c.substr(++l,c.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=a.maxAge){var l=a.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");c+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");c+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},970:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,a,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var p,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),d){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,i);break;default:if(!c)for(p=1,c=Array(d-1);p<d;p++)c[p-1]=arguments[p];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||o(this,a);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||i&&!s[c].once||n&&s[c].context!==n)&&l.push(s[c]);l.length?this._events[a]=1===l.length?l[0]:l:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let a=i/2|0,o=n+a;0>=r(e[o],t)?(n=++o,i-=a+1):i=a}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let a=(e,t,r)=>new Promise((a,o)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void a(e);let s=setTimeout(()=>{if("function"==typeof r){try{a(r())}catch(e){o(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),o(s)},t);n(e.then(a,o),()=>{clearTimeout(s)})});e.exports=a,e.exports.default=a,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),a=()=>{},o=new t.TimeoutError;class s extends e{constructor(e){var t,n,i,o;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=a,this._resolveIdle=a,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(o=null==(i=e.interval)?void 0:i.toString())?o:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=a,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=a,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let a=async()=>{this._pendingCount++,this._intervalCount++;try{let a=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(o)});n(await a)}catch(e){i(e)}this._next()};this._queue.enqueue(a,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=s})(),e.exports=i})()},981:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(320).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},992:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,a.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,s.h)(t)||(0,o.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(785);let i=Symbol.for("react.postpone");var a=r(447),o=r(349),s=r(64)}},e=>{var t=e(e.s=491);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map