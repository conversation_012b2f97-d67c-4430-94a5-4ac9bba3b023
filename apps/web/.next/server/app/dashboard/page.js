(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6058:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=r(42585),a=r(59246),n=r(63528),i=r.n(n),o=r(83599),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38314)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12969:(e,t,r)=>{"use strict";r.d(t,{A:()=>b,Z:()=>g});var s=r(49068);r(77048);var a=r(13275),n=r(80322),i=r(43806),o=r(38625),d=r(39100),l=r(32310),c=r(93405),u=r(52849),m=r(42475),h=r(84672);let f=(e,t=1e4)=>Promise.race([e,new Promise((e,r)=>setTimeout(()=>r(Error("Operation timed out")),t))]);async function p(){try{let e=await (0,a.u6)(),t=await (0,a.Jt)(new Date(e));if(!t)return[];let r=JSON.parse(t).filter(e=>{if("BULLISH"===e.trend){let t=.1*e.buyTrade,r=e.buyTrade-e.previousClose;return r>0&&r<=t}if("BEARISH"===e.trend){let t=.1*e.sellTrade,r=e.previousClose-e.sellTrade;return r>0&&r<=t}return!1}).map(e=>e.index);return(await Promise.all(r.map(e=>(0,m.$)([e]).catch(()=>null)))).filter(e=>null!==e).map(e=>({symbol:e.symbol,isStock:e.isStock}))}catch(e){return console.error("Error calculating symbols within 10% window:",e),[]}}let g=async e=>{let t={stocks_in_watchlist:0,symbols_trend_changed_from_previous_date:[],symbols_within_10_percent_window_entry:[],risk_signals_last_updated:new Date().toISOString(),connection_status:{is_connected:!1,account_id:"",last_checked:new Date().toISOString()},trading_data:{open_orders:0,auto_prefill_orders:0,open_positions:0,positions_performance:{top_gainer:{symbol:"",position_size:0},top_loser:{symbol:"",position_size:0}}}};try{let r;try{r=await f((0,i.V)(e))}catch(e){console.error("Error fetching user profile:",e),r=null}let s=t.connection_status;try{s={is_connected:!!await f((0,n.F)(e),3e3).catch(()=>!1),account_id:r?.settings?.defaultIBAccountId||"",last_checked:new Date().toISOString()}}catch(e){console.error("Error checking IB connection:",e)}if(!s.is_connected)return{...t,stocks_in_watchlist:0,symbols_trend_changed_from_previous_date:[],symbols_within_10_percent_window_entry:[],connection_status:s,risk_signals_last_updated:new Date().toISOString()};let m=[];try{m=await f((0,o.CP)(e))}catch(e){console.error("Error fetching watchlist:",e)}let h=new Date().toISOString(),g=[];try{h=await f((0,a.u6)()),g=await f((0,d.n)(h))}catch(e){console.error("Error fetching trend changes:",e)}let b=null;try{b=await f((0,l.R)(e),1e4)}catch(e){console.error("Error fetching positions data:",e)}let x=0,w=0;try{let[t,r]=await Promise.all([f((0,c.V)(e)),f((0,u.i)(e))]);x=t.length,w=r.length}catch(e){console.error("Error fetching orders:",e)}let _=function(e){if(!e||0===e.length)return{top_gainer:{symbol:"",position_size:0},top_loser:{symbol:"",position_size:0}};let t={symbol:"",gain:0,gain_percentage:0,position_size:0},r={symbol:"",loss:0,loss_percentage:0,position_size:0};return e.forEach(e=>{let s=e.return,a=e.value,n=a*s/100;s>0&&s>(t.gain_percentage||0)?t={symbol:e.symbol,gain:Number(n.toFixed(2)),gain_percentage:Number(s.toFixed(2)),position_size:Number(a.toFixed(2))}:s<0&&Math.abs(s)>(r.loss_percentage||0)&&(r={symbol:e.symbol,loss:Number(Math.abs(n).toFixed(2)),loss_percentage:Number(Math.abs(s).toFixed(2)),position_size:Number(a.toFixed(2))})}),{top_gainer:t,top_loser:r}}(b),y=[];try{y=await f(p())}catch(e){console.error("Error fetching symbols within 10% window:",e)}return{stocks_in_watchlist:m.length,symbols_trend_changed_from_previous_date:g,symbols_within_10_percent_window_entry:y.map(({symbol:e,isStock:t})=>({symbol:e,isStock:t})),trading_data:{open_orders:x,auto_prefill_orders:w,open_positions:b?.length||0,positions_performance:_},risk_signals_last_updated:h,connection_status:s}}catch(e){return console.error("Error fetching dashboard analytics:",e),t}},b=g;(0,h.D)([g,g]),(0,s.A)(g,"7ffc034743d80b11108044faedc7036e2c99200a8c",null),(0,s.A)(g,"7fc40755def1dfb92b2ccae5c437e6edca922f700c",null)},13275:(e,t,r)=>{"use strict";r.d(t,{Jt:()=>o,KU:()=>d,Nd:()=>l,u6:()=>c,w4:()=>i});var s=r(49068);r(77048);var a=r(68941),n=r(84672);let i=async(e,t=!0)=>{if(console.info("1. Input targetDate:",e),isNaN(e.getTime()))return console.info("2. Invalid date detected:",e),[];let r=e.toISOString().split("T")[0];console.info("3. Converted dateString:",r);let s=await l();console.info("4. Retrieved archives:",{archiveDates:s.trendChanges.map(e=>e.toISOString().split("T")[0])});let n=s.trendChanges.some(e=>e.toISOString().split("T")[0]===r);console.info("5. Date exists in archives?",n),!n&&t&&(console.info("6. Target date not found, finding closest date..."),console.info("7. Found closest date:",r=s.trendChanges.reduce((t,r)=>{let s=Math.abs(r.getTime()-e.getTime()),a=Math.abs(t.getTime()-e.getTime()),n=s<a?r:t;return console.info("6a. Comparing dates:",{date:r.toISOString(),diff:s,isCloser:s<a}),n},s.trendChanges[0]).toISOString().split("T")[0])),console.info("8. Querying database with dateString:",r);let i=(await a.A.$queryRaw`SELECT id, original_index as "originalIndex", index, description, trend, buy_trade as "buyTrade", sell_trade as "sellTrade", previous_close as "previousClose", date FROM "trend_change" WHERE CAST(date AS DATE) = CAST(${r} AS DATE);`).map(e=>({...e,buyTrade:"object"==typeof e.buyTrade&&null!==e.buyTrade?Number(e.buyTrade.toString()):e.buyTrade,sellTrade:"object"==typeof e.sellTrade&&null!==e.sellTrade?Number(e.sellTrade.toString()):e.sellTrade,previousClose:"object"==typeof e.previousClose&&null!==e.previousClose?Number(e.previousClose.toString()):e.previousClose}));return console.info("9. Query results:",{count:i.length,firstResult:i[0]}),i};async function o(e){let t=e?new Date(e):new Date;t.setUTCHours(12,0,0,0);let r=await i(t);if(0===r.length){let e=await a.A.trendChange.findFirst({orderBy:{date:"desc"}});return JSON.stringify(await i(e?.date?new Date(e.date.setUTCHours(12,0,0,0)):new Date))}return JSON.stringify(r.map(e=>({...e,date:e.date.toISOString()})))}async function d(e){let t=await i(e[0].date,!1);if(console.info("existing.length ->",t.length),t&&t.length>0)return console.log("Existing trend change already in the database for this date: ",e[0].date),0;let r=e.map((e,t)=>({...e,originalIndex:e.originalIndex??t,buyTrade:"number"==typeof e.buyTrade?e.buyTrade:Number(e.buyTrade),sellTrade:"number"==typeof e.sellTrade?e.sellTrade:Number(e.sellTrade),previousClose:"number"==typeof e.previousClose?e.previousClose:Number(e.previousClose)}));return(await a.A.trendChange.createMany({data:r})).count}async function l(){try{let e=await a.A.trendChange.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[],t=await a.A.upsideDownsidePotential.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[];return{trendChanges:e.map(e=>e._max.date).filter(e=>null!==e),visualization:t.map(e=>e._max.date).filter(e=>null!==e)}}catch(e){return console.error("Error fetching archives of risk signals:",e),{trendChanges:[],visualization:[]}}}async function c(){return(await l()).trendChanges[0].toISOString()}(0,n.D)([i,o,d,l,c]),(0,s.A)(i,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a",null),(0,s.A)(o,"4082914884143dd2970140de0dd1f1be2975301463",null),(0,s.A)(d,"40e54e4c2e785198c3edb84e94b2846d18cc39479b",null),(0,s.A)(l,"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b",null),(0,s.A)(c,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520",null)},13317:(e,t,r)=>{"use strict";r.d(t,{j2:()=>l,Y9:()=>d,Jv:()=>c});var s=r(74723),a=r(89886),n=r(68941),i=r(30935);let o={...{secret:process.env.AUTH_SECRET,providers:[i.A],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}},adapter:(0,a.y)(n.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:t}){let r=await n.A.user.findUnique({where:{email:t.email},include:{user_profiles:!0}});if(!r)return e;let s=r.user_profiles?.settings;return{...e,user:{...e.user,id:r.id,role:s.role??"user"}}}}},{handlers:d,auth:l,signIn:c,signOut:u}=(0,s.Ay)(o)},18767:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/_components/dashboard-main.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/_components/dashboard-main.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24017:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return s.callServer},createServerReference:function(){return n},findSourceMapURL:function(){return a.findSourceMapURL}});let s=r(89562),a=r(41890),n=r(54183).createServerReference},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29838:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b":()=>a.Nd,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520":()=>a.u6,"40070bd324e71428f5da05bccd7818bdf7d2e9fb50":()=>o.Q2,"40130103a12210564b9cc2eb00ee5bfd15ed9e9469":()=>l.R,"40396872244520ce764475f00a22baf0aa7a603940":()=>m.$,"40618b691c6e99388ef4bfa6ff8eca71c2abcfb3f7":()=>c.V,"4076ae6917a9197faf33016ff9b25083685fba3edd":()=>o.CP,"4082914884143dd2970140de0dd1f1be2975301463":()=>a.Jt,"409a303cc9c9a1946c183a09b48b42bcabb7822fd3":()=>u.i,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c":()=>n.F,"40e54e4c2e785198c3edb84e94b2846d18cc39479b":()=>a.KU,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665":()=>i.V,"604b184f55fb3c880747ad4d6ee2ff556c780bbbca":()=>o.Qv,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e":()=>i.e,"60afccde42b0dc1f25d6ac9ae7ee09877d448467b4":()=>d.n,"60fe6244bf31f4bd3601e492f7ad4652260e84cac1":()=>o.w7,"7f1ecaf8199bd1b8cdce70e54ad162711b6ade20b5":()=>h.$,"7f5a3b429f9bf86298bbf02e1856c79e4e462a7d0e":()=>h.s,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a":()=>a.w4,"7fc40755def1dfb92b2ccae5c437e6edca922f700c":()=>s.A,"7ffc034743d80b11108044faedc7036e2c99200a8c":()=>s.Z});var s=r(12969),a=r(13275),n=r(80322),i=r(43806),o=r(38625),d=r(39100),l=r(32310),c=r(93405),u=r(52849),m=r(42475),h=r(67096)},32310:(e,t,r)=>{"use strict";r.d(t,{R:()=>d});var s=r(49068);r(77048);var a=r(76977),n=r(55830),i=r(91581),o=r(80322);async function d(e){try{if(!await (0,o.F)(e))return console.log("IBKR not connected"),null;let t=await (0,n.M)(e),r=[],s=new Promise((e,s)=>{let n=setTimeout(()=>{t.disconnect(),s(Error("Timeout waiting for positions data"))},1e4);t.on(a.EventName.error,(e,t,r)=>{console.error(`IBKR Error: ${e.message} - code: ${t} - reqId: ${r}`),502===t&&s(Error("Connection refused"))}),t.on(a.EventName.connected,()=>{console.log("IBKR Connected successfully"),t.reqPositions()}),t.on(a.EventName.position,(e,t,s,a)=>{console.log("Received position:",t.symbol,s),t.symbol&&r.push({symbol:t.symbol,shares:s,avgCost:a})}),t.on(a.EventName.positionEnd,async()=>{console.log("Position end received. Total IBKR positions:",r.length);try{if(0===r.length){console.log("No positions found"),clearTimeout(n),t.disconnect(),e([]);return}let s=r.map(e=>e.symbol);console.log("Fetching quotes for symbols:",s);let a=await (0,i.gO)(s);console.log("Received quotes for",Object.keys(a).length,"symbols");let o=r.map(e=>{let t=a[e.symbol];if(!t)return console.warn(`No quote found for symbol: ${e.symbol}`),null;let r=t.regularMarketPrice,s=e.shares*r,n=e.shares*e.avgCost;return{symbol:e.symbol,shares:e.shares,price:r,value:s,avgCost:e.avgCost,return:(s-n)/n*100}}).filter(e=>null!==e);console.log("All positions processed. Total valid positions:",o.length),clearTimeout(n),t.disconnect(),e(o)}catch(e){console.error("Error processing positions:",e),s(e)}}),t.on(a.EventName.disconnected,()=>{console.log("IBKR Disconnected")}),console.log("Initiating IBKR connection..."),t.connect()}),d=await s;return console.log("Retrieved positions:",d),d}catch(e){return console.error("Error fetching positions data:",e),null}}(0,r(84672).D)([d]),(0,s.A)(d,"40130103a12210564b9cc2eb00ee5bfd15ed9e9469",null)},33873:e=>{"use strict";e.exports=require("path")},38314:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(52927),a=r(13317),n=r(52058),i=r(12969),o=r(18767);let d=async()=>{let e=await (0,a.j2)();e||(0,n.redirect)("/signin");let t=e.user.id,r=await (0,i.Z)(t);return(0,s.jsx)(o.default,{initialDashboardData:r,userId:t})}},38625:(e,t,r)=>{"use strict";r.d(t,{CP:()=>d,Q2:()=>c,Qv:()=>l,w7:()=>o});var s=r(49068);r(77048);var a=r(68941),n=r(91581),i=r(91773);async function o(e,t){try{if(!e||!t)throw Error("Missing required fields");if(await a.A.watchlist.findFirst({where:{user_id:t,ticker:e.toUpperCase(),deleted_at:null}}))throw Error("Stock already in watchlist");return await a.A.watchlist.create({data:{user_id:t,ticker:e.toUpperCase()}}),(0,i.revalidatePath)("/watchlist"),{success:!0}}catch(e){if(console.error("[WATCHLIST_ADD_ERROR]",e),e instanceof Error)throw Error(e.message);throw Error("Failed to add stock to watchlist")}}async function d(e){try{if(!e)throw Error("User ID is required");return await a.A.watchlist.findMany({where:{user_id:e,deleted_at:null},orderBy:{created_at:"desc"}})}catch(e){return console.error("[WATCHLIST_GET_ERROR]",e),[]}}async function l(e,t){try{if(!e||!t)throw Error("Missing required fields");if(!await a.A.watchlist.findFirst({where:{id:e,user_id:t,deleted_at:null}}))throw Error("Stock not found in watchlist");return await a.A.watchlist.update({where:{id:e},data:{deleted_at:new Date}}),(0,i.revalidatePath)("/watchlist"),{success:!0}}catch(e){if(console.error("[WATCHLIST_REMOVE_ERROR]",e),e instanceof Error)throw Error(e.message);throw Error("Failed to remove stock from watchlist")}}async function c(e){try{let t={};for(let r of e)try{let e=await (0,n.TZ)(r);t[r]=e}catch(e){console.error(`[QUOTE_FETCH_ERROR] ${r}:`,e),t[r]=null}return t}catch(e){return console.error("[QUOTES_FETCH_ERROR]",e),{}}}(0,r(84672).D)([o,d,l,c]),(0,s.A)(o,"60fe6244bf31f4bd3601e492f7ad4652260e84cac1",null),(0,s.A)(d,"4076ae6917a9197faf33016ff9b25083685fba3edd",null),(0,s.A)(l,"604b184f55fb3c880747ad4d6ee2ff556c780bbbca",null),(0,s.A)(c,"40070bd324e71428f5da05bccd7818bdf7d2e9fb50",null)},39100:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var s=r(49068);r(77048);var a=r(13275);function n(e){let t=e instanceof Date?e:new Date(e);if(isNaN(t.getTime()))throw Error("Invalid date provided");let r=t.getFullYear();return new Date(Date.UTC(r,t.getMonth(),t.getDate(),0,0,0))}async function i(e,t){let r,s=n(e);if(t?r=n(t):((r=new Date(s)).setDate(r.getDate()-1),r=n(r)),r.getTime()===s.getTime())throw Error("previousTrendChangeDate and selectedTrendChangeDate cannot be the same date");if(r>s)throw Error("previousTrendChangeDate must be before selectedTrendChangeDate");if(r.getFullYear()!==s.getFullYear())throw Error("dates must be in the same year");if(r.getMonth()!==s.getMonth())throw Error("dates must be in the same month");let i=await (0,a.w4)(s),o=await (0,a.w4)(r),d=new Map(i.map(e=>[e.index,e])),l=new Map(o.map(e=>[e.index,e])),c=[],u=new Set;for(let[e,t]of d){if(u.has(e))continue;let r=l.get(e);r&&t.trend!==r.trend&&("BULLISH"===t.trend&&"BEARISH"===r.trend||"BEARISH"===t.trend&&"BULLISH"===r.trend||"NEUTRAL"===t.trend&&("BULLISH"===r.trend||"BEARISH"===r.trend)||("BULLISH"===t.trend||"BEARISH"===t.trend)&&"NEUTRAL"===r.trend)&&(c.push({current:t,previous:r}),u.add(e))}return c}(0,r(84672).D)([i]),(0,s.A)(i,"60afccde42b0dc1f25d6ac9ae7ee09877d448467b4",null)},39385:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(43197),a=r(14824),n=r(60880),i=r.n(n),o=r(45023);function d({userId:e,initialDashboardData:t}){let[r,n]=(0,a.useState)(!0),[d,l]=(0,a.useState)(t.connection_status.is_connected),[c,u]=(0,a.useState)(t),[m,h]=(0,a.useState)(!1),f=r?c.symbols_within_10_percent_window_entry.filter(e=>e.isStock):c.symbols_within_10_percent_window_entry;return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8 flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Welcome back!"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Here's what's happening with your portfolio"})]}),(0,s.jsx)("div",{className:"mt-4 md:mt-0",children:(0,s.jsxs)("div",{className:`flex items-center gap-2 px-4 py-2 rounded-full border ${c.connection_status.is_connected?"status-bullish":"status-bearish"}`,children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full animate-pulse ${c.connection_status.is_connected?"bg-green-600":"bg-red-600"}`}),(0,s.jsx)("span",{className:`text-size-sm font-weight-medium ${c.connection_status.is_connected?"text-green-700":"text-red-700"}`,children:c.connection_status.is_connected?(0,s.jsxs)(s.Fragment,{children:["Connected to IBKR",(0,s.jsxs)("span",{className:"ml-2 text-green-600 font-weight-normal",children:["(",c.connection_status.account_id,")"]})]}):"Disconnected from IBKR"}),!d&&(0,s.jsx)("button",{className:"ml-2 p-1 hover:bg-red-100 rounded-full transition-colors disabled:opacity-50",title:"Retry connection",disabled:m,onClick:async()=>{h(!0);try{let t=await (0,o.F)(e);l(t)}finally{h(!1)}},children:(0,s.jsx)("svg",{className:`w-4 h-4 text-red-600 ${m?"animate-spin":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),(0,s.jsx)("div",{className:"ml-2 text-xs text-gray-500",title:new Date(c.connection_status.last_checked).toLocaleString(),children:function(e){let t=new Date,r=new Date(e),s=Math.floor((t.getTime()-r.getTime())/1e3);return s<60?"just now":s<3600?`${Math.floor(s/60)}m ago`:s<86400?`${Math.floor(s/3600)}h ago`:`${Math.floor(s/86400)}d ago`}(c.connection_status.last_checked)})]})})]}),(0,s.jsxs)("div",{className:"mb-8 bg-gradient-to-r from-muted to-accent rounded-xl p-6 border",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-size-xl font-weight-semibold text-heading",children:"Risk Signals Monitor"}),(0,s.jsxs)("div",{className:"mt-2 md:mt-0 px-3 py-1 bg-accent text-accent-foreground rounded-full text-size-sm",children:["Last Updated:"," ",new Date(c.risk_signals_last_updated).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"bg-card rounded-lg p-4 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-size-lg font-weight-medium text-heading",children:"Trend Changes"}),(0,s.jsxs)("span",{className:"px-3 py-1 bg-accent text-accent-foreground rounded-full text-size-sm font-weight-medium",children:[c.symbols_trend_changed_from_previous_date.length," ","symbols"]})]}),(0,s.jsx)("p",{className:"text-size-sm text-muted mb-3",children:"Symbols changed trend"}),(0,s.jsx)("div",{className:"flex flex-col gap-2",children:c.symbols_trend_changed_from_previous_date.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg bg-muted hover:bg-accent transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"font-weight-medium text-body",children:e.current.index}),(0,s.jsx)("svg",{className:"w-4 h-4 text-muted",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:`px-2 py-0.5 text-size-xs font-weight-medium rounded-full ${"BULLISH"===e.previous.trend?"status-bullish":"status-bearish"}`,children:e.previous.trend}),(0,s.jsx)("svg",{className:"w-4 h-4 text-muted",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 5l7 7-7 7M5 5l7 7-7 7"})}),(0,s.jsx)("span",{className:`px-2 py-0.5 text-size-xs font-weight-medium rounded-full ${"BULLISH"===e.current.trend?"status-bullish":"status-bearish"}`,children:e.current.trend})]})]},e.current.index))})]}),(0,s.jsx)("div",{className:"bg-accent p-3 rounded-full h-fit ml-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-accent-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})})]})}),(0,s.jsx)("div",{className:"bg-card rounded-lg p-4 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-size-lg font-weight-medium text-heading",children:"Entry Opportunities"}),(0,s.jsxs)("label",{className:"flex items-center space-x-2 text-size-sm text-muted",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-primary rounded border-input focus:ring-ring",checked:r,onChange:e=>n(e.target.checked)}),(0,s.jsx)("span",{children:"Stocks only"})]})]}),(0,s.jsx)("p",{className:"text-3xl font-weight-bold text-primary mt-2",children:f.length}),(0,s.jsx)("p",{className:"text-size-sm text-muted mt-1",children:"Within 10% entry window"}),(0,s.jsx)("div",{className:"mt-3 flex flex-wrap gap-2",children:f.map(e=>(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-size-xs font-weight-medium bg-secondary text-secondary-foreground",children:e.symbol},e.symbol))})]}),(0,s.jsx)("div",{className:"bg-secondary p-3 rounded-full h-fit",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-secondary-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l3 3 3-3m0 0V3.6a9 9 0 11-6 0V12"})})})]})}),(0,s.jsx)("div",{className:"bg-card rounded-lg p-4 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-size-lg font-weight-medium text-heading",children:"Signal Status"}),(0,s.jsx)("p",{className:"text-size-sm font-weight-medium text-green-600 mt-2",children:"Download Complete"}),(0,s.jsxs)("p",{className:"text-size-sm text-muted mt-1",children:["Next update in"," ",function(e){let t=new Date(new Date().toUTCString()),r=new Date(Date.UTC(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),14,0,0));t.getUTCHours()>=14&&r.setUTCDate(r.getUTCDate()+1);let s=r.getTime()-t.getTime(),a=Math.floor(s/36e5),n=Math.floor(s%36e5/6e4);return a>0?`${a}h ${n}m`:n>0?`${n}m`:"updating soon"}(c.risk_signals_last_updated)]})]}),(0,s.jsx)("div",{className:"bg-green-100 p-3 rounded-full",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})})]})]}),(0,s.jsx)("h2",{className:"text-size-xl font-weight-semibold text-heading mb-4",children:"Trading Overview"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)(i(),{href:"/watchlist",className:"block transition-transform hover:scale-105",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-sm p-6 border hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-size-sm font-weight-medium text-muted",children:"Watchlist"}),(0,s.jsx)("span",{className:"p-2 bg-accent rounded-lg",children:(0,s.jsxs)("svg",{className:"w-6 h-6 text-accent-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),(0,s.jsx)("p",{className:"text-2xl font-weight-semibold text-heading",children:c.stocks_in_watchlist}),(0,s.jsx)("p",{className:"text-size-sm text-muted",children:"Stocks being tracked"})]})}),(0,s.jsx)(i(),{href:"/portfolio?tab=orders",className:"block transition-transform hover:scale-105",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-sm p-6 border hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-size-sm font-weight-medium text-muted",children:"Open Orders"}),(0,s.jsx)("span",{className:"p-2 bg-secondary rounded-lg",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-secondary-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})})]}),(0,s.jsx)("p",{className:"text-2xl font-weight-semibold text-heading",children:c.trading_data.open_orders}),(0,s.jsx)("p",{className:"text-size-sm text-muted",children:"Pending orders"})]})}),(0,s.jsx)(i(),{href:"/portfolio",className:"block transition-transform hover:scale-105",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-sm p-6 border hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-size-sm font-weight-medium text-muted",children:"Open Positions"}),(0,s.jsx)("span",{className:"p-2 bg-green-100 rounded-lg",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})})]}),(0,s.jsx)("p",{className:"text-2xl font-weight-semibold text-heading",children:c.trading_data.open_positions}),(0,s.jsx)("p",{className:"text-size-sm text-muted",children:"Active positions"})]})}),(0,s.jsx)(i(),{href:"/portfolio?tab=auto-prefill",className:"block transition-transform hover:scale-105",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-sm p-6 border hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-size-sm font-weight-medium text-muted",children:"Auto Orders"}),(0,s.jsx)("span",{className:"p-2 bg-primary/10 rounded-lg",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})]}),(0,s.jsx)("p",{className:"text-2xl font-weight-semibold text-heading",children:c.trading_data.auto_prefill_orders}),(0,s.jsx)("p",{className:"text-size-sm text-muted",children:"Pending auto orders"})]})})]}),(0,s.jsx)("h2",{className:"text-size-xl font-weight-semibold text-heading mb-4",children:"Current Holdings Performance"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(i(),{href:"/portfolio",className:"block transition-transform hover:scale-105",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-sm p-6 border hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-size-lg font-weight-semibold text-heading",children:"Best Performing Position"}),(0,s.jsxs)("span",{className:"px-3 py-1 bg-green-100 text-green-600 rounded-full text-size-sm font-weight-medium",children:["+",c.trading_data.positions_performance.top_gainer.gain_percentage,"%"]})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-weight-bold text-heading",children:c.trading_data.positions_performance.top_gainer.symbol}),(0,s.jsxs)("p",{className:"text-size-sm text-muted",children:["Position Size: $",c.trading_data.positions_performance.top_gainer.position_size.toLocaleString()]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-2xl font-weight-bold text-green-600",children:["+$",c.trading_data.positions_performance.top_gainer.gain?.toLocaleString()||"0"]}),(0,s.jsx)("p",{className:"text-size-sm text-muted",children:"Unrealized Gain"})]})]})})]})}),(0,s.jsx)(i(),{href:"/portfolio",className:"block transition-transform hover:scale-105",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-sm p-6 border hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-size-lg font-weight-semibold text-heading",children:"Worst Performing Position"}),(0,s.jsxs)("span",{className:"px-3 py-1 bg-red-100 text-red-600 rounded-full text-size-sm font-weight-medium",children:["-",c.trading_data.positions_performance.top_loser.loss_percentage,"%"]})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-weight-bold text-heading",children:c.trading_data.positions_performance.top_loser.symbol}),(0,s.jsxs)("p",{className:"text-size-sm text-muted",children:["Position Size: $",c.trading_data.positions_performance.top_loser.position_size.toLocaleString()]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-2xl font-weight-bold text-red-600",children:["-$",c.trading_data.positions_performance.top_loser.loss?.toLocaleString()||"0"]}),(0,s.jsx)("p",{className:"text-size-sm text-muted",children:"Unrealized Loss"})]})]})})]})})]})]})}},42475:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var s=r(49068);r(77048);var a=r(13275);global.__cachedSymbols||(global.__cachedSymbols=new Map);var n=r(67096);async function i(e){if(console.log("getCachedInstrumentDetailBatch():symbols->",e),0===e.length){let t=await (0,a.u6)();e=JSON.parse(await (0,a.Jt)(new Date(t))).map(e=>e.index)}let t=e.filter(e=>!global.__cachedSymbols.get(e));console.log("getCachedInstrumentDetailBatch():cachedSymbols->",t);let r=JSON.parse(await (0,n.s)(t.map(e=>({idValue:e,idType:"TICKER"}))));Array.isArray(r)&&r.forEach(e=>{if(e.data&&e.data.length>0){var t;let r={ticker:e.data[0].ticker,name:e.data[0].name,securityType:e.data[0].securityType,securityType2:e.data[0].securityType2,figi:e.data[0].figi,exchange:e.data[0].exchCode};t=r.ticker,global.__cachedSymbols.set(t,{...r,lastUpdated:new Date})}});let s=global.__cachedSymbols;return console.log("getCachedInstrumentDetailBatch():results->",s),s}(0,r(84672).D)([i]),(0,s.A)(i,"40396872244520ce764475f00a22baf0aa7a603940",null)},43169:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(99292);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},43806:(e,t,r)=>{"use strict";r.d(t,{V:()=>n,e:()=>i});var s=r(49068);r(77048);var a=r(68941);async function n(e){let t=await a.A.userProfile.findUnique({where:{user_id:e}});return t?{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}:null}async function i(e,t){let r=await a.A.userProfile.update({where:{user_id:e},data:{first_name:t.firstName,last_name:t.lastName,settings:JSON.parse(JSON.stringify(t.settings)),updated_at:new Date}});return{userId:r.user_id,firstName:r.first_name||"",lastName:r.last_name||"",createdAt:r.created_at,updatedAt:r.updated_at,deletedAt:r.deleted_at,settings:r.settings}}(0,r(84672).D)([n,i]),(0,s.A)(n,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,s.A)(i,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},45023:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(24017);let a=(0,s.createServerReference)("40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",s.callServer,void 0,s.findSourceMapURL,"testIBKRConnection")},51324:(e,t,r)=>{Promise.resolve().then(r.bind(r,18767))},52849:(e,t,r)=>{"use strict";r.d(t,{i:()=>i});var s=r(49068);r(77048);var a=r(43806),n=r(68941);async function i(e){if(!e)throw Error("User ID is required");try{let t=await (0,a.V)(e),r=t?.settings?.enableAutoOrderPrefill??!1;if(!r)return[];return(await n.A.autoOrderPrefill.findMany({where:{user_id:e,deleted_at:null},orderBy:{created_at:"desc"}})).map(e=>({...e,isEnabled:r}))}catch(e){throw console.error("[FETCH_AUTO_ORDER_PREFILL_ERROR]",e),Error(e instanceof Error?e.message:"Failed to fetch auto order prefill records")}}(0,r(84672).D)([i]),(0,s.A)(i,"409a303cc9c9a1946c183a09b48b42bcabb7822fd3",null)},55511:e=>{"use strict";e.exports=require("crypto")},55830:(e,t,r)=>{"use strict";r.d(t,{M:()=>n,f:()=>i});var s=r(43806),a=r(76977);async function n(e){try{let t=await (0,s.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return new a.IBApi({clientId:r,host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port})}catch(e){throw console.error("Error creating IBKR connection:",e),e}}async function i(e){try{let t=await (0,s.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=new a.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port}),n=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return{ibApiNext:r,clientId:n}}catch(e){throw console.error("Error creating IBKR connection:",e),e}}},57975:e=>{"use strict";e.exports=require("node:util")},61052:(e,t,r)=>{Promise.resolve().then(r.bind(r,39385))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67096:(e,t,r)=>{"use strict";r.d(t,{$:()=>n,s:()=>a});var s=r(49068);async function a(e){try{let t=await fetch("https://api.openfigi.com/v3/mapping",{method:"POST",headers:{"Content-Type":"application/json","X-OPENFIGI-APIKEY":"563d9fd2-f93d-4d39-998d-5fbf54baae06"},body:JSON.stringify(e)}),r=await t.json();return JSON.stringify(r)}catch(e){return JSON.stringify({errorMessage:e})}}async function n(e){try{let t=await fetch("https://api.openfigi.com/v3/mapping",{method:"POST",headers:{"Content-Type":"application/json","X-OPENFIGI-APIKEY":"563d9fd2-f93d-4d39-998d-5fbf54baae06"},body:JSON.stringify([e])}),r=await t.json();return JSON.stringify(r)}catch(e){return JSON.stringify({errorMessage:e})}}r(77048),(0,r(84672).D)([a,n]),(0,s.A)(a,"7f5a3b429f9bf86298bbf02e1856c79e4e462a7d0e",null),(0,s.A)(n,"7f1ecaf8199bd1b8cdce70e54ad162711b6ade20b5",null)},68941:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(67566);let a=globalThis.__prisma||new s.PrismaClient},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},80322:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(49068);r(77048);var a=r(55830);async function n(e){try{let{ibApiNext:t,clientId:r}=await (0,a.f)(e);return new Promise((e,r)=>{let s=setTimeout(()=>r(Error("Connection timeout")),3e3);t.connect().getAccountSummary("All","NetLiquidation").subscribe({next:()=>{clearTimeout(s),e(!0),t.disconnect()},error:e=>{clearTimeout(s),r(e),t.disconnect()}}).add(()=>{t.isConnected&&(t.disconnect(),console.log("Disconnected from IBKR"))})})}catch(e){return console.error("Error testing IBKR connection:",e),!1}}(0,r(84672).D)([n]),(0,s.A)(n,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",null)},91645:e=>{"use strict";e.exports=require("net")},93405:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var s=r(49068);r(77048);var a=r(76977),n=r(43806);async function i(e){let t=await (0,n.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=new a.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port});try{r.connect(t.settings.ibkrConnectionDetail.clientId??void 0);let e=(await r.getAllOpenOrders()).map(e=>({orderId:e.order.orderId,orderType:e.order.orderType,permId:e.order.permId,symbol:e.contract.symbol,action:e.order.action,quantity:e.order.totalQuantity,price:e.order.lmtPrice||e.order.auxPrice,status:e.orderState.status,timestamp:new Date(e.orderState.timestamp||Date.now()),clientId:e.order.clientId,accountId:e.order.account})),s={};return e.forEach(e=>{s[e.accountId]||(s[e.accountId]=[]),s[e.accountId].push(e)}),e}catch(e){return console.error("Error fetching all orders:",e),[]}finally{r.disconnect()}}(0,r(84672).D)([i]),(0,s.A)(i,"40618b691c6e99388ef4bfa6ff8eca71c2abcfb3f7",null)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3491,7728,6631,1773,7400,5438,7524,4923,9292,3314,4515],()=>r(6058));module.exports=s})();