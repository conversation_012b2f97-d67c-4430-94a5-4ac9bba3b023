(()=>{var e={};e.id=5281,e.ids=[5281],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5314:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"40618b691c6e99388ef4bfa6ff8eca71c2abcfb3f7":()=>u,"407632f70646522c6188f1587ba8e93b5fb0177dd2":()=>c,"407feaf61217dad38d5d724eef943a6451a09c2dc3":()=>x,"409a303cc9c9a1946c183a09b48b42bcabb7822fd3":()=>h.i,"40fea484d196ffc03250e6fc7effd806957ac9a456":()=>a.I,"702df2c2bf9d7b6941865381f6850baa0c7332ebd6":()=>m,"7eb434a6bda9ff01072b0388a3ec4a71531853042e":()=>r.w});var r=s(75241),a=s(65629),n=s(20349);s(71241);var i=s(80336),l=s(68785);async function c(e){try{let t=await i.A.trendChange.findFirst({where:{index:e},orderBy:{date:"desc"}});if(!t)return null;return{...t,description:t.description??void 0,buyTrade:Number(t.buyTrade),sellTrade:Number(t.sellTrade),previousClose:Number(t.previousClose)}}catch(e){return console.error("[GET_RISK_SIGNAL_ERROR]",e),null}}(0,l.D)([c]),(0,n.A)(c,"407632f70646522c6188f1587ba8e93b5fb0177dd2",null);var o=s(90742),d=s(31595);async function u(e){let t=await (0,d.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let s=new o.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port});try{s.connect(t.settings.ibkrConnectionDetail.clientId??void 0);let e=(await s.getAllOpenOrders()).map(e=>({orderId:e.order.orderId,orderType:e.order.orderType,permId:e.order.permId,symbol:e.contract.symbol,action:e.order.action,quantity:e.order.totalQuantity,price:e.order.lmtPrice||e.order.auxPrice,status:e.orderState.status,timestamp:new Date(e.orderState.timestamp||Date.now()),clientId:e.order.clientId,accountId:e.order.account})),r={};return e.forEach(e=>{r[e.accountId]||(r[e.accountId]=[]),r[e.accountId].push(e)}),e}catch(e){return console.error("Error fetching all orders:",e),[]}finally{s.disconnect()}}async function m(e,t,s){try{let r=await (0,d.V)(e);if(!r?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let a=new o.IBApiNext({host:r.settings.ibkrConnectionDetail.host,port:r.settings.ibkrConnectionDetail.port});return new Promise((e,r)=>{try{a.connect(s),a.cancelOrder(t),setTimeout(()=>{a.isConnected&&a.disconnect(),e(!0)},2e3)}catch(e){a.isConnected&&a.disconnect(),r(e)}})}catch(e){return console.error("Error canceling order:",e),!1}}async function x(e){try{let t=await (0,d.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let s=new o.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port});return new Promise((e,t)=>{try{s.connect(),s.cancelAllOrders(),setTimeout(()=>{s.isConnected&&s.disconnect(),e(!0)},2e3)}catch(e){s.isConnected&&s.disconnect(),t(e)}})}catch(e){return console.error("Error canceling all orders:",e),!1}}(0,l.D)([u]),(0,n.A)(u,"40618b691c6e99388ef4bfa6ff8eca71c2abcfb3f7",null),(0,l.D)([m,x]),(0,n.A)(m,"702df2c2bf9d7b6941865381f6850baa0c7332ebd6",null),(0,n.A)(x,"407feaf61217dad38d5d724eef943a6451a09c2dc3",null);var h=s(62806)},10013:(e,t,s)=>{"use strict";s.d(t,{Lt:()=>T,Rx:()=>z,Zr:()=>_,EO:()=>M,$v:()=>O,ck:()=>L,wd:()=>$,r7:()=>q,tv:()=>F});var r=s(43197),a=s(14824),n=s(6125),i=s(15167),l=s(65264),c=s(57535),o=s(65443),d="AlertDialog",[u,m]=(0,n.A)(d,[l.Hs]),x=(0,l.Hs)(),h=e=>{let{__scopeAlertDialog:t,...s}=e,a=x(t);return(0,r.jsx)(l.bL,{...a,...s,modal:!0})};h.displayName=d;var f=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,n=x(s);return(0,r.jsx)(l.l9,{...n,...a,ref:t})});f.displayName="AlertDialogTrigger";var p=e=>{let{__scopeAlertDialog:t,...s}=e,a=x(t);return(0,r.jsx)(l.ZL,{...a,...s})};p.displayName="AlertDialogPortal";var j=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,n=x(s);return(0,r.jsx)(l.hJ,{...n,...a,ref:t})});j.displayName="AlertDialogOverlay";var v="AlertDialogContent",[b,y]=u(v),g=(0,o.Dc)("AlertDialogContent"),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,children:n,...o}=e,d=x(s),u=a.useRef(null),m=(0,i.s)(t,u),h=a.useRef(null);return(0,r.jsx)(l.G$,{contentName:v,titleName:w,docsSlug:"alert-dialog",children:(0,r.jsx)(b,{scope:s,cancelRef:h,children:(0,r.jsxs)(l.UC,{role:"alertdialog",...d,...o,ref:m,onOpenAutoFocus:(0,c.m)(o.onOpenAutoFocus,e=>{e.preventDefault(),h.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(g,{children:n}),(0,r.jsx)(S,{contentRef:u})]})})})});N.displayName=v;var w="AlertDialogTitle",A=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,n=x(s);return(0,r.jsx)(l.hE,{...n,...a,ref:t})});A.displayName=w;var C="AlertDialogDescription",k=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,n=x(s);return(0,r.jsx)(l.VY,{...n,...a,ref:t})});k.displayName=C;var I=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,n=x(s);return(0,r.jsx)(l.bm,{...n,...a,ref:t})});I.displayName="AlertDialogAction";var R="AlertDialogCancel",D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,{cancelRef:n}=y(R,s),c=x(s),o=(0,i.s)(t,n);return(0,r.jsx)(l.bm,{...c,...a,ref:o})});D.displayName=R;var S=({contentRef:e})=>{let t=`\`${v}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${v}\` by passing a \`${C}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${v}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return a.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},E=s(51001),P=s(89806);let T=h,F=f,B=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(j,{className:(0,E.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));B.displayName=j.displayName;let M=a.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(p,{children:[(0,r.jsx)(B,{}),(0,r.jsx)(N,{ref:s,className:(0,E.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));M.displayName=N.displayName;let $=({className:e,...t})=>(0,r.jsx)("div",{className:(0,E.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});$.displayName="AlertDialogHeader";let L=({className:e,...t})=>(0,r.jsx)("div",{className:(0,E.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});L.displayName="AlertDialogFooter";let q=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(A,{ref:s,className:(0,E.cn)("text-lg font-semibold",e),...t}));q.displayName=A.displayName;let O=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(k,{ref:s,className:(0,E.cn)("text-sm text-muted-foreground",e),...t}));O.displayName=k.displayName;let z=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(I,{ref:s,className:(0,E.cn)((0,P.r)(),e),...t}));z.displayName=I.displayName;let _=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(D,{ref:s,className:(0,E.cn)((0,P.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));_.displayName=D.displayName},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15565:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(21601).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/portfolio-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/portfolio-page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24308:(e,t,s)=>{"use strict";s.d(t,{default:()=>eq});var r=s(43197),a=s(14824),n=s(57535),i=s(6125),l=s(45384),c=s(15167),o=s(41920),d=s(2185),u=s(21760),m=s(92708),x=s(57536),h="rovingFocusGroup.onEntryFocus",f={bubbles:!1,cancelable:!0},p="RovingFocusGroup",[j,v,b]=(0,l.N)(p),[y,g]=(0,i.A)(p,[b]),[N,w]=y(p),A=a.forwardRef((e,t)=>(0,r.jsx)(j.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,r.jsx)(j.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,r.jsx)(C,{...e,ref:t})})}));A.displayName=p;var C=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:s,orientation:i,loop:l=!1,dir:o,currentTabStopId:j,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:y,onEntryFocus:g,preventScrollOnEntryFocus:w=!1,...A}=e,C=a.useRef(null),k=(0,c.s)(t,C),I=(0,x.jH)(o),[R,S]=(0,m.i)({prop:j,defaultProp:b??null,onChange:y,caller:p}),[E,P]=a.useState(!1),T=(0,u.c)(g),F=v(s),B=a.useRef(!1),[M,$]=a.useState(0);return a.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(h,T),()=>e.removeEventListener(h,T)},[T]),(0,r.jsx)(N,{scope:s,orientation:i,dir:I,loop:l,currentTabStopId:R,onItemFocus:a.useCallback(e=>S(e),[S]),onItemShiftTab:a.useCallback(()=>P(!0),[]),onFocusableItemAdd:a.useCallback(()=>$(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>$(e=>e-1),[]),children:(0,r.jsx)(d.sG.div,{tabIndex:E||0===M?-1:0,"data-orientation":i,...A,ref:k,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{B.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!B.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(h,f);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),w)}}B.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>P(!1))})})}),k="RovingFocusGroupItem",I=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:s,focusable:i=!0,active:l=!1,tabStopId:c,children:u,...m}=e,x=(0,o.B)(),h=c||x,f=w(k,s),p=f.currentTabStopId===h,b=v(s),{onFocusableItemAdd:y,onFocusableItemRemove:g,currentTabStopId:N}=f;return a.useEffect(()=>{if(i)return y(),()=>g()},[i,y,g]),(0,r.jsx)(j.ItemSlot,{scope:s,id:h,focusable:i,active:l,children:(0,r.jsx)(d.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...m,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i?f.onItemFocus(h):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>f.onItemFocus(h)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,s){var r;let a=(r=e.key,"rtl"!==s?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return R[a]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let s=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)s.reverse();else if("prev"===t||"next"===t){"prev"===t&&s.reverse();let r=s.indexOf(e.currentTarget);s=f.loop?function(e,t){return e.map((s,r)=>e[(t+r)%e.length])}(s,r+1):s.slice(r+1)}setTimeout(()=>D(s))}}),children:"function"==typeof u?u({isCurrentTabStop:p,hasTabStop:null!=N}):u})})});I.displayName=k;var R={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e,t=!1){let s=document.activeElement;for(let r of e)if(r===s||(r.focus({preventScroll:t}),document.activeElement!==s))return}var S=s(41661),E="Tabs",[P,T]=(0,i.A)(E,[g]),F=g(),[B,M]=P(E),$=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:c,activationMode:u="automatic",...h}=e,f=(0,x.jH)(c),[p,j]=(0,m.i)({prop:a,onChange:n,defaultProp:i??"",caller:E});return(0,r.jsx)(B,{scope:s,baseId:(0,o.B)(),value:p,onValueChange:j,orientation:l,dir:f,activationMode:u,children:(0,r.jsx)(d.sG.div,{dir:f,"data-orientation":l,...h,ref:t})})});$.displayName=E;var L="TabsList",q=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...n}=e,i=M(L,s),l=F(s);return(0,r.jsx)(A,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:a,children:(0,r.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});q.displayName=L;var O="TabsTrigger",z=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...l}=e,c=M(O,s),o=F(s),u=U(c.baseId,a),m=G(c.baseId,a),x=a===c.value;return(0,r.jsx)(I,{asChild:!0,...o,focusable:!i,active:x,children:(0,r.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;x||i||!e||c.onValueChange(a)})})})});z.displayName=O;var _="TabsContent",V=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:l,...c}=e,o=M(_,s),u=U(o.baseId,n),m=G(o.baseId,n),x=n===o.value,h=a.useRef(x);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(S.C,{present:i||x,children:({present:s})=>(0,r.jsx)(d.sG.div,{"data-state":x?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:s&&l})})});function U(e,t){return`${e}-trigger-${t}`}function G(e,t){return`${e}-content-${t}`}V.displayName=_;var K=s(51001);let H=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(q,{ref:s,className:(0,K.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));H.displayName=q.displayName;let Z=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(z,{ref:s,className:(0,K.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));Z.displayName=z.displayName;let Y=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(V,{ref:s,className:(0,K.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Y.displayName=V.displayName;var Q=s(37534),W=s(416),X=s(44736);let J=(0,X.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),ee=(0,X.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),et=(0,X.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),es=(0,X.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var er=s(49040),ea=s(61789),en=s(21307);let ei=(0,X.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var el=s(95681),ec=s(60880),eo=s.n(ec);let ed=({title:e,value:t,icon:s,description:a})=>(0,r.jsx)(W.Zp,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between space-x-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-size-sm font-weight-medium text-muted-foreground",children:e}),(0,r.jsx)("h3",{className:"text-2xl font-weight-bold",children:t}),a&&(0,r.jsx)("p",{className:"text-size-xs text-muted-foreground",children:a})]}),(0,r.jsx)("div",{className:"text-muted-foreground",children:s})]})});function eu({data:e}){let[t,s]=(0,a.useState)(""),n=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),i=e=>null==e?"0.00%":`${e.toFixed(2)}%`;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-2xl font-weight-bold",children:"Portfolio"}),(0,r.jsx)("div",{className:"text-size-sm text-muted-foreground",children:t&&`Last updated: ${t}`})]}),(0,r.jsx)(W.Zp,{className:"my-4 p-6",children:(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-size-lg font-weight-semibold mb-4 flex items-center",children:[(0,r.jsx)(J,{className:"mr-2",size:20}),"Account Information"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Account Type"}),(0,r.jsx)("span",{className:"font-weight-medium",children:"Margin Account"})]}),(0,r.jsx)(el.w,{}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Account Status"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(ee,{className:"mr-1 h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"font-weight-medium text-green-500",children:"Active"})]})]}),(0,r.jsx)(el.w,{}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Account Number"}),(0,r.jsx)(eo(),{href:`/trading-account/${e.accountNumber}`,className:"font-weight-medium hover:underline hover:text-primary transition-colors",children:e.accountNumber})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-size-lg font-weight-semibold mb-4 flex items-center",children:[(0,r.jsx)(et,{className:"mr-2",size:20}),"Broker Details"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Broker Name"}),(0,r.jsx)("span",{className:"font-weight-medium",children:"Interactive Brokers"})]}),(0,r.jsx)(el.w,{}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Connection Status"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500 mr-2"}),(0,r.jsx)("span",{className:"font-weight-medium",children:"Connected"})]})]}),(0,r.jsx)(el.w,{}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Last Sync"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(es,{className:"mr-1 h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"font-weight-medium",children:t})]})]})]})]})]})}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)(ed,{title:"Total Value",value:n(e.totalValue),icon:(0,r.jsx)(er.A,{size:24})}),(0,r.jsx)(ed,{title:"Day Change",value:n(e.dayChange),description:i(e.dayChangePercent),icon:(0,r.jsx)(ea.A,{size:24})}),(0,r.jsx)(ed,{title:"Total Return",value:n(e.totalReturn),description:`${i(e.totalReturnPercent)} YTD`,icon:(0,r.jsx)(en.A,{size:24})}),(0,r.jsx)(ed,{title:"Cash Balance",value:n(e.cashBalance),description:`${i(e.cashBalancePercent)} of portfolio`,icon:(0,r.jsx)(ei,{size:24})})]})]})}var em=s(93004),ex=s(45806),eh=s(90169);let ef=(0,X.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var ep=s(24017);ep.callServer,ep.findSourceMapURL;var ej=s(61013),ev=s(51948),eb=s(64357);function ey({session:e,data:t,defaultAccountId:s}){let[n,i]=(0,a.useState)({}),[l,c]=(0,a.useState)(!0),o=(0,ev.U)("(min-width: 768px)");if(!t.positions&&l)return(0,r.jsx)(W.Zp,{className:"p-4 flex items-center justify-center",children:(0,r.jsx)(ex.A,{className:"h-6 w-6 animate-spin"})});if(0===(t.positions??[]).length)return(0,r.jsx)(W.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center py-6 text-muted-foreground",children:[(0,r.jsx)(eh.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("h3",{className:"font-medium mb-1",children:"No Positions"}),(0,r.jsx)("p",{className:"text-sm",children:"You don't have any open positions yet."})]})});let d=e=>{let t=n[e];if(!t)return null;let s="BULLISH"===t.trend,a="BEARISH"===t.trend;return s||a?(0,r.jsx)(ej.Bc,{children:(0,r.jsxs)(ej.m_,{children:[(0,r.jsx)(ej.k$,{children:s?(0,r.jsx)(ea.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(ef,{className:"h-4 w-4 text-red-500"})}),(0,r.jsxs)(ej.ZI,{children:[(0,r.jsxs)("p",{children:["Trending ",s?"Bullish":"Bearish"]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Buy: $",t.buyTrade," | Sell: $",t.sellTrade]})]})]})}):null};return o?(0,r.jsx)(W.Zp,{className:"p-4",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"text-left text-sm text-muted-foreground",children:[(0,r.jsx)("th",{className:"pb-3",children:"Symbol"}),(0,r.jsx)("th",{className:"pb-3",children:"Shares"}),(0,r.jsx)("th",{className:"pb-3",children:"Price"}),(0,r.jsx)("th",{className:"pb-3",children:"Avg Cost"}),(0,r.jsx)("th",{className:"pb-3",children:"Value"}),(0,r.jsx)("th",{className:"pb-3",children:"Return"}),(0,r.jsx)("th",{className:"pb-3",children:"Action"})]})}),(0,r.jsx)("tbody",{children:0===(t.positions??[]).length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:7,className:"text-center py-4 text-muted-foreground",children:"No positions found"})}):(t.positions??[]).map(t=>(0,r.jsxs)("tr",{className:"border-t",children:[(0,r.jsx)("td",{className:"py-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"font-medium",children:t.symbol}),d(t.symbol)]})}),(0,r.jsx)("td",{className:"py-3",children:t.shares}),(0,r.jsxs)("td",{className:"py-3",children:["$",t.price.toFixed(2)]}),(0,r.jsxs)("td",{className:"py-3",children:["$",t.avgCost.toFixed(2)]}),(0,r.jsxs)("td",{className:"py-3",children:["$",t.value.toFixed(2)]}),(0,r.jsxs)("td",{className:`py-3 ${t.return>=0?"text-green-600":"text-red-600"}`,children:[t.return>=0?"+":"",t.return.toFixed(2),"%"]}),(0,r.jsx)("td",{className:"py-3",children:(0,r.jsx)(em.A,{userId:e.user.id,symbol:t.symbol,action:"SELL",price:t.price,quantity:t.shares,defaultAccountId:s})})]},t.symbol))})]})}):(0,r.jsx)("div",{className:"space-y-4",children:(t.positions??[]).map(t=>(0,r.jsxs)(W.Zp,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg font-semibold",children:t.symbol}),d(t.symbol)]}),(0,r.jsx)(em.A,{userId:e.user.id,symbol:t.symbol,action:"SELL",price:t.price,quantity:t.shares,defaultAccountId:s})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)(er.A,{className:"h-3 w-3"})," Current Price"]}),(0,r.jsxs)("div",{className:"font-medium",children:["$",t.price.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)(eh.A,{className:"h-3 w-3"})," Shares"]}),(0,r.jsx)("div",{className:"font-medium",children:t.shares})]})]}),(0,r.jsx)(el.w,{className:"my-4"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground mb-1",children:"Position Value"}),(0,r.jsxs)("div",{className:"font-semibold",children:["$",t.value.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground mb-1",children:"Return"}),(0,r.jsxs)(eb.E,{variant:t.return>=0?"success":"destructive",children:[(0,r.jsx)(ei,{className:"h-3 w-3 mr-1"}),t.return>=0?"+":"",t.return.toFixed(2),"%"]})]})]}),(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t",children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Average Cost"}),(0,r.jsxs)("div",{className:"font-medium",children:["$",t.avgCost.toFixed(2)]})]})]},t.symbol))})}var eg=s(39832),eN=s(40017),ew=s(55751),eA=s(85504);let eC=(0,ep.createServerReference)("40618b691c6e99388ef4bfa6ff8eca71c2abcfb3f7",ep.callServer,void 0,ep.findSourceMapURL,"getAllOrders");var ek=s(91213),eI=s(89806),eR=s(77703);let eD=(0,ep.createServerReference)("702df2c2bf9d7b6941865381f6850baa0c7332ebd6",ep.callServer,void 0,ep.findSourceMapURL,"cancelOrder"),eS=(0,ep.createServerReference)("407feaf61217dad38d5d724eef943a6451a09c2dc3",ep.callServer,void 0,ep.findSourceMapURL,"cancelAllOrders");var eE=s(20830),eP=s(10013),eT=s(80519),eF=s(64020);let eB=["PreSubmitted","Submitted"];function eM({data:e}){let[t,s]=(0,a.useState)([]),[n,i]=(0,a.useState)(!0),[l,c]=(0,a.useState)(null),[o,d]=(0,a.useState)(null),[u,m]=(0,a.useState)(!1),{toast:x}=(0,eE.dj)();(0,eT.useSearchParams)().get("tab");let h=(0,ev.U)("(min-width: 768px)"),f=(0,a.useCallback)(async()=>{try{i(!0);let t=await eC(e.userId),r=e.accountId?t.filter(t=>t.accountId===e.accountId):t;s(r)}catch(e){c("Failed to fetch orders"),console.error("Error fetching orders:",e)}finally{i(!1)}},[e.userId,e.accountId]),p=async(s,r)=>{try{if(d(s),await eD(e.userId,s,r)){let e=async()=>{for(let e=0;e<3;e++){await new Promise(e=>setTimeout(e,1e3)),await f();let e=t.find(e=>e.orderId===s);if(!e||"Cancelled"===e.status)break}};await e(),x({title:"Order cancelled successfully",description:`Order #${s} has been cancelled.`})}else x({variant:"destructive",title:"Failed to cancel order",description:"Please try again or check your connection."})}catch(e){x({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"An unexpected error occurred."})}finally{d(null)}},j=async()=>{try{if(m(!0),await eS(e.userId)){let e=async()=>{for(let e=0;e<3;e++)await new Promise(e=>setTimeout(e,1e3)),await f()};await e(),x({title:"Orders cancelled successfully",description:"All pending orders have been cancelled."})}else x({variant:"destructive",title:"Failed to cancel orders",description:"Please try again or check your connection."})}catch(e){x({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"An unexpected error occurred."})}finally{m(!1)}};if(n)return(0,r.jsx)(ek.S,{});if(l)return(0,r.jsx)(W.Zp,{className:"p-4",children:(0,r.jsx)("p",{className:"text-destructive text-center",children:l})});if(!t||0===t.length)return(0,r.jsx)(W.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center py-6 text-muted-foreground",children:[(0,r.jsx)(eF.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("h3",{className:"font-medium mb-1",children:"No Orders"}),(0,r.jsx)("p",{className:"text-sm",children:"You haven't placed any orders yet."})]})});let v=t.filter(e=>eB.includes(e.status));return(0,r.jsxs)(W.Zp,{children:[v.length>0&&(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)(eP.Lt,{children:[(0,r.jsx)(eP.tv,{asChild:!0,children:(0,r.jsx)(eI.$,{variant:"destructive",size:"sm",disabled:u,children:u?(0,r.jsx)(ek.S,{}):(0,r.jsxs)(r.Fragment,{children:["Cancel All Orders (",v.length,")"]})})}),(0,r.jsxs)(eP.EO,{children:[(0,r.jsxs)(eP.wd,{children:[(0,r.jsx)(eP.r7,{children:"Cancel All Orders"}),(0,r.jsxs)(eP.$v,{children:["Are you sure you want to cancel all pending orders? This will cancel ",v.length," order",1===v.length?"":"s",". This action cannot be undone."]})]}),(0,r.jsxs)(eP.ck,{children:[(0,r.jsx)(eP.Zr,{children:"Cancel"}),(0,r.jsx)(eP.Rx,{onClick:j,className:"bg-destructive hover:bg-destructive/90",children:"Confirm"})]})]})]})}),h?(0,r.jsx)(ew.F,{className:"h-[600px]",children:(0,r.jsxs)(eg.XI,{children:[(0,r.jsx)(eg.A0,{children:(0,r.jsxs)(eg.Hj,{children:[(0,r.jsx)(eg.nd,{children:"Date"}),(0,r.jsx)(eg.nd,{children:"Symbol"}),(0,r.jsx)(eg.nd,{children:"Side"}),(0,r.jsx)(eg.nd,{className:"text-right",children:"Quantity"}),(0,r.jsx)(eg.nd,{className:"text-right",children:"Price"}),(0,r.jsx)(eg.nd,{className:"text-right",children:"Total"}),(0,r.jsx)(eg.nd,{children:"Status"}),(0,r.jsx)(eg.nd,{children:"Actions"})]})}),(0,r.jsx)(eg.BF,{children:t.map(e=>(0,r.jsxs)(eg.Hj,{children:[(0,r.jsx)(eg.nA,{children:(0,eA.GP)(new Date(e.timestamp),"MMM dd, yyyy HH:mm")}),(0,r.jsx)(eg.nA,{className:"font-medium",children:e.symbol}),(0,r.jsx)(eg.nA,{children:(0,r.jsx)(eb.E,{variant:"BUY"===e.action?"default":"destructive",children:e.action})}),(0,r.jsx)(eg.nA,{className:"text-right",children:(0,eN.T)(e.quantity)}),(0,r.jsxs)(eg.nA,{className:"text-right",children:["$",(0,eN.T)(e.price)]}),(0,r.jsxs)(eg.nA,{className:"text-right",children:["$",(0,eN.T)(e.quantity*e.price)]}),(0,r.jsx)(eg.nA,{children:(0,r.jsx)(eb.E,{variant:"Filled"===e.status?"default":"Error"===e.status?"destructive":"secondary",children:e.status})}),(0,r.jsx)(eg.nA,{children:eB.includes(e.status)&&(0,r.jsxs)(eP.Lt,{children:[(0,r.jsx)(eP.tv,{asChild:!0,children:(0,r.jsxs)(eI.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",disabled:o===e.orderId,children:[o===e.orderId?(0,r.jsx)(ek.S,{}):(0,r.jsx)(eR.A,{className:"h-4 w-4 text-destructive"}),(0,r.jsx)("span",{className:"sr-only",children:"Cancel order"})]})}),(0,r.jsxs)(eP.EO,{children:[(0,r.jsxs)(eP.wd,{children:[(0,r.jsx)(eP.r7,{children:"Cancel Order"}),(0,r.jsx)(eP.$v,{children:"Are you sure you want to cancel this order? This action cannot be undone."})]}),(0,r.jsxs)(eP.ck,{children:[(0,r.jsx)(eP.Zr,{children:"Cancel"}),(0,r.jsx)(eP.Rx,{onClick:()=>p(e.orderId,e.clientId),className:"bg-destructive hover:bg-destructive/90",children:"Confirm"})]})]})]})})]},e.orderId))})]})}):(0,r.jsx)(ew.F,{className:"h-[calc(100vh-300px)]",children:(0,r.jsx)("div",{className:"space-y-2 p-4",children:t.map(e=>(0,r.jsxs)(W.Zp,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg font-semibold",children:e.symbol}),(0,r.jsx)(eb.E,{variant:"Filled"===e.status?"default":"Error"===e.status?"destructive":"secondary",children:e.status})]}),eB.includes(e.status)&&(0,r.jsxs)(eP.Lt,{children:[(0,r.jsx)(eP.tv,{asChild:!0,children:(0,r.jsx)(eI.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",disabled:o===e.orderId,children:o===e.orderId?(0,r.jsx)(ek.S,{}):(0,r.jsx)(eR.A,{className:"h-4 w-4 text-destructive"})})}),(0,r.jsxs)(eP.EO,{children:[(0,r.jsxs)(eP.wd,{children:[(0,r.jsx)(eP.r7,{children:"Cancel Order"}),(0,r.jsx)(eP.$v,{children:"Are you sure you want to cancel this order? This action cannot be undone."})]}),(0,r.jsxs)(eP.ck,{children:[(0,r.jsx)(eP.Zr,{children:"Cancel"}),(0,r.jsx)(eP.Rx,{onClick:()=>p(e.orderId,e.clientId),className:"bg-destructive hover:bg-destructive/90",children:"Confirm"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)(eF.A,{className:"h-3 w-3"})," Action"]}),(0,r.jsx)(eb.E,{variant:"BUY"===e.action?"default":"destructive",children:e.action})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)(er.A,{className:"h-3 w-3"})," Price"]}),(0,r.jsxs)("div",{className:"font-medium",children:["$",(0,eN.T)(e.price)]})]})]}),(0,r.jsx)(el.w,{className:"my-4"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground mb-1",children:"Quantity"}),(0,r.jsx)("div",{className:"font-medium",children:(0,eN.T)(e.quantity)})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground mb-1",children:"Total Value"}),(0,r.jsxs)("div",{className:"font-semibold",children:["$",(0,eN.T)(e.quantity*e.price)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mt-4 pt-4 border-t",children:[(0,r.jsx)(es,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:(0,eA.GP)(new Date(e.timestamp),"MMM dd, yyyy HH:mm")})]})]},e.orderId))})})]})}var e$=s(95311),eL=s(35545);function eq({session:e,portfolioData:t}){let s=(0,eT.useRouter)(),n=(0,eT.useSearchParams)(),i=n.get("tab")||"holdings",[l,c]=(0,a.useState)(""),[o,d]=(0,a.useState)(null);return(0,r.jsxs)("div",{className:"space-y-4 py-4 p-4",children:[t.length>1&&(0,r.jsx)("div",{className:"w-[240px]",children:(0,r.jsxs)(Q.l6,{value:l,onValueChange:e=>{c(e);let s=t.find(t=>t.accountNumber===e);s&&d(s)},children:[(0,r.jsx)(Q.bq,{children:(0,r.jsx)(Q.yv,{placeholder:"Select account"})}),(0,r.jsx)(Q.gC,{children:t.map(e=>(0,r.jsx)(Q.eb,{value:e.accountNumber,children:e.accountNumber},e.accountNumber))})]})}),(0,r.jsx)("div",{children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(e$.E,{className:"h-[200px]"}),children:o&&(0,r.jsx)(eu,{data:o})})}),(0,r.jsxs)($,{value:i,onValueChange:e=>{let t=new URLSearchParams(n);t.set("tab",e),s.push(`?${t.toString()}`)},className:"w-full",children:[(0,r.jsxs)(H,{children:[(0,r.jsx)(Z,{value:"holdings",children:"Holdings"}),(0,r.jsx)(Z,{value:"orders",children:"Orders"}),(0,r.jsx)(Z,{value:"auto-prefill",children:"Auto Order Prefill"})]}),(0,r.jsx)(Y,{value:"holdings",className:"space-y-4",children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(e$.E,{className:"h-[400px]"}),children:o&&(0,r.jsx)(ey,{session:e,data:o,defaultAccountId:l})})}),(0,r.jsx)(Y,{value:"orders",className:"space-y-4",children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(e$.E,{className:"h-[400px]"}),children:(0,r.jsx)(eM,{data:{userId:e.user.id,accountId:l}})})}),(0,r.jsx)(Y,{value:"auto-prefill",className:"space-y-4",children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(e$.E,{className:"h-[400px]"}),children:(0,r.jsx)(eL.AutoOrderPrefillList,{userId:e.user.id})})})]})]})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32723:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c":()=>n.F,"40d00296bbd66cc5fcc797b15497b533bde5714055":()=>r.Q,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665":()=>a.V,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e":()=>a.e});var r=s(82063),a=s(43806),n=s(80322)},33873:e=>{"use strict";e.exports=require("path")},38842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(52927),a=s(52058),n=s(13317),i=s(15565),l=s(82063),c=s(53471),o=s(57657);async function d(){let e=await (0,n.j2)();e||(0,a.redirect)("/auth");let[t]=await Promise.all([(0,l.Q)(e.user.id)]);return t&&0!==t.length?(0,r.jsx)(i.default,{session:e,portfolioData:t}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full space-y-6 text-center border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,r.jsx)(c.A,{className:"h-12 w-12 text-yellow-500"}),(0,r.jsx)(o.A,{className:"h-12 w-12 text-gray-400"})]}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Unable to Load Portfolio Data"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We're having trouble connecting to the server. This might be due to:"}),(0,r.jsxs)("ul",{className:"text-gray-600 text-left list-disc list-inside",children:[(0,r.jsx)("li",{children:"A temporary connection issue"}),(0,r.jsx)("li",{children:"Server maintenance"}),(0,r.jsx)("li",{children:"No portfolio items created yet"})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55830:(e,t,s)=>{"use strict";s.d(t,{M:()=>n,f:()=>i});var r=s(43806),a=s(76977);async function n(e){try{let t=await (0,r.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let s=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return new a.IBApi({clientId:s,host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port})}catch(e){throw console.error("Error creating IBKR connection:",e),e}}async function i(e){try{let t=await (0,r.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let s=new a.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port}),n=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return{ibApiNext:s,clientId:n}}catch(e){throw console.error("Error creating IBKR connection:",e),e}}},57975:e=>{"use strict";e.exports=require("node:util")},61242:(e,t,s)=>{Promise.resolve().then(s.bind(s,24308))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63402:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=s(42585),a=s(59246),n=s(63528),i=s.n(n),l=s(83599),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["portfolio",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38842)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/portfolio/page",pathname:"/portfolio",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},71858:(e,t,s)=>{Promise.resolve().then(s.bind(s,15565))},77598:e=>{"use strict";e.exports=require("node:crypto")},77703:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(44736).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},79551:e=>{"use strict";e.exports=require("url")},80322:(e,t,s)=>{"use strict";s.d(t,{F:()=>n});var r=s(49068);s(77048);var a=s(55830);async function n(e){try{let{ibApiNext:t,clientId:s}=await (0,a.f)(e);return new Promise((e,s)=>{let r=setTimeout(()=>s(Error("Connection timeout")),3e3);t.connect().getAccountSummary("All","NetLiquidation").subscribe({next:()=>{clearTimeout(r),e(!0),t.disconnect()},error:e=>{clearTimeout(r),s(e),t.disconnect()}}).add(()=>{t.isConnected&&(t.disconnect(),console.log("Disconnected from IBKR"))})})}catch(e){return console.error("Error testing IBKR connection:",e),!1}}(0,s(84672).D)([n]),(0,r.A)(n,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",null)},82063:(e,t,s)=>{"use strict";s.d(t,{Q:()=>c});var r=s(49068);s(77048);var a=s(55830),n=s(80322),i=s(91581),l=s(76977);async function c(e){try{if(!await (0,n.F)(e))return null;let t=await (0,a.M)(e),s={},r=new Promise((e,r)=>{let a=setTimeout(()=>{t.disconnect(),r(Error("Timeout waiting for data"))},15e3),n=!1,c=!1,o=0,d=new Map,u=()=>{n&&c&&(clearTimeout(a),t.disconnect(),e(Object.values(s)))};t.on(l.EventName.error,(e,t,s)=>{console.error(`Error: ${e.message} - code: ${t} - reqId: ${s}`)}),t.on(l.EventName.accountSummary,(e,t,r,a,n)=>{s[t]||(s[t]={accountNumber:t,totalValue:0,dayChange:0,dayChangePercent:0,totalReturn:0,totalReturnPercent:0,cashBalance:0,cashBalancePercent:0,positions:[],orders:[]});let i=s[t];switch(r){case"NetLiquidation":i.totalValue=parseFloat(a);break;case"AvailableFunds":i.cashBalance=parseFloat(a);break;case"UnrealizedPnL":i.totalReturn=parseFloat(a);break;case"DayPnL":i.dayChange=parseFloat(a)}});let m=async e=>{if(0===e.length)return;let t=e.map(e=>e.symbol),r=await (0,i.gO)(t);e.forEach(e=>{let t=r[e.symbol].regularMarketPrice,a=e.shares*t,n=e.shares*e.avgCost;s[e.account].positions=s[e.account].positions.filter(t=>t.symbol!==e.symbol),s[e.account].positions.push({symbol:e.symbol,shares:e.shares,price:t,value:a,avgCost:e.avgCost,return:(a-n)/n*100})})};t.on(l.EventName.position,async(e,t,r,a)=>{if(!t.symbol)return;let n=`${e}-${t.symbol}`,i=d.get(n);i?(i.shares+=r,i.avgCost=(i.avgCost*(i.shares-r)+a*r)/i.shares):(o++,d.set(n,{account:e,symbol:t.symbol,shares:r,avgCost:a})),s[e]||(s[e]={accountNumber:e,totalValue:0,dayChange:0,dayChangePercent:0,totalReturn:0,totalReturnPercent:0,cashBalance:0,cashBalancePercent:0,positions:[],orders:[]});let l=Array.from(d.values());l.length>=20&&(await m(l),d.size)}),t.on(l.EventName.positionEnd,async()=>{let e=Array.from(d.values());await m(e),d.size,c=!0,n&&u()}),t.on(l.EventName.accountSummaryEnd,()=>{for(let e of Object.values(s))e.dayChangePercent=0!==e.totalValue?e.dayChange/(e.totalValue-e.dayChange)*100:0,e.totalReturnPercent=0!==e.totalValue?e.totalReturn/(e.totalValue-e.totalReturn)*100:0,e.cashBalancePercent=0!==e.totalValue?e.cashBalance/e.totalValue*100:0;n=!0,u()}),t.connect(),t.reqAccountSummary(1,"All","NetLiquidation,AvailableFunds,UnrealizedPnL,DayPnL"),t.reqPositions()});return await r}catch(e){return console.error("Error fetching portfolio data:",e),null}}(0,s(84672).D)([c]),(0,r.A)(c,"40d00296bbd66cc5fcc797b15497b533bde5714055",null)},91645:e=>{"use strict";e.exports=require("net")},95311:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(43197),a=s(51001);function n({className:e,...t}){return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[3491,7728,6631,1773,7400,5438,7524,4923,9292,575,3314,6765,1841,2452,3114,4515,5669,6839,100],()=>s(63402));module.exports=r})();