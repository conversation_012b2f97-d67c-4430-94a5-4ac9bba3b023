(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,r,t)=>{"use strict";t.d(r,{j2:()=>u,Y9:()=>l,Jv:()=>c});var n=t(74723),i=t(89886),a=t(68941),o=t(30935);let s={...{secret:process.env.AUTH_SECRET,providers:[o.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,i.y)(a.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let t=await a.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!t)return e;let n=t.user_profiles?.settings;return{...e,user:{...e.user,id:t.id,role:n.role??"user"}}}}},{handlers:l,auth:u,signIn:c,signOut:d}=(0,n.Ay)(s)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43169:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(99292);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49068:(e,r,t)=>{"use strict";Object.defineProperty(r,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=t(21601)},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var n=t(67566);let i=globalThis.__prisma||new n.PrismaClient},69433:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$RSC_SERVER_ACTION_0:()=>s,default:()=>l});var n=t(52927),i=t(49068);t(77048);var a=t(13317),o=t(52058);let s=async function(){await (0,a.Jv)("Google")};async function l(){let e=await (0,a.j2)();if(!e?.user)return(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[(0,n.jsx)("div",{className:"text-size-lg mb-4",children:"Please log in to view your profile."}),(0,n.jsx)("form",{action:(0,i.A)(s,"008c456793b8f928c926a46a00c819c151008d27dc",null),children:(0,n.jsx)("button",{className:"px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90",children:"Sign in with Google"})})]});let{user:r}=e;e&&(0,o.redirect)("/risk-signals")}},73788:(e,r,t)=>{"use strict";let n;Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{arrayBufferToString:function(){return s},decrypt:function(){return c},encrypt:function(){return u},getActionEncryptionKey:function(){return y},getClientReferenceManifestForRsc:function(){return g},getServerModuleMap:function(){return f},setReferenceManifestsSingleton:function(){return p},stringToUint8Array:function(){return l}});let i=t(86715),a=t(64404),o=t(29294);function s(e){let r=new Uint8Array(e),t=r.byteLength;if(t<65535)return String.fromCharCode.apply(null,r);let n="";for(let e=0;e<t;e++)n+=String.fromCharCode(r[e]);return n}function l(e){let r=e.length,t=new Uint8Array(r);for(let n=0;n<r;n++)t[n]=e.charCodeAt(n);return t}function u(e,r,t){return crypto.subtle.encrypt({name:"AES-GCM",iv:r},e,t)}function c(e,r,t){return crypto.subtle.decrypt({name:"AES-GCM",iv:r},e,t)}let d=Symbol.for("next.server.action-manifests");function p({page:e,clientReferenceManifest:r,serverActionsManifest:t,serverModuleMap:n}){var i;let o=null==(i=globalThis[d])?void 0:i.clientReferenceManifestsPerPage;globalThis[d]={clientReferenceManifestsPerPage:{...o,[(0,a.normalizeAppPath)(e)]:r},serverActionsManifest:t,serverModuleMap:n}}function f(){let e=globalThis[d];if(!e)throw Object.defineProperty(new i.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function g(){let e=globalThis[d];if(!e)throw Object.defineProperty(new i.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:r}=e,t=o.workAsyncStorage.getStore();if(!t){var n=r;let e=Object.values(n),t={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let r of e)t.clientModules={...t.clientModules,...r.clientModules},t.edgeRscModuleMapping={...t.edgeRscModuleMapping,...r.edgeRscModuleMapping},t.rscModuleMapping={...t.rscModuleMapping,...r.rscModuleMapping};return t}let a=r[t.route];if(!a)throw Object.defineProperty(new i.InvariantError(`Missing Client Reference Manifest for ${t.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return a}async function y(){if(n)return n;let e=globalThis[d];if(!e)throw Object.defineProperty(new i.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let r=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===r)throw Object.defineProperty(new i.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",l(atob(r)),"AES-GCM",!0,["encrypt","decrypt"])}},77048:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{decryptActionBoundArgs:function(){return y},encryptActionBoundArgs:function(){return g}}),t(40188);let n=t(21601),i=t(59175),a=t(95933),o=t(73788),s=t(63033),l=t(349),u=function(e){return e&&e.__esModule?e:{default:e}}(t(60154)),c=new TextEncoder,d=new TextDecoder;async function p(e,r){let t=await (0,o.getActionEncryptionKey)();if(void 0===t)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(r),i=n.slice(0,16),a=n.slice(16),s=d.decode(await (0,o.decrypt)(t,(0,o.stringToUint8Array)(i),(0,o.stringToUint8Array)(a)));if(!s.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return s.slice(e.length)}async function f(e,r){let t=await (0,o.getActionEncryptionKey)();if(void 0===t)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);s.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let i=(0,o.arrayBufferToString)(n.buffer),a=await (0,o.encrypt)(t,n,c.encode(e+r));return btoa(i+(0,o.arrayBufferToString)(a))}let g=u.default.cache(async function e(r,...t){let{clientModules:i}=(0,o.getClientReferenceManifestForRsc)(),u=Error();Error.captureStackTrace(u,e);let c=!1,d=s.workUnitAsyncStorage.getStore(),p=(null==d?void 0:d.type)==="prerender"?(0,l.createHangingInputAbortSignal)(d):void 0,g=await (0,a.streamToString)((0,n.renderToReadableStream)(t,i,{signal:p,onError(e){(null==p||!p.aborted)&&(c||(c=!0,u.message=e instanceof Error?e.message:String(e)))}}),p);if(c)throw u;if(!d)return f(r,g);let y=(0,s.getPrerenderResumeDataCache)(d),m=(0,s.getRenderResumeDataCache)(d),v=r+g,b=(null==y?void 0:y.encryptedBoundArgs.get(v))??(null==m?void 0:m.encryptedBoundArgs.get(v));if(b)return b;let _="prerender"===d.type?d.cacheSignal:void 0;null==_||_.beginRead();let E=await f(r,g);return null==_||_.endRead(),null==y||y.encryptedBoundArgs.set(v,E),E});async function y(e,r){let t,n=await r,a=s.workUnitAsyncStorage.getStore();if(a){let r="prerender"===a.type?a.cacheSignal:void 0,i=(0,s.getPrerenderResumeDataCache)(a),o=(0,s.getRenderResumeDataCache)(a);(t=(null==i?void 0:i.decryptedBoundArgs.get(n))??(null==o?void 0:o.decryptedBoundArgs.get(n)))||(null==r||r.beginRead(),t=await p(e,n),null==r||r.endRead(),null==i||i.decryptedBoundArgs.set(n,t))}else t=await p(e,n);let{edgeRscModuleMapping:l,rscModuleMapping:u}=(0,o.getClientReferenceManifestForRsc)();return await (0,i.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(c.encode(t)),(null==a?void 0:a.type)==="prerender"?a.renderSignal.aborted?e.close():a.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:u,serverModuleMap:(0,o.getServerModuleMap)()}})}},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},86641:()=>{},87313:()=>{},97744:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"008c456793b8f928c926a46a00c819c151008d27dc":()=>n.$$RSC_SERVER_ACTION_0});var n=t(69433)},99402:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var n=t(42585),i=t(59246),a=t(63528),o=t.n(a),s=t(83599),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);t.d(r,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69433)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[3491,7728,6631,1773,7400,5438,7524,4923,9292,4515],()=>t(99402));module.exports=n})();