(()=>{var e={};e.id=3015,e.ids=[3015],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34203:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>i});var n=t(96849),a=t(59246),p=t(27109),o=t(87728);async function i(){let e=process.env.VAPID_PUBLIC_KEY;return e?(console.log("Sending VAPID public key:",e),new o.NextResponse(e)):(console.error("VAPID public key not found in environment variables"),new o.NextResponse(null,{status:500}))}let u=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/push/vapid-key/route",pathname:"/api/push/vapid-key",filename:"route",bundlePath:"app/api/push/vapid-key/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/vapid-key/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=u;function v(){return(0,p.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86641:()=>{},87313:()=>{},96849:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3491,7728],()=>t(34203));module.exports=s})();