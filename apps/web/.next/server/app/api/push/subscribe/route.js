(()=>{var e={};e.id=243,e.ids=[243],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},21820:e=>{"use strict";e.exports=require("os")},21827:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>b});var t={};s.r(t),s.d(t,{POST:()=>d});var i=s(96849),u=s(59246),o=s(27109),p=s(87728),a=s(17304),n=s(68941),c=s(95045);async function d(e){try{let r=await (0,c.gf)({req:e,secret:process.env.NEXTAUTH_SECRET});if(console.log("POST /api/push/subscribe -> token:",r),!r?.sub)return p.NextResponse.json({error:"Unauthorized"},{status:401});let{subscription:s}=await e.json();console.log("POST /api/push/subscribe -> subscription:",s);let{endpoint:t,keys:i}=s,{p256dh:u,auth:o}=i,a=await n.A.pushSubscription.findFirst({where:{endpoint:t,user_id:r.sub}});if(a)return console.log("Subscription already exists"),p.NextResponse.json({message:"Subscription already exists",subscription:a});let d=await n.A.pushSubscription.create({data:{endpoint:t,p256dh:u,auth:o,user_id:r.sub}});return console.log("Subscription saved:",d),p.NextResponse.json({message:"Subscription added successfully",subscription:d})}catch(e){return console.error("Error in POST /api/push/subscribe:",e),p.NextResponse.json({error:"Failed to add subscription"},{status:500})}}a.setVapidDetails("mailto:<EMAIL>",process.env.VAPID_PUBLIC_KEY,process.env.VAPID_PRIVATE_KEY);let l=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/push/subscribe/route",pathname:"/api/push/subscribe",filename:"route",bundlePath:"app/api/push/subscribe/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/subscribe/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:b,serverHooks:h}=l;function q(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:b})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,r,s)=>{"use strict";s.d(r,{A:()=>i});var t=s(67566);let i=globalThis.__prisma||new t.PrismaClient},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86641:()=>{},87313:()=>{},91645:e=>{"use strict";e.exports=require("net")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[3491,7728,7400,5496],()=>s(21827));module.exports=t})();