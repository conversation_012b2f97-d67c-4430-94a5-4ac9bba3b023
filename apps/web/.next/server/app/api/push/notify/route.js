"use strict";(()=>{var e={};e.id=7798,e.ids=[7798],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10470:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var i=r(96849),a=r(59246),s=r(27109),o=r(39863),l=e([o]);o=(l.then?(await l)():l)[0];let p=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/push/notify/route",pathname:"/api/push/notify",filename:"route",bundlePath:"app/api/push/notify/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:x}=p;function d(){return(0,s.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}n()}catch(e){n(e)}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},22461:(e,t,r)=>{r.r(t),r.d(t,{"4061815cf2a2c2868673e85dee1a5fab71cb0423aa":()=>n.Z});var n=r(78631)},22690:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{A:()=>R});var i=r(52927),a=r(95444),s=r(11611),o=r(83756),l=r(64214),d=r(82906),p=r(57582),u=r(62892),c=r(64313),x=r(34314);let e={backgroundColor:"#f6f9fc",fontFamily:'-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'},f={backgroundColor:"#ffffff",margin:"0 auto",padding:"40px 20px",borderRadius:"5px",maxWidth:"600px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},h={color:"#1a1a1a",fontSize:"24px",lineHeight:"1.3",margin:"0 0 20px",textAlign:"center"},g={fontSize:"16px",lineHeight:"1.5",color:"#4a5568",margin:"0 0 20px"},m={margin:"20px 0"},y={backgroundColor:"#f8fafc",padding:"20px",borderRadius:"8px",border:"1px solid #e2e8f0",margin:"20px 0"},b={fontSize:"16px",color:"#2d3748",margin:"10px 0",lineHeight:"1.5"},v={backgroundColor:"#3182ce",borderRadius:"5px",color:"#fff",fontSize:"16px",fontWeight:"bold",textDecoration:"none",textAlign:"center",display:"block",width:"100%",maxWidth:"200px",margin:"20px auto"},j={fontSize:"14px",color:"#718096",fontStyle:"italic",margin:"20px 0",padding:"10px",backgroundColor:"#f7fafc",borderRadius:"5px"},w={fontSize:"14px",color:"#718096",margin:"30px 0 10px",textAlign:"center"},k={fontSize:"12px",color:"#a0aec0",margin:"10px 0 0",textAlign:"center"},R=({recipientName:t,updateDate:r,totalSignals:n,dashboardUrl:R})=>(0,i.jsxs)(a.E,{children:[(0,i.jsx)(s.p,{}),(0,i.jsx)(o.l,{children:"Risk Range Signals Have Been Updated"}),(0,i.jsx)(l.n,{style:e,children:(0,i.jsxs)(d.m,{style:f,children:[(0,i.jsx)(p.D,{as:"h1",style:h,children:"Risk Range Signals Update"}),(0,i.jsx)(u.E,{style:g,children:t?`Hello ${t},`:"Hello,"}),(0,i.jsxs)(c.w,{style:m,children:[(0,i.jsx)(u.E,{style:g,children:"We've successfully updated our Risk Range Signals database. Here's a quick summary:"}),(0,i.jsxs)("div",{style:y,children:[(0,i.jsxs)(u.E,{style:b,children:["✨ ",(0,i.jsx)("strong",{children:"Update Completed:"})," ",r]}),(0,i.jsxs)(u.E,{style:b,children:["\uD83D\uDCCA ",(0,i.jsx)("strong",{children:"Total Signals Processed:"})," ",n]})]}),(0,i.jsx)(u.E,{style:g,children:"You can view the latest signals and analysis on your dashboard:"}),(0,i.jsx)(x.$,{style:{...v,padding:"12px 20px"},href:R,children:"View Dashboard"})]}),(0,i.jsx)(u.E,{style:j,children:"Note: This data is updated automatically to ensure you have the most current market insights."}),(0,i.jsx)(u.E,{style:w,children:"This is an automated notification. Please do not reply to this email."}),(0,i.jsx)(u.E,{style:k,children:"The information provided is for informational purposes only and should not be considered as investment advice."})]})})]});n()}catch(e){n(e)}})},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34314:(e,t,r)=>{r.d(t,{$:()=>y});var n=r(60154),i=r(52927),a=Object.defineProperty,s=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,d=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))d.call(t,r)&&u(e,r,t[r]);if(l)for(var r of l(t))p.call(t,r)&&u(e,r,t[r]);return e},x=(e,t)=>s(e,o(t)),f=(e,t)=>{var r={};for(var n in e)d.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>t.indexOf(n)&&p.call(e,n)&&(r[n]=e[n]);return r};function h(e){let t=0;if(!e)return t;if("number"==typeof e)return e;let r=/^([\d.]+)(px|em|rem|%)$/.exec(e);if(!r||3!==r.length)return 0;{let e=parseFloat(r[1]);switch(r[2]){case"px":default:return e;case"em":case"rem":return 16*e;case"%":return e/100*600}}}var g=e=>"number"!=typeof e||isNaN(Number(e))?null:3*e/4;function m(e){if(0===e)return[0,0];let t=0,r=()=>t>0?e/t/2:1/0;for(;r()>5;)t++;return[r(),t]}var y=n.forwardRef((e,t)=>{var r,n,a,s,{children:o,style:l,target:d="_blank"}=e,p=f(e,["children","style","target"]);let{pt:u,pr:y,pb:j,pl:w}=function({padding:e="",paddingTop:t,paddingRight:r,paddingBottom:n,paddingLeft:i}){let a=0,s=0,o=0,l=0;if("number"==typeof e)a=e,s=e,o=e,l=e;else{let t=e.split(/\s+/);switch(t.length){case 1:a=h(t[0]),s=h(t[0]),o=h(t[0]),l=h(t[0]);break;case 2:a=h(t[0]),o=h(t[0]),s=h(t[1]),l=h(t[1]);break;case 3:a=h(t[0]),s=h(t[1]),l=h(t[1]),o=h(t[2]);break;case 4:a=h(t[0]),s=h(t[1]),o=h(t[2]),l=h(t[3])}}return{pt:t?h(t):a,pr:r?h(r):s,pb:n?h(n):o,pl:i?h(i):l}}({padding:null==l?void 0:l.padding,paddingLeft:null!=(r=null==l?void 0:l.paddingLeft)?r:null==l?void 0:l.paddingInline,paddingRight:null!=(n=null==l?void 0:l.paddingRight)?n:null==l?void 0:l.paddingInline,paddingTop:null!=(a=null==l?void 0:l.paddingTop)?a:null==l?void 0:l.paddingBlock,paddingBottom:null!=(s=null==l?void 0:l.paddingBottom)?s:null==l?void 0:l.paddingBlock}),k=g(u+j),[R,S]=m(w),[q,P]=m(y);return(0,i.jsxs)("a",x(c({},p),{ref:t,style:b(x(c({},l),{pt:u,pr:y,pb:j,pl:w})),target:d,children:[(0,i.jsx)("span",{dangerouslySetInnerHTML:{__html:`<!--[if mso]><i style="mso-font-width:${100*R}%;mso-text-raise:${k}" hidden>${"&#8202;".repeat(S)}</i><![endif]-->`}}),(0,i.jsx)("span",{style:v(j),children:o}),(0,i.jsx)("span",{dangerouslySetInnerHTML:{__html:`<!--[if mso]><i style="mso-font-width:${100*q}%" hidden>${"&#8202;".repeat(P)}&#8203;</i><![endif]-->`}})]}))});y.displayName="Button";var b=e=>{let t=e||{},{pt:r,pr:n,pb:i,pl:a}=t;return x(c({lineHeight:"100%",textDecoration:"none",display:"inline-block",maxWidth:"100%",msoPaddingAlt:"0px"},f(t,["pt","pr","pb","pl"])),{padding:`${r}px ${n}px ${i}px ${a}px`})},v=e=>({maxWidth:"100%",display:"inline-block",lineHeight:"120%",msoPaddingAlt:"0px",msoTextRaise:g(e||0)})},34631:e=>{e.exports=require("tls")},39863:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{POST:()=>c});var i=r(87728),a=r(17304),s=r(68941),o=r(13317),l=r(78631),d=r(22690),p=r(61835),u=e([d,p]);async function c(e){try{let t=e.headers.get("x-api-key")===process.env.INTERNAL_API_KEY,{message:r,count:n}=await e.json();if(!t){let e=await (0,o.j2)();if(!e?.user?.id)return i.NextResponse.json({error:"Unauthorized"},{status:401})}let u=await s.A.pushSubscription.findMany({where:{deleted_at:null}});if(!u.length)return i.NextResponse.json({message:"No active subscriptions found"});let c={};u.forEach(e=>{c[e.user_id]||(c[e.user_id]=[]),c[e.user_id].push(e)});let x={title:"Risk Signal Update",body:r,icon:"/icon-512x512.png",badge:"/icon-96x96.png",data:{url:"/risk-signals"}},f=await Promise.allSettled(Object.entries(c).map(async([e,t])=>{try{await Promise.all(t.map(async e=>{if(!e.endpoint||!e.p256dh||!e.auth)throw Error("Invalid subscription details");try{await a.sendNotification({endpoint:e.endpoint,keys:{p256dh:e.p256dh,auth:e.auth}},JSON.stringify(x))}catch(t){throw 410===t.statusCode&&await s.A.pushSubscription.update({where:{id:e.id},data:{deleted_at:new Date}}),t}}));let r=await s.A.user.findUnique({where:{id:e},select:{name:!0,email:!0}});if(!r?.email)throw Error("User email not found");let i={body:await (0,p.render)((0,d.A)({recipientName:r.name||void 0,updateDate:new Date().toLocaleDateString(),totalSignals:n,dashboardUrl:`${process.env.NEXT_PUBLIC_APP_URL}/risk-signals`})),to:[r.email],subject:"Risk Range Signals Have Been Updated"};return await (0,l.Z)(i),{success:!0,userId:e}}catch(t){return{success:!1,userId:e,error:t.message||"Unknown error"}}}));return i.NextResponse.json({results:f})}catch(e){return console.error("Error sending notifications:",e),i.NextResponse.json({error:"Failed to send notifications"},{status:500})}}[d,p]=u.then?(await u)():u,a.setVapidDetails("mailto:<EMAIL>",process.env.VAPID_PUBLIC_KEY,process.env.VAPID_PRIVATE_KEY),n()}catch(e){n(e)}})},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83505:e=>{e.exports=import("prettier/standalone")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[3491,7728,6631,7400,5438,1835,5496,5227,2677],()=>r(10470));module.exports=n})();