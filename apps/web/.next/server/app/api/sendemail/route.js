(()=>{var e={};e.id=1192,e.ids=[1192],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22461:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"4061815cf2a2c2868673e85dee1a5fab71cb0423aa":()=>s.Z});var s=t(78631)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37683:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(96849),i=t(59246),n=t(27109),o=t(87728),u=t(78631);let p=async e=>{try{let r=await e.json(),t=await (0,u.Z)(r);if(!t)return o.NextResponse.json("Ok",{status:400});return o.NextResponse.json(t)}catch(e){return o.NextResponse.json(e,{status:400})}},c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/sendemail/route",pathname:"/api/sendemail",filename:"route",bundlePath:"app/api/sendemail/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/sendemail/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78631:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var s=t(49068);t(77048);var a=t(98206);async function i({body:e,to:r,subject:t}){console.log("sendEmail()",{body:e,to:r,subject:t});let s=new a.u(process.env.RESEND_API_KEY);try{let{data:a,error:i}=await s.emails.send({from:`Aquarius Technologies <${process.env.RESEND_EMAIL_FROM}>`,to:r,subject:t,html:e});if(i)return!1;return!0}catch(e){return!1}}(0,t(84672).D)([i]),(0,s.A)(i,"4061815cf2a2c2868673e85dee1a5fab71cb0423aa",null)},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},86641:()=>{},87313:()=>{},96849:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3491,7728,6631,5227],()=>t(37683));module.exports=s})();