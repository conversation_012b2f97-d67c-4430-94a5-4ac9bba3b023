(()=>{var e={};e.id=1014,e.ids=[1014],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,r,t)=>{"use strict";t.d(r,{j2:()=>p,Y9:()=>n,Jv:()=>d});var s=t(74723),a=t(89886),i=t(68941),u=t(30935);let o={...{secret:process.env.AUTH_SECRET,providers:[u.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,a.y)(i.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let t=await i.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!t)return e;let s=t.user_profiles?.settings;return{...e,user:{...e.user,id:t.id,role:s.role??"user"}}}}},{handlers:n,auth:p,signIn:d,signOut:c}=(0,s.Ay)(o)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(67566);let a=globalThis.__prisma||new s.PrismaClient},77598:e=>{"use strict";e.exports=require("node:crypto")},83263:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>o,POST:()=>n});var a=t(96849),i=t(59246),u=t(27109);let{GET:o,POST:n}=t(13317).Y9,p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=p;function x(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},86641:()=>{},87313:()=>{},96849:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3491,7728,7400,5438],()=>t(83263));module.exports=s})();