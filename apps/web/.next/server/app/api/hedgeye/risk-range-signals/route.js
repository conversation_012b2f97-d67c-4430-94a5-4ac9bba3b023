(()=>{var e={};e.id=1799,e.ids=[1799],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13275:(e,t,r)=>{"use strict";r.d(t,{Jt:()=>o,KU:()=>d,Nd:()=>l,u6:()=>c,w4:()=>i});var a=r(49068);r(77048);var s=r(68941),n=r(84672);let i=async(e,t=!0)=>{if(console.info("1. Input targetDate:",e),isNaN(e.getTime()))return console.info("2. Invalid date detected:",e),[];let r=e.toISOString().split("T")[0];console.info("3. Converted dateString:",r);let a=await l();console.info("4. Retrieved archives:",{archiveDates:a.trendChanges.map(e=>e.toISOString().split("T")[0])});let n=a.trendChanges.some(e=>e.toISOString().split("T")[0]===r);console.info("5. Date exists in archives?",n),!n&&t&&(console.info("6. Target date not found, finding closest date..."),console.info("7. Found closest date:",r=a.trendChanges.reduce((t,r)=>{let a=Math.abs(r.getTime()-e.getTime()),s=Math.abs(t.getTime()-e.getTime()),n=a<s?r:t;return console.info("6a. Comparing dates:",{date:r.toISOString(),diff:a,isCloser:a<s}),n},a.trendChanges[0]).toISOString().split("T")[0])),console.info("8. Querying database with dateString:",r);let i=(await s.A.$queryRaw`SELECT id, original_index as "originalIndex", index, description, trend, buy_trade as "buyTrade", sell_trade as "sellTrade", previous_close as "previousClose", date FROM "trend_change" WHERE CAST(date AS DATE) = CAST(${r} AS DATE);`).map(e=>({...e,buyTrade:"object"==typeof e.buyTrade&&null!==e.buyTrade?Number(e.buyTrade.toString()):e.buyTrade,sellTrade:"object"==typeof e.sellTrade&&null!==e.sellTrade?Number(e.sellTrade.toString()):e.sellTrade,previousClose:"object"==typeof e.previousClose&&null!==e.previousClose?Number(e.previousClose.toString()):e.previousClose}));return console.info("9. Query results:",{count:i.length,firstResult:i[0]}),i};async function o(e){let t=e?new Date(e):new Date;t.setUTCHours(12,0,0,0);let r=await i(t);if(0===r.length){let e=await s.A.trendChange.findFirst({orderBy:{date:"desc"}});return JSON.stringify(await i(e?.date?new Date(e.date.setUTCHours(12,0,0,0)):new Date))}return JSON.stringify(r.map(e=>({...e,date:e.date.toISOString()})))}async function d(e){let t=await i(e[0].date,!1);if(console.info("existing.length ->",t.length),t&&t.length>0)return console.log("Existing trend change already in the database for this date: ",e[0].date),0;let r=e.map((e,t)=>({...e,originalIndex:e.originalIndex??t,buyTrade:"number"==typeof e.buyTrade?e.buyTrade:Number(e.buyTrade),sellTrade:"number"==typeof e.sellTrade?e.sellTrade:Number(e.sellTrade),previousClose:"number"==typeof e.previousClose?e.previousClose:Number(e.previousClose)}));return(await s.A.trendChange.createMany({data:r})).count}async function l(){try{let e=await s.A.trendChange.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[],t=await s.A.upsideDownsidePotential.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[];return{trendChanges:e.map(e=>e._max.date).filter(e=>null!==e),visualization:t.map(e=>e._max.date).filter(e=>null!==e)}}catch(e){return console.error("Error fetching archives of risk signals:",e),{trendChanges:[],visualization:[]}}}async function c(){return(await l()).trendChanges[0].toISOString()}(0,n.D)([i,o,d,l,c]),(0,a.A)(i,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a",null),(0,a.A)(o,"4082914884143dd2970140de0dd1f1be2975301463",null),(0,a.A)(d,"40e54e4c2e785198c3edb84e94b2846d18cc39479b",null),(0,a.A)(l,"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b",null),(0,a.A)(c,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520",null)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32325:e=>{"use strict";e.exports=require("jsdom")},33873:e=>{"use strict";e.exports=require("path")},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},59343:(e,t,r)=>{"use strict";function a(e){return process.env[e]||""}r.d(t,{A:()=>a})},62849:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"0007678de1756c39a1ebf423f8ff65f412a154ca44":()=>a.M4,"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b":()=>s.Nd,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520":()=>s.u6,"0066bd8c4e0f5fbf16018f5142229b7813fee3af46":()=>a.Lv,"00faa5035bbdcc4d45998dc22a86a949f2dfb796f7":()=>a.LB,"4082914884143dd2970140de0dd1f1be2975301463":()=>s.Jt,"40dd82ab3d6d3c22d3be9a14e44923ca6caa280ddc":()=>a.Ay,"40e54e4c2e785198c3edb84e94b2846d18cc39479b":()=>s.KU,"40fea060f6eb973a2674de96eff54bdb24dc2ec292":()=>a.Rn,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a":()=>s.w4});var a=r(93680),s=r(13275)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67566:(e,t,r)=>{e.exports={...r(94970)}},68941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(67566);let s=globalThis.__prisma||new a.PrismaClient},73566:e=>{"use strict";e.exports=require("worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},83997:e=>{"use strict";e.exports=require("tty")},86641:()=>{},87313:()=>{},92664:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,WD:()=>c});var a=r(63501),s=r.n(a),n=r(30981),i=r.n(n),o=r(59343);let d=i()({colorize:!0}),l=s()({level:process.env.PINO_LOG_LEVEL||"info",customLevels:{emerg:80,alert:70,crit:60,error:50,warn:40,notice:30,info:20,debug:10},useOnlyCustomLevels:!0,base:{env:"production"},formatters:{level:e=>({level:e.toUpperCase()})}},d);async function c(e){let t="production"===(0,o.A)("NODE_ENV")?"UJt6jTT1Tj8MmbNQgn1WemXW":"uboJNurXKDEm5FSZcTfqfT51";await fetch("https://in.logs.betterstack.com",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}})}},93680:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,LB:()=>f,Lv:()=>u,M4:()=>p,Rn:()=>c});var a=r(49068);r(77048);var s=r(92664),n=r(59343),i=r(32325),o=r(84672);let d=()=>{let e=new Headers;return e.append("Cookie",(0,n.A)("HEDGEYE_SESSION_COOKIE")),e.append("cache-control","no-store"),e};async function l(e="https://app.hedgeye.com/feed_items/all?page=1&with_category=33-risk-range-signals"){return await fetch(e,{method:"GET",headers:d()}).then(async e=>{if(await (0,s.WD)({dt:new Date,message:e.ok?"Fetch risk range signal page url successful.":"Fetch risk range signal result is undefined."}),!e.ok)throw Error("Fetching risk signal returns nothing");return await e.text()}).catch(async e=>{throw await (0,s.WD)({dt:new Date,message:e}),Error(e)})}async function c(e){let t=e.match(/<p class='med-article__meta__date'>(.*?)<\/p>/),r=e.match(/<a class='med-article__title-link' href='(.*?)'>/);if(t&&r){let e=t[1],a=r[1];if(!e)return null;let s=e.split(" "),[n,i,o]=s[0].split("/"),d=s[1].split(":"),l=d[0],c=d[1].substring(0,2),u=d[1].substring(3,5);return{date:new Date(`${n}/${i}/${o} ${l}:${c} ${u} EST`),href:`https://app.hedgeye.com${a}`}}return null}async function u(){return e=>new g().parseFromString(e,"text/html").querySelector('table[class*="dtr-table"]')}async function p(){return(e,t)=>{let r=[],a=0;return Array.from(e.querySelector("tbody").rows).forEach(e=>{let s={},n=e.cells[0],i=n.textContent.match(/(.+) \((.+)\)/);i&&(s.index=i[1],s.trend=i[2]);let o=n.querySelector("em");o&&(s.description=o.textContent);let d=e.cells[1],l=e.cells[2],c=e.cells[3];s.buyTrade=Number(d.textContent.replace(/,/g,"")),s.sellTrade=Number(l.textContent.replace(/,/g,"")),s.previousClose=Number(c.textContent.replace(/,/g,"")),s.originalIndex=a,s.date=t,r.push(s),a++}),r}}async function f(){let e=await u(),t=await p();return async(r,a)=>t(e(r),a)}class g{parseFromString(e,t="text/html"){return new i.JSDOM(e,{contentType:t}).window.document}}(0,o.D)([l,c,u,p,f]),(0,a.A)(l,"40dd82ab3d6d3c22d3be9a14e44923ca6caa280ddc",null),(0,a.A)(c,"40fea060f6eb973a2674de96eff54bdb24dc2ec292",null),(0,a.A)(u,"0066bd8c4e0f5fbf16018f5142229b7813fee3af46",null),(0,a.A)(p,"0007678de1756c39a1ebf423f8ff65f412a154ca44",null),(0,a.A)(f,"00faa5035bbdcc4d45998dc22a86a949f2dfb796f7",null)},94735:e=>{"use strict";e.exports=require("events")},94970:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};((e,r)=>{for(var a in r)t(e,a,{get:r[a],enumerable:!0})})(n,{Prisma:()=>o,PrismaClient:()=>i,default:()=>d}),e.exports=((e,n,i,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let i of a(n))s.call(e,i)||void 0===i||t(e,i,{get:()=>n[i],enumerable:!(o=r(n,i))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var i=class{constructor(){throw Error('@prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.')}},o={defineExtension:function(e){return"function"==typeof e?e:t=>t.$extends(e)},getExtensionContext:function(e){return e},prismaVersion:{client:"6.9.0",engine:"81e4af48011447c3cc503a190e86995b66d2a28e"}},d={Prisma:o}},98179:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>T,serverHooks:()=>C,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>S});var a={};r.r(a),r.d(a,{GET:()=>w,POST:()=>x,dynamic:()=>y});var s=r(96849),n=r(59246),i=r(27109),o=r(93680),d=r(13275);let l=e=>new Date(Date.UTC(parseInt(e.slice(0,4)),parseInt(e.slice(5,7))-1,parseInt(e.slice(8,10)),parseInt(e.slice(11,13)),parseInt(e.slice(14,16)),parseInt(e.slice(17,19))));class c{static{this.DATE_PUBLISHED_REGEX=/<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/}static extractPublishedDate(e){let t=e.match(this.DATE_PUBLISHED_REGEX);return t?l(t[1]):null}}async function u(e){return c.extractPublishedDate(e)}class p{static async notifyRiskSignalsUpdate(e){await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/push/notify`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":process.env.INTERNAL_API_KEY??""},body:JSON.stringify({message:"Risk signals data has been updated!",count:e})})}static async notifyRiskSignalsUpdateWithTrendChanges(e){await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/push/notify-trend-changes`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":process.env.INTERNAL_API_KEY??""},body:JSON.stringify({selectedTrendChangeDate:e})})}}async function f(e){await p.notifyRiskSignalsUpdate(e)}async function g(e){await p.notifyRiskSignalsUpdateWithTrendChanges(e)}var b=r(91773),h=r(87728);let y="force-dynamic",m=async e=>{console.info("getLatestRiskRangeSignalsHandler() started ...");try{(0,b.unstable_noStore)();let e=await (0,o.Ay)();if(!e)throw Error("Failed to fetch risk signal page");let t=await u(e);if(!t)throw Error("Could not extract published date from HTML");let r=(0,o.LB)();if(!r)throw Error("Failed to create trend change model");let a=await (await r)(e,t);if(!a||!Array.isArray(a))throw Error("Invalid trend changes result");if(a.length>0){console.info("Saving trend changes to the database ...");let e=await (0,d.KU)(a);console.info("Trend changes saved to the database ... saveResult ->",e),e&&e>0&&(await f(a.length),await g(t))}return h.NextResponse.json({message:t.toISOString(),timestamp:new Date,status:200,count:a.length})}catch(r){console.error("Error in risk range signals handler:",r);let e=r instanceof Error?r.message:"Unknown error occurred",t=r instanceof Error&&r.message.includes("Failed to fetch")?503:500;return h.NextResponse.json({message:e,timestamp:new Date,status:t})}},w=m,x=m,T=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/hedgeye/risk-range-signals/route",pathname:"/api/hedgeye/risk-range-signals",filename:"route",bundlePath:"app/api/hedgeye/risk-range-signals/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/risk-range-signals/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:v,workUnitAsyncStorage:S,serverHooks:C}=T;function E(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:S})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[3491,7728,6631,1773,2874],()=>r(98179));module.exports=a})();