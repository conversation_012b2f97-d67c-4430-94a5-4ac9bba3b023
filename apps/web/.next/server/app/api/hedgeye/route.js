(()=>{var e={};e.id=4279,e.ids=[4279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10997:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>h,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>l});var a=r(96849),o=r(59246),n=r(27109),i=r(82641),u=r(92664),c=r(59343),d=r(91773),p=r(87728);let l=async()=>{let e;d.unstable_noStore;try{let t=new Headers;t.append("Cookie",(0,c.A)("HEDGEYE_SESSION_COOKIE")),t.append("cache-control","no-store");let r=await fetch("https://app.hedgeye.com/feed_items/all?page=1&with_category=33-risk-range-signals",{method:"GET",headers:t}).then(async e=>{if(await (0,u.WD)({dt:new Date,message:e.ok?"Fetch risk range signal page url successful.":"Fetch risk range signal result is undefined."}),!e.ok)throw Error("Fetching risk signal returns nothing");return await e.text()}).catch(async e=>{throw await (0,u.WD)({dt:new Date,message:e}),Error(e)});e=await (0,i.T)(r);let s=r.match(/<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/);if(s&&s[1]&&console.log(s[1].toString()),!e)return p.NextResponse.json({htmlRaw:e,status:200});{let t=await (0,i.K)(e);if(t)return new p.NextResponse(t,{status:200,headers:new Headers({"Content-Type":"image/png","Cache-Control":"no-store","Content-Length":t+""})});return await (0,u.WD)({dt:new Date,message:"No data - it maybe that the url not found or it parsing error."}),p.NextResponse.json({message:"No data",status:204})}}catch(e){return await (0,u.WD)({dt:new Date,message:e}),u.Ay.error(e),p.NextResponse.json({message:e,status:500})}},h=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/hedgeye/route",pathname:"/api/hedgeye",filename:"route",bundlePath:"app/api/hedgeye/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:x}=h;function f(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},59343:(e,t,r)=>{"use strict";function s(e){return process.env[e]||""}r.d(t,{A:()=>s})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67036:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"406f42eea2675a3d23fe8ddcc90a63376aadac5cc8":()=>s.K,"40c88ae3d31f771b8e364a36bfc04cab67dbc08815":()=>s.T});var s=r(82641)},73566:e=>{"use strict";e.exports=require("worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},82641:(e,t,r)=>{"use strict";r.d(t,{K:()=>o,T:()=>n});var s=r(49068);r(77048);var a=r(92664);async function o(e){try{let t=await fetch(e,{headers:{"cache-control":"no-store"}});return await t.blob()}catch(e){console.log(e)}}async function n(e){let t=/https:\/\/d1yhils6iwh5l5\.cloudfront\.net\/charts\/resized\/(\d+)\/large\/equityTable[^.]*\.png/;var r=e,s=t;let o=r.match(RegExp(s,"g"));o&&o.length;let n=e.match(t);return await (0,a.WD)({dt:new Date,message:`extractEquityTableUrlRegEx() - ${n}`}),n?n[0]:void 0}(0,r(84672).D)([o,n]),(0,s.A)(o,"406f42eea2675a3d23fe8ddcc90a63376aadac5cc8",null),(0,s.A)(n,"40c88ae3d31f771b8e364a36bfc04cab67dbc08815",null)},83997:e=>{"use strict";e.exports=require("tty")},86641:()=>{},87313:()=>{},92664:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c,WD:()=>d});var s=r(63501),a=r.n(s),o=r(30981),n=r.n(o),i=r(59343);let u=n()({colorize:!0}),c=a()({level:process.env.PINO_LOG_LEVEL||"info",customLevels:{emerg:80,alert:70,crit:60,error:50,warn:40,notice:30,info:20,debug:10},useOnlyCustomLevels:!0,base:{env:"production"},formatters:{level:e=>({level:e.toUpperCase()})}},u);async function d(e){let t="production"===(0,i.A)("NODE_ENV")?"UJt6jTT1Tj8MmbNQgn1WemXW":"uboJNurXKDEm5FSZcTfqfT51";await fetch("https://in.logs.betterstack.com",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3491,7728,6631,1773,2874],()=>r(10997));module.exports=s})();