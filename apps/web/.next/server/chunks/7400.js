exports.id=7400,exports.ids=[7400],exports.modules={1663:(e,t,r)=>{"use strict";r.d(t,{D0:()=>n,Kp:()=>p,MT:()=>o,Rd:()=>i,VS:()=>l,mx:()=>d,xW:()=>s,yI:()=>h});var a=r(57994);let i=new TextEncoder,n=new TextDecoder;function s(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}function o(e,t){return s(i.encode(e),new Uint8Array([0]),t)}function c(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function d(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return c(r,t,0),c(r,e%0x100000000,4),r}function l(e){let t=new Uint8Array(4);return c(t,e),t}function p(e){return s(l(e.length),e)}async function h(e,t,r){let i=Math.ceil((t>>3)/32),n=new Uint8Array(32*i);for(let t=0;t<i;t++){let i=new Uint8Array(4+e.length+r.length);i.set(l(t+1)),i.set(e,4),i.set(r,4+e.length),n.set(await (0,a.A)("sha256",i),32*t)}return n.slice(0,t>>3)}},16848:(e,t,r)=>{"use strict";r.d(t,{D:()=>n,l:()=>i});var a=r(28312);let i=a.lF,n=a.D4},22221:(e,t)=>{"use strict";t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},a=e.length;if(a<2)return r;var i=t&&t.decode||d,n=0,s=0,l=0;do{if(-1===(s=e.indexOf("=",n)))break;if(-1===(l=e.indexOf(";",n)))l=a;else if(s>l){n=e.lastIndexOf(";",s-1)+1;continue}var p=o(e,n,s),h=c(e,s,p),u=e.slice(p,h);if(!r.hasOwnProperty(u)){var y=o(e,s+1,l),f=c(e,l,y);34===e.charCodeAt(y)&&34===e.charCodeAt(f-1)&&(y++,f--);var m=e.slice(y,f);r[u]=function(e,t){try{return t(e)}catch(t){return e}}(m,i)}n=l+1}while(n<a);return r},t.l=function(e,t,o){var c=o&&o.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var d=c(t);if(!i.test(d))throw TypeError("argument val is invalid");var l=e+"="+d;if(!o)return l;if(null!=o.maxAge){var p=Math.floor(o.maxAge);if(!isFinite(p))throw TypeError("option maxAge is invalid");l+="; Max-Age="+p}if(o.domain){if(!n.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!s.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){var h,u=o.expires;if(h=u,"[object Date]"!==r.call(h)||isNaN(u.valueOf()))throw TypeError("option expires is invalid");l+="; Expires="+u.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.partitioned&&(l+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():o.priority){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var r=Object.prototype.toString,a=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/;function o(e,t,r){do{var a=e.charCodeAt(t);if(32!==a&&9!==a)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var a=e.charCodeAt(--t);if(32!==a&&9!==a)return t+1}return r}function d(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},22737:(e,t,r)=>{"use strict";function a(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}r.d(t,{A:()=>a})},23585:(e,t,r)=>{"use strict";r.d(t,{Dp:()=>l,Rb:()=>s,T0:()=>o,_L:()=>p,aA:()=>d,ie:()=>i,n:()=>n,xO:()=>c});class a extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class i extends a{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class n extends a{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class s extends a{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class o extends a{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class c extends a{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class d extends a{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class l extends a{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class p extends a{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class h extends a{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}},28312:(e,t,r)=>{"use strict";r.d(t,{D4:()=>s,lF:()=>n});var a=r(4573),i=r(1663);let n=e=>a.Buffer.from(e).toString("base64url"),s=e=>new Uint8Array(a.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=i.D0.decode(t)),t}(e),"base64url"))},31096:(e,t,r)=>{"use strict";r.d(t,{CM:()=>c,Hj:()=>x,Lx:()=>C,OZ:()=>f,Oy:()=>s,P8:()=>T,PM:()=>d,QU:()=>v,SW:()=>y,WS:()=>D,XP:()=>E,_2:()=>W,_z:()=>b,dy:()=>K,eH:()=>k,gs:()=>h,i8:()=>m,jo:()=>A,k9:()=>u,lR:()=>a,me:()=>l,nd:()=>R,o6:()=>P,om:()=>n,rk:()=>g,s5:()=>I,t3:()=>o,tP:()=>H,u$:()=>w,w2:()=>J,xm:()=>M,xz:()=>p});class a extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class i extends a{}i.kind="signIn";class n extends a{}n.type="AdapterError";class s extends a{}s.type="AccessDenied";class o extends a{}o.type="CallbackRouteError";class c extends a{}c.type="ErrorPageLoop";class d extends a{}d.type="EventError";class l extends a{}l.type="InvalidCallbackUrl";class p extends i{constructor(){super(...arguments),this.code="credentials"}}p.type="CredentialsSignin";class h extends a{}h.type="InvalidEndpoints";class u extends a{}u.type="InvalidCheck";class y extends a{}y.type="JWTSessionError";class f extends a{}f.type="MissingAdapter";class m extends a{}m.type="MissingAdapterMethods";class w extends a{}w.type="MissingAuthorize";class A extends a{}A.type="MissingSecret";class E extends i{}E.type="OAuthAccountNotLinked";class g extends i{}g.type="OAuthCallbackError";class b extends a{}b.type="OAuthProfileParseError";class v extends a{}v.type="SessionTokenError";class _ extends i{}_.type="OAuthSignInError";class S extends i{}S.type="EmailSignInError";class k extends a{}k.type="SignOutError";class T extends a{}T.type="UnknownAction";class C extends a{}C.type="UnsupportedStrategy";class x extends a{}x.type="InvalidProvider";class H extends a{}H.type="UntrustedHost";class P extends a{}P.type="Verification";class K extends i{}K.type="MissingCSRF";let O=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function W(e){return e instanceof a&&O.has(e.type)}class I extends a{}I.type="DuplicateConditionalUI";class R extends a{}R.type="MissingWebAuthnAutocomplete";class J extends a{}J.type="WebAuthnVerificationError";class D extends i{}D.type="AccountNotLinked";class M extends a{}M.type="ExperimentalFeatureNotEnabled"},53619:(e,t,r)=>{"use strict";r.d(t,{X:()=>p,c:()=>h});var a,i,n,s,o,c,d=function(e,t,r,a,i){if("m"===a)throw TypeError("Private method is not writable");if("a"===a&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===a?i.call(e,r):i?i.value=r:t.set(e,r),r},l=function(e,t,r,a){if("a"===r&&!a)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?a:"a"===r?a.call(e):a?a.value:t.get(e)};function p(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class h{constructor(e,t,r){if(a.add(this),i.set(this,{}),n.set(this,void 0),s.set(this,void 0),d(this,s,r,"f"),d(this,n,e,"f"),!t)return;let{name:o}=e;for(let[e,r]of Object.entries(t))e.startsWith(o)&&r&&(l(this,i,"f")[e]=r)}get value(){return Object.keys(l(this,i,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>l(this,i,"f")[e]).join("")}chunk(e,t){let r=l(this,a,"m",c).call(this);for(let i of l(this,a,"m",o).call(this,{name:l(this,n,"f").name,value:e,options:{...l(this,n,"f").options,...t}}))r[i.name]=i;return Object.values(r)}clean(){return Object.values(l(this,a,"m",c).call(this))}}i=new WeakMap,n=new WeakMap,s=new WeakMap,a=new WeakSet,o=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return l(this,i,"f")[e.name]=e.value,[e];let r=[];for(let a=0;a<t;a++){let t=`${e.name}.${a}`,n=e.value.substr(3936*a,3936);r.push({...e,name:t,value:n}),l(this,i,"f")[t]=n}return l(this,s,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},c=function(){let e={};for(let t in l(this,i,"f"))delete l(this,i,"f")?.[t],e[t]={name:t,value:"",options:{...l(this,n,"f").options,maxAge:0}};return e}},57994:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(77598);let i=(e,t)=>(0,a.createHash)(e).update(t).digest()},67566:(e,t,r)=>{e.exports={...r(94970)}},94970:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,n={};((e,r)=>{for(var a in r)t(e,a,{get:r[a],enumerable:!0})})(n,{Prisma:()=>o,PrismaClient:()=>s,default:()=>c}),e.exports=((e,n,s,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let c of a(n))i.call(e,c)||c===s||t(e,c,{get:()=>n[c],enumerable:!(o=r(n,c))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var s=class{constructor(){throw Error('@prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.')}},o={defineExtension:function(e){return"function"==typeof e?e:t=>t.$extends(e)},getExtensionContext:function(e){return e},prismaVersion:{client:"6.9.0",engine:"81e4af48011447c3cc503a190e86995b66d2a28e"}},c={Prisma:o}},95045:(e,t,r)=>{"use strict";let a,i;r.d(t,{D4:()=>eZ,lF:()=>eY,gf:()=>eQ});var n=r(55511);let s=(e,t,r,a,i)=>{let s=parseInt(e.substr(3),10)>>3||20,o=(0,n.createHmac)(e,r.byteLength?r:new Uint8Array(s)).update(t).digest(),c=Math.ceil(i/s),d=new Uint8Array(s*c+a.byteLength+1),l=0,p=0;for(let t=1;t<=c;t++)d.set(a,p),d[p+a.byteLength]=t,d.set((0,n.createHmac)(e,o).update(d.subarray(l,p+a.byteLength+1)).digest(),p),l=p,p+=s;return d.slice(0,i)};"function"!=typeof n.hkdf||process.versions.electron||(a=async(...e)=>new Promise((t,r)=>{n.hkdf(...e,(e,a)=>{e?r(e):t(new Uint8Array(a))})}));let o=async(e,t,r,i,n)=>(a||s)(e,t,r,i,n);function c(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function d(e,t,r,a,i){return o(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=c(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),c(r,"salt"),function(e){let t=c(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(a),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}var l=r(57994),p=r(28312),h=r(23585),u=r(1663),y=r(22737);let f=(e,t)=>{if("string"!=typeof e||!e)throw new h._L(`${t} missing or invalid`)};async function m(e,t){let r;if(!(0,y.A)(e))throw TypeError("JWK must be an object");if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":f(e.crv,'"crv" (Curve) Parameter'),f(e.x,'"x" (X Coordinate) Parameter'),f(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":f(e.crv,'"crv" (Subtype of Key Pair) Parameter'),f(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":f(e.e,'"e" (Exponent) Parameter'),f(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":f(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new h.T0('"kty" (Key Type) Parameter missing or unsupported')}let a=u.Rd.encode(JSON.stringify(r));return(0,p.lF)(await (0,l.A)(t,a))}var w=r(16848);let A=Symbol();var E=r(77598);function g(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new h.T0(`Unsupported JWE Algorithm: ${e}`)}}let b=e=>(0,E.randomFillSync)(new Uint8Array(g(e)>>3)),v=(e,t)=>{if(t.length<<3!==g(e))throw new h.aA("Invalid Initialization Vector length")};var _=r(57975);let S=e=>_.types.isKeyObject(e),k=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new h.T0(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new h.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if(S(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new h.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")};function T(e,t,r,a,i,n){let s=(0,u.xW)(e,t,r,(0,u.mx)(e.length<<3)),o=(0,E.createHmac)(`sha${a}`,i);return o.update(s),o.digest().slice(0,n>>3)}let C=E.webcrypto,x=e=>_.types.isCryptoKey(e);function H(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function P(e,t){return e.name===t}function K(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!P(e.algorithm,"AES-GCM"))throw H("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw H(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!P(e.algorithm,"AES-KW"))throw H("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw H(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw H("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!P(e.algorithm,"PBKDF2"))throw H("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!P(e.algorithm,"RSA-OAEP"))throw H("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw H(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var a=e,i=r;if(i.length&&!i.some(e=>a.usages.includes(e))){let e="CryptoKey does not support this operation, its usages must include ";if(i.length>2){let t=i.pop();e+=`one of ${i.join(", ")}, or ${t}.`}else 2===i.length?e+=`one of ${i[0]} or ${i[1]}.`:e+=`${i[0]}.`;throw TypeError(e)}}function O(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let W=(e,...t)=>O("Key must be ",e,...t);function I(e,t,...r){return O(`Key for the ${e} algorithm must be `,t,...r)}let R=e=>(i||=new Set((0,E.getCiphers)())).has(e),J=e=>S(e)||x(e),D=["KeyObject"];(globalThis.CryptoKey||C?.CryptoKey)&&D.push("CryptoKey");let M=(e,t,r,a,i)=>{let n;if(x(r))K(r,e,"encrypt"),n=E.KeyObject.from(r);else if(r instanceof Uint8Array||S(r))n=r;else throw TypeError(W(r,...D,"Uint8Array"));switch(k(e,n),a?v(e,a):a=b(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,a,i){let n=parseInt(e.slice(1,4),10);S(r)&&(r=r.export());let s=r.subarray(n>>3),o=r.subarray(0,n>>3),c=`aes-${n}-cbc`;if(!R(c))throw new h.T0(`alg ${e} is not supported by your javascript runtime`);let d=(0,E.createCipheriv)(c,s,a),l=(0,u.xW)(d.update(t),d.final()),p=T(i,a,l,parseInt(e.slice(-3),10),o,n);return{ciphertext:l,tag:p,iv:a}}(e,t,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,a,i){let n=parseInt(e.slice(1,4),10),s=`aes-${n}-gcm`;if(!R(s))throw new h.T0(`alg ${e} is not supported by your javascript runtime`);let o=(0,E.createCipheriv)(s,r,a,{authTagLength:16});i.byteLength&&o.setAAD(i,{plaintextLength:t.length});let c=o.update(t);return o.final(),{ciphertext:c,tag:o.getAuthTag(),iv:a}}(e,t,n,a,i);default:throw new h.T0("Unsupported JWE Content Encryption Algorithm")}};var U=r(4573);function $(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function j(e,t,r){if(S(e))return e;if(e instanceof Uint8Array)return(0,E.createSecretKey)(e);if(x(e))return K(e,t,r),E.KeyObject.from(e);throw TypeError(W(e,...D,"Uint8Array"))}let L=(e,t,r)=>{let a=parseInt(e.slice(1,4),10),i=`aes${a}-wrap`;if(!R(i))throw new h.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`);let n=j(t,e,"wrapKey");$(n,e);let s=(0,E.createCipheriv)(i,n,U.Buffer.alloc(8,166));return(0,u.xW)(s.update(r),s.final())},N=(e,t,r)=>{let a=parseInt(e.slice(1,4),10),i=`aes${a}-wrap`;if(!R(i))throw new h.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`);let n=j(t,e,"unwrapKey");$(n,e);let s=(0,E.createDecipheriv)(i,n,U.Buffer.alloc(8,166));return(0,u.xW)(s.update(r),s.final())};function B(e){return(0,y.A)(e)&&"string"==typeof e.kty}new WeakMap;let F=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new h.T0("Unsupported key curve for this operation")}},G=(e,t)=>{let r;if(x(e))r=E.KeyObject.from(e);else if(S(e))r=e;else if(B(e))return e.crv;else throw TypeError(W(e,...D));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return F(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}},z=(0,_.promisify)(E.generateKeyPair);async function V(e,t,r,a,i=new Uint8Array(0),n=new Uint8Array(0)){let s,o;if(x(e))K(e,"ECDH"),s=E.KeyObject.from(e);else if(S(e))s=e;else throw TypeError(W(e,...D));if(x(t))K(t,"ECDH","deriveBits"),o=E.KeyObject.from(t);else if(S(t))o=t;else throw TypeError(W(t,...D));let c=(0,u.xW)((0,u.Kp)(u.Rd.encode(r)),(0,u.Kp)(i),(0,u.Kp)(n),(0,u.VS)(a)),d=(0,E.diffieHellman)({privateKey:o,publicKey:s});return(0,u.yI)(d,a,c)}async function X(e){let t;if(x(e))t=E.KeyObject.from(e);else if(S(e))t=e;else throw TypeError(W(e,...D));switch(t.asymmetricKeyType){case"x25519":return z("x25519");case"x448":return z("x448");case"ec":return z("ec",{namedCurve:G(t)});default:throw new h.T0("Invalid or unsupported EPK")}}let q=e=>["P-256","P-384","P-521","X25519","X448"].includes(G(e));function Y(e){if(!(e instanceof Uint8Array)||e.length<8)throw new h.aA("PBES2 Salt Input must be 8 or more octets")}let Z=(0,_.promisify)(E.pbkdf2);function Q(e,t){if(S(e))return e.export();if(e instanceof Uint8Array)return e;if(x(e))return K(e,t,"deriveBits","deriveKey"),E.KeyObject.from(e).export();throw TypeError(W(e,...D,"Uint8Array"))}let ee=async(e,t,r,a=2048,i=(0,E.randomFillSync)(new Uint8Array(16)))=>{Y(i);let n=(0,u.MT)(e,i),s=parseInt(e.slice(13,16),10)>>3,o=Q(t,e),c=await Z(o,n,a,s,`sha${e.slice(8,11)}`);return{encryptedKey:await L(e.slice(-6),c,r),p2c:a,p2s:(0,p.lF)(i)}},et=async(e,t,r,a,i)=>{Y(i);let n=(0,u.MT)(e,i),s=parseInt(e.slice(13,16),10)>>3,o=Q(t,e),c=await Z(o,n,a,s,`sha${e.slice(8,11)}`);return N(e.slice(-6),c,r)},er=(e,t)=>{let r;try{r=e instanceof E.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)},ea=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");er(e,t)},ei=(0,_.deprecate)(()=>E.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),en=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return E.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return ei();default:return}},es=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function eo(e,t,...r){if(S(e))return e;if(x(e))return K(e,t,...r),E.KeyObject.from(e);throw TypeError(W(e,...D))}let ec=(e,t,r)=>{let a=en(e),i=es(e),n=eo(t,e,"wrapKey","encrypt");return ea(n,e),(0,E.publicEncrypt)({key:n,oaepHash:i,padding:a},r)},ed=(e,t,r)=>{let a=en(e),i=es(e),n=eo(t,e,"unwrapKey","decrypt");return ea(n,e),(0,E.privateDecrypt)({key:n,oaepHash:i,padding:a},r)},el={};function ep(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new h.T0(`Unsupported JWE Algorithm: ${e}`)}}let eh=e=>(0,E.randomFillSync)(new Uint8Array(ep(e)>>3)),eu=e=>{let t;if(x(e)){if(!e.extractable)throw TypeError("CryptoKey is not extractable");t=E.KeyObject.from(e)}else if(S(e))t=e;else if(e instanceof Uint8Array)return{kty:"oct",k:(0,p.lF)(e)};else throw TypeError(W(e,...D,"Uint8Array"));if("secret"!==t.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(t.asymmetricKeyType))throw new h.T0("Unsupported key asymmetricKeyType");return t.export({format:"jwk"})};async function ey(e){return eu(e)}let ef=e=>e?.[Symbol.toStringTag],em=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},ew=(e,t,r,a)=>{if(!(t instanceof Uint8Array)){if(a&&B(t)){if(function(e){return B(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&em(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!J(t))throw TypeError(I(e,t,...D,"Uint8Array",a?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${ef(t)} instances for symmetric algorithms must be of type "secret"`)}},eA=(e,t,r,a)=>{if(a&&B(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&em(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&em(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!J(t))throw TypeError(I(e,t,...D,a?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${ef(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${ef(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${ef(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${ef(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${ef(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function eE(e,t,r,a){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?ew(t,r,a,e):eA(t,r,a,e)}let eg=eE.bind(void 0,!1);eE.bind(void 0,!0);let eb=E.timingSafeEqual,ev=(e,t,r,a,i,n)=>{let s;if(x(t))K(t,e,"decrypt"),s=E.KeyObject.from(t);else if(t instanceof Uint8Array||S(t))s=t;else throw TypeError(W(t,...D,"Uint8Array"));if(!a)throw new h.aA("JWE Initialization Vector missing");if(!i)throw new h.aA("JWE Authentication Tag missing");switch(k(e,s),v(e,a),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,a,i,n){let s,o,c=parseInt(e.slice(1,4),10);S(t)&&(t=t.export());let d=t.subarray(c>>3),l=t.subarray(0,c>>3),p=parseInt(e.slice(-3),10),y=`aes-${c}-cbc`;if(!R(y))throw new h.T0(`alg ${e} is not supported by your javascript runtime`);let f=T(n,a,r,p,l,c);try{s=eb(i,f)}catch{}if(!s)throw new h.xO;try{let e=(0,E.createDecipheriv)(y,d,a);o=(0,u.xW)(e.update(r),e.final())}catch{}if(!o)throw new h.xO;return o}(e,s,r,a,i,n);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,a,i,n){let s=parseInt(e.slice(1,4),10),o=`aes-${s}-gcm`;if(!R(o))throw new h.T0(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,E.createDecipheriv)(o,t,a,{authTagLength:16});e.setAuthTag(i),n.byteLength&&e.setAAD(n,{plaintextLength:r.length});let s=e.update(r);return e.final(),s}catch{throw new h.xO}}(e,s,r,a,i,n);default:throw new h.T0("Unsupported JWE Content Encryption Algorithm")}};async function e_(e,t,r,a){let i=e.slice(0,7),n=await M(i,r,t,a,new Uint8Array(0));return{encryptedKey:n.ciphertext,iv:(0,p.lF)(n.iv),tag:(0,p.lF)(n.tag)}}async function eS(e,t,r,a,i){return ev(e.slice(0,7),t,r,a,i,new Uint8Array(0))}async function ek(e,t,r,a,i={}){let n,s,o;switch(eg(e,r,"encrypt"),r=await el.normalizePublicKey?.(r,e)||r,e){case"dir":o=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!q(r))throw new h.T0("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:d}=i,{epk:l}=i;l||=(await X(r)).privateKey;let{x:u,y,crv:f,kty:m}=await ey(l),w=await V(r,l,"ECDH-ES"===e?t:e,"ECDH-ES"===e?ep(t):parseInt(e.slice(-5,-2),10),c,d);if(s={epk:{x:u,crv:f,kty:m}},"EC"===m&&(s.epk.y=y),c&&(s.apu=(0,p.lF)(c)),d&&(s.apv=(0,p.lF)(d)),"ECDH-ES"===e){o=w;break}o=a||eh(t);let A=e.slice(-6);n=await L(A,w,o);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":o=a||eh(t),n=await ec(e,r,o);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{o=a||eh(t);let{p2c:c,p2s:d}=i;({encryptedKey:n,...s}=await ee(e,r,o,c,d));break}case"A128KW":case"A192KW":case"A256KW":o=a||eh(t),n=await L(e,r,o);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{o=a||eh(t);let{iv:c}=i;({encryptedKey:n,...s}=await e_(e,r,o,c));break}default:throw new h.T0('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:o,encryptedKey:n,parameters:s}}let eT=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},eC=function(e,t,r,a,i){let n;if(void 0!==i.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let s of(n=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!n.has(s))throw new h.T0(`Extension Header Parameter "${s}" is not recognized`);if(void 0===i[s])throw new e(`Extension Header Parameter "${s}" is missing`);if(n.get(s)&&void 0===a[s])throw new e(`Extension Header Parameter "${s}" MUST be integrity protected`)}return new Set(a.crit)};class ex{_plaintext;_protectedHeader;_sharedUnprotectedHeader;_unprotectedHeader;_aad;_cek;_iv;_keyManagementParameters;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,a,i,n,s;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new h.aA("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!eT(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new h.aA("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let o={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(eC(h.aA,new Map,t?.crit,this._protectedHeader,o),void 0!==o.zip)throw new h.T0('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:d}=o;if("string"!=typeof c||!c)throw new h.aA('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof d||!d)throw new h.aA('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);{let i;({cek:a,encryptedKey:r,parameters:i}=await ek(c,d,e,this._cek,this._keyManagementParameters)),i&&(t&&A in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...i}:this.setUnprotectedHeader(i):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...i}:this.setProtectedHeader(i))}n=this._protectedHeader?u.Rd.encode((0,p.lF)(JSON.stringify(this._protectedHeader))):u.Rd.encode(""),this._aad?(s=(0,p.lF)(this._aad),i=(0,u.xW)(n,u.Rd.encode("."),u.Rd.encode(s))):i=n;let{ciphertext:l,tag:y,iv:f}=await M(d,this._plaintext,a,this._iv,i),m={ciphertext:(0,p.lF)(l)};return f&&(m.iv=(0,p.lF)(f)),y&&(m.tag=(0,p.lF)(y)),r&&(m.encrypted_key=(0,p.lF)(r)),s&&(m.aad=s),this._protectedHeader&&(m.protected=u.D0.decode(n)),this._sharedUnprotectedHeader&&(m.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(m.header=this._unprotectedHeader),m}}class eH{_flattened;constructor(e){this._flattened=new ex(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let eP=e=>Math.floor(e.getTime()/1e3),eK=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,eO=e=>{let t,r=eK.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t};function eW(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class eI{_payload;constructor(e={}){if(!(0,y.A)(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:eW("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:eW("setNotBefore",eP(e))}:this._payload={...this._payload,nbf:eP(new Date)+eO(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:eW("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:eW("setExpirationTime",eP(e))}:this._payload={...this._payload,exp:eP(new Date)+eO(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:eP(new Date)}:e instanceof Date?this._payload={...this._payload,iat:eW("setIssuedAt",eP(e))}:"string"==typeof e?this._payload={...this._payload,iat:eW("setIssuedAt",eP(new Date)+eO(e))}:this._payload={...this._payload,iat:eW("setIssuedAt",e)},this}}class eR extends eI{_cek;_iv;_keyManagementParameters;_protectedHeader;_replicateIssuerAsHeader;_replicateSubjectAsHeader;_replicateAudienceAsHeader;setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new eH(u.Rd.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let eJ=e=>e.d?(0,E.createPrivateKey)({format:"jwk",key:e}):(0,E.createPublicKey)({format:"jwk",key:e});async function eD(e,t){if(!(0,y.A)(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return(0,p.D4)(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new h.T0('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return eJ({...e,alg:t});default:throw new h.T0('Unsupported "kty" (Key Type) Parameter value')}}async function eM(e,t,r,a,i){switch(eg(e,t,"decrypt"),t=await el.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new h.aA("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new h.aA("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,n;if(!(0,y.A)(a.epk))throw new h.aA('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!q(t))throw new h.T0("ECDH with the provided key is not allowed or not supported by your javascript runtime");let s=await eD(a.epk,e);if(void 0!==a.apu){if("string"!=typeof a.apu)throw new h.aA('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=(0,p.D4)(a.apu)}catch{throw new h.aA("Failed to base64url decode the apu")}}if(void 0!==a.apv){if("string"!=typeof a.apv)throw new h.aA('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{n=(0,p.D4)(a.apv)}catch{throw new h.aA("Failed to base64url decode the apv")}}let o=await V(s,t,"ECDH-ES"===e?a.enc:e,"ECDH-ES"===e?ep(a.enc):parseInt(e.slice(-5,-2),10),i,n);if("ECDH-ES"===e)return o;if(void 0===r)throw new h.aA("JWE Encrypted Key missing");return N(e.slice(-6),o,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new h.aA("JWE Encrypted Key missing");return ed(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let n;if(void 0===r)throw new h.aA("JWE Encrypted Key missing");if("number"!=typeof a.p2c)throw new h.aA('JOSE Header "p2c" (PBES2 Count) missing or invalid');let s=i?.maxPBES2Count||1e4;if(a.p2c>s)throw new h.aA('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof a.p2s)throw new h.aA('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{n=(0,p.D4)(a.p2s)}catch{throw new h.aA("Failed to base64url decode the p2s")}return et(e,t,r,a.p2c,n)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new h.aA("JWE Encrypted Key missing");return N(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,n;if(void 0===r)throw new h.aA("JWE Encrypted Key missing");if("string"!=typeof a.iv)throw new h.aA('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof a.tag)throw new h.aA('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=(0,p.D4)(a.iv)}catch{throw new h.aA("Failed to base64url decode the iv")}try{n=(0,p.D4)(a.tag)}catch{throw new h.aA("Failed to base64url decode the tag")}return eS(e,t,r,i,n)}default:throw new h.T0('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let eU=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function e$(e,t,r){let a,i,n,s,o,c,d;if(!(0,y.A)(e))throw new h.aA("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new h.aA("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new h.aA("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new h.aA("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new h.aA("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new h.aA("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new h.aA("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new h.aA("JWE AAD incorrect type");if(void 0!==e.header&&!(0,y.A)(e.header))throw new h.aA("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!(0,y.A)(e.unprotected))throw new h.aA("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=(0,p.D4)(e.protected);a=JSON.parse(u.D0.decode(t))}catch{throw new h.aA("JWE Protected Header is invalid")}if(!eT(a,e.header,e.unprotected))throw new h.aA("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let l={...a,...e.header,...e.unprotected};if(eC(h.aA,new Map,r?.crit,a,l),void 0!==l.zip)throw new h.T0('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:f,enc:m}=l;if("string"!=typeof f||!f)throw new h.aA("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof m||!m)throw new h.aA("missing JWE Encryption Algorithm (enc) in JWE Header");let w=r&&eU("keyManagementAlgorithms",r.keyManagementAlgorithms),A=r&&eU("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(w&&!w.has(f)||!w&&f.startsWith("PBES2"))throw new h.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(A&&!A.has(m))throw new h.Rb('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=(0,p.D4)(e.encrypted_key)}catch{throw new h.aA("Failed to base64url decode the encrypted_key")}let E=!1;"function"==typeof t&&(t=await t(a,e),E=!0);try{n=await eM(f,t,i,l,r)}catch(e){if(e instanceof TypeError||e instanceof h.aA||e instanceof h.T0)throw e;n=eh(m)}if(void 0!==e.iv)try{s=(0,p.D4)(e.iv)}catch{throw new h.aA("Failed to base64url decode the iv")}if(void 0!==e.tag)try{o=(0,p.D4)(e.tag)}catch{throw new h.aA("Failed to base64url decode the tag")}let g=u.Rd.encode(e.protected??"");c=void 0!==e.aad?(0,u.xW)(g,u.Rd.encode("."),u.Rd.encode(e.aad)):g;try{d=(0,p.D4)(e.ciphertext)}catch{throw new h.aA("Failed to base64url decode the ciphertext")}let b={plaintext:await ev(m,n,d,s,o,c)};if(void 0!==e.protected&&(b.protectedHeader=a),void 0!==e.aad)try{b.additionalAuthenticatedData=(0,p.D4)(e.aad)}catch{throw new h.aA("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(b.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(b.unprotectedHeader=e.header),E)?{...b,key:t}:b}async function ej(e,t,r){if(e instanceof Uint8Array&&(e=u.D0.decode(e)),"string"!=typeof e)throw new h.aA("Compact JWE must be a string or Uint8Array");let{0:a,1:i,2:n,3:s,4:o,length:c}=e.split(".");if(5!==c)throw new h.aA("Invalid Compact JWE");let d=await e$({ciphertext:s,iv:n||void 0,protected:a,tag:o||void 0,encrypted_key:i||void 0},t,r),l={plaintext:d.plaintext,protectedHeader:d.protectedHeader};return"function"==typeof t?{...l,key:d.key}:l}let eL=e=>e.toLowerCase().replace(/^application\//,""),eN=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),eB=(e,t,r={})=>{let a,i;try{a=JSON.parse(u.D0.decode(t))}catch{}if(!(0,y.A)(a))throw new h.Dp("JWT Claims Set must be a top-level JSON object");let{typ:n}=r;if(n&&("string"!=typeof e.typ||eL(e.typ)!==eL(n)))throw new h.ie('unexpected "typ" JWT header value',a,"typ","check_failed");let{requiredClaims:s=[],issuer:o,subject:c,audience:d,maxTokenAge:l}=r,p=[...s];for(let e of(void 0!==l&&p.push("iat"),void 0!==d&&p.push("aud"),void 0!==c&&p.push("sub"),void 0!==o&&p.push("iss"),new Set(p.reverse())))if(!(e in a))throw new h.ie(`missing required "${e}" claim`,a,e,"missing");if(o&&!(Array.isArray(o)?o:[o]).includes(a.iss))throw new h.ie('unexpected "iss" claim value',a,"iss","check_failed");if(c&&a.sub!==c)throw new h.ie('unexpected "sub" claim value',a,"sub","check_failed");if(d&&!eN(a.aud,"string"==typeof d?[d]:d))throw new h.ie('unexpected "aud" claim value',a,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=eO(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,m=eP(f||new Date);if((void 0!==a.iat||l)&&"number"!=typeof a.iat)throw new h.ie('"iat" claim must be a number',a,"iat","invalid");if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw new h.ie('"nbf" claim must be a number',a,"nbf","invalid");if(a.nbf>m+i)throw new h.ie('"nbf" claim timestamp check failed',a,"nbf","check_failed")}if(void 0!==a.exp){if("number"!=typeof a.exp)throw new h.ie('"exp" claim must be a number',a,"exp","invalid");if(a.exp<=m-i)throw new h.n('"exp" claim timestamp check failed',a,"exp","check_failed")}if(l){let e=m-a.iat;if(e-i>("number"==typeof l?l:eO(l)))throw new h.n('"iat" claim timestamp check failed (too far in the past)',a,"iat","check_failed");if(e<0-i)throw new h.ie('"iat" claim timestamp check failed (it should be in the past)',a,"iat","check_failed")}return a};async function eF(e,t,r){let a=await ej(e,t,r),i=eB(a.protectedHeader,a.plaintext,r),{protectedHeader:n}=a;if(void 0!==n.iss&&n.iss!==i.iss)throw new h.ie('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==n.sub&&n.sub!==i.sub)throw new h.ie('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==n.aud&&JSON.stringify(n.aud)!==JSON.stringify(i.aud))throw new h.ie('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let s={payload:i,protectedHeader:n};return"function"==typeof t?{...s,key:a.key}:s}var eG=r(53619),ez=r(31096),eV=r(22221);let eX=()=>Date.now()/1e3|0,eq="A256CBC-HS512";async function eY(e){let{token:t={},secret:r,maxAge:a=2592e3,salt:i}=e,n=Array.isArray(r)?r:[r],s=await e0(eq,n[0],i),o=await m({kty:"oct",k:w.l(s)},`sha${s.byteLength<<3}`);return await new eR(t).setProtectedHeader({alg:"dir",enc:eq,kid:o}).setIssuedAt().setExpirationTime(eX()+a).setJti(crypto.randomUUID()).encrypt(s)}async function eZ(e){let{token:t,secret:r,salt:a}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:n}=await eF(t,async({kid:e,enc:t})=>{for(let r of i){let i=await e0(t,r,a);if(void 0===e||e===await m({kty:"oct",k:w.l(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[eq,"A256GCM"]});return n}async function eQ(e){let{secureCookie:t,cookieName:r=(0,eG.X)(t??!1).sessionToken.name,decode:a=eZ,salt:i=r,secret:n,logger:s=console,raw:o,req:c}=e;if(!c)throw Error("Must pass `req` to JWT getToken()");let d=c.headers instanceof Headers?c.headers:new Headers(c.headers),l=new eG.c({name:r,options:{secure:t}},(0,eV.q)(d.get("cookie")??""),s).value,p=d.get("authorization");if(l||p?.split(" ")[0]!=="Bearer"||(l=decodeURIComponent(p.split(" ")[1])),!l)return null;if(o)return l;if(!n)throw new ez.jo("Must pass `secret` if not set to JWT getToken()");try{return await a({token:l,secret:n,salt:i})}catch{return null}}async function e0(e,t,r){let a;switch(e){case"A256CBC-HS512":a=64;break;case"A256GCM":a=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await d("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,a)}}};