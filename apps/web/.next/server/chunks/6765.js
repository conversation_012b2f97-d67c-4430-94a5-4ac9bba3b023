exports.id=6765,exports.ids=[6765],exports.modules={11:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectionState=void 0,function(e){e[e.Disconnected=0]="Disconnected",e[e.Connecting=1]="Connecting",e[e.Connected=2]="Connected"}(r||(t.ConnectionState=r={}))},260:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Forex=void 0;let i=n(r(15850));class o{constructor(e,t){let r;this.symbol=e,this.currency=t,this.exchange="IDEALPRO",this.secType=i.default.CASH,o.CURRENCY_SYMBOL_PRIO.indexOf(e)>o.CURRENCY_SYMBOL_PRIO.indexOf(t)&&(r=this.symbol,this.symbol=this.currency,this.currency=r)}}t.Forex=o,o.CURRENCY_SYMBOL_PRIO=["KRW","EUR","GBP","AUD","USD","TRY","ZAR","CAD","CHF","MXN","HKD","JPY","INR","NOK","SEK","RUB"],t.default=o},607:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.startWith=void 0;var n=r(17299),i=r(66389),o=r(96829);t.startWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=i.popScheduler(e);return o.operate(function(t,i){(r?n.concat(e,t,r):n.concat(e,t)).subscribe(i)})}},810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.switchScan=void 0;var n=r(33361),i=r(96829);t.switchScan=function(e,t){return i.operate(function(r,i){var o=t;return n.switchMap(function(t,r){return e(o,t,r)},function(e,t){return o=t,t})(r).subscribe(i),function(){o=null}})}},1071:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.arrRemove=void 0,t.arrRemove=function(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}},1697:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.not=void 0,t.not=function(e,t){return function(r,n){return!e.call(t,r,n)}}},1937:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.AnonymousSubject=t.Subject=void 0;var o=r(57248),s=r(46124),a=r(13804),u=r(1071),c=r(95369),l=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return n(t,e),t.prototype.lift=function(e){var t=new d(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new a.ObjectUnsubscribedError},t.prototype.next=function(e){var t=this;c.errorContext(function(){var r,n;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var o=i(t.currentObservers),s=o.next();!s.done;s=o.next())s.value.next(e)}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}}})},t.prototype.error=function(e){var t=this;c.errorContext(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}})},t.prototype.complete=function(){var e=this;c.errorContext(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null==(e=this.observers)?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,r=this.hasError,n=this.isStopped,i=this.observers;return r||n?s.EMPTY_SUBSCRIPTION:(this.currentObservers=null,i.push(e),new s.Subscription(function(){t.currentObservers=null,u.arrRemove(i,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this.thrownError,n=this.isStopped;t?e.error(r):n&&e.complete()},t.prototype.asObservable=function(){var e=new o.Observable;return e.source=this,e},t.create=function(e,t){return new d(e,t)},t}(o.Observable);t.Subject=l;var d=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return n(t,e),t.prototype.next=function(e){var t,r;null==(r=null==(t=this.destination)?void 0:t.next)||r.call(t,e)},t.prototype.error=function(e){var t,r;null==(r=null==(t=this.destination)?void 0:t.error)||r.call(t,e)},t.prototype.complete=function(){var e,t;null==(t=null==(e=this.destination)?void 0:e.complete)||t.call(e)},t.prototype._subscribe=function(e){var t,r;return null!=(r=null==(t=this.source)?void 0:t.subscribe(e))?r:s.EMPTY_SUBSCRIPTION},t}(l);t.AnonymousSubject=d},2673:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.refCount=void 0;var n=r(96829),i=r(28719);t.refCount=function(){return n.operate(function(e,t){var r=null;e._refCount++;var n=i.createOperatorSubscriber(t,void 0,void 0,void 0,function(){if(!e||e._refCount<=0||0<--e._refCount){r=null;return}var n=e._connection,i=r;r=null,n&&(!i||n===i)&&n.unsubscribe(),t.unsubscribe()});e.subscribe(n),n.closed||(r=e.connect())})}},3349:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.EMPTY_OBSERVER=t.SafeSubscriber=t.Subscriber=void 0;var i=r(17886),o=r(46124),s=r(87273),a=r(64517),u=r(63410),c=r(47540),l=r(78417),d=r(95369),h=function(e){function r(r){var n=e.call(this)||this;return n.isStopped=!1,r?(n.destination=r,o.isSubscription(r)&&r.add(n)):n.destination=t.EMPTY_OBSERVER,n}return n(r,e),r.create=function(e,t,r){return new p(e,t,r)},r.prototype.next=function(e){this.isStopped?A(c.nextNotification(e),this):this._next(e)},r.prototype.error=function(e){this.isStopped?A(c.errorNotification(e),this):(this.isStopped=!0,this._error(e))},r.prototype.complete=function(){this.isStopped?A(c.COMPLETE_NOTIFICATION,this):(this.isStopped=!0,this._complete())},r.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},r.prototype._next=function(e){this.destination.next(e)},r.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},r.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},r}(o.Subscription);t.Subscriber=h;var _=Function.prototype.bind;function f(e,t){return _.call(e,t)}var E=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){T(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){T(e)}else T(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){T(e)}},e}(),p=function(e){function t(t,r,n){var o,a,u=e.call(this)||this;return i.isFunction(t)||!t?o={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:u&&s.config.useDeprecatedNextContext?((a=Object.create(t)).unsubscribe=function(){return u.unsubscribe()},o={next:t.next&&f(t.next,a),error:t.error&&f(t.error,a),complete:t.complete&&f(t.complete,a)}):o=t,u.destination=new E(o),u}return n(t,e),t}(h);function T(e){s.config.useDeprecatedSynchronousErrorHandling?d.captureError(e):a.reportUnhandledError(e)}function A(e,t){var r=s.config.onStoppedNotification;r&&l.timeoutProvider.setTimeout(function(){return r(e,t)})}t.SafeSubscriber=p,t.EMPTY_OBSERVER={closed:!0,next:u.noop,error:function(e){throw e},complete:u.noop}},4310:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.throttle=void 0;var n=r(96829),i=r(28719),o=r(81773);t.throttle=function(e,t){return n.operate(function(r,n){var s=null!=t?t:{},a=s.leading,u=void 0===a||a,c=s.trailing,l=void 0!==c&&c,d=!1,h=null,_=null,f=!1,E=function(){null==_||_.unsubscribe(),_=null,l&&(A(),f&&n.complete())},p=function(){_=null,f&&n.complete()},T=function(t){return _=o.innerFrom(e(t)).subscribe(i.createOperatorSubscriber(n,E,p))},A=function(){if(d){d=!1;var e=h;h=null,n.next(e),f||T(e)}};r.subscribe(i.createOperatorSubscriber(n,function(e){d=!0,h=e,_&&!_.closed||(u?A():T(e))},function(){f=!0,l&&d&&_&&!_.closed||n.complete()}))})}},4510:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TrailingStopOrder=void 0;let n=r(55332),i=r(72785);class o{constructor(e,t,r,o,s,a,u){this.action=e,this.totalQuantity=t,this.auxPrice=r,this.trailingPercent=o,this.transmit=s,this.parentId=a,this.tif=u,this.orderType=n.OrderType.TRAIL,this.transmit=this.transmit??!0,this.parentId=this.parentId??0,this.tif=this.tif??i.TimeInForce.DAY}}t.TrailingStopOrder=o,t.default=o},4759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reduce=void 0;var n=r(91168),i=r(96829);t.reduce=function(e,t){return i.operate(n.scanInternals(e,t,arguments.length>=2,!1,!0))}},5096:e=>{e.exports=function(e,t){var r="";e=(e=e||"Run the trap, drop the bass").split("");var n={a:["@","Ą","Ⱥ","Ʌ","Δ","Λ","Д"],b:["\xdf","Ɓ","Ƀ","ɮ","β","฿"],c:["\xa9","Ȼ","Ͼ"],d:["\xd0","Ɗ","Ԁ","ԁ","Ԃ","ԃ"],e:["\xcb","ĕ","Ǝ","ɘ","Σ","ξ","Ҽ","੬"],f:["Ӻ"],g:["ɢ"],h:["Ħ","ƕ","Ң","Һ","Ӈ","Ԋ"],i:["༏"],j:["Ĵ"],k:["ĸ","Ҡ","Ӄ","Ԟ"],l:["Ĺ"],m:["ʍ","Ӎ","ӎ","Ԡ","ԡ","൩"],n:["\xd1","ŋ","Ɲ","Ͷ","Π","Ҋ"],o:["\xd8","\xf5","\xf8","Ǿ","ʘ","Ѻ","ם","۝","๏"],p:["Ƿ","Ҏ"],q:["্"],r:["\xae","Ʀ","Ȑ","Ɍ","ʀ","Я"],s:["\xa7","Ϟ","ϟ","Ϩ"],t:["Ł","Ŧ","ͳ"],u:["Ʊ","Ս"],v:["ט"],w:["Ш","Ѡ","Ѽ","൰"],x:["Ҳ","Ӿ","Ӽ","ӽ"],y:["\xa5","Ұ","Ӌ"],z:["Ƶ","ɀ"]};return e.forEach(function(e){var t=Math.floor(Math.random()*(n[e=e.toLowerCase()]||[" "]).length);void 0!==n[e]?r+=n[e][t]:r+=e}),r}},5181:e=>{var t={};e.exports=t;var r={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(r).forEach(function(e){var n=r[e],i=t[e]=[];i.open="\x1b["+n[0]+"m",i.close="\x1b["+n[1]+"m"})},6462:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.switchMapTo=void 0;var n=r(33361),i=r(17886);t.switchMapTo=function(e,t){return i.isFunction(t)?n.switchMap(function(){return e},t):n.switchMap(function(){return e})}},6747:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.from=void 0;var n=r(61472),i=r(81773);t.from=function(e,t){return t?n.scheduled(e,t):i.innerFrom(e)}},7133:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isNonFatalError=t.ErrorCode=void 0,function(e){e[e.NO_VALID_ID=-1]="NO_VALID_ID",e[e.ORDER_CANCELLED=202]="ORDER_CANCELLED",e[e.ORDER_MESSAGE=399]="ORDER_MESSAGE",e[e.SCANNER_LOW_PRECISION=492]="SCANNER_LOW_PRECISION",e[e.REQ_MKT_DATA_NOT_AVAIL=354]="REQ_MKT_DATA_NOT_AVAIL",e[e.NO_TRADING_PERMISSIONS=460]="NO_TRADING_PERMISSIONS",e[e.ALREADY_CONNECTED=501]="ALREADY_CONNECTED",e[e.CONNECT_FAIL=502]="CONNECT_FAIL",e[e.UPDATE_TWS=503]="UPDATE_TWS",e[e.NOT_CONNECTED=504]="NOT_CONNECTED",e[e.UNKNOWN_ID=505]="UNKNOWN_ID",e[e.UNSUPPORTED_VERSION=506]="UNSUPPORTED_VERSION",e[e.BAD_LENGTH=507]="BAD_LENGTH",e[e.BAD_MESSAGE=508]="BAD_MESSAGE",e[e.FAIL_SEND=509]="FAIL_SEND",e[e.FAIL_SEND_REQMKT=510]="FAIL_SEND_REQMKT",e[e.FAIL_SEND_CANMKT=511]="FAIL_SEND_CANMKT",e[e.FAIL_SEND_ORDER=512]="FAIL_SEND_ORDER",e[e.FAIL_SEND_ACCT=513]="FAIL_SEND_ACCT",e[e.FAIL_SEND_EXEC=514]="FAIL_SEND_EXEC",e[e.FAIL_SEND_CORDER=515]="FAIL_SEND_CORDER",e[e.FAIL_SEND_OORDER=516]="FAIL_SEND_OORDER",e[e.UNKNOWN_CONTRACT=517]="UNKNOWN_CONTRACT",e[e.FAIL_SEND_REQCONTRACT=518]="FAIL_SEND_REQCONTRACT",e[e.FAIL_SEND_REQMKTDEPTH=519]="FAIL_SEND_REQMKTDEPTH",e[e.FAIL_SEND_CANMKTDEPTH=520]="FAIL_SEND_CANMKTDEPTH",e[e.FAIL_SEND_SERVER_LOG_LEVEL=521]="FAIL_SEND_SERVER_LOG_LEVEL",e[e.FAIL_SEND_FA_REQUEST=522]="FAIL_SEND_FA_REQUEST",e[e.FAIL_SEND_FA_REPLACE=523]="FAIL_SEND_FA_REPLACE",e[e.FAIL_SEND_REQSCANNER=524]="FAIL_SEND_REQSCANNER",e[e.FAIL_SEND_CANSCANNER=525]="FAIL_SEND_CANSCANNER",e[e.FAIL_SEND_REQSCANNERPARAMETERS=526]="FAIL_SEND_REQSCANNERPARAMETERS",e[e.FAIL_SEND_REQHISTDATA=527]="FAIL_SEND_REQHISTDATA",e[e.FAIL_SEND_CANHISTDATA=528]="FAIL_SEND_CANHISTDATA",e[e.FAIL_SEND_REQRTBARS=529]="FAIL_SEND_REQRTBARS",e[e.FAIL_SEND_CANRTBARS=530]="FAIL_SEND_CANRTBARS",e[e.FAIL_SEND_REQCURRTIME=531]="FAIL_SEND_REQCURRTIME",e[e.FAIL_SEND_REQFUNDDATA=532]="FAIL_SEND_REQFUNDDATA",e[e.FAIL_SEND_CANFUNDDATA=533]="FAIL_SEND_CANFUNDDATA",e[e.FAIL_SEND_REQCALCIMPLIEDVOLAT=534]="FAIL_SEND_REQCALCIMPLIEDVOLAT",e[e.FAIL_SEND_REQCALCOPTIONPRICE=535]="FAIL_SEND_REQCALCOPTIONPRICE",e[e.FAIL_SEND_CANCALCIMPLIEDVOLAT=536]="FAIL_SEND_CANCALCIMPLIEDVOLAT",e[e.FAIL_SEND_CANCALCOPTIONPRICE=537]="FAIL_SEND_CANCALCOPTIONPRICE",e[e.FAIL_SEND_REQGLOBALCANCEL=538]="FAIL_SEND_REQGLOBALCANCEL",e[e.FAIL_SEND_REQMARKETDATATYPE=539]="FAIL_SEND_REQMARKETDATATYPE",e[e.FAIL_SEND_REQPOSITIONS=540]="FAIL_SEND_REQPOSITIONS",e[e.FAIL_SEND_CANPOSITIONS=541]="FAIL_SEND_CANPOSITIONS",e[e.FAIL_SEND_REQACCOUNTDATA=542]="FAIL_SEND_REQACCOUNTDATA",e[e.FAIL_SEND_CANACCOUNTDATA=543]="FAIL_SEND_CANACCOUNTDATA",e[e.FAIL_SEND_VERIFYREQUEST=544]="FAIL_SEND_VERIFYREQUEST",e[e.FAIL_SEND_VERIFYMESSAGE=545]="FAIL_SEND_VERIFYMESSAGE",e[e.FAIL_SEND_QUERYDISPLAYGROUPS=546]="FAIL_SEND_QUERYDISPLAYGROUPS",e[e.FAIL_SEND_SUBSCRIBETOGROUPEVENTS=547]="FAIL_SEND_SUBSCRIBETOGROUPEVENTS",e[e.FAIL_SEND_UPDATEDISPLAYGROUP=548]="FAIL_SEND_UPDATEDISPLAYGROUP",e[e.FAIL_SEND_UNSUBSCRIBEFROMGROUPEVENTS=549]="FAIL_SEND_UNSUBSCRIBEFROMGROUPEVENTS",e[e.FAIL_SEND_STARTAPI=550]="FAIL_SEND_STARTAPI",e[e.FAIL_SEND_VERIFYANDAUTHREQUEST=551]="FAIL_SEND_VERIFYANDAUTHREQUEST",e[e.FAIL_SEND_VERIFYANDAUTHMESSAGE=552]="FAIL_SEND_VERIFYANDAUTHMESSAGE",e[e.FAIL_SEND_REQPOSITIONSMULTI=553]="FAIL_SEND_REQPOSITIONSMULTI",e[e.FAIL_SEND_CANPOSITIONSMULTI=554]="FAIL_SEND_CANPOSITIONSMULTI",e[e.FAIL_SEND_REQACCOUNTUPDATESMULTI=555]="FAIL_SEND_REQACCOUNTUPDATESMULTI",e[e.FAIL_SEND_CANACCOUNTUPDATESMULTI=556]="FAIL_SEND_CANACCOUNTUPDATESMULTI",e[e.FAIL_SEND_REQSECDEFOPTPARAMS=557]="FAIL_SEND_REQSECDEFOPTPARAMS",e[e.FAIL_SEND_REQSOFTDOLLARTIERS=558]="FAIL_SEND_REQSOFTDOLLARTIERS",e[e.FAIL_SEND_REQFAMILYCODES=559]="FAIL_SEND_REQFAMILYCODES",e[e.FAIL_SEND_REQMATCHINGSYMBOLS=560]="FAIL_SEND_REQMATCHINGSYMBOLS",e[e.FAIL_SEND_REQMKTDEPTHEXCHANGES=561]="FAIL_SEND_REQMKTDEPTHEXCHANGES",e[e.FAIL_SEND_REQSMARTCOMPONENTS=562]="FAIL_SEND_REQSMARTCOMPONENTS",e[e.FAIL_SEND_REQNEWSPROVIDERS=563]="FAIL_SEND_REQNEWSPROVIDERS",e[e.FAIL_SEND_REQNEWSARTICLE=564]="FAIL_SEND_REQNEWSARTICLE",e[e.FAIL_SEND_REQHISTORICALNEWS=565]="FAIL_SEND_REQHISTORICALNEWS",e[e.FAIL_SEND_REQHEADTIMESTAMP=566]="FAIL_SEND_REQHEADTIMESTAMP",e[e.FAIL_SEND_REQHISTOGRAMDATA=567]="FAIL_SEND_REQHISTOGRAMDATA",e[e.FAIL_SEND_CANCELHISTOGRAMDATA=568]="FAIL_SEND_CANCELHISTOGRAMDATA",e[e.FAIL_SEND_CANCELHEADTIMESTAMP=569]="FAIL_SEND_CANCELHEADTIMESTAMP",e[e.FAIL_SEND_HISTORICAL_TICK=569]="FAIL_SEND_HISTORICAL_TICK",e[e.FAIL_SEND_REQMARKETRULE=570]="FAIL_SEND_REQMARKETRULE",e[e.FAIL_SEND_REQPNL=571]="FAIL_SEND_REQPNL",e[e.FAIL_SEND_CANCELPNL=572]="FAIL_SEND_CANCELPNL",e[e.FAIL_SEND_REQPNLSINGLE=573]="FAIL_SEND_REQPNLSINGLE",e[e.FAIL_SEND_CANCELPNLSINGLE=574]="FAIL_SEND_CANCELPNLSINGLE",e[e.FAIL_SEND_REQHISTORICALTICKS=575]="FAIL_SEND_REQHISTORICALTICKS",e[e.FAIL_SEND_REQTICKBYTICKDATA=576]="FAIL_SEND_REQTICKBYTICKDATA",e[e.FAIL_SEND_CANCELTICKBYTICKDATA=577]="FAIL_SEND_CANCELTICKBYTICKDATA",e[e.FAIL_SEND_REQCOMPLETEDORDERS=578]="FAIL_SEND_REQCOMPLETEDORDERS",e[e.INVALID_SYMBOL=579]="INVALID_SYMBOL",e[e.FAIL_SEND_REQ_WSH_META_DATA=580]="FAIL_SEND_REQ_WSH_META_DATA",e[e.FAIL_SEND_CAN_WSH_META_DATA=581]="FAIL_SEND_CAN_WSH_META_DATA",e[e.FAIL_SEND_REQ_WSH_EVENT_DATA=582]="FAIL_SEND_REQ_WSH_EVENT_DATA",e[e.FAIL_SEND_CAN_WSH_EVENT_DATA=583]="FAIL_SEND_CAN_WSH_EVENT_DATA",e[e.FAIL_SEND_REQ_USER_INFO=584]="FAIL_SEND_REQ_USER_INFO",e[e.FA_PROFILE_NOT_SUPPORTED=585]="FA_PROFILE_NOT_SUPPORTED",e[e.FAIL_READ_MESSAGE=586]="FAIL_READ_MESSAGE",e[e.FAIL_CONNECTION_LOST_BETWEEN_SERVER_AND_TWS=1100]="FAIL_CONNECTION_LOST_BETWEEN_SERVER_AND_TWS",e[e.FAIL_CONNECTION_LOST_BETWEEN_TWS_AND_SERVER=2110]="FAIL_CONNECTION_LOST_BETWEEN_TWS_AND_SERVER",e[e.INVALID_POSITION_TRADE_DERIVATED_VALUE=2150]="INVALID_POSITION_TRADE_DERIVATED_VALUE",e[e.PART_OF_REQUESTED_DATA_NOT_SUBSCRIBED=10090]="PART_OF_REQUESTED_DATA_NOT_SUBSCRIBED",e[e.DISPLAYING_DELAYED_DATA=10167]="DISPLAYING_DELAYED_DATA",e[e.NEWS_FEED_NOT_ALLOWED=10276]="NEWS_FEED_NOT_ALLOWED"}(r||(t.ErrorCode=r={})),t.isNonFatalError=(e,t)=>{if(e>=2100&&e<3e3||t.message.includes("Warning:"))return!0;switch(e){case r.PART_OF_REQUESTED_DATA_NOT_SUBSCRIBED:case r.DISPLAYING_DELAYED_DATA:case r.ORDER_MESSAGE:case r.SCANNER_LOW_PRECISION:return!0;default:return!1}}},8707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.finalize=void 0;var n=r(96829);t.finalize=function(e){return n.operate(function(t,r){try{t.subscribe(r)}finally{r.add(e)}})}},8742:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isValidDate=void 0,t.isValidDate=function(e){return e instanceof Date&&!isNaN(e)}},9738:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.share=void 0;var o=r(81773),s=r(1937),a=r(3349),u=r(96829);function c(e,t){for(var r=[],s=2;s<arguments.length;s++)r[s-2]=arguments[s];if(!0===t)return void e();if(!1!==t){var u=new a.SafeSubscriber({next:function(){u.unsubscribe(),e()}});return o.innerFrom(t.apply(void 0,i([],n(r)))).subscribe(u)}}t.share=function(e){void 0===e&&(e={});var t=e.connector,r=void 0===t?function(){return new s.Subject}:t,n=e.resetOnError,i=void 0===n||n,l=e.resetOnComplete,d=void 0===l||l,h=e.resetOnRefCountZero,_=void 0===h||h;return function(e){var t,n,s,l=0,h=!1,f=!1,E=function(){null==n||n.unsubscribe(),n=void 0},p=function(){E(),t=s=void 0,h=f=!1},T=function(){var e=t;p(),null==e||e.unsubscribe()};return u.operate(function(e,u){l++,f||h||E();var A=s=null!=s?s:r();u.add(function(){0!=--l||f||h||(n=c(T,_))}),A.subscribe(u),!t&&l>0&&(t=new a.SafeSubscriber({next:function(e){return A.next(e)},error:function(e){f=!0,E(),n=c(p,i,e),A.error(e)},complete:function(){h=!0,E(),n=c(p,d),A.complete()}}),o.innerFrom(e).subscribe(t))})(e)}}},10138:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.publish=void 0;var n=r(1937),i=r(86281),o=r(94165);t.publish=function(e){return e?function(t){return o.connect(e)(t)}:function(e){return i.multicast(new n.Subject)(e)}}},10898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.delay=void 0;var n=r(97847),i=r(19682),o=r(21574);t.delay=function(e,t){void 0===t&&(t=n.asyncScheduler);var r=o.timer(e,t);return i.delayWhen(function(){return r})}},11429:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ConjunctionConnection=void 0,function(e){e.AND="a",e.OR="o"}(r||(t.ConjunctionConnection=r={})),t.default=r},11594:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.throttleTime=void 0;var n=r(97847),i=r(4310),o=r(21574);t.throttleTime=function(e,t,r){void 0===t&&(t=n.asyncScheduler);var s=o.timer(e,t);return i.throttle(function(){return s},r)}},12143:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TimeCondition=void 0;let n=r(48909);class i{constructor(e,t,r){this.time=e,this.isMore=t,this.conjunctionConnection=r,this.type=n.OrderConditionType.Time}get strValue(){return this.time}}t.TimeCondition=i,t.default=i},12296:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.repeatWhen=void 0;var n=r(81773),i=r(1937),o=r(96829),s=r(28719);t.repeatWhen=function(e){return o.operate(function(t,r){var o,a,u=!1,c=!1,l=!1,d=function(){return l&&c&&(r.complete(),!0)},h=function(){l=!1,o=t.subscribe(s.createOperatorSubscriber(r,void 0,function(){l=!0,d()||(!a&&(a=new i.Subject,n.innerFrom(e(a)).subscribe(s.createOperatorSubscriber(r,function(){o?h():u=!0},function(){c=!0,d()}))),a).next()})),u&&(o.unsubscribe(),o=null,u=!1,h())};h()})}},12515:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OptionExerciseAction=void 0,function(e){e[e.EXERCISE=1]="EXERCISE",e[e.LAPSE=2]="LAPSE"}(r||(t.OptionExerciseAction=r={})),t.default=r},12897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatMap=void 0;var n=r(24071),i=r(17886);t.concatMap=function(e,t){return i.isFunction(t)?n.mergeMap(e,t,1):n.mergeMap(e,1)}},13804:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ObjectUnsubscribedError=void 0,t.ObjectUnsubscribedError=r(23008).createErrorClass(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})},13971:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OrderStatus=void 0,function(e){e.ApiPending="ApiPending",e.ApiCancelled="ApiCancelled",e.PreSubmitted="PreSubmitted",e.PendingCancel="PendingCancel",e.Cancelled="Cancelled",e.Submitted="Submitted",e.Filled="Filled",e.Inactive="Inactive",e.PendingSubmit="PendingSubmit",e.Unknown="Unknown"}(r||(t.OrderStatus=r={})),t.default=r},14028:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WshEventData=void 0;class r{constructor(e,t=!1,r=!1,n=!1,i="",o="",s=0){this.conId=e,this.fillWatchlist=t,this.fillPortfolio=r,this.fillCompetitors=n,this.startDate=i,this.endDate=o,this.totalLimit=s,this.filter=""}}t.WshEventData=r,t.default=r},14092:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isAsyncIterable=void 0;var n=r(17886);t.isAsyncIterable=function(e){return Symbol.asyncIterator&&n.isFunction(null==e?void 0:e[Symbol.asyncIterator])}},14431:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.bufferWhen=void 0;var n=r(96829),i=r(63410),o=r(28719),s=r(81773);t.bufferWhen=function(e){return n.operate(function(t,r){var n=null,a=null,u=function(){null==a||a.unsubscribe();var t=n;n=[],t&&r.next(t),s.innerFrom(e()).subscribe(a=o.createOperatorSubscriber(r,u,i.noop))};u(),t.subscribe(o.createOperatorSubscriber(r,function(e){return null==n?void 0:n.push(e)},function(){n&&r.next(n),r.complete()},void 0,function(){return n=a=null}))})}},15109:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.distinct=void 0;var n=r(96829),i=r(28719),o=r(63410),s=r(81773);t.distinct=function(e,t){return n.operate(function(r,n){var a=new Set;r.subscribe(i.createOperatorSubscriber(n,function(t){var r=e?e(t):t;a.has(r)||(a.add(r),n.next(t))})),t&&s.innerFrom(t).subscribe(i.createOperatorSubscriber(n,function(){return a.clear()},o.noop))})}},15738:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.zip=void 0;var o=r(59452),s=r(96829);t.zip=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return s.operate(function(t,r){o.zip.apply(void 0,i([t],n(e))).subscribe(r)})}},15850:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.SecType=void 0,function(e){e.STK="STK",e.OPT="OPT",e.FUT="FUT",e.CONTFUT="CONTFUT",e.CASH="CASH",e.BOND="BOND",e.CFD="CFD",e.FOP="FOP",e.WAR="WAR",e.IOPT="IOPT",e.FWD="FWD",e.BAG="BAG",e.IND="IND",e.BILL="BILL",e.FUND="FUND",e.FIXED="FIXED",e.SLB="SLB",e.NEWS="NEWS",e.CMDTY="CMDTY",e.BSK="BSK",e.ICU="ICU",e.ICS="ICS",e.CRYPTO="CRYPTO"}(r||(t.SecType=r={})),t.default=r},15931:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NotFoundError=void 0,t.NotFoundError=r(23008).createErrorClass(function(e){return function(t){e(this),this.name="NotFoundError",this.message=t}})},15943:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shareReplay=void 0;var n=r(82794),i=r(9738);t.shareReplay=function(e,t,r){var o,s,a,u,c=!1;return e&&"object"==typeof e?(u=void 0===(o=e.bufferSize)?1/0:o,t=void 0===(s=e.windowTime)?1/0:s,c=void 0!==(a=e.refCount)&&a,r=e.scheduler):u=null!=e?e:1/0,i.share({connector:function(){return new n.ReplaySubject(u,t,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:c})}},16137:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.max=void 0;var n=r(4759),i=r(17886);t.max=function(e){return n.reduce(i.isFunction(e)?function(t,r){return e(t,r)>0?t:r}:function(e,t){return e>t?e:t})}},16717:e=>{e.exports=function(e){var t=["red","yellow","green","blue","magenta"];return function(r,n,i){return" "===r?r:e[t[n++%t.length]](r)}}},17299:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concat=void 0;var n=r(77982),i=r(66389),o=r(6747);t.concat=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n.concatAll()(o.from(e,i.popScheduler(e)))}},17476:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultIfEmpty=void 0;var n=r(96829),i=r(28719);t.defaultIfEmpty=function(e){return n.operate(function(t,r){var n=!1;t.subscribe(i.createOperatorSubscriber(r,function(e){n=!0,r.next(e)},function(){n||r.next(e),r.complete()}))})}},17886:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isFunction=void 0,t.isFunction=function(e){return"function"==typeof e}},17905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.auditTime=void 0;var n=r(97847),i=r(91802),o=r(21574);t.auditTime=function(e,t){return void 0===t&&(t=n.asyncScheduler),i.audit(function(){return o.timer(e,t)})}},18043:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.Action=void 0,t.Action=function(e){function t(t,r){return e.call(this)||this}return n(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(r(46124).Subscription)},18397:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.LogLevel=void 0,function(e){e[e.SYSTEM=1]="SYSTEM",e[e.ERROR=2]="ERROR",e[e.WARN=3]="WARN",e[e.INFO=4]="INFO",e[e.DETAIL=5]="DETAIL"}(r||(t.LogLevel=r={})),t.default=r},18401:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.window=void 0;var n=r(1937),i=r(96829),o=r(28719),s=r(63410),a=r(81773);t.window=function(e){return i.operate(function(t,r){var i=new n.Subject;r.next(i.asObservable());var u=function(e){i.error(e),r.error(e)};return t.subscribe(o.createOperatorSubscriber(r,function(e){return null==i?void 0:i.next(e)},function(){i.complete(),r.complete()},u)),a.innerFrom(e).subscribe(o.createOperatorSubscriber(r,function(){i.complete(),r.next(i=new n.Subject)},s.noop,u)),function(){null==i||i.unsubscribe(),i=null}})}},19029:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.argsOrArgArray=void 0;var r=Array.isArray;t.argsOrArgArray=function(e){return 1===e.length&&r(e[0])?e[0]:e}},19654:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isCompeteAgainstBestOffsetUpToMid=t.COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID=void 0,t.COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID=1/0,t.isCompeteAgainstBestOffsetUpToMid=e=>e.competeAgainstBestOffset===t.COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID},19682:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.delayWhen=void 0;var n=r(17299),i=r(90580),o=r(91204),s=r(20318),a=r(24071),u=r(81773);t.delayWhen=function e(t,r){return r?function(s){return n.concat(r.pipe(i.take(1),o.ignoreElements()),s.pipe(e(t)))}:a.mergeMap(function(e,r){return u.innerFrom(t(e,r)).pipe(i.take(1),s.mapTo(e))})}},19743:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.TickType=void 0,function(e){e[e.BID_SIZE=0]="BID_SIZE",e[e.BID=1]="BID",e[e.ASK=2]="ASK",e[e.ASK_SIZE=3]="ASK_SIZE",e[e.LAST=4]="LAST",e[e.LAST_SIZE=5]="LAST_SIZE",e[e.HIGH=6]="HIGH",e[e.LOW=7]="LOW",e[e.VOLUME=8]="VOLUME",e[e.CLOSE=9]="CLOSE",e[e.BID_OPTION=10]="BID_OPTION",e[e.ASK_OPTION=11]="ASK_OPTION",e[e.LAST_OPTION=12]="LAST_OPTION",e[e.MODEL_OPTION=13]="MODEL_OPTION",e[e.OPEN=14]="OPEN",e[e.LOW_13_WEEK=15]="LOW_13_WEEK",e[e.HIGH_13_WEEK=16]="HIGH_13_WEEK",e[e.LOW_26_WEEK=17]="LOW_26_WEEK",e[e.HIGH_26_WEEK=18]="HIGH_26_WEEK",e[e.LOW_52_WEEK=19]="LOW_52_WEEK",e[e.HIGH_52_WEEK=20]="HIGH_52_WEEK",e[e.AVG_VOLUME=21]="AVG_VOLUME",e[e.OPEN_INTEREST=22]="OPEN_INTEREST",e[e.OPTION_HISTORICAL_VOL=23]="OPTION_HISTORICAL_VOL",e[e.OPTION_IMPLIED_VOL=24]="OPTION_IMPLIED_VOL",e[e.OPTION_BID_EXCH=25]="OPTION_BID_EXCH",e[e.OPTION_ASK_EXCH=26]="OPTION_ASK_EXCH",e[e.OPTION_CALL_OPEN_INTEREST=27]="OPTION_CALL_OPEN_INTEREST",e[e.OPTION_PUT_OPEN_INTEREST=28]="OPTION_PUT_OPEN_INTEREST",e[e.OPTION_CALL_VOLUME=29]="OPTION_CALL_VOLUME",e[e.OPTION_PUT_VOLUME=30]="OPTION_PUT_VOLUME",e[e.INDEX_FUTURE_PREMIUM=31]="INDEX_FUTURE_PREMIUM",e[e.BID_EXCH=32]="BID_EXCH",e[e.ASK_EXCH=33]="ASK_EXCH",e[e.AUCTION_VOLUME=34]="AUCTION_VOLUME",e[e.AUCTION_PRICE=35]="AUCTION_PRICE",e[e.AUCTION_IMBALANCE=36]="AUCTION_IMBALANCE",e[e.MARK_PRICE=37]="MARK_PRICE",e[e.BID_EFP_COMPUTATION=38]="BID_EFP_COMPUTATION",e[e.ASK_EFP_COMPUTATION=39]="ASK_EFP_COMPUTATION",e[e.LAST_EFP_COMPUTATION=40]="LAST_EFP_COMPUTATION",e[e.OPEN_EFP_COMPUTATION=41]="OPEN_EFP_COMPUTATION",e[e.HIGH_EFP_COMPUTATION=42]="HIGH_EFP_COMPUTATION",e[e.LOW_EFP_COMPUTATION=43]="LOW_EFP_COMPUTATION",e[e.CLOSE_EFP_COMPUTATION=44]="CLOSE_EFP_COMPUTATION",e[e.LAST_TIMESTAMP=45]="LAST_TIMESTAMP",e[e.SHORTABLE=46]="SHORTABLE",e[e.FUNDAMENTAL_RATIOS=47]="FUNDAMENTAL_RATIOS",e[e.RT_VOLUME=48]="RT_VOLUME",e[e.HALTED=49]="HALTED",e[e.BID_YIELD=50]="BID_YIELD",e[e.ASK_YIELD=51]="ASK_YIELD",e[e.LAST_YIELD=52]="LAST_YIELD",e[e.CUST_OPTION_COMPUTATION=53]="CUST_OPTION_COMPUTATION",e[e.TRADE_COUNT=54]="TRADE_COUNT",e[e.TRADE_RATE=55]="TRADE_RATE",e[e.VOLUME_RATE=56]="VOLUME_RATE",e[e.LAST_RTH_TRADE=57]="LAST_RTH_TRADE",e[e.RT_HISTORICAL_VOL=58]="RT_HISTORICAL_VOL",e[e.IB_DIVIDENDS=59]="IB_DIVIDENDS",e[e.BOND_FACTOR_MULTIPLIER=60]="BOND_FACTOR_MULTIPLIER",e[e.REGULATORY_IMBALANCE=61]="REGULATORY_IMBALANCE",e[e.NEWS_TICK=62]="NEWS_TICK",e[e.SHORT_TERM_VOLUME_3_MIN=63]="SHORT_TERM_VOLUME_3_MIN",e[e.SHORT_TERM_VOLUME_5_MIN=64]="SHORT_TERM_VOLUME_5_MIN",e[e.SHORT_TERM_VOLUME_10_MIN=65]="SHORT_TERM_VOLUME_10_MIN",e[e.DELAYED_BID=66]="DELAYED_BID",e[e.DELAYED_ASK=67]="DELAYED_ASK",e[e.DELAYED_LAST=68]="DELAYED_LAST",e[e.DELAYED_BID_SIZE=69]="DELAYED_BID_SIZE",e[e.DELAYED_ASK_SIZE=70]="DELAYED_ASK_SIZE",e[e.DELAYED_LAST_SIZE=71]="DELAYED_LAST_SIZE",e[e.DELAYED_HIGH=72]="DELAYED_HIGH",e[e.DELAYED_LOW=73]="DELAYED_LOW",e[e.DELAYED_VOLUME=74]="DELAYED_VOLUME",e[e.DELAYED_CLOSE=75]="DELAYED_CLOSE",e[e.DELAYED_OPEN=76]="DELAYED_OPEN",e[e.RT_TRD_VOLUME=77]="RT_TRD_VOLUME",e[e.CREDITMAN_MARK_PRICE=78]="CREDITMAN_MARK_PRICE",e[e.CREDITMAN_SLOW_MARK_PRICE=79]="CREDITMAN_SLOW_MARK_PRICE",e[e.DELAYED_BID_OPTION=80]="DELAYED_BID_OPTION",e[e.DELAYED_ASK_OPTION=81]="DELAYED_ASK_OPTION",e[e.DELAYED_LAST_OPTION=82]="DELAYED_LAST_OPTION",e[e.DELAYED_MODEL_OPTION=83]="DELAYED_MODEL_OPTION",e[e.LAST_EXCH=84]="LAST_EXCH",e[e.LAST_REG_TIME=85]="LAST_REG_TIME",e[e.FUTURES_OPEN_INTEREST=86]="FUTURES_OPEN_INTEREST",e[e.AVG_OPT_VOLUME=87]="AVG_OPT_VOLUME",e[e.DELAYED_LAST_TIMESTAMP=88]="DELAYED_LAST_TIMESTAMP",e[e.SHORTABLE_SHARES=89]="SHORTABLE_SHARES",e[e.DELAYED_HALTED=90]="DELAYED_HALTED",e[e.REUTERS_2_MUTUAL_FUNDS=91]="REUTERS_2_MUTUAL_FUNDS",e[e.ETF_NAV_CLOSE=92]="ETF_NAV_CLOSE",e[e.ETF_NAV_PRIOR_CLOSE=93]="ETF_NAV_PRIOR_CLOSE",e[e.ETF_NAV_BID=94]="ETF_NAV_BID",e[e.ETF_NAV_ASK=95]="ETF_NAV_ASK",e[e.ETF_NAV_LAST=96]="ETF_NAV_LAST",e[e.ETF_NAV_FROZEN_LAST=97]="ETF_NAV_FROZEN_LAST",e[e.ETF_NAV_HIGH=98]="ETF_NAV_HIGH",e[e.ETF_NAV_LOW=99]="ETF_NAV_LOW",e[e.SOCIAL_MARKET_ANALYTICS=100]="SOCIAL_MARKET_ANALYTICS",e[e.ESTIMATED_IPO_MIDPOINT=101]="ESTIMATED_IPO_MIDPOINT",e[e.FINAL_IPO_LAST=102]="FINAL_IPO_LAST",e[e.DELAYED_YIELD_BID=103]="DELAYED_YIELD_BID",e[e.DELAYED_YIELD_ASK=104]="DELAYED_YIELD_ASK",e[e.UNKNOWN=0x7fffffff]="UNKNOWN"}(r||(t.TickType=r={})),t.default=r},19916:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.endWith=void 0;var o=r(17299),s=r(80848);t.endWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){return o.concat(t,s.of.apply(void 0,i([],n(e))))}}},20128:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pluck=void 0;var n=r(44817);t.pluck=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.length;if(0===r)throw Error("list of properties cannot be empty.");return n.map(function(t){for(var n=t,i=0;i<r;i++){var o=null==n?void 0:n[e[i]];if(void 0===o)return;n=o}return n})}},20318:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mapTo=void 0;var n=r(44817);t.mapTo=function(e){return n.map(function(){return e})}},20341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SequenceError=void 0,t.SequenceError=r(23008).createErrorClass(function(e){return function(t){e(this),this.name="SequenceError",this.message=t}})},20407:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.publishReplay=void 0;var n=r(82794),i=r(86281),o=r(17886);t.publishReplay=function(e,t,r,s){r&&!o.isFunction(r)&&(s=r);var a=o.isFunction(r)?r:void 0;return function(r){return i.multicast(new n.ReplaySubject(e,t,s),a)(r)}}},20566:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.Liquidities=void 0,function(e){e[e.None=0]="None",e[e.Added=1]="Added",e[e.Removed=2]="Removed",e[e.RoudedOut=3]="RoudedOut"}(r||(t.Liquidities=r={}))},20646:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.zipWith=void 0;var o=r(15738);t.zipWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.zip.apply(void 0,i([],n(e)))}},20695:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.takeWhile=void 0;var n=r(96829),i=r(28719);t.takeWhile=function(e,t){return void 0===t&&(t=!1),n.operate(function(r,n){var o=0;r.subscribe(i.createOperatorSubscriber(n,function(r){var i=e(r,o++);(i||t)&&n.next(r),i||n.complete()}))})}},21028:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.takeUntil=void 0;var n=r(96829),i=r(28719),o=r(81773),s=r(63410);t.takeUntil=function(e){return n.operate(function(t,r){o.innerFrom(e).subscribe(i.createOperatorSubscriber(r,function(){return r.complete()},s.noop)),r.closed||t.subscribe(r)})}},21099:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createObject=void 0,t.createObject=function(e,t){return e.reduce(function(e,r,n){return e[r]=t[n],e},{})}},21282:(e,t,r)=>{var n=r(57395);e.exports=function(){var e=function(e,t){String.prototype.__defineGetter__(e,t)};e("strip",function(){return n.strip(this)}),e("stripColors",function(){return n.strip(this)}),e("trap",function(){return n.trap(this)}),e("zalgo",function(){return n.zalgo(this)}),e("zebra",function(){return n.zebra(this)}),e("rainbow",function(){return n.rainbow(this)}),e("random",function(){return n.random(this)}),e("america",function(){return n.america(this)}),Object.keys(n.styles).forEach(function(t){e(t,function(){return n.stylize(this,t)})}),n.setTheme=function(t){var r;if("string"==typeof t)return void console.log("colors.setTheme now only accepts an object, not a string. If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");r=["__defineGetter__","__defineSetter__","__lookupGetter__","__lookupSetter__","charAt","constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf","charCodeAt","indexOf","lastIndexOf","length","localeCompare","match","repeat","replace","search","slice","split","substring","toLocaleLowerCase","toLocaleUpperCase","toLowerCase","toUpperCase","trim","trimLeft","trimRight"],Object.keys(t).forEach(function(i){if(-1!==r.indexOf(i))console.log((void 0)+("String.prototype"+i).magenta+" is probably something you don't want to override.  Ignoring style name");else if("string"==typeof t[i])n[i]=n[t[i]],e(i,function(){return n[i](this)});else{var o=function(e){for(var r=e||this,o=0;o<t[i].length;o++)r=n[t[i][o]](r);return r};e(i,o),n[i]=function(e){return o(e)}}})}}},21421:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.combineAll=void 0,t.combineAll=r(78650).combineLatestAll},21553:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.groupBy=void 0;var n=r(57248),i=r(81773),o=r(1937),s=r(96829),a=r(28719);t.groupBy=function(e,t,r,u){return s.operate(function(s,c){t&&"function"!=typeof t?(r=t.duration,l=t.element,u=t.connector):l=t;var l,d=new Map,h=function(e){d.forEach(e),e(c)},_=function(e){return h(function(t){return t.error(e)})},f=0,E=!1,p=new a.OperatorSubscriber(c,function(t){try{var s=e(t),h=d.get(s);if(!h){d.set(s,h=u?u():new o.Subject);var T,A,I,v=(T=s,A=h,(I=new n.Observable(function(e){f++;var t=A.subscribe(e);return function(){t.unsubscribe(),0==--f&&E&&p.unsubscribe()}})).key=T,I);if(c.next(v),r){var O=a.createOperatorSubscriber(h,function(){h.complete(),null==O||O.unsubscribe()},void 0,void 0,function(){return d.delete(s)});p.add(i.innerFrom(r(v)).subscribe(O))}}h.next(l?l(t):t)}catch(e){_(e)}},function(){return h(function(e){return e.complete()})},_,function(){return d.clear()},function(){return E=!0,0===f});s.subscribe(p)})}},21574:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.timer=void 0;var n=r(57248),i=r(97847),o=r(90597),s=r(8742);t.timer=function(e,t,r){void 0===e&&(e=0),void 0===r&&(r=i.async);var a=-1;return null!=t&&(o.isScheduler(t)?r=t:a=t),new n.Observable(function(t){var n=s.isValidDate(e)?e-r.now():e;n<0&&(n=0);var i=0;return r.schedule(function(){t.closed||(t.next(i++),0<=a?this.schedule(void 0,a):t.complete())},n)})}},21743:e=>{e.exports=function(e){return function(t,r,n){if(" "===t)return t;switch(r%3){case 0:return e.red(t);case 1:return e.white(t);case 2:return e.blue(t)}}}},22410:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.VolumeCondition=void 0;let n=r(48909);class i{constructor(e,t,r,i,o){this.volume=e,this.conId=t,this.exchange=r,this.isMore=i,this.conjunctionConnection=o,this.type=n.OrderConditionType.Volume}get strValue(){return""+this.volume}}t.VolumeCondition=i,t.default=i},23008:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createErrorClass=void 0,t.createErrorClass=function(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}},23814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StopOrder=void 0;let n=r(55332),i=r(72785);class o{constructor(e,t,r,o,s,a){this.action=e,this.auxPrice=t,this.totalQuantity=r,this.transmit=o,this.parentId=s,this.tif=a,this.orderType=n.OrderType.STP,this.transmit=this.transmit??!0,this.parentId=this.parentId??0,this.tif=this.tif??i.TimeInForce.DAY}}t.StopOrder=o,t.default=o},24071:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeMap=void 0;var n=r(44817),i=r(81773),o=r(96829),s=r(37279),a=r(17886);t.mergeMap=function e(t,r,u){return(void 0===u&&(u=1/0),a.isFunction(r))?e(function(e,o){return n.map(function(t,n){return r(e,t,o,n)})(i.innerFrom(t(e,o)))},u):("number"==typeof r&&(u=r),o.operate(function(e,r){return s.mergeInternals(e,r,t,u)}))}},24528:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.distinctUntilKeyChanged=void 0;var n=r(41133);t.distinctUntilKeyChanged=function(e,t){return n.distinctUntilChanged(function(r,n){return t?t(r[e],n[e]):r[e]===n[e]})}},25041:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.throwError=void 0;var n=r(57248),i=r(17886);t.throwError=function(e,t){var r=i.isFunction(e)?e:function(){return e},o=function(e){return e.error(r())};return new n.Observable(t?function(e){return t.schedule(o,0,e)}:o)}},25042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.empty=t.EMPTY=void 0;var n=r(57248);t.EMPTY=new n.Observable(function(e){return e.complete()}),t.empty=function(e){var r;return e?(r=e,new n.Observable(function(e){return r.schedule(function(){return e.complete()})})):t.EMPTY}},25648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ScanCode=t.LocationCode=t.Instrument=void 0;var n=r(87198);Object.defineProperty(t,"Instrument",{enumerable:!0,get:function(){return n.Instrument}}),Object.defineProperty(t,"LocationCode",{enumerable:!0,get:function(){return n.LocationCode}}),Object.defineProperty(t,"ScanCode",{enumerable:!0,get:function(){return n.ScanCode}})},27573:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.windowToggle=void 0;var i=r(1937),o=r(46124),s=r(96829),a=r(81773),u=r(28719),c=r(63410),l=r(1071);t.windowToggle=function(e,t){return s.operate(function(r,s){var d=[],h=function(e){for(;0<d.length;)d.shift().error(e);s.error(e)};a.innerFrom(e).subscribe(u.createOperatorSubscriber(s,function(e){var r,n=new i.Subject;d.push(n);var _=new o.Subscription;try{r=a.innerFrom(t(e))}catch(e){h(e);return}s.next(n.asObservable()),_.add(r.subscribe(u.createOperatorSubscriber(s,function(){l.arrRemove(d,n),n.complete(),_.unsubscribe()},c.noop,h)))},c.noop)),r.subscribe(u.createOperatorSubscriber(s,function(e){var t,r,i=d.slice();try{for(var o=n(i),s=o.next();!s.done;s=o.next())s.value.next(e)}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}},function(){for(;0<d.length;)d.shift().complete();s.complete()},h,function(){for(;0<d.length;)d.shift().unsubscribe()}))})}},28013:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toArray=void 0;var n=r(4759),i=r(96829),o=function(e,t){return e.push(t),e};t.toArray=function(){return i.operate(function(e,t){n.reduce(o,[])(e).subscribe(t)})}},28299:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.buffer=void 0;var n=r(96829),i=r(63410),o=r(28719),s=r(81773);t.buffer=function(e){return n.operate(function(t,r){var n=[];return t.subscribe(o.createOperatorSubscriber(r,function(e){return n.push(e)},function(){r.next(n),r.complete()})),s.innerFrom(e).subscribe(o.createOperatorSubscriber(r,function(){var e=n;n=[],r.next(e)},i.noop)),function(){n=null}})}},28719:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.OperatorSubscriber=t.createOperatorSubscriber=void 0;var i=r(3349);t.createOperatorSubscriber=function(e,t,r,n,i){return new o(e,t,r,n,i)};var o=function(e){function t(t,r,n,i,o,s){var a=e.call(this,t)||this;return a.onFinalize=o,a.shouldUnsubscribe=s,a._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=i?function(e){try{i(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return n(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null==(t=this.onFinalize)||t.call(this)}},t}(i.Subscriber);t.OperatorSubscriber=o},28794:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.windowCount=void 0;var i=r(1937),o=r(96829),s=r(28719);t.windowCount=function(e,t){void 0===t&&(t=0);var r=t>0?t:e;return o.operate(function(t,o){var a=[new i.Subject],u=0;o.next(a[0].asObservable()),t.subscribe(s.createOperatorSubscriber(o,function(t){try{for(var s,c,l=n(a),d=l.next();!d.done;d=l.next())d.value.next(t)}catch(e){s={error:e}}finally{try{d&&!d.done&&(c=l.return)&&c.call(l)}finally{if(s)throw s.error}}var h=u-e+1;if(h>=0&&h%r==0&&a.shift().complete(),++u%r==0){var _=new i.Subject;a.push(_),o.next(_.asObservable())}},function(){for(;a.length>0;)a.shift().complete();o.complete()},function(e){for(;a.length>0;)a.shift().error(e);o.error(e)},function(){a=null}))})}},29017:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.concatWith=void 0;var o=r(85249);t.concatWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.concat.apply(void 0,i([],n(e)))}},30001:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exhaustMap=void 0;var n=r(44817),i=r(81773),o=r(96829),s=r(28719);t.exhaustMap=function e(t,r){return r?function(o){return o.pipe(e(function(e,o){return i.innerFrom(t(e,o)).pipe(n.map(function(t,n){return r(e,t,o,n)}))}))}:o.operate(function(e,r){var n=0,o=null,a=!1;e.subscribe(s.createOperatorSubscriber(r,function(e){o||(o=s.createOperatorSubscriber(r,void 0,function(){o=null,a&&r.complete()}),i.innerFrom(t(e,n++)).subscribe(o))},function(){a=!0,o||r.complete()}))})}},31124:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.elementAt=void 0;var n=r(77945),i=r(45007),o=r(98193),s=r(17476),a=r(90580);t.elementAt=function(e,t){if(e<0)throw new n.ArgumentOutOfRangeError;var r=arguments.length>=2;return function(u){return u.pipe(i.filter(function(t,r){return r===e}),a.take(1),r?s.defaultIfEmpty(t):o.throwIfEmpty(function(){return new n.ArgumentOutOfRangeError}))}}},31374:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.publishLast=void 0;var n=r(56627),i=r(55100);t.publishLast=function(){return function(e){var t=new n.AsyncSubject;return new i.ConnectableObservable(e,function(){return t})}}},31649:(e,t,r)=>{e.exports=r(57395),r(21282)()},32984:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNextSubscription=void 0;let n=r(79249),i=r(54499),o=r(11);class s{constructor(e,t,r,i,o){this.api=e,this.requestFunction=t,this.cancelFunction=r,this.cleanupFunction=i,this.instanceId=o,this.observersCount=0,this.subject=new n.ReplaySubject(1),this.hasError=!1,this.endEventReceived=!1,this.reqId=e.nextReqId}get lastAllValue(){return this._lastAllValue}set lastAllValue(e){this._lastAllValue=e}next(e){this._lastAllValue=e.all,this.subject.next(e)}complete(){this.subject.complete()}error(e){delete this._lastAllValue,this.hasError=!0,this.subject.error(e),this.cancelTwsSubscription()}createObservable(){return new n.Observable(e=>{this.hasError&&(this.hasError=!1,this.subject=new n.ReplaySubject(1),this.reqId=this.api.nextReqId);let t=this.subject.pipe((0,i.map)((e,t)=>0===t?{all:e.all,added:e.all}:e)).subscribe(e);return 0==this.observersCount++&&this.requestTwsSubscription(),()=>{t.unsubscribe(),--this.observersCount<=0&&(this.cancelTwsSubscription(),this.cleanupFunction())}})}requestTwsSubscription(){this.connectionState$||(this.connectionState$=this.api.connectionState.subscribe(e=>{e===o.ConnectionState.Connected&&(delete this._lastAllValue,this.endEventReceived=!1,this.requestFunction())}))}cancelTwsSubscription(){this.connectionState$?.unsubscribe(),delete this.connectionState$,this.api.isConnected&&this.cancelFunction()}}t.IBApiNextSubscription=s},33361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.switchMap=void 0;var n=r(81773),i=r(96829),o=r(28719);t.switchMap=function(e,t){return i.operate(function(r,i){var s=null,a=0,u=!1,c=function(){return u&&!s&&i.complete()};r.subscribe(o.createOperatorSubscriber(i,function(r){null==s||s.unsubscribe();var u=0,l=a++;n.innerFrom(e(r,l)).subscribe(s=o.createOperatorSubscriber(i,function(e){return i.next(t?t(r,e,l,u++):e)},function(){s=null,c()}))},function(){u=!0,c()}))})}},33681:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.expand=void 0;var n=r(96829),i=r(37279);t.expand=function(e,t,r){return void 0===t&&(t=1/0),t=1>(t||0)?1/0:t,n.operate(function(n,o){return i.mergeInternals(n,o,e,t,void 0,!0,r)})}},34138:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncScheduler=void 0;var i=r(53038);t.AsyncScheduler=function(e){function t(t,r){void 0===r&&(r=i.Scheduler.now);var n=e.call(this,t,r)||this;return n.actions=[],n._active=!1,n}return n(t,e),t.prototype.flush=function(e){var t,r=this.actions;if(this._active)return void r.push(e);this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,t){for(;e=r.shift();)e.unsubscribe();throw t}},t}(i.Scheduler)},34220:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.skip=void 0;var n=r(45007);t.skip=function(e){return n.filter(function(t,r){return e<=r})}},35228:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.argsArgArrayOrObject=void 0;var r=Array.isArray,n=Object.getPrototypeOf,i=Object.prototype,o=Object.keys;t.argsArgArrayOrObject=function(e){if(1===e.length){var t,s=e[0];if(r(s))return{args:s,keys:null};if((t=s)&&"object"==typeof t&&n(t)===i){var a=o(s);return{args:a.map(function(e){return s[e]}),keys:a}}}return{args:e,keys:null}}},35373:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatestWith=void 0;var o=r(77685);t.combineLatestWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.combineLatest.apply(void 0,i([],n(e)))}},35488:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.MarketDataType=void 0,function(e){e[e.REALTIME=1]="REALTIME",e[e.FROZEN=2]="FROZEN",e[e.DELAYED=3]="DELAYED",e[e.DELAYED_FROZEN=4]="DELAYED_FROZEN"}(r||(t.MarketDataType=r={}))},35561:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Bond=void 0;let i=n(r(15850));class o{constructor(e,t,r,n){this.symbol=e,this.maturity=t,this.exchange=r,this.currency=n,this.secType=i.default.BOND,this.currency=this.currency??"USD"}get lastTradeDateOrContractMonth(){return this.maturity}}t.Bond=o,t.default=o},37279:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeInternals=void 0;var n=r(81773),i=r(88840),o=r(28719);t.mergeInternals=function(e,t,r,s,a,u,c,l){var d=[],h=0,_=0,f=!1,E=function(){!f||d.length||h||t.complete()},p=function(e){return h<s?T(e):d.push(e)},T=function(e){u&&t.next(e),h++;var l=!1;n.innerFrom(r(e,_++)).subscribe(o.createOperatorSubscriber(t,function(e){null==a||a(e),u?p(e):t.next(e)},function(){l=!0},void 0,function(){if(l)try{for(h--;d.length&&h<s;)!function(){var e=d.shift();c?i.executeSchedule(t,c,function(){return T(e)}):T(e)}();E()}catch(e){t.error(e)}}))};return e.subscribe(o.createOperatorSubscriber(t,p,function(){f=!0,E()})),function(){null==l||l()}}},37431:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.CFD=void 0;let i=n(r(15850));class o{constructor(e,t,r){this.symbol=e,this.currency=t,this.exchange=r,this.secType=i.default.STK,this.currency=this.currency??"USD",this.exchange=this.exchange??"SMART"}}t.CFD=o,t.default=o},37495:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Future=void 0;let i=n(r(15850));class o{constructor(e,t,r,n,o,s){this.symbol=e,this.localSymbol=t,this.lastTradeDateOrContractMonth=r,this.exchange=n,this.multiplier=o,this.currency=s,this.secType=i.default.FUT,this.currency=this.currency??"USD"}}t.Future=o,t.default=o},38138:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.observeNotification=t.Notification=t.NotificationKind=void 0;var n=r(25042),i=r(80848),o=r(25041),s=r(17886);function a(e,t){var r,n,i,o=e.kind,s=e.value,a=e.error;if("string"!=typeof o)throw TypeError('Invalid notification, missing "kind"');"N"===o?null==(r=t.next)||r.call(t,s):"E"===o?null==(n=t.error)||n.call(t,a):null==(i=t.complete)||i.call(t)}!function(e){e.NEXT="N",e.ERROR="E",e.COMPLETE="C"}(t.NotificationKind||(t.NotificationKind={})),t.Notification=function(){function e(e,t,r){this.kind=e,this.value=t,this.error=r,this.hasValue="N"===e}return e.prototype.observe=function(e){return a(this,e)},e.prototype.do=function(e,t,r){var n=this.kind,i=this.value,o=this.error;return"N"===n?null==e?void 0:e(i):"E"===n?null==t?void 0:t(o):null==r?void 0:r()},e.prototype.accept=function(e,t,r){return s.isFunction(null==e?void 0:e.next)?this.observe(e):this.do(e,t,r)},e.prototype.toObservable=function(){var e=this.kind,t=this.value,r=this.error,s="N"===e?i.of(t):"E"===e?o.throwError(function(){return r}):"C"===e?n.EMPTY:0;if(!s)throw TypeError("Unexpected notification kind "+e);return s},e.createNext=function(t){return new e("N",t)},e.createError=function(t){return new e("E",void 0,t)},e.createComplete=function(){return e.completeNotification},e.completeNotification=new e("C"),e}(),t.observeNotification=a},38200:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.materialize=void 0;var n=r(38138),i=r(96829),o=r(28719);t.materialize=function(){return i.operate(function(e,t){e.subscribe(o.createOperatorSubscriber(t,function(e){t.next(n.Notification.createNext(e))},function(){t.next(n.Notification.createComplete()),t.complete()},function(e){t.next(n.Notification.createError(e)),t.complete()}))})}},38576:function(e,t,r){var n;e=r.nmd(e),(function(){var i={boolean:!1,function:!0,object:!0,number:!1,string:!1,undefined:!1},o=i[typeof window]&&window||this;i[typeof t]&&t&&t.nodeType;var s=i.object&&e&&!e.nodeType&&e;s&&s.exports;var a=i[typeof global]&&global;a&&(a.global===a||a.window===a)&&(o=a);var u=function(e,t){this._cb=e,this._cbCtx=t,this._commands=[],this._commandsCurrent=[],this._paused=!1,this._processing=!1};u.prototype._concatCurrentCommands=function(){this._commands=this._commandsCurrent.concat(this._commands),this._commandsCurrent.length=0},u.prototype._process=function(){for(var e;!this._paused&&(this._processing=!0,this._commands.length||this._concatCurrentCommands(),e=this._commands.shift());)this._cb.call(this._cbCtx,e.type,e.data),this._concatCurrentCommands();this._processing=!1},u.prototype.pause=function(){this._paused=!0},u.prototype.resume=function(){this._paused=!1,this._concatCurrentCommands(),this._processing||this._process()},u.prototype.run=function(e,t){this._commandsCurrent.push({type:e,data:t}),this._processing||this._process()},u.prototype.schedule=function(e,t){this._commands.push({type:e,data:t}),this._processing||this._process()},o.CommandBuffer=u,void 0===(n=(function(){return u}).call(t,r,t,e))||(e.exports=n)}).call(this)},38760:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.publishBehavior=void 0;var n=r(43611),i=r(55100);t.publishBehavior=function(e){return function(t){var r=new n.BehaviorSubject(e);return new i.ConnectableObservable(t,function(){return r})}}},39278:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatMapTo=void 0;var n=r(12897),i=r(17886);t.concatMapTo=function(e,t){return i.isFunction(t)?n.concatMap(function(){return e},t):n.concatMap(function(){return e})}},39969:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.last=void 0;var n=r(46889),i=r(45007),o=r(68468),s=r(98193),a=r(17476),u=r(56032);t.last=function(e,t){var r=arguments.length>=2;return function(c){return c.pipe(e?i.filter(function(t,r){return e(t,r,c)}):u.identity,o.takeLast(1),r?a.defaultIfEmpty(t):s.throwIfEmpty(function(){return new n.EmptyError}))}}},40552:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeMapTo=void 0;var n=r(24071),i=r(17886);t.mergeMapTo=function(e,t,r){return(void 0===r&&(r=1/0),i.isFunction(t))?n.mergeMap(function(){return e},t,r):("number"==typeof t&&(r=t),n.mergeMap(function(){return e},r))}},40575:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OrderAction=void 0,function(e){e.BUY="BUY",e.SELL="SELL",e.SSHORT="SSHORT",e.SLONG="SLONG"}(r||(t.OrderAction=r={})),t.default=r},41133:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.distinctUntilChanged=void 0;var n=r(56032),i=r(96829),o=r(28719);function s(e,t){return e===t}t.distinctUntilChanged=function(e,t){return void 0===t&&(t=n.identity),e=null!=e?e:s,i.operate(function(r,n){var i,s=!0;r.subscribe(o.createOperatorSubscriber(n,function(r){var o=t(r);(s||!e(i,o))&&(s=!1,i=o,n.next(r))}))})}},41252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sampleTime=void 0;var n=r(97847),i=r(53747),o=r(85632);t.sampleTime=function(e,t){return void 0===t&&(t=n.asyncScheduler),i.sample(o.interval(e,t))}},42306:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,s){if("function"!=typeof n)throw TypeError("The listener must be a function");var a=new i(n,o||e,s),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],a]:e._events[u].push(a):(e._events[u]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function a(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),a.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},a.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,s=Array(o);i<o;i++)s[i]=n[i].fn;return s},a.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},a.prototype.emit=function(e,t,n,i,o,s){var a=r?r+e:e;if(!this._events[a])return!1;var u,c,l=this._events[a],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,i),!0;case 5:return l.fn.call(l.context,t,n,i,o),!0;case 6:return l.fn.call(l.context,t,n,i,o,s),!0}for(c=1,u=Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var h,_=l.length;for(c=0;c<_;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,n);break;case 4:l[c].fn.call(l[c].context,t,n,i);break;default:if(!u)for(h=1,u=Array(d-1);h<d;h++)u[h-1]=arguments[h];l[c].fn.apply(l[c].context,u)}}return!0},a.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},a.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},a.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return s(this,o),this;var a=this._events[o];if(a.fn)a.fn!==t||i&&!a.once||n&&a.context!==n||s(this,o);else{for(var u=0,c=[],l=a.length;u<l;u++)(a[u].fn!==t||i&&!a[u].once||n&&a[u].context!==n)&&c.push(a[u]);c.length?this._events[o]=1===c.length?c[0]:c:s(this,o)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new n,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=r,a.EventEmitter=a,e.exports=a},42605:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.mapOneOrManyArgs=void 0;var o=r(44817),s=Array.isArray;t.mapOneOrManyArgs=function(e){return o.map(function(t){return s(t)?e.apply(void 0,i([],n(t))):e(t)})}},42767:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.skipWhile=void 0;var n=r(96829),i=r(28719);t.skipWhile=function(e){return n.operate(function(t,r){var n=!1,o=0;t.subscribe(i.createOperatorSubscriber(r,function(t){return(n||(n=!e(t,o++)))&&r.next(t)}))})}},43611:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.BehaviorSubject=void 0,t.BehaviorSubject=function(e){function t(t){var r=e.call(this)||this;return r._value=t,r}return n(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),t.prototype._subscribe=function(t){var r=e.prototype._subscribe.call(this,t);return r.closed||t.next(this._value),r},t.prototype.getValue=function(){var e=this.hasError,t=this.thrownError,r=this._value;if(e)throw t;return this._throwIfClosed(),r},t.prototype.next=function(t){e.prototype.next.call(this,this._value=t)},t}(r(1937).Subject)},44310:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MarketOrder=void 0;let n=r(55332);class i{constructor(e,t,r,i,o){this.action=e,this.totalQuantity=t,this.transmit=r,this.goodAfterTime=i,this.goodTillDate=o,this.orderType=n.OrderType.MKT,this.transmit=this.transmit??!0,this.goodAfterTime=this.goodAfterTime??"",this.goodTillDate=this.goodTillDate??""}}t.MarketOrder=i,t.default=i},44694:function(e,t,r){"use strict";var n,i=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return(n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&i(t,e,r[s]);return o(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.get=function(){return l||(l=E()),l};let u=r(29021),c=s(r(33873));a(r(57685)).default.config();let l=null,d=["ci","env_config_test","ib_host","ib_port","ib_test_account","client_version","max_req_per_second"];function h(e){var t=c.resolve(__dirname,"..","..","config",`${e}.json`);try{let e=(0,u.readFileSync)(t,"utf8");return JSON.parse(e)}catch(e){if("ENOENT"!==e.code||void 0!==e.errno&&-4058!==e.errno&&-2!==e.errno)throw e}return{}}function _(e,t){let r=e;if(t){let e=h(t);e&&(r={...r,...e})}return r}let f=(e,t)=>e.forEach(e=>{let r=t[e];"string"==typeof r&&(t[e]=parseInt(r,10))});function E(){var e;let t="production",r=h("default");return r=_(r,"local"),e=r=_(r,t),d.forEach(t=>{let r=t.toLowerCase(),n=t.toUpperCase();e[r]=process.env[n]||e[r]}),(r=e).environment=t||"local",r.isProduction=!0,r.isStaging=!1,r.isDevelopment=!1,r.isTest=!1,r.isLocal=!t,r.ci=r.ci||process.env.CIRCLECI,f(["ib_port","default_client_id","client_version","max_req_per_second"],r),r}t.default=l=E()},44817:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.map=void 0;var n=r(96829),i=r(28719);t.map=function(e,t){return n.operate(function(r,n){var o=0;r.subscribe(i.createOperatorSubscriber(n,function(r){n.next(e.call(t,r,o++))}))})}},44868:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isArrayLike=void 0,t.isArrayLike=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},45007:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.filter=void 0;var n=r(96829),i=r(28719);t.filter=function(e,t){return n.operate(function(r,n){var o=0;r.subscribe(i.createOperatorSubscriber(n,function(r){return e.call(t,r,o++)&&n.next(r)}))})}},45090:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleIterable=void 0;var n=r(57248),i=r(84968),o=r(17886),s=r(88840);t.scheduleIterable=function(e,t){return new n.Observable(function(r){var n;return s.executeSchedule(r,t,function(){n=e[i.iterator](),s.executeSchedule(r,t,function(){var e,t,i;try{t=(e=n.next()).value,i=e.done}catch(e){r.error(e);return}i?r.complete():r.next(t)},0,!0)}),function(){return o.isFunction(null==n?void 0:n.return)&&n.return()}})}},45388:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.debounce=void 0;var n=r(96829),i=r(63410),o=r(28719),s=r(81773);t.debounce=function(e){return n.operate(function(t,r){var n=!1,a=null,u=null,c=function(){if(null==u||u.unsubscribe(),u=null,n){n=!1;var e=a;a=null,r.next(e)}};t.subscribe(o.createOperatorSubscriber(r,function(t){null==u||u.unsubscribe(),n=!0,a=t,u=o.createOperatorSubscriber(r,c,i.noop),s.innerFrom(e(t)).subscribe(u)},function(){c(),r.complete()},void 0,function(){a=u=null}))})}},45714:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Socket=t.ConnectionStatus=void 0;let i=n(r(91645)),o=r(28354),s=r(59643),a=r(79898),u=n(r(48369)),c=n(r(44694)),l=r(7133),d=r(89123);t.ConnectionStatus={Disconnected:0,Disconnecting:1,Connecting:2,Connected:3};class h{constructor(e,r={}){this.controller=e,this.options=r,this._status=t.ConnectionStatus.Disconnected,this._serverVersion=0,this._serverConnectionTime="",this.dataFragment="",this.neverReceived=!0,this.waitingAsync=!1,this.useV100Plus=!0,this._v100MessageBuffer=Buffer.alloc(0),this._clientId=void 0!==this.options.clientId?Math.floor(this.options.clientId):c.default.default_client_id}get connected(){return this._status===t.ConnectionStatus.Connected}get status(){return this._status}get serverVersion(){return this._serverVersion}get serverConnectionTime(){return this._serverConnectionTime}get clientId(){return this._clientId}disableUseV100Plus(){this.useV100Plus=!1}connect(e){this._status>=t.ConnectionStatus.Connecting||(this._status=t.ConnectionStatus.Connecting,void 0!==e&&(this._clientId=Math.floor(e)),this.controller.pause(),this.dataFragment="",this.neverReceived=!0,this.waitingAsync=!1,this._v100MessageBuffer=Buffer.alloc(0),this.client=i.default.connect({host:this.options.host??c.default.ib_host,port:this.options.port??c.default.ib_port},()=>this.onConnect()).on("data",e=>this.onData(e)).on("close",()=>this.onEnd()).on("end",()=>this.onEnd()).on("error",e=>this.onError(e)))}disconnect(){this._status=t.ConnectionStatus.Disconnecting,this.controller.pause(),this.client?.end(),this.client?.destroy()}send(e){(e=this.flattenDeep(e)).forEach((t,r)=>{(!0===t||!1===t||t instanceof Boolean)&&(e[r]=+!!t)});let t=e.join("\0");if(this.useV100Plus){let r;"API\0"===e[0]?(t=e.slice(5)[0],r=[...this.stringToUTF8Array(e[0]),...e.slice(1,5),...this.stringToUTF8Array(t)]):r=this.stringToUTF8Array(t),"string"!=typeof e[0]&&(r=[...this.numberTo32BitBigEndian(r.length+1),...r,0]),this.client?.write(Buffer.from(new Uint8Array(r)))}else this.client?.write(t+"\0");this.controller.emitEvent(a.EventName.sent,e,t)}onData(e){if(this.useV100Plus){if(this._v100MessageBuffer=Buffer.concat([this._v100MessageBuffer,e]),this._v100MessageBuffer.length>0xffffff){let e=this._v100MessageBuffer.length;this._v100MessageBuffer=Buffer.alloc(0),this.onError(Error(`Message of size ${e} exceeded max message length 16777215`)),this.disconnect();return}for(;this._v100MessageBuffer.length>4;){let e=this._v100MessageBuffer.readInt32BE();if(!(this._v100MessageBuffer.length>=4+e))return;{let t=this._v100MessageBuffer.slice(4,4+e);this._v100MessageBuffer=this._v100MessageBuffer.slice(4+e),this.onMessage(t.toString("utf8"))}}}else this.onMessage(e.toString())}onMessage(e){let t=(this.dataFragment+e).split("\0");""!==t[t.length-1]?this.dataFragment=t[t.length-1]:this.dataFragment="",t=t.slice(0,-1),this.controller.emitEvent(a.EventName.received,t.slice(0),e),this.neverReceived?(this.neverReceived=!1,this.onServerVersion(t)):(this.useV100Plus?this.controller.onMessage(t):this.controller.onTokens(t),this.controller.processIngressQueue()),this.waitingAsync&&(this.waitingAsync=!1,this.controller.resume())}onServerVersion(e){if(this._status=t.ConnectionStatus.Connected,this._serverVersion=parseInt(e[0],10),this._serverConnectionTime=e[1],this.useV100Plus&&(this._serverVersion<100||this._serverVersion>s.MAX_SUPPORTED_SERVER_VERSION)){this.disconnect(),this.controller.emitError(`Unsupported Version ${this._serverVersion}`,l.ErrorCode.UNSUPPORTED_VERSION);return}if(this._serverVersion<s.MIN_SERVER_VER_SUPPORTED){this.disconnect(),this.controller.emitError("The TWS is out of date and must be upgraded.",l.ErrorCode.UPDATE_TWS);return}this.startAPI(),this.controller.emitEvent(a.EventName.connected),this.controller.emitEvent(a.EventName.server,this.serverVersion,this.serverConnectionTime)}startAPI(){this.serverVersion>=3&&(this.serverVersion<u.default.LINKING?this.send([this._clientId]):this.serverVersion>=u.default.OPTIONAL_CAPABILITIES?this.send([d.OUT_MSG_ID.START_API,2,this._clientId,""]):this.send([d.OUT_MSG_ID.START_API,2,this._clientId]))}onConnect(){if(this.useV100Plus){let e=this.buildVersionString(100,s.MAX_SUPPORTED_SERVER_VERSION);this.send(["API\0",...this.numberTo32BitBigEndian(e.length),e])}else this.send([c.default.client_version]),this.send([this._clientId])}onEnd(){this._status&&(this._status=t.ConnectionStatus.Disconnected,this.controller.emitEvent(a.EventName.disconnected)),this.controller.pause()}onError(e){this.controller.emitError(e.message,l.ErrorCode.CONNECT_FAIL)}buildVersionString(e,t){return"v"+(e<t?e+".."+t:e)}numberTo32BitBigEndian(e){let t=[,,,,],r=0;return t[r++]=255&e>>24,t[r++]=255&e>>16,t[r++]=255&e>>8,t[r++]=255&e,t}stringToUTF8Array(e){return Array.from(new o.TextEncoder().encode(e))}flattenDeep(e,t=[]){for(let r=0,n=e.length;r<n;r++){let n=e[r];Array.isArray(n)?this.flattenDeep(n,t):t.push(n)}return t}}t.Socket=h},45806:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(44736).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},45870:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exhaustAll=void 0;var n=r(30001),i=r(56032);t.exhaustAll=function(){return n.exhaustMap(i.identity)}},46124:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},o=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.isSubscription=t.EMPTY_SUBSCRIPTION=t.Subscription=void 0;var s=r(17886),a=r(96634),u=r(1071),c=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,u,c,d=this._parentage;if(d)if(this._parentage=null,Array.isArray(d))try{for(var h=n(d),_=h.next();!_.done;_=h.next())_.value.remove(this)}catch(t){e={error:t}}finally{try{_&&!_.done&&(t=h.return)&&t.call(h)}finally{if(e)throw e.error}}else d.remove(this);var f=this.initialTeardown;if(s.isFunction(f))try{f()}catch(e){c=e instanceof a.UnsubscriptionError?e.errors:[e]}var E=this._finalizers;if(E){this._finalizers=null;try{for(var p=n(E),T=p.next();!T.done;T=p.next()){var A=T.value;try{l(A)}catch(e){c=null!=c?c:[],e instanceof a.UnsubscriptionError?c=o(o([],i(c)),i(e.errors)):c.push(e)}}}catch(e){r={error:e}}finally{try{T&&!T.done&&(u=p.return)&&u.call(p)}finally{if(r)throw r.error}}}if(c)throw new a.UnsubscriptionError(c)}},t.prototype.add=function(e){var r;if(e&&e!==this)if(this.closed)l(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!=(r=this._finalizers)?r:[]).push(e)}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&u.arrRemove(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&u.arrRemove(r,e),e instanceof t&&e._removeParent(this)},(e=new t).closed=!0,t.EMPTY=e,t}();function l(e){s.isFunction(e)?e():e.unsubscribe()}t.Subscription=c,t.EMPTY_SUBSCRIPTION=c.EMPTY,t.isSubscription=function(e){return e instanceof c||e&&"closed"in e&&s.isFunction(e.remove)&&s.isFunction(e.add)&&s.isFunction(e.unsubscribe)}},46488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.findIndex=void 0;var n=r(96829),i=r(68300);t.findIndex=function(e,t){return n.operate(i.createFind(e,t,"index"))}},46889:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EmptyError=void 0,t.EmptyError=r(23008).createErrorClass(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}})},46952:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.tap=void 0;var n=r(17886),i=r(96829),o=r(28719),s=r(56032);t.tap=function(e,t,r){var a=n.isFunction(e)||t||r?{next:e,error:t,complete:r}:e;return a?i.operate(function(e,t){null==(r=a.subscribe)||r.call(a);var r,n=!0;e.subscribe(o.createOperatorSubscriber(t,function(e){var r;null==(r=a.next)||r.call(a,e),t.next(e)},function(){var e;n=!1,null==(e=a.complete)||e.call(a),t.complete()},function(e){var r;n=!1,null==(r=a.error)||r.call(a,e),t.error(e)},function(){var e,t;n&&(null==(e=a.unsubscribe)||e.call(a)),null==(t=a.finalize)||t.call(a)}))}):s.identity}},47012:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.catchError=void 0;var n=r(81773),i=r(28719),o=r(96829);t.catchError=function e(t){return o.operate(function(r,o){var s,a=null,u=!1;a=r.subscribe(i.createOperatorSubscriber(o,void 0,void 0,function(i){s=n.innerFrom(t(i,e(t)(r))),a?(a.unsubscribe(),a=null,s.subscribe(o)):u=!0})),u&&(a.unsubscribe(),a=null,s.subscribe(o))})}},47164:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.skipUntil=void 0;var n=r(96829),i=r(28719),o=r(81773),s=r(63410);t.skipUntil=function(e){return n.operate(function(t,r){var n=!1,a=i.createOperatorSubscriber(r,function(){null==a||a.unsubscribe(),n=!0},s.noop);o.innerFrom(e).subscribe(a),t.subscribe(i.createOperatorSubscriber(r,function(e){return n&&r.next(e)}))})}},47540:(e,t)=>{"use strict";function r(e,t,r){return{kind:e,value:t,error:r}}Object.defineProperty(t,"__esModule",{value:!0}),t.createNotification=t.nextNotification=t.errorNotification=t.COMPLETE_NOTIFICATION=void 0,t.COMPLETE_NOTIFICATION=r("C",void 0,void 0),t.errorNotification=function(e){return r("E",void 0,e)},t.nextNotification=function(e){return r("N",e,void 0)},t.createNotification=r},48002:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.windowTime=void 0;var n=r(1937),i=r(97847),o=r(46124),s=r(96829),a=r(28719),u=r(1071),c=r(66389),l=r(88840);t.windowTime=function(e){for(var t,r,d=[],h=1;h<arguments.length;h++)d[h-1]=arguments[h];var _=null!=(t=c.popScheduler(d))?t:i.asyncScheduler,f=null!=(r=d[0])?r:null,E=d[1]||1/0;return s.operate(function(t,r){var i=[],s=!1,c=function(e){var t=e.window,r=e.subs;t.complete(),r.unsubscribe(),u.arrRemove(i,e),s&&d()},d=function(){if(i){var t=new o.Subscription;r.add(t);var s=new n.Subject,a={window:s,subs:t,seen:0};i.push(a),r.next(s.asObservable()),l.executeSchedule(t,_,function(){return c(a)},e)}};null!==f&&f>=0?l.executeSchedule(r,_,d,f,!0):s=!0,d();var h=function(e){return i.slice().forEach(e)},p=function(e){h(function(t){return e(t.window)}),e(r),r.unsubscribe()};return t.subscribe(a.createOperatorSubscriber(r,function(e){h(function(t){t.window.next(e),E<=++t.seen&&c(t)})},function(){return p(function(e){return e.complete()})},function(e){return p(function(t){return t.error(e)})})),function(){i=null}})}},48115:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.timestamp=void 0;var n=r(87816),i=r(44817);t.timestamp=function(e){return void 0===e&&(e=n.dateTimestampProvider),i.map(function(t){return{value:t,timestamp:e.now()}})}},48369:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.MIN_SERVER_VER=void 0,function(e){e[e.REAL_TIME_BARS=34]="REAL_TIME_BARS",e[e.SCALE_ORDERS=35]="SCALE_ORDERS",e[e.SNAPSHOT_MKT_DATA=35]="SNAPSHOT_MKT_DATA",e[e.SSHORT_COMBO_LEGS=35]="SSHORT_COMBO_LEGS",e[e.WHAT_IF_ORDERS=36]="WHAT_IF_ORDERS",e[e.CONTRACT_CONID=37]="CONTRACT_CONID",e[e.PTA_ORDERS=39]="PTA_ORDERS",e[e.FUNDAMENTAL_DATA=40]="FUNDAMENTAL_DATA",e[e.DELTA_NEUTRAL=40]="DELTA_NEUTRAL",e[e.CONTRACT_DATA_CHAIN=40]="CONTRACT_DATA_CHAIN",e[e.SCALE_ORDERS2=40]="SCALE_ORDERS2",e[e.ALGO_ORDERS=41]="ALGO_ORDERS",e[e.EXECUTION_DATA_CHAIN=42]="EXECUTION_DATA_CHAIN",e[e.NOT_HELD=44]="NOT_HELD",e[e.SEC_ID_TYPE=45]="SEC_ID_TYPE",e[e.PLACE_ORDER_CONID=46]="PLACE_ORDER_CONID",e[e.REQ_MKT_DATA_CONID=47]="REQ_MKT_DATA_CONID",e[e.REQ_CALC_IMPLIED_VOLAT=49]="REQ_CALC_IMPLIED_VOLAT",e[e.CANCEL_CALC_IMPLIED_VOLAT=50]="CANCEL_CALC_IMPLIED_VOLAT",e[e.CANCEL_CALC_OPTION_PRICE=50]="CANCEL_CALC_OPTION_PRICE",e[e.REQ_CALC_OPTION_PRICE=50]="REQ_CALC_OPTION_PRICE",e[e.SSHORTX_OLD=51]="SSHORTX_OLD",e[e.SSHORTX=52]="SSHORTX",e[e.REQ_GLOBAL_CANCEL=53]="REQ_GLOBAL_CANCEL",e[e.HEDGE_ORDERS=54]="HEDGE_ORDERS",e[e.REQ_MARKET_DATA_TYPE=55]="REQ_MARKET_DATA_TYPE",e[e.OPT_OUT_SMART_ROUTING=56]="OPT_OUT_SMART_ROUTING",e[e.SMART_COMBO_ROUTING_PARAMS=57]="SMART_COMBO_ROUTING_PARAMS",e[e.DELTA_NEUTRAL_CONID=58]="DELTA_NEUTRAL_CONID",e[e.SCALE_ORDERS3=60]="SCALE_ORDERS3",e[e.ORDER_COMBO_LEGS_PRICE=61]="ORDER_COMBO_LEGS_PRICE",e[e.TRAILING_PERCENT=62]="TRAILING_PERCENT",e[e.DELTA_NEUTRAL_OPEN_CLOSE=66]="DELTA_NEUTRAL_OPEN_CLOSE",e[e.POSITIONS=67]="POSITIONS",e[e.ACCT_SUMMARY=67]="ACCT_SUMMARY",e[e.TRADING_CLASS=68]="TRADING_CLASS",e[e.SCALE_TABLE=69]="SCALE_TABLE",e[e.LINKING=70]="LINKING",e[e.ALGO_ID=71]="ALGO_ID",e[e.OPTIONAL_CAPABILITIES=72]="OPTIONAL_CAPABILITIES",e[e.ORDER_SOLICITED=73]="ORDER_SOLICITED",e[e.LINKING_AUTH=74]="LINKING_AUTH",e[e.PRIMARYEXCH=75]="PRIMARYEXCH",e[e.RANDOMIZE_SIZE_AND_PRICE=76]="RANDOMIZE_SIZE_AND_PRICE",e[e.FRACTIONAL_POSITIONS=101]="FRACTIONAL_POSITIONS",e[e.PEGGED_TO_BENCHMARK=102]="PEGGED_TO_BENCHMARK",e[e.MODELS_SUPPORT=103]="MODELS_SUPPORT",e[e.SEC_DEF_OPT_PARAMS_REQ=104]="SEC_DEF_OPT_PARAMS_REQ",e[e.EXT_OPERATOR=105]="EXT_OPERATOR",e[e.SOFT_DOLLAR_TIER=106]="SOFT_DOLLAR_TIER",e[e.REQ_FAMILY_CODES=107]="REQ_FAMILY_CODES",e[e.REQ_MATCHING_SYMBOLS=108]="REQ_MATCHING_SYMBOLS",e[e.PAST_LIMIT=109]="PAST_LIMIT",e[e.MD_SIZE_MULTIPLIER=110]="MD_SIZE_MULTIPLIER",e[e.CASH_QTY=111]="CASH_QTY",e[e.REQ_MKT_DEPTH_EXCHANGES=112]="REQ_MKT_DEPTH_EXCHANGES",e[e.TICK_NEWS=113]="TICK_NEWS",e[e.REQ_SMART_COMPONENTS=114]="REQ_SMART_COMPONENTS",e[e.REQ_NEWS_PROVIDERS=115]="REQ_NEWS_PROVIDERS",e[e.REQ_NEWS_ARTICLE=116]="REQ_NEWS_ARTICLE",e[e.REQ_HISTORICAL_NEWS=117]="REQ_HISTORICAL_NEWS",e[e.REQ_HEAD_TIMESTAMP=118]="REQ_HEAD_TIMESTAMP",e[e.REQ_HISTOGRAM=119]="REQ_HISTOGRAM",e[e.SERVICE_DATA_TYPE=120]="SERVICE_DATA_TYPE",e[e.AGG_GROUP=121]="AGG_GROUP",e[e.UNDERLYING_INFO=122]="UNDERLYING_INFO",e[e.CANCEL_HEADTIMESTAMP=123]="CANCEL_HEADTIMESTAMP",e[e.SYNT_REALTIME_BARS=124]="SYNT_REALTIME_BARS",e[e.CFD_REROUTE=125]="CFD_REROUTE",e[e.MARKET_RULES=126]="MARKET_RULES",e[e.PNL=127]="PNL",e[e.NEWS_QUERY_ORIGINS=128]="NEWS_QUERY_ORIGINS",e[e.UNREALIZED_PNL=129]="UNREALIZED_PNL",e[e.HISTORICAL_TICKS=130]="HISTORICAL_TICKS",e[e.MARKET_CAP_PRICE=131]="MARKET_CAP_PRICE",e[e.PRE_OPEN_BID_ASK=132]="PRE_OPEN_BID_ASK",e[e.REAL_EXPIRATION_DATE=134]="REAL_EXPIRATION_DATE",e[e.REALIZED_PNL=135]="REALIZED_PNL",e[e.LAST_LIQUIDITY=136]="LAST_LIQUIDITY",e[e.TICK_BY_TICK=137]="TICK_BY_TICK",e[e.DECISION_MAKER=138]="DECISION_MAKER",e[e.MIFID_EXECUTION=139]="MIFID_EXECUTION",e[e.TICK_BY_TICK_IGNORE_SIZE=140]="TICK_BY_TICK_IGNORE_SIZE",e[e.AUTO_PRICE_FOR_HEDGE=141]="AUTO_PRICE_FOR_HEDGE",e[e.WHAT_IF_EXT_FIELDS=142]="WHAT_IF_EXT_FIELDS",e[e.SCANNER_GENERIC_OPTS=143]="SCANNER_GENERIC_OPTS",e[e.API_BIND_ORDER=144]="API_BIND_ORDER",e[e.ORDER_CONTAINER=145]="ORDER_CONTAINER",e[e.SMART_DEPTH=146]="SMART_DEPTH",e[e.REMOVE_NULL_ALL_CASTING=147]="REMOVE_NULL_ALL_CASTING",e[e.D_PEG_ORDERS=148]="D_PEG_ORDERS",e[e.MKT_DEPTH_PRIM_EXCHANGE=149]="MKT_DEPTH_PRIM_EXCHANGE",e[e.REQ_COMPLETED_ORDERS=150]="REQ_COMPLETED_ORDERS",e[e.PRICE_MGMT_ALGO=151]="PRICE_MGMT_ALGO",e[e.STOCK_TYPE=152]="STOCK_TYPE",e[e.ENCODE_MSG_ASCII7=153]="ENCODE_MSG_ASCII7",e[e.SEND_ALL_FAMILY_CODES=154]="SEND_ALL_FAMILY_CODES",e[e.NO_DEFAULT_OPEN_CLOSE=155]="NO_DEFAULT_OPEN_CLOSE",e[e.PRICE_BASED_VOLATILITY=156]="PRICE_BASED_VOLATILITY",e[e.REPLACE_FA_END=157]="REPLACE_FA_END",e[e.DURATION=158]="DURATION",e[e.MARKET_DATA_IN_SHARES=159]="MARKET_DATA_IN_SHARES",e[e.POST_TO_ATS=160]="POST_TO_ATS",e[e.WSHE_CALENDAR=161]="WSHE_CALENDAR",e[e.AUTO_CANCEL_PARENT=162]="AUTO_CANCEL_PARENT",e[e.FRACTIONAL_SIZE_SUPPORT=163]="FRACTIONAL_SIZE_SUPPORT",e[e.SIZE_RULES=164]="SIZE_RULES",e[e.HISTORICAL_SCHEDULE=165]="HISTORICAL_SCHEDULE",e[e.ADVANCED_ORDER_REJECT=166]="ADVANCED_ORDER_REJECT",e[e.USER_INFO=167]="USER_INFO",e[e.CRYPTO_AGGREGATED_TRADES=168]="CRYPTO_AGGREGATED_TRADES",e[e.MANUAL_ORDER_TIME=169]="MANUAL_ORDER_TIME",e[e.PEGBEST_PEGMID_OFFSETS=170]="PEGBEST_PEGMID_OFFSETS",e[e.WSH_EVENT_DATA_FILTERS=171]="WSH_EVENT_DATA_FILTERS",e[e.IPO_PRICES=172]="IPO_PRICES",e[e.WSH_EVENT_DATA_FILTERS_DATE=173]="WSH_EVENT_DATA_FILTERS_DATE",e[e.INSTRUMENT_TIMEZONE=174]="INSTRUMENT_TIMEZONE",e[e.HMDS_MARKET_DATA_IN_SHARES=175]="HMDS_MARKET_DATA_IN_SHARES",e[e.BOND_ISSUERID=176]="BOND_ISSUERID",e[e.FA_PROFILE_DESUPPORT=177]="FA_PROFILE_DESUPPORT",e[e.PENDING_PRICE_REVISION=178]="PENDING_PRICE_REVISION",e[e.FUND_DATA_FIELDS=179]="FUND_DATA_FIELDS",e[e.MANUAL_ORDER_TIME_EXERCISE_OPTIONS=180]="MANUAL_ORDER_TIME_EXERCISE_OPTIONS",e[e.OPEN_ORDER_AD_STRATEGY=181]="OPEN_ORDER_AD_STRATEGY",e[e.LAST_TRADE_DATE=182]="LAST_TRADE_DATE",e[e.CUSTOMER_ACCOUNT=183]="CUSTOMER_ACCOUNT",e[e.PROFESSIONAL_CUSTOMER=184]="PROFESSIONAL_CUSTOMER",e[e.BOND_ACCRUED_INTEREST=185]="BOND_ACCRUED_INTEREST",e[e.INELIGIBILITY_REASONS=186]="INELIGIBILITY_REASONS",e[e.RFQ_FIELDS=187]="RFQ_FIELDS",e[e.BOND_TRADING_HOURS=188]="BOND_TRADING_HOURS",e[e.INCLUDE_OVERNIGHT=189]="INCLUDE_OVERNIGHT",e[e.UNDO_RFQ_FIELDS=190]="UNDO_RFQ_FIELDS",e[e.PERM_ID_AS_LONG=191]="PERM_ID_AS_LONG",e[e.CME_TAGGING_FIELDS=192]="CME_TAGGING_FIELDS",e[e.CME_TAGGING_FIELDS_IN_OPEN_ORDER=193]="CME_TAGGING_FIELDS_IN_OPEN_ORDER"}(r||(t.MIN_SERVER_VER=r={})),t.default=r},48909:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OrderConditionType=void 0,function(e){e[e.Price=1]="Price",e[e.Time=3]="Time",e[e.Margin=4]="Margin",e[e.Execution=5]="Execution",e[e.Volume=6]="Volume",e[e.PercentChange=7]="PercentChange"}(r||(t.OrderConditionType=r={})),t.default=r},50099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dematerialize=void 0;var n=r(38138),i=r(96829),o=r(28719);t.dematerialize=function(){return i.operate(function(e,t){e.subscribe(o.createOperatorSubscriber(t,function(e){return n.observeNotification(e,t)}))})}},50513:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.retry=void 0;var n=r(96829),i=r(28719),o=r(56032),s=r(21574),a=r(81773);t.retry=function(e){void 0===e&&(e=1/0);var t=e&&"object"==typeof e?e:{count:e},r=t.count,u=void 0===r?1/0:r,c=t.delay,l=t.resetOnSuccess,d=void 0!==l&&l;return u<=0?o.identity:n.operate(function(e,t){var r,n=0,o=function(){var l=!1;r=e.subscribe(i.createOperatorSubscriber(t,function(e){d&&(n=0),t.next(e)},void 0,function(e){if(n++<u){var d=function(){r?(r.unsubscribe(),r=null,o()):l=!0};if(null!=c){var h="number"==typeof c?s.timer(c):a.innerFrom(c(e,n)),_=i.createOperatorSubscriber(t,function(){_.unsubscribe(),d()},function(){t.complete()});h.subscribe(_)}else d()}else t.error(e)})),l&&(r.unsubscribe(),r=null,o())};o()})}},50747:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.debounceTime=void 0;var n=r(97847),i=r(96829),o=r(28719);t.debounceTime=function(e,t){return void 0===t&&(t=n.asyncScheduler),i.operate(function(r,n){var i=null,s=null,a=null,u=function(){if(i){i.unsubscribe(),i=null;var e=s;s=null,n.next(e)}};function c(){var r=a+e,o=t.now();if(o<r){i=this.schedule(void 0,r-o),n.add(i);return}u()}r.subscribe(o.createOperatorSubscriber(n,function(r){s=r,a=t.now(),i||(i=t.schedule(c,e),n.add(i))},function(){u(),n.complete()},void 0,function(){s=i=null}))})}},51551:function(e,t,r){"use strict";var n,i=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return(n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&i(t,e,r[s]);return o(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ConsoleLogger=void 0;let u=a(r(31649)),c=s(r(28354)),l=r(90742);class d{constructor(){this._logLevel=l.LogLevel.SYSTEM}get logLevel(){return this._logLevel}set logLevel(e){this._logLevel=e}debug(e,t){this._logLevel>=l.LogLevel.DETAIL&&console.debug(`[${new Date().toLocaleTimeString()}] [DEBUG] [${e}]: `,t)}info(e,t){this._logLevel>=l.LogLevel.INFO&&console.log(`[${new Date().toLocaleTimeString()}] [INFO] [${e}]: `,t)}warn(e,t){if(this._logLevel>=l.LogLevel.WARN){let r=t;Array.isArray(t)&&(r=t.map(u.default.yellow)),console.warn(u.default.bold.yellow(`[${new Date().toLocaleTimeString()}] [WARN] [${e}]: `),r)}}error(e,t){if(this._logLevel>=l.LogLevel.ERROR){let r=t;Array.isArray(t)&&(r=t.map(e=>u.default.bold.red(c.inspect(e,{showHidden:!1,depth:null})))),console.error(u.default.bold.red(`[${new Date().toLocaleTimeString()}] [ERROR] [${e}]:`),r)}}}t.ConsoleLogger=d},51682:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.flatMap=void 0,t.flatMap=r(24071).mergeMap},52443:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.schedulePromise=void 0;var n=r(81773),i=r(95628),o=r(81700);t.schedulePromise=function(e,t){return n.innerFrom(e).pipe(o.subscribeOn(t),i.observeOn(t))}},52605:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Controller=void 0;let i=n(r(38576)),o=n(r(87834)),s=r(79898),a=n(r(44694)),u=r(7133),c=r(89519),l=r(89123),d=r(45714);class h{constructor(e,t){this.ib=e,this.options=t,this.commands=new i.default(h.execute,this),this.encoder=new l.Encoder(this),this.decoder=new c.Decoder(this),this.socket=new d.Socket(this,this.options),this.commands.pause();let r=t?.maxReqPerSec??a.default.max_req_per_second??40;this.rateLimiter=(0,o.default)(r/10,100,e=>{this.socket.send(e)})}pause(){this.commands.pause()}resume(){this.commands.resume()}connect(e){this.executeConnect(e)}disconnect(){this.executeDisconnect()}schedule(e){this.commands.schedule(()=>e())}send(...e){this.commands.run(()=>this.executeSend(e))}processIngressQueue(){this.decoder.process()}onMessage(e){this.decoder.enqueueMessage(e)}onTokens(e){this.decoder.enqueueTokens(e)}get serverVersion(){return this.socket.serverVersion}get connected(){return this.socket.connected}disableUseV100Plus(){return this.socket.disableUseV100Plus()}sendMsg(...e){this.rateLimiter(e)}emitEvent(e,...t){this.ib.emit(e,...t),e!==s.EventName.connected&&e!==s.EventName.disconnected&&e!==s.EventName.error&&e!==s.EventName.received&&e!==s.EventName.sent&&e!==s.EventName.server&&this.ib.emit(s.EventName.result,e,t),this.ib.emit(s.EventName.all,e,t),e===s.EventName.nextValidId&&this.resume()}emitInfo(e,t){this.emitEvent(s.EventName.info,e,t)}emitError(e,t,r,n){this.emitEvent(s.EventName.error,Error(e),t,r??u.ErrorCode.NO_VALID_ID,n)}static execute(e,t){e(t)}executeConnect(e){this.socket.connected?this.emitInfo("Cannot connect if already connected.",u.ErrorCode.ALREADY_CONNECTED):this.socket.connect(e)}executeDisconnect(){this.socket.status>=d.ConnectionStatus.Connecting?this.socket.disconnect():this.emitInfo("Cannot disconnect if already disconnected.",u.ErrorCode.NOT_CONNECTED)}executeSend(e){this.socket.connected?this.socket.send(e):this.emitError("Cannot send data when disconnected.",u.ErrorCode.NOT_CONNECTED,u.ErrorCode.NO_VALID_ID)}}t.Controller=h},52874:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scan=void 0;var n=r(96829),i=r(91168);t.scan=function(e,t){return n.operate(i.scanInternals(e,t,arguments.length>=2,!0))}},52974:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MarginCondition=void 0;let n=r(48909);class i{constructor(e,t,r){this.percent=e,this.isMore=t,this.conjunctionConnection=r,this.type=n.OrderConditionType.Margin}get strValue(){return""+this.percent}}t.MarginCondition=i,t.default=i},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Scheduler=void 0;var n=r(87816);t.Scheduler=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=n.dateTimestampProvider.now,e}()},53747:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sample=void 0;var n=r(81773),i=r(96829),o=r(63410),s=r(28719);t.sample=function(e){return i.operate(function(t,r){var i=!1,a=null;t.subscribe(s.createOperatorSubscriber(r,function(e){i=!0,a=e})),n.innerFrom(e).subscribe(s.createOperatorSubscriber(r,function(){if(i){i=!1;var e=a;a=null,r.next(e)}},o.noop))})}},54431:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MutableAccountSummariesUpdate=t.MutableAccountSummaries=t.MutableAccountSummaryTagValues=t.MutableAccountSummaryValues=void 0;let n=r(65043),i=r(66811);class o extends n.IBApiNextMap{}t.MutableAccountSummaryValues=o;class s extends n.IBApiNextMap{}t.MutableAccountSummaryTagValues=s;class a extends n.IBApiNextMap{}t.MutableAccountSummaries=a;class u extends i.IBApiNextItemListUpdate{}t.MutableAccountSummariesUpdate=u},54499:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeAll=t.merge=t.max=t.materialize=t.mapTo=t.map=t.last=t.isEmpty=t.ignoreElements=t.groupBy=t.first=t.findIndex=t.find=t.finalize=t.filter=t.expand=t.exhaustMap=t.exhaustAll=t.exhaust=t.every=t.endWith=t.elementAt=t.distinctUntilKeyChanged=t.distinctUntilChanged=t.distinct=t.dematerialize=t.delayWhen=t.delay=t.defaultIfEmpty=t.debounceTime=t.debounce=t.count=t.connect=t.concatWith=t.concatMapTo=t.concatMap=t.concatAll=t.concat=t.combineLatestWith=t.combineLatest=t.combineLatestAll=t.combineAll=t.catchError=t.bufferWhen=t.bufferToggle=t.bufferTime=t.bufferCount=t.buffer=t.auditTime=t.audit=void 0,t.timeInterval=t.throwIfEmpty=t.throttleTime=t.throttle=t.tap=t.takeWhile=t.takeUntil=t.takeLast=t.take=t.switchScan=t.switchMapTo=t.switchMap=t.switchAll=t.subscribeOn=t.startWith=t.skipWhile=t.skipUntil=t.skipLast=t.skip=t.single=t.shareReplay=t.share=t.sequenceEqual=t.scan=t.sampleTime=t.sample=t.refCount=t.retryWhen=t.retry=t.repeatWhen=t.repeat=t.reduce=t.raceWith=t.race=t.publishReplay=t.publishLast=t.publishBehavior=t.publish=t.pluck=t.partition=t.pairwise=t.onErrorResumeNext=t.observeOn=t.multicast=t.min=t.mergeWith=t.mergeScan=t.mergeMapTo=t.mergeMap=t.flatMap=void 0,t.zipWith=t.zipAll=t.zip=t.withLatestFrom=t.windowWhen=t.windowToggle=t.windowTime=t.windowCount=t.window=t.toArray=t.timestamp=t.timeoutWith=t.timeout=void 0;var n=r(91802);Object.defineProperty(t,"audit",{enumerable:!0,get:function(){return n.audit}});var i=r(17905);Object.defineProperty(t,"auditTime",{enumerable:!0,get:function(){return i.auditTime}});var o=r(28299);Object.defineProperty(t,"buffer",{enumerable:!0,get:function(){return o.buffer}});var s=r(94108);Object.defineProperty(t,"bufferCount",{enumerable:!0,get:function(){return s.bufferCount}});var a=r(59740);Object.defineProperty(t,"bufferTime",{enumerable:!0,get:function(){return a.bufferTime}});var u=r(83979);Object.defineProperty(t,"bufferToggle",{enumerable:!0,get:function(){return u.bufferToggle}});var c=r(14431);Object.defineProperty(t,"bufferWhen",{enumerable:!0,get:function(){return c.bufferWhen}});var l=r(47012);Object.defineProperty(t,"catchError",{enumerable:!0,get:function(){return l.catchError}});var d=r(21421);Object.defineProperty(t,"combineAll",{enumerable:!0,get:function(){return d.combineAll}});var h=r(78650);Object.defineProperty(t,"combineLatestAll",{enumerable:!0,get:function(){return h.combineLatestAll}});var _=r(77685);Object.defineProperty(t,"combineLatest",{enumerable:!0,get:function(){return _.combineLatest}});var f=r(35373);Object.defineProperty(t,"combineLatestWith",{enumerable:!0,get:function(){return f.combineLatestWith}});var E=r(85249);Object.defineProperty(t,"concat",{enumerable:!0,get:function(){return E.concat}});var p=r(77982);Object.defineProperty(t,"concatAll",{enumerable:!0,get:function(){return p.concatAll}});var T=r(12897);Object.defineProperty(t,"concatMap",{enumerable:!0,get:function(){return T.concatMap}});var A=r(39278);Object.defineProperty(t,"concatMapTo",{enumerable:!0,get:function(){return A.concatMapTo}});var I=r(29017);Object.defineProperty(t,"concatWith",{enumerable:!0,get:function(){return I.concatWith}});var v=r(94165);Object.defineProperty(t,"connect",{enumerable:!0,get:function(){return v.connect}});var O=r(82570);Object.defineProperty(t,"count",{enumerable:!0,get:function(){return O.count}});var S=r(45388);Object.defineProperty(t,"debounce",{enumerable:!0,get:function(){return S.debounce}});var b=r(50747);Object.defineProperty(t,"debounceTime",{enumerable:!0,get:function(){return b.debounceTime}});var m=r(17476);Object.defineProperty(t,"defaultIfEmpty",{enumerable:!0,get:function(){return m.defaultIfEmpty}});var D=r(10898);Object.defineProperty(t,"delay",{enumerable:!0,get:function(){return D.delay}});var N=r(19682);Object.defineProperty(t,"delayWhen",{enumerable:!0,get:function(){return N.delayWhen}});var y=r(50099);Object.defineProperty(t,"dematerialize",{enumerable:!0,get:function(){return y.dematerialize}});var P=r(15109);Object.defineProperty(t,"distinct",{enumerable:!0,get:function(){return P.distinct}});var R=r(41133);Object.defineProperty(t,"distinctUntilChanged",{enumerable:!0,get:function(){return R.distinctUntilChanged}});var C=r(24528);Object.defineProperty(t,"distinctUntilKeyChanged",{enumerable:!0,get:function(){return C.distinctUntilKeyChanged}});var g=r(31124);Object.defineProperty(t,"elementAt",{enumerable:!0,get:function(){return g.elementAt}});var L=r(19916);Object.defineProperty(t,"endWith",{enumerable:!0,get:function(){return L.endWith}});var M=r(65014);Object.defineProperty(t,"every",{enumerable:!0,get:function(){return M.every}});var U=r(66193);Object.defineProperty(t,"exhaust",{enumerable:!0,get:function(){return U.exhaust}});var V=r(45870);Object.defineProperty(t,"exhaustAll",{enumerable:!0,get:function(){return V.exhaustAll}});var x=r(30001);Object.defineProperty(t,"exhaustMap",{enumerable:!0,get:function(){return x.exhaustMap}});var w=r(33681);Object.defineProperty(t,"expand",{enumerable:!0,get:function(){return w.expand}});var F=r(45007);Object.defineProperty(t,"filter",{enumerable:!0,get:function(){return F.filter}});var B=r(8707);Object.defineProperty(t,"finalize",{enumerable:!0,get:function(){return B.finalize}});var H=r(68300);Object.defineProperty(t,"find",{enumerable:!0,get:function(){return H.find}});var G=r(46488);Object.defineProperty(t,"findIndex",{enumerable:!0,get:function(){return G.findIndex}});var k=r(86525);Object.defineProperty(t,"first",{enumerable:!0,get:function(){return k.first}});var j=r(21553);Object.defineProperty(t,"groupBy",{enumerable:!0,get:function(){return j.groupBy}});var K=r(91204);Object.defineProperty(t,"ignoreElements",{enumerable:!0,get:function(){return K.ignoreElements}});var W=r(61512);Object.defineProperty(t,"isEmpty",{enumerable:!0,get:function(){return W.isEmpty}});var Y=r(39969);Object.defineProperty(t,"last",{enumerable:!0,get:function(){return Y.last}});var Q=r(44817);Object.defineProperty(t,"map",{enumerable:!0,get:function(){return Q.map}});var q=r(20318);Object.defineProperty(t,"mapTo",{enumerable:!0,get:function(){return q.mapTo}});var z=r(38200);Object.defineProperty(t,"materialize",{enumerable:!0,get:function(){return z.materialize}});var X=r(16137);Object.defineProperty(t,"max",{enumerable:!0,get:function(){return X.max}});var $=r(86059);Object.defineProperty(t,"merge",{enumerable:!0,get:function(){return $.merge}});var Z=r(87820);Object.defineProperty(t,"mergeAll",{enumerable:!0,get:function(){return Z.mergeAll}});var J=r(51682);Object.defineProperty(t,"flatMap",{enumerable:!0,get:function(){return J.flatMap}});var ee=r(24071);Object.defineProperty(t,"mergeMap",{enumerable:!0,get:function(){return ee.mergeMap}});var et=r(40552);Object.defineProperty(t,"mergeMapTo",{enumerable:!0,get:function(){return et.mergeMapTo}});var er=r(59920);Object.defineProperty(t,"mergeScan",{enumerable:!0,get:function(){return er.mergeScan}});var en=r(63759);Object.defineProperty(t,"mergeWith",{enumerable:!0,get:function(){return en.mergeWith}});var ei=r(70671);Object.defineProperty(t,"min",{enumerable:!0,get:function(){return ei.min}});var eo=r(86281);Object.defineProperty(t,"multicast",{enumerable:!0,get:function(){return eo.multicast}});var es=r(95628);Object.defineProperty(t,"observeOn",{enumerable:!0,get:function(){return es.observeOn}});var ea=r(97118);Object.defineProperty(t,"onErrorResumeNext",{enumerable:!0,get:function(){return ea.onErrorResumeNext}});var eu=r(69105);Object.defineProperty(t,"pairwise",{enumerable:!0,get:function(){return eu.pairwise}});var ec=r(67165);Object.defineProperty(t,"partition",{enumerable:!0,get:function(){return ec.partition}});var el=r(20128);Object.defineProperty(t,"pluck",{enumerable:!0,get:function(){return el.pluck}});var ed=r(10138);Object.defineProperty(t,"publish",{enumerable:!0,get:function(){return ed.publish}});var eh=r(38760);Object.defineProperty(t,"publishBehavior",{enumerable:!0,get:function(){return eh.publishBehavior}});var e_=r(31374);Object.defineProperty(t,"publishLast",{enumerable:!0,get:function(){return e_.publishLast}});var ef=r(20407);Object.defineProperty(t,"publishReplay",{enumerable:!0,get:function(){return ef.publishReplay}});var eE=r(94022);Object.defineProperty(t,"race",{enumerable:!0,get:function(){return eE.race}});var ep=r(82258);Object.defineProperty(t,"raceWith",{enumerable:!0,get:function(){return ep.raceWith}});var eT=r(4759);Object.defineProperty(t,"reduce",{enumerable:!0,get:function(){return eT.reduce}});var eA=r(56256);Object.defineProperty(t,"repeat",{enumerable:!0,get:function(){return eA.repeat}});var eI=r(12296);Object.defineProperty(t,"repeatWhen",{enumerable:!0,get:function(){return eI.repeatWhen}});var ev=r(50513);Object.defineProperty(t,"retry",{enumerable:!0,get:function(){return ev.retry}});var eO=r(84805);Object.defineProperty(t,"retryWhen",{enumerable:!0,get:function(){return eO.retryWhen}});var eS=r(2673);Object.defineProperty(t,"refCount",{enumerable:!0,get:function(){return eS.refCount}});var eb=r(53747);Object.defineProperty(t,"sample",{enumerable:!0,get:function(){return eb.sample}});var em=r(41252);Object.defineProperty(t,"sampleTime",{enumerable:!0,get:function(){return em.sampleTime}});var eD=r(52874);Object.defineProperty(t,"scan",{enumerable:!0,get:function(){return eD.scan}});var eN=r(76046);Object.defineProperty(t,"sequenceEqual",{enumerable:!0,get:function(){return eN.sequenceEqual}});var ey=r(9738);Object.defineProperty(t,"share",{enumerable:!0,get:function(){return ey.share}});var eP=r(15943);Object.defineProperty(t,"shareReplay",{enumerable:!0,get:function(){return eP.shareReplay}});var eR=r(62633);Object.defineProperty(t,"single",{enumerable:!0,get:function(){return eR.single}});var eC=r(34220);Object.defineProperty(t,"skip",{enumerable:!0,get:function(){return eC.skip}});var eg=r(70236);Object.defineProperty(t,"skipLast",{enumerable:!0,get:function(){return eg.skipLast}});var eL=r(47164);Object.defineProperty(t,"skipUntil",{enumerable:!0,get:function(){return eL.skipUntil}});var eM=r(42767);Object.defineProperty(t,"skipWhile",{enumerable:!0,get:function(){return eM.skipWhile}});var eU=r(607);Object.defineProperty(t,"startWith",{enumerable:!0,get:function(){return eU.startWith}});var eV=r(81700);Object.defineProperty(t,"subscribeOn",{enumerable:!0,get:function(){return eV.subscribeOn}});var ex=r(56814);Object.defineProperty(t,"switchAll",{enumerable:!0,get:function(){return ex.switchAll}});var ew=r(33361);Object.defineProperty(t,"switchMap",{enumerable:!0,get:function(){return ew.switchMap}});var eF=r(6462);Object.defineProperty(t,"switchMapTo",{enumerable:!0,get:function(){return eF.switchMapTo}});var eB=r(810);Object.defineProperty(t,"switchScan",{enumerable:!0,get:function(){return eB.switchScan}});var eH=r(90580);Object.defineProperty(t,"take",{enumerable:!0,get:function(){return eH.take}});var eG=r(68468);Object.defineProperty(t,"takeLast",{enumerable:!0,get:function(){return eG.takeLast}});var ek=r(21028);Object.defineProperty(t,"takeUntil",{enumerable:!0,get:function(){return ek.takeUntil}});var ej=r(20695);Object.defineProperty(t,"takeWhile",{enumerable:!0,get:function(){return ej.takeWhile}});var eK=r(46952);Object.defineProperty(t,"tap",{enumerable:!0,get:function(){return eK.tap}});var eW=r(4310);Object.defineProperty(t,"throttle",{enumerable:!0,get:function(){return eW.throttle}});var eY=r(11594);Object.defineProperty(t,"throttleTime",{enumerable:!0,get:function(){return eY.throttleTime}});var eQ=r(98193);Object.defineProperty(t,"throwIfEmpty",{enumerable:!0,get:function(){return eQ.throwIfEmpty}});var eq=r(60925);Object.defineProperty(t,"timeInterval",{enumerable:!0,get:function(){return eq.timeInterval}});var ez=r(59798);Object.defineProperty(t,"timeout",{enumerable:!0,get:function(){return ez.timeout}});var eX=r(94498);Object.defineProperty(t,"timeoutWith",{enumerable:!0,get:function(){return eX.timeoutWith}});var e$=r(48115);Object.defineProperty(t,"timestamp",{enumerable:!0,get:function(){return e$.timestamp}});var eZ=r(28013);Object.defineProperty(t,"toArray",{enumerable:!0,get:function(){return eZ.toArray}});var eJ=r(18401);Object.defineProperty(t,"window",{enumerable:!0,get:function(){return eJ.window}});var e0=r(28794);Object.defineProperty(t,"windowCount",{enumerable:!0,get:function(){return e0.windowCount}});var e1=r(48002);Object.defineProperty(t,"windowTime",{enumerable:!0,get:function(){return e1.windowTime}});var e2=r(27573);Object.defineProperty(t,"windowToggle",{enumerable:!0,get:function(){return e2.windowToggle}});var e8=r(59797);Object.defineProperty(t,"windowWhen",{enumerable:!0,get:function(){return e8.windowWhen}});var e9=r(92772);Object.defineProperty(t,"withLatestFrom",{enumerable:!0,get:function(){return e9.withLatestFrom}});var e7=r(15738);Object.defineProperty(t,"zip",{enumerable:!0,get:function(){return e7.zip}});var e4=r(76523);Object.defineProperty(t,"zipAll",{enumerable:!0,get:function(){return e4.zipAll}});var e5=r(20646);Object.defineProperty(t,"zipWith",{enumerable:!0,get:function(){return e5.zipWith}})},54819:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MutableAccountPositionsUpdate=t.MutableAccountPositions=void 0;let n=r(65043),i=r(66811);class o extends n.IBApiNextMap{}t.MutableAccountPositions=o;class s extends i.IBApiNextItemListUpdate{}t.MutableAccountPositionsUpdate=s},54972:e=>{"use strict";e.exports=function(e,t){var r=(t=t||process.argv).indexOf("--"),n=/^-{1,2}/.test(e)?"":"--",i=t.indexOf(n+e);return -1!==i&&(-1===r||i<r)}},55100:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectableObservable=void 0;var i=r(57248),o=r(46124),s=r(2673),a=r(28719),u=r(96829);t.ConnectableObservable=function(e){function t(t,r){var n=e.call(this)||this;return n.source=t,n.subjectFactory=r,n._subject=null,n._refCount=0,n._connection=null,u.hasLift(t)&&(n.lift=t.lift),n}return n(t,e),t.prototype._subscribe=function(e){return this.getSubject().subscribe(e)},t.prototype.getSubject=function(){var e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject},t.prototype._teardown=function(){this._refCount=0;var e=this._connection;this._subject=this._connection=null,null==e||e.unsubscribe()},t.prototype.connect=function(){var e=this,t=this._connection;if(!t){t=this._connection=new o.Subscription;var r=this.getSubject();t.add(this.source.subscribe(a.createOperatorSubscriber(r,void 0,function(){e._teardown(),r.complete()},function(t){e._teardown(),r.error(t)},function(){return e._teardown()}))),t.closed&&(this._connection=null,t=o.Subscription.EMPTY)}return t},t.prototype.refCount=function(){return s.refCount()(this)},t}(i.Observable)},55293:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.FOP=void 0;let i=n(r(15850));class o{constructor(e,t,r,n,o,s,a){this.symbol=e,this.expiry=t,this.strike=r,this.right=n,this.multiplier=o,this.exchange=s,this.currency=a,this.secType=i.default.FOP,this.currency=this.currency??"USD",this.exchange=this.exchange??"GLOBEX",this.multiplier=this.multiplier??50}}t.FOP=o,t.default=o},55332:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isPegMidOrder=t.isPegBestOrder=t.isPegBenchOrder=t.isVolOrder=t.OrderType=void 0,function(e){e.None="",e.MKT="MKT",e.LMT="LMT",e.STP="STP",e.STP_LMT="STP LMT",e.REL="REL",e.TRAIL="TRAIL",e.BOX_TOP="BOX TOP",e.FIX_PEGGED="FIX PEGGED",e.LIT="LIT",e.LMT_PLUS_MKT="LMT + MKT",e.LOC="LOC",e.MIDPRICE="MIDPRICE",e.MIT="MIT",e.MKT_PRT="MKT PRT",e.MOC="MOC",e.MTL="MTL",e.PASSV_REL="PASSV REL",e.PEG_BENCH="PEG BENCH",e.PEG_BEST="PEG BEST",e.PEG_MID="PEG MID",e.PEG_MKT="PEG MKT",e.PEG_PRIM="PEG PRIM",e.PEG_STK="PEG STK",e.REL_PLUS_LMT="REL + LMT",e.REL_PLUS_MKT="REL + MKT",e.SNAP_MID="SNAP MID",e.SNAP_MKT="SNAP MKT",e.SNAP_PRIM="SNAP PRIM",e.STP_PRT="STP PRT",e.TRAIL_LIMIT="TRAIL LIMIT",e.TRAIL_LIT="TRAIL LIT",e.TRAIL_LMT_PLUS_MKT="TRAIL LMT + MKT",e.TRAIL_MIT="TRAIL MIT",e.TRAIL_REL_PLUS_MKT="TRAIL REL + MKT",e.VOL="VOL",e.VWAP="VWAP",e.QUOTE="QUOTE",e.PEG_PRIM_VOL="PPV",e.PEG_MID_VOL="PDV",e.PEG_MKT_VOL="PMV",e.PEG_SRF_VOL="PSV"}(r||(t.OrderType=r={})),t.isVolOrder=e=>e==r.VOL,t.isPegBenchOrder=e=>e==r.PEG_BENCH||"PEGBENCH"==e,t.isPegBestOrder=e=>e==r.PEG_BEST||"PEGBEST"==e,t.isPegMidOrder=e=>e==r.PEG_MID||"PEGMID"==e,t.default=r},56032:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.identity=void 0,t.identity=function(e){return e}},56256:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.repeat=void 0;var n=r(25042),i=r(96829),o=r(28719),s=r(81773),a=r(21574);t.repeat=function(e){var t,r,u=1/0;return null!=e&&("object"==typeof e?(u=void 0===(t=e.count)?1/0:t,r=e.delay):u=e),u<=0?function(){return n.EMPTY}:i.operate(function(e,t){var n,i=0,c=function(){if(null==n||n.unsubscribe(),n=null,null!=r){var e="number"==typeof r?a.timer(r):s.innerFrom(r(i)),u=o.createOperatorSubscriber(t,function(){u.unsubscribe(),l()});e.subscribe(u)}else l()},l=function(){var r=!1;n=e.subscribe(o.createOperatorSubscriber(t,void 0,function(){++i<u?n?c():r=!0:t.complete()})),r&&c()};l()})}},56457:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MutableMarketDataUpdate=t.MutableMarketData=void 0;let n=r(66811),i=r(65043);class o extends i.IBApiNextMap{}t.MutableMarketData=o;class s extends n.IBApiNextItemListUpdate{}t.MutableMarketDataUpdate=s},56627:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncSubject=void 0,t.AsyncSubject=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._value=null,t._hasValue=!1,t._isComplete=!1,t}return n(t,e),t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this._hasValue,n=this._value,i=this.thrownError,o=this.isStopped,s=this._isComplete;t?e.error(i):(o||s)&&(r&&e.next(n),e.complete())},t.prototype.next=function(e){this.isStopped||(this._value=e,this._hasValue=!0)},t.prototype.complete=function(){var t=this._hasValue,r=this._value;this._isComplete||(this._isComplete=!0,t&&e.prototype.next.call(this,r),e.prototype.complete.call(this))},t}(r(1937).Subject)},56814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.switchAll=void 0;var n=r(33361),i=r(56032);t.switchAll=function(){return n.switchMap(i.identity)}},57248:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Observable=void 0;var n=r(3349),i=r(46124),o=r(63701),s=r(58538),a=r(87273),u=r(17886),c=r(95369);function l(e){var t;return null!=(t=null!=e?e:a.config.Promise)?t:Promise}t.Observable=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var o=this,s=!function(e){return e&&e instanceof n.Subscriber||e&&u.isFunction(e.next)&&u.isFunction(e.error)&&u.isFunction(e.complete)&&i.isSubscription(e)}(e)?new n.SafeSubscriber(e,t,r):e;return c.errorContext(function(){var e=o.operator,t=o.source;s.add(e?e.call(s,t):t?o._subscribe(s):o._trySubscribe(s))}),s},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=l(t))(function(t,i){var o=new n.SafeSubscriber({next:function(t){try{e(t)}catch(e){i(e),o.unsubscribe()}},error:i,complete:t});r.subscribe(o)})},e.prototype._subscribe=function(e){var t;return null==(t=this.source)?void 0:t.subscribe(e)},e.prototype[o.observable]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return s.pipeFromArray(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=l(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}()},57395:(e,t,r)=>{var n={};e.exports=n,n.themes={};var i=r(28354),o=n.styles=r(5181),s=Object.defineProperties,a=new RegExp(/[\r\n]+/g);n.supportsColor=r(66707).supportsColor,void 0===n.enabled&&(n.enabled=!1!==n.supportsColor()),n.enable=function(){n.enabled=!0},n.disable=function(){n.enabled=!1},n.stripColors=n.strip=function(e){return(""+e).replace(/\x1B\[\d+m/g,"")},n.stylize=function(e,t){if(!n.enabled)return e+"";var r=o[t];return!r&&t in n?n[t](e):r.open+e+r.close};var u=/[|\\{}()[\]^$+*?.]/g,c=function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(u,"\\$&")};function l(e){var t=function e(){return _.apply(e,arguments)};return t._styles=e,t.__proto__=h,t}var d=function(){var e={};return o.grey=o.gray,Object.keys(o).forEach(function(t){o[t].closeRe=RegExp(c(o[t].close),"g"),e[t]={get:function(){return l(this._styles.concat(t))}}}),e}(),h=s(function(){},d);function _(){var e=Array.prototype.slice.call(arguments),t=e.map(function(e){return null!=e&&e.constructor===String?e:i.inspect(e)}).join(" ");if(!n.enabled||!t)return t;for(var r=-1!=t.indexOf("\n"),s=this._styles,u=s.length;u--;){var c=o[s[u]];t=c.open+t.replace(c.closeRe,c.open)+c.close,r&&(t=t.replace(a,function(e){return c.close+e+c.open}))}return t}n.setTheme=function(e){if("string"==typeof e)return void console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");for(var t in e)!function(t){n[t]=function(r){if("object"==typeof e[t]){var i=r;for(var o in e[t])i=n[e[t][o]](i);return i}return n[e[t]](r)}}(t)};var f=function(e,t){var r=t.split("");return(r=r.map(e)).join("")};for(var E in n.trap=r(5096),n.zalgo=r(93440),n.maps={},n.maps.america=r(21743)(n),n.maps.zebra=r(74711)(n),n.maps.rainbow=r(16717)(n),n.maps.random=r(67152)(n),n.maps)!function(e){n[e]=function(t){return f(n.maps[e],t)}}(E);s(n,function(){var e={};return Object.keys(d).forEach(function(t){e[t]={get:function(){return l([t])}}}),e}())},57685:(e,t,r)=>{let n=r(29021),i=r(33873),o=r(21820),s=r(55511),a=r(81054).version,u=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function c(e){console.log(`[dotenv@${a}][DEBUG] ${e}`)}function l(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function d(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)n.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=i.resolve(process.cwd(),".env.vault");return n.existsSync(t)?t:null}function h(e){return"~"===e[0]?i.join(o.homedir(),e.slice(1)):e}let _={configDotenv:function(e){let t,r=i.resolve(process.cwd(),".env"),o="utf8",s=!!(e&&e.debug);e&&e.encoding?o=e.encoding:s&&c("No encoding is specified. UTF-8 is used by default");let a=[r];if(e&&e.path)if(Array.isArray(e.path))for(let t of(a=[],e.path))a.push(h(t));else a=[h(e.path)];let u={};for(let r of a)try{let t=_.parse(n.readFileSync(r,{encoding:o}));_.populate(u,t,e)}catch(e){s&&c(`Failed to load ${r} ${e.message}`),t=e}let l=process.env;return(e&&null!=e.processEnv&&(l=e.processEnv),_.populate(l,u,e),t)?{parsed:u,error:t}:{parsed:u}},_configVault:function(e){e&&e.debug&&c("Loading env from encrypted .env.vault");let t=_._parseVault(e),r=process.env;return e&&null!=e.processEnv&&(r=e.processEnv),_.populate(r,t,e),{parsed:t}},_parseVault:function(e){let t,r=d(e),n=_.configDotenv({path:r});if(!n.parsed){let e=Error(`MISSING_DATA: Cannot parse ${r} for an unknown reason`);throw e.code="MISSING_DATA",e}let i=l(e).split(","),o=i.length;for(let e=0;e<o;e++)try{let r=i[e].trim(),o=function(e,t){let r;try{r=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let n=r.password;if(!n){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let i=r.searchParams.get("environment");if(!i){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:s,key:n}}(n,r);t=_.decrypt(o.ciphertext,o.key);break}catch(t){if(e+1>=o)throw t}return _.parse(t)},config:function(e){if(0===l(e).length)return _.configDotenv(e);let t=d(e);if(!t){var r;return r=`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`,console.log(`[dotenv@${a}][WARN] ${r}`),_.configDotenv(e)}return _._configVault(e)},decrypt:function(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let e=s.createDecipheriv("aes-256-gcm",r,i);return e.setAuthTag(o),`${e.update(n)}${e.final()}`}catch(n){let e=n instanceof RangeError,t="Invalid key length"===n.message,r="Unsupported state or unable to authenticate data"===n.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(r){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw n}},parse:function(e){let t,r={},n=e.toString();for(n=n.replace(/\r\n?/mg,"\n");null!=(t=u.exec(n));){let e=t[1],n=t[2]||"",i=(n=n.trim())[0];n=n.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===i&&(n=(n=n.replace(/\\n/g,"\n")).replace(/\\r/g,"\r")),r[e]=n}return r},populate:function(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let r of Object.keys(t))Object.prototype.hasOwnProperty.call(e,r)?(!0===i&&(e[r]=t[r]),n&&(!0===i?c(`"${r}" is already defined and WAS overwritten`):c(`"${r}" is already defined and was NOT overwritten`))):e[r]=t[r]}};e.exports.configDotenv=_.configDotenv,e.exports._configVault=_._configVault,e.exports._parseVault=_._parseVault,e.exports.config=_.config,e.exports.decrypt=_.decrypt,e.exports.parse=_.parse,e.exports.populate=_.populate,e.exports=_},57896:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNextSubscriptionRegistry=void 0;let n=r(65043),i=r(32984),o="IBApiNextSubscriptionRegistry";class s{constructor(e,t){this.eventName=e,this.callback=t,this.subscriptions=new Map,this.listener=(...e)=>{this.callback(this.subscriptions,...e)}}}class a{constructor(e,t){this.api=e,this.apiNext=t,this.entries=new n.IBApiNextMap}register(e,t,r,n){let a,u=[];return r.forEach(e=>{let t=e[0],r=e[1],n=this.entries.getOrAdd(t,()=>{let e=new s(t,r);return this.apiNext.logger.debug(o,`Add RegistryEntry for EventName.${t}`),this.api.addListener(t,e.listener),e});u.push(n)}),n&&u.forEach(e=>{let t=e.subscriptions.values();for(;!a;){let e=t.next();if(e.done)break;e.value.instanceId===n&&(a=e.value)}}),a||(a=new i.IBApiNextSubscription(this.apiNext,()=>{e(a.reqId)},()=>{t&&t(a.reqId)},()=>{u.forEach(e=>{e.subscriptions.delete(a.reqId),e.subscriptions.size||(this.api.removeListener(e.eventName,e.listener),this.apiNext.logger.debug(o,`Remove RegistryEntry for EventName.${e.eventName}.`),this.entries.delete(e.eventName))}),this.apiNext.logger.debug(o,`Deleted IBApiNextSubscription for ${a.reqId}.`)},n),u.forEach(e=>{this.apiNext.logger.debug(o,`Add IBApiNextSubscription on EventName.${e.eventName} for ${a.reqId}.`),e.subscriptions.set(a.reqId,a)})),a.createObservable()}dispatchError(e){this.entries.forEach(t=>{t.subscriptions.get(e.reqId)?.error(e)})}}t.IBApiNextSubscriptionRegistry=a},58538:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pipeFromArray=t.pipe=void 0;var n=r(56032);function i(e){return 0===e.length?n.identity:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)}}t.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i(e)},t.pipeFromArray=i},59452:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.zip=void 0;var o=r(57248),s=r(81773),a=r(19029),u=r(25042),c=r(28719),l=r(66389);t.zip=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l.popResultSelector(e),d=a.argsOrArgArray(e);return d.length?new o.Observable(function(e){var t=d.map(function(){return[]}),o=d.map(function(){return!1});e.add(function(){t=o=null});for(var a=function(a){s.innerFrom(d[a]).subscribe(c.createOperatorSubscriber(e,function(s){if(t[a].push(s),t.every(function(e){return e.length})){var u=t.map(function(e){return e.shift()});e.next(r?r.apply(void 0,i([],n(u))):u),t.some(function(e,t){return!e.length&&o[t]})&&e.complete()}},function(){o[a]=!0,t[a].length||e.complete()}))},u=0;!e.closed&&u<d.length;u++)a(u);return function(){t=o=null}}):u.EMPTY}},59643:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.IBApi=t.MIN_SERVER_VER_SUPPORTED=t.MAX_SUPPORTED_SERVER_VERSION=void 0;let i=r(42306),o=r(52605),s=n(r(14028));t.MAX_SUPPORTED_SERVER_VERSION=n(r(48369)).default.CME_TAGGING_FIELDS_IN_OPEN_ORDER,t.MIN_SERVER_VER_SUPPORTED=38;class a extends i.EventEmitter{constructor(e){super(),this.controller=new o.Controller(this,e)}get serverVersion(){return this.controller.serverVersion}get isConnected(){return this.controller.connected}disableUseV100Plus(){return this.controller.disableUseV100Plus()}connect(e){return this.controller.connect(e),this}disconnect(){return this.controller.disconnect(),this}calculateImpliedVolatility(e,t,r,n){return this.controller.schedule(()=>this.controller.encoder.calculateImpliedVolatility(e,t,r,n)),this}calculateOptionPrice(e,t,r,n){return this.controller.schedule(()=>this.controller.encoder.calculateOptionPrice(e,t,r,n)),this}cancelAccountSummary(e){return this.controller.schedule(()=>this.controller.encoder.cancelAccountSummary(e)),this}cancelAccountUpdatesMulti(e){return this.controller.schedule(()=>this.controller.encoder.cancelAccountUpdatesMulti(e)),this}cancelCalculateImpliedVolatility(e){return this.controller.schedule(()=>this.controller.encoder.cancelCalculateImpliedVolatility(e)),this}cancelCalculateOptionPrice(e){return this.controller.schedule(()=>this.controller.encoder.cancelCalculateOptionPrice(e)),this}cancelFundamentalData(e){return this.controller.schedule(()=>this.controller.encoder.cancelFundamentalData(e)),this}cancelHeadTimestamp(e){return this.controller.schedule(()=>this.controller.encoder.cancelHeadTimestamp(e)),this}cancelHistogramData(e){return this.controller.schedule(()=>this.controller.encoder.cancelHistogramData(e)),this}cancelHistoricalData(e){return this.controller.schedule(()=>this.controller.encoder.cancelHistoricalData(e)),this}cancelMktData(e){return this.controller.schedule(()=>this.controller.encoder.cancelMktData(e)),this}cancelMktDepth(e,t){return this.controller.schedule(()=>this.controller.encoder.cancelMktDepth(e,t)),this}cancelNewsBulletins(){return this.controller.schedule(()=>this.controller.encoder.cancelNewsBulletins()),this}cancelOrder(e,t){let r;return r=void 0==t?{manualOrderCancelTime:void 0,extOperator:"",manualOrderIndicator:void 0}:"string"==typeof t?{manualOrderCancelTime:t,extOperator:"",manualOrderIndicator:void 0}:t,this.controller.schedule(()=>this.controller.encoder.cancelOrder(e,r)),this}cancelPnL(e){return this.controller.schedule(()=>this.controller.encoder.cancelPnL(e)),this}cancelPnLSingle(e){return this.controller.schedule(()=>this.controller.encoder.cancelPnLSingle(e)),this}cancelPositions(){return this.controller.schedule(()=>this.controller.encoder.cancelPositions()),this}cancelPositionsMulti(e){return this.controller.schedule(()=>this.controller.encoder.cancelPositionsMulti(e)),this}cancelRealTimeBars(e){return this.controller.schedule(()=>this.controller.encoder.cancelRealTimeBars(e)),this}cancelScannerSubscription(e){return this.controller.schedule(()=>this.controller.encoder.cancelScannerSubscription(e)),this}cancelTickByTickData(e){return this.controller.schedule(()=>this.controller.encoder.cancelTickByTickData(e)),this}exerciseOptions(e,t,r,n,i,o){return this.controller.schedule(()=>this.controller.encoder.exerciseOptions(e,t,r,n,i,o)),this}placeOrder(e,t,r){return this.controller.schedule(()=>this.controller.encoder.placeOrder(e,t,r)),this}queryDisplayGroups(e){return this.controller.schedule(()=>this.controller.encoder.queryDisplayGroups(e)),this}replaceFA(e,t,r){return this.controller.schedule(()=>this.controller.encoder.replaceFA(e,t,r)),this}reqAccountSummary(e,t,r){return this.controller.schedule(()=>this.controller.encoder.reqAccountSummary(e,t,r)),this}reqAccountUpdates(e,t){return this.controller.schedule(()=>this.controller.encoder.reqAccountUpdates(e,t)),this}reqAccountUpdatesMulti(e,t,r,n){return this.controller.schedule(()=>this.controller.encoder.reqAccountUpdatesMulti(e,t,r,n)),this}reqAllOpenOrders(){return this.controller.schedule(()=>this.controller.encoder.reqAllOpenOrders()),this}reqAutoOpenOrders(e){return this.controller.schedule(()=>this.controller.encoder.reqAutoOpenOrders(e)),this}reqCompletedOrders(e){return this.controller.schedule(()=>this.controller.encoder.reqCompletedOrders(e)),this}reqWshMetaData(e){return this.controller.schedule(()=>this.controller.encoder.reqWshMetaData(e)),this}reqCancelWshMetaData(e){return this.controller.schedule(()=>this.controller.encoder.reqCancelWshMetaData(e)),this}reqWshEventData(e,t){let r;return r="number"==typeof t?new s.default(t):t,this.controller.schedule(()=>this.controller.encoder.reqWshEventData(e,r)),this}reqCancelWshEventData(e){return this.controller.schedule(()=>this.controller.encoder.reqCancelWshEventData(e)),this}reqContractDetails(e,t){return this.controller.schedule(()=>this.controller.encoder.reqContractDetails(e,t)),this}reqCurrentTime(){return this.controller.schedule(()=>this.controller.encoder.reqCurrentTime()),this}reqExecutions(e,t){return this.controller.schedule(()=>this.controller.encoder.reqExecutions(e,t)),this}reqFamilyCodes(){return this.controller.schedule(()=>this.controller.encoder.reqFamilyCodes()),this}reqFundamentalData(e,t,r,n=[]){return this.controller.schedule(()=>this.controller.encoder.reqFundamentalData(e,t,r,n)),this}reqGlobalCancel(e){return this.controller.schedule(()=>this.controller.encoder.reqGlobalCancel(e||{manualOrderCancelTime:void 0,extOperator:"",manualOrderIndicator:void 0})),this}reqHeadTimestamp(e,t,r,n,i){return this.controller.schedule(()=>this.controller.encoder.reqHeadTimestamp(e,t,r,n,i)),this}reqHistogramData(e,t,r,n,i){let o=n+" "+i.toString().toLowerCase()+"s";return this.controller.schedule(()=>this.controller.encoder.reqHistogramData(e,t,r,o)),this}reqHistoricalData(e,t,r,n,i,o,s,a,u){return this.controller.schedule(()=>this.controller.encoder.reqHistoricalData(e,t,r,n,i,o,s,a,u)),this}reqHistoricalNews(e,t,r,n,i,o,s=[]){return this.controller.schedule(()=>this.controller.encoder.reqHistoricalNews(e,t,r,n,i,o,s)),this}reqHistoricalTicks(e,t,r,n,i,o,s,a){return this.controller.schedule(()=>this.controller.encoder.reqHistoricalTicks(e,t,r,n,i,o,s,a)),this}reqIds(e=0){return this.controller.schedule(()=>this.controller.encoder.reqIds(e)),this}reqManagedAccts(){return this.controller.schedule(()=>this.controller.encoder.reqManagedAccts()),this}reqMarketDataType(e){return this.controller.schedule(()=>this.controller.encoder.reqMarketDataType(e)),this}reqMktDepth(e,t,r,n,i){return this.controller.schedule(()=>this.controller.encoder.reqMktDepth(e,t,r,n,i)),this}reqMarketRule(e){return this.controller.schedule(()=>this.controller.encoder.reqMarketRule(e)),this}reqMatchingSymbols(e,t){return this.controller.schedule(()=>this.controller.encoder.reqMatchingSymbols(e,t)),this}reqMktData(e,t,r,n,i){return this.controller.schedule(()=>this.controller.encoder.reqMktData(e,t,r,n,i)),this}reqMktDepthExchanges(){return this.controller.schedule(()=>this.controller.encoder.reqMktDepthExchanges()),this}reqNewsArticle(e,t,r,n=[]){return this.controller.schedule(()=>this.controller.encoder.reqNewsArticle(e,t,r,n)),this}reqNewsBulletins(e){return this.controller.schedule(()=>this.controller.encoder.reqNewsBulletins(e)),this}reqNewsProviders(){return this.controller.schedule(()=>this.controller.encoder.reqNewsProviders()),this}reqOpenOrders(){return this.controller.schedule(()=>this.controller.encoder.reqOpenOrders()),this}reqPnL(e,t,r){return this.controller.schedule(()=>this.controller.encoder.reqPnL(e,t,r??null)),this}reqPnLSingle(e,t,r,n){return this.controller.schedule(()=>this.controller.encoder.reqPnLSingle(e,t,r,n)),this}reqPositions(){return this.controller.schedule(()=>this.controller.encoder.reqPositions()),this}reqPositionsMulti(e,t,r){return this.controller.schedule(()=>this.controller.encoder.reqPositionsMulti(e,t,r)),this}reqRealTimeBars(e,t,r,n,i,o=[]){return this.controller.schedule(()=>this.controller.encoder.reqRealTimeBars(e,t,r,n,i,o)),this}reqScannerParameters(){return this.controller.schedule(()=>this.controller.encoder.reqScannerParameters()),this}reqScannerSubscription(e,t,r=[],n=[]){return this.controller.schedule(()=>this.controller.encoder.reqScannerSubscription(e,t,r,n)),this}reqSecDefOptParams(e,t,r,n,i){return this.controller.schedule(()=>this.controller.encoder.reqSecDefOptParams(e,t,r,n,i)),this}reqSmartComponents(e,t){return this.controller.schedule(()=>this.controller.encoder.reqSmartComponents(e,t)),this}reqSoftDollarTiers(e){return this.controller.schedule(()=>this.controller.encoder.reqSoftDollarTiers(e)),this}reqTickByTickData(e,t,r,n,i){return this.controller.schedule(()=>this.controller.encoder.reqTickByTickData(e,t,r,n,i)),this}requestFA(e){return this.controller.schedule(()=>this.controller.encoder.requestFA(e)),this}subscribeToGroupEvents(e,t){return this.controller.schedule(()=>this.controller.encoder.subscribeToGroupEvents(e,t)),this}updateDisplayGroup(e,t){return this.controller.schedule(()=>this.controller.encoder.updateDisplayGroup(e,t)),this}unsubscribeFromGroupEvents(e){return this.controller.schedule(()=>this.controller.encoder.unsubscribeToGroupEvents(e)),this}setServerLogLevel(e){return this.controller.schedule(()=>this.controller.encoder.setServerLogLevel(e)),this}reqUserInfo(e){return this.controller.schedule(()=>this.controller.encoder.reqUserInfo(e)),this}}t.IBApi=a},59740:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.bufferTime=void 0;var i=r(46124),o=r(96829),s=r(28719),a=r(1071),u=r(97847),c=r(66389),l=r(88840);t.bufferTime=function(e){for(var t,r,d=[],h=1;h<arguments.length;h++)d[h-1]=arguments[h];var _=null!=(t=c.popScheduler(d))?t:u.asyncScheduler,f=null!=(r=d[0])?r:null,E=d[1]||1/0;return o.operate(function(t,r){var o=[],u=!1,c=function(e){var t=e.buffer;e.subs.unsubscribe(),a.arrRemove(o,e),r.next(t),u&&d()},d=function(){if(o){var t=new i.Subscription;r.add(t);var n={buffer:[],subs:t};o.push(n),l.executeSchedule(t,_,function(){return c(n)},e)}};null!==f&&f>=0?l.executeSchedule(r,_,d,f,!0):u=!0,d();var h=s.createOperatorSubscriber(r,function(e){var t,r,i=o.slice();try{for(var s=n(i),a=s.next();!a.done;a=s.next()){var u=a.value,l=u.buffer;l.push(e),E<=l.length&&c(u)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=s.return)&&r.call(s)}finally{if(t)throw t.error}}},function(){for(;null==o?void 0:o.length;)r.next(o.shift().buffer);null==h||h.unsubscribe(),r.complete(),r.unsubscribe()},void 0,function(){return o=null});t.subscribe(h)})}},59797:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.windowWhen=void 0;var n=r(1937),i=r(96829),o=r(28719),s=r(81773);t.windowWhen=function(e){return i.operate(function(t,r){var i,a,u=function(e){i.error(e),r.error(e)},c=function(){var t;null==a||a.unsubscribe(),null==i||i.complete(),i=new n.Subject,r.next(i.asObservable());try{t=s.innerFrom(e())}catch(e){u(e);return}t.subscribe(a=o.createOperatorSubscriber(r,c,c,u))};c(),t.subscribe(o.createOperatorSubscriber(r,function(e){return i.next(e)},function(){i.complete(),r.complete()},u,function(){null==a||a.unsubscribe(),i=null}))})}},59798:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.timeout=t.TimeoutError=void 0;var n=r(97847),i=r(8742),o=r(96829),s=r(81773),a=r(23008),u=r(28719),c=r(88840);function l(e){throw new t.TimeoutError(e)}t.TimeoutError=a.createErrorClass(function(e){return function(t){void 0===t&&(t=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t}}),t.timeout=function(e,t){var r=i.isValidDate(e)?{first:e}:"number"==typeof e?{each:e}:e,a=r.first,d=r.each,h=r.with,_=void 0===h?l:h,f=r.scheduler,E=void 0===f?null!=t?t:n.asyncScheduler:f,p=r.meta,T=void 0===p?null:p;if(null==a&&null==d)throw TypeError("No timeout provided.");return o.operate(function(e,t){var r,n,i=null,o=0,l=function(e){n=c.executeSchedule(t,E,function(){try{r.unsubscribe(),s.innerFrom(_({meta:T,lastValue:i,seen:o})).subscribe(t)}catch(e){t.error(e)}},e)};r=e.subscribe(u.createOperatorSubscriber(t,function(e){null==n||n.unsubscribe(),o++,t.next(i=e),d>0&&l(d)},void 0,void 0,function(){(null==n?void 0:n.closed)||null==n||n.unsubscribe(),i=null})),o||l(null!=a?"number"==typeof a?a:a-E.now():d)})}},59920:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeScan=void 0;var n=r(96829),i=r(37279);t.mergeScan=function(e,t,r){return void 0===r&&(r=1/0),n.operate(function(n,o){var s=t;return i.mergeInternals(n,o,function(t,r){return e(s,t,r)},r,function(e){s=e},!1,void 0,function(){return s=null})})}},60925:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TimeInterval=t.timeInterval=void 0;var n=r(97847),i=r(96829),o=r(28719);t.timeInterval=function(e){return void 0===e&&(e=n.asyncScheduler),i.operate(function(t,r){var n=e.now();t.subscribe(o.createOperatorSubscriber(r,function(t){var i=e.now(),o=i-n;n=i,r.next(new s(t,o))}))})};var s=function(e,t){this.value=e,this.interval=t};t.TimeInterval=s},61472:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scheduled=void 0;var n=r(64491),i=r(52443),o=r(78231),s=r(45090),a=r(93638),u=r(91602),c=r(66921),l=r(44868),d=r(82952),h=r(14092),_=r(61854),f=r(70791),E=r(69325);t.scheduled=function(e,t){if(null!=e){if(u.isInteropObservable(e))return n.scheduleObservable(e,t);if(l.isArrayLike(e))return o.scheduleArray(e,t);if(c.isPromise(e))return i.schedulePromise(e,t);if(h.isAsyncIterable(e))return a.scheduleAsyncIterable(e,t);if(d.isIterable(e))return s.scheduleIterable(e,t);if(f.isReadableStreamLike(e))return E.scheduleReadableStreamLike(e,t)}throw _.createInvalidObservableTypeError(e)}},61512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isEmpty=void 0;var n=r(96829),i=r(28719);t.isEmpty=function(){return n.operate(function(e,t){e.subscribe(i.createOperatorSubscriber(t,function(){t.next(!1),t.complete()},function(){t.next(!0),t.complete()}))})}},61854:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInvalidObservableTypeError=void 0,t.createInvalidObservableTypeError=function(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}},62524:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Stock=void 0;let i=n(r(15850));class o{constructor(e,t,r){this.symbol=e,this.exchange=t,this.currency=r,this.secType=i.default.STK,this.currency=this.currency??"USD",this.exchange=this.exchange??"SMART"}}t.Stock=o,t.default=o},62633:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.single=void 0;var n=r(46889),i=r(20341),o=r(15931),s=r(96829),a=r(28719);t.single=function(e){return s.operate(function(t,r){var s,u=!1,c=!1,l=0;t.subscribe(a.createOperatorSubscriber(r,function(n){c=!0,(!e||e(n,l++,t))&&(u&&r.error(new i.SequenceError("Too many matching values")),u=!0,s=n)},function(){u?(r.next(s),r.complete()):r.error(c?new o.NotFoundError("No matching values"):new n.EmptyError)}))})}},62773:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNext=void 0;let n=r(79249),i=r(54499),o=r(90742),s=r(7133),a=r(54431),u=r(56457),c=r(54819),l=r(86767),d=r(51551),h=r(80127),_=r(57896),f=r(71485),E="IBApiNext";function p(e,t){let r=new Map;for(let[n,i]of e)t(n,i)&&r.set(n,i);return r}class T{constructor(e){this._nextReqId=1,this.errorSubject=new n.Subject,this.onCurrentTime=(e,t)=>{e.forEach(e=>{e.next({all:t}),e.complete()})},this.onManagedAccts=(e,t)=>{let r=t.split(",");e.forEach(e=>{e.next({all:r}),e.complete()})},this.onAccountSummary=(e,t,r,n,i,o)=>{let s=e.get(t);if(!s)return;let u=s.lastAllValue??new a.MutableAccountSummaries,c=u.getOrAdd(r,()=>new a.MutableAccountSummaryTagValues).getOrAdd(n,()=>new a.MutableAccountSummaryValues),l=c.has(o),d={value:i,ingressTm:Date.now()};c.set(o,d);let h=new a.MutableAccountSummaries([[r,new a.MutableAccountSummaryTagValues([[n,new a.MutableAccountSummaryValues([[o,d]])]])]]);s.endEventReceived?l?s.next({all:u,changed:h}):s.next({all:u,added:h}):s.lastAllValue=u},this.onAccountSummaryEnd=(e,t)=>{let r=e.get(t);if(!r)return;let n=r.lastAllValue??new a.MutableAccountSummaries;r.endEventReceived=!0,r.next({all:n})},this.onUpdateAccountValue=(e,t,r,n,i)=>{p(e,(e,t)=>"getAccountUpdates"===t.instanceId||t.instanceId===`getAccountUpdates+${i}`).forEach(e=>{let o=e.lastAllValue??{},s=o?.value??new a.MutableAccountSummaries,u=s.getOrAdd(i,()=>new a.MutableAccountSummaryTagValues).getOrAdd(t,()=>new a.MutableAccountSummaryValues),c=(u.has(n),{value:r,ingressTm:Date.now()});u.set(n,c);let l=new a.MutableAccountSummaries([[i,new a.MutableAccountSummaryTagValues([[t,new a.MutableAccountSummaryValues([[n,c]])]])]]);o.value=s,e.next({all:o,changed:{value:l}})})},this.onUpdatePortfolio=(e,t,r,n,i,o,s,a,u)=>{let l={account:u,contract:t,pos:r,avgCost:o,marketPrice:n,marketValue:i,unrealizedPNL:s,realizedPNL:a};p(e,(e,t)=>"getAccountUpdates"===t.instanceId||t.instanceId===`getAccountUpdates+${u}`).forEach(e=>{let n=!1,i=!1,o=e.lastAllValue??{},s=o?.portfolio??new c.MutableAccountPositions,a=s.getOrAdd(u,()=>[]),d=a.findIndex(e=>e.contract.conId==t.conId);-1===d?(a.push(l),n=!0):r?a[d]=l:(a.splice(d),i=!0),o.portfolio=s,n?e.next({all:o,added:{portfolio:new c.MutableAccountPositions([[u,[l]]])}}):i?e.next({all:o,removed:{portfolio:new c.MutableAccountPositions([[u,[l]]])}}):e.next({all:o,changed:{portfolio:new c.MutableAccountPositions([[u,[l]]])}})})},this.onUpdateAccountTime=(e,t)=>{e.forEach(e=>{let r={timestamp:t},n=e.lastAllValue??{};n.timestamp=r.timestamp,e.next({all:n,changed:r})})},this.onAccountDownloadEnd=(e,t)=>{p(e,(e,r)=>"getAccountUpdates"===r.instanceId||r.instanceId===`getAccountUpdates+${t}`).forEach(e=>{let t=e.lastAllValue??{};e.endEventReceived=!0,e.next({all:t})})},this.onPosition=(e,t,r,n,i)=>{let o={account:t,contract:r,pos:n,avgCost:i};e.forEach(e=>{let i=!1,s=!1,a=e.lastAllValue??new c.MutableAccountPositions,u=a.getOrAdd(t,()=>[]),l=u.findIndex(e=>e.contract.conId==r.conId);-1===l?(u.push(o),i=!0):n?u[l]=o:(u.splice(l),s=!0),e.endEventReceived?i?e.next({all:a,added:new c.MutableAccountPositions([[t,[o]]])}):s?e.next({all:a,removed:new c.MutableAccountPositions([[t,[o]]])}):e.next({all:a,changed:new c.MutableAccountPositions([[t,[o]]])}):e.lastAllValue=a})},this.onPositionEnd=e=>{e.forEach(e=>{let t=e.lastAllValue??new c.MutableAccountPositions;e.endEventReceived=!0,e.next({all:t})})},this.onContractDetails=(e,t,r)=>{let n=e.get(t);if(!n)return;let i=n.lastAllValue??[];i.push(r),n.next({all:i})},this.onContractDetailsEnd=(e,t)=>{e.get(t)?.complete()},this.onSecurityDefinitionOptionParameter=(e,t,r,n,i,o,s,a)=>{let u=e.get(t);if(!u)return;let c=u.lastAllValue??[];c.push({exchange:r,underlyingConId:n,tradingClass:i,multiplier:parseInt(o),expirations:s,strikes:a}),u.next({all:c})},this.onSecurityDefinitionOptionParameterEnd=(e,t)=>{e.get(t)?.complete()},this.onPnL=(e,t,r,n,i)=>{let o=e.get(t);o&&o.next({all:{dailyPnL:r,unrealizedPnL:n,realizedPnL:i}})},this.onPnLSingle=(e,t,r,n,i,o,s)=>{let a=e.get(t);a&&a.next({all:{position:r,dailyPnL:n,unrealizedPnL:i,realizedPnL:o,marketValue:s}})},this.onTick=(e,t,r,n)=>{-1===n&&(r===f.IBApiTickType.BID||r===f.IBApiTickType.DELAYED_BID||r===f.IBApiTickType.ASK||r===f.IBApiTickType.DELAYED_ASK)&&(n=void 0);let i=e.get(t);if(!i)return;let o=i.lastAllValue??new u.MutableMarketData,s=o.has(r),a={value:n,ingressTm:Date.now()};o.set(r,a),s?i.next({all:o,changed:new u.MutableMarketData([[r,a]])}):i.next({all:o,added:new u.MutableMarketData([[r,a]])})},this.onTickOptionComputation=(e,t,r,n,i,o,s,a,c,l,d)=>{let h=e.get(t);if(!h)return;let _=Date.now(),E=[[f.IBApiNextTickType.OPTION_UNDERLYING,{value:d,ingressTm:_}],[f.IBApiNextTickType.OPTION_PV_DIVIDEND,{value:s,ingressTm:_}]];switch(r){case f.IBApiTickType.BID_OPTION:E.push([f.IBApiNextTickType.BID_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.BID_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.BID_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.BID_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.BID_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.BID_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.DELAYED_BID_OPTION:E.push([f.IBApiNextTickType.DELAYED_BID_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.DELAYED_BID_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.DELAYED_BID_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.DELAYED_BID_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.DELAYED_BID_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.DELAYED_BID_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.ASK_OPTION:E.push([f.IBApiNextTickType.ASK_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.ASK_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.ASK_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.ASK_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.ASK_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.ASK_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.DELAYED_ASK_OPTION:E.push([f.IBApiNextTickType.DELAYED_ASK_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.DELAYED_ASK_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.DELAYED_ASK_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.DELAYED_ASK_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.DELAYED_ASK_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.DELAYED_ASK_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.LAST_OPTION:E.push([f.IBApiNextTickType.LAST_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.LAST_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.LAST_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.LAST_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.LAST_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.LAST_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.DELAYED_LAST_OPTION:E.push([f.IBApiNextTickType.DELAYED_LAST_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.DELAYED_LAST_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.DELAYED_LAST_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.DELAYED_LAST_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.DELAYED_LAST_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.DELAYED_LAST_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.MODEL_OPTION:E.push([f.IBApiNextTickType.MODEL_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.MODEL_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.MODEL_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.MODEL_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.MODEL_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.MODEL_OPTION_THETA,{value:l,ingressTm:_}]);break;case f.IBApiTickType.DELAYED_MODEL_OPTION:E.push([f.IBApiNextTickType.DELAYED_MODEL_OPTION_IV,{value:n,ingressTm:_}],[f.IBApiNextTickType.DELAYED_MODEL_OPTION_DELTA,{value:i,ingressTm:_}],[f.IBApiNextTickType.DELAYED_MODEL_OPTION_PRICE,{value:o,ingressTm:_}],[f.IBApiNextTickType.DELAYED_MODEL_OPTION_GAMMA,{value:a,ingressTm:_}],[f.IBApiNextTickType.DELAYED_MODEL_OPTION_VEGA,{value:c,ingressTm:_}],[f.IBApiNextTickType.DELAYED_MODEL_OPTION_THETA,{value:l,ingressTm:_}])}let p=h.lastAllValue??new u.MutableMarketData,T=new u.MutableMarketData,A=new u.MutableMarketData;E.forEach(e=>{p.has(e[0])?A.set(e[0],e[1]):T.set(e[0],e[1]),p.set(e[0],e[1])}),p.size&&h.next({all:p,added:T.size?T:void 0,changed:A.size?A:void 0})},this.onTickSnapshotEnd=(e,t)=>{e.get(t)?.complete()},this.getMarketDataSingle=this.getMarketDataSnapshot,this.onHeadTimestamp=(e,t,r)=>{let n=e.get(t);n&&(n.next({all:r}),n.complete())},this.onHistoricalData=(e,t,r,n,i,o,s,a,u,c)=>{let l=e.get(t);if(l)if(r.startsWith("finished"))l.complete();else{let e=l.lastAllValue??[],t={time:r};-1!==n&&(t.open=n),-1!==i&&(t.high=i),-1!==o&&(t.low=o),-1!==s&&(t.close=s),-1!==a&&(t.volume=a),-1!==u&&(t.count=u),-1!==c&&(t.WAP=c),e.push(t),l.next({all:e})}},this.onHistoricalDataUpdate=(e,t,r,n,i,o,s,a,u,c)=>{let l=e.get(t);if(!l)return;let d=l.lastAllValue??{};d.time=r,d.open=-1!==n?n:void 0,d.high=-1!==i?i:void 0,d.low=-1!==o?o:void 0,d.close=-1!==s?s:void 0,d.volume=-1!==a?a:void 0,d.count=-1!==u?u:void 0,d.WAP=-1!==c?c:void 0,l.next({all:d})},this.onHistoricalTicks=(e,t,r,n)=>{let i=e.get(t);if(!i)return;let o=i.lastAllValue;o=o?o.concat(r):r,i.next({all:o}),n&&i.complete()},this.onHistoricalTicksBidAsk=(e,t,r,n)=>{let i=e.get(t);if(!i)return;let o=i.lastAllValue;o=o?o.concat(r):r,i.next({all:o}),n&&i.complete()},this.onHistoricalTicksLast=(e,t,r,n)=>{let i=e.get(t);if(!i)return;let o=i.lastAllValue;o=o?o.concat(r):r,i.next({all:o}),n&&i.complete()},this.onMktDepthExchanges=(e,t)=>{e.forEach(e=>{e.next({all:t}),e.complete()})},this.onUpdateMktDepth=(e,t,r,n,i,o,s)=>{this.onUpdateMktDepthL2(e,t,r,void 0,n,i,o,s,!1)},this.onUpdateMktDepthL2=(e,t,r,n,i,o,s,a,u)=>{let c,l,d=e.get(t);if(!d)return;let h=d.lastAllValue??{bids:new Map,asks:new Map},_={bids:new Map,asks:new Map};if(0==o?(c=h.asks,l=_.asks):1==o&&(c=h.bids,l=_.bids),void 0===c||void 0===l)return void this.logger.error(E,`onUpdateMktDepthL2: unknown side value ${o} received from TWS`);switch(i){case 0:this.insertAtMapIndex(r,r,{marketMaker:n,price:s,size:a,isSmartDepth:u},c),this.insertAtMapIndex(r,r,{marketMaker:n,price:s,size:a,isSmartDepth:u},l),d.next({all:h,added:_});break;case 1:c.set(r,{marketMaker:n,price:s,size:a,isSmartDepth:u}),l.set(r,{marketMaker:n,price:s,size:a,isSmartDepth:u}),d.next({all:h,changed:_});break;case 2:{let e=c.get(r);c.delete(r),l.set(r,e),d.next({all:h,removed:_})}break;default:this.logger.error(E,`onUpdateMktDepthL2: unknown operation value ${i} received from TWS`)}},this.onScannerParameters=(e,t)=>{e.forEach(e=>{e.next({all:t}),e.complete()})},this.onScannerData=(e,t,r,n,i,o,s,a)=>{let u=e.get(t);if(!u)return;let c={rank:r,contract:n,distance:i,benchmark:o,projection:s,legStr:a},l=u.lastAllValue??new Map,d=void 0!=l.get(r);if(l.set(r,c),u.endEventReceived){let e=new Map;e.set(r,c),u.next({all:l,changed:d?e:void 0,added:d?void 0:e})}else u.lastAllValue=l},this.onScannerDataEnd=(e,t)=>{let r=e.get(t);if(!r)return;let n=r.lastAllValue??new Map;r.endEventReceived=!0,r.next({all:n})},this.onHistogramData=(e,t,r)=>{let n=e.get(t);n&&(n.next({all:r}),n.complete())},this.onOpenOrder=(e,t,r,n,i)=>{e.forEach(e=>{let o=e.lastAllValue??[],s=o.findIndex(e=>e.order.permId==n.permId);if(-1===s){let s={orderId:t,contract:r,order:n,orderState:i,orderStatus:void 0};o.push(s),e.endEventReceived?e.next({all:o,added:[s]}):e.lastAllValue=o}else{let t=o[s];t.order=n,t.orderState=i,void 0!==t.orderStatus&&(t.orderStatus.clientId=n.clientId,t.orderStatus.permId=n.permId,t.orderStatus.parentId=n.parentId,t.orderStatus.status=i.status),e.next({all:o,changed:[t]})}})},this.onOpenOrderComplete=e=>{e.forEach(e=>{let t=e.lastAllValue??[];e.endEventReceived=!0,e.next({all:t}),e.complete()})},this.onOrderBound=(e,t,r,n)=>{this.logger.warn(E,`Unexpected onOrderBound(${t}, ${r}, ${n}) called.`)},this.onOrderStatus=(e,t,r,n,i,o,s,a,u,c,l,d)=>{let h={status:r,filled:n,remaining:i,avgFillPrice:void 0,permId:s,parentId:a,lastFillPrice:void 0,clientId:c,whyHeld:l,mktCapPrice:d};n&&(h.avgFillPrice=o,h.lastFillPrice=u),e.forEach(e=>{let n=e.lastAllValue??[],i=n.findIndex(e=>e.order.permId==s);if(-1!==i){let t=n[i];t.orderStatus=h,t.orderState.status=r,void 0!==a&&(t.order.parentId=a),void 0!==s&&(t.order.permId=s),void 0!==c&&(t.order.clientId=c),e.next({all:n,changed:[t]})}else this.logger.warn(E,`onOrderStatus: non existent order ignored. orderId: ${t}, permId: ${s}.`)})},this.onOpenOrderEnd=e=>{e.forEach(e=>{let t=e.lastAllValue??[];e.endEventReceived=!0,e.next({all:t})})},this.onNextValidId=(e,t)=>{let r=e.entries().next();r&&!r.done&&r.value[1]&&(r.value[1].next({all:t}),r.value[1].complete())},this.onExecDetails=(e,t,r,n)=>{e.forEach(e=>{let i=e.lastAllValue??[];i.push({reqId:t,contract:r,execution:n}),e.next({all:i})})},this.onExecDetailsEnd=(e,t)=>{let r=e.get(t);r&&(r.lastAllValue||r.next({all:[]}),r.complete())},this.onComissionReport=(e,t)=>{e.forEach(e=>{let r=e.lastAllValue??[];r.push(t),e.next({all:r})})},this.onSymbolSamples=(e,t,r)=>{let n=e.get(t);e.delete(t),n?.next({all:r}),n?.complete()},this.searchContracts=this.getMatchingSymbols,this.onUserInfo=(e,t,r)=>{let n=e.get(t);e.delete(t),n?.next({all:r}),n?.complete()},this.onMarketRule=(e,t,r)=>{p(e,(e,r)=>r.instanceId===`getMarketRule+${t}`).forEach(e=>{e.next({all:r}),e.complete()})},this.onTickByTickAllLastDataUpdates=e=>(t,r,n,i,o,s,a,u,c)=>{let l=t.get(r);if(!l)return;let d=l.lastAllValue??{};d.tickType=n,d.time=i?+i:void 0,d.price=-1!==o?o:void 0,d.size=-1!==s?s:void 0,d.tickAttribLast=a,d.exchange=u,d.specialConditions=c,d.contract=e,l.next({all:d})},this.logger=new h.IBApiNextLogger(e?.logger??new d.ConsoleLogger),this.api=new l.IBApiAutoConnection(e?.reconnectInterval??0,(e?.connectionWatchdogInterval??0)*1e3,this.logger,e),this.subscriptions=new _.IBApiNextSubscriptionRegistry(this.api,this),this.api.on(o.EventName.error,(e,t,r,n)=>{let i=new f.IBApiNextError(e,t,r,n);r===o.ErrorCode.NO_VALID_ID||(0,s.isNonFatalError)(t,e)||this.subscriptions.dispatchError(i),this.errorSubject.next(i)}),this.api.on(o.EventName.server,(e,t)=>{this.logger.info("TWS",`Server Version: ${e}. Connection time ${t}`)}),this.api.on(o.EventName.info,(e,t)=>{(t===o.ErrorCode.FAIL_CONNECTION_LOST_BETWEEN_SERVER_AND_TWS||t===o.ErrorCode.FAIL_CONNECTION_LOST_BETWEEN_TWS_AND_SERVER)&&this.api.onDisconnected(),this.logger.info("TWS",`${e} - Code: ${t}`)})}get nextReqId(){return this._nextReqId++}get logLevel(){return this.logger.logLevel}set logLevel(e){this.logger.logLevel=e,this.api.setServerLogLevel(e)}get error(){return this.errorSubject}get connectionState(){return this.api.connectionState}get isConnected(){return this.api.isConnected}connect(e){return this.logger.debug(E,`connect(${e})`),this.api.connect(e),this}disconnect(){return this.logger.debug(E,"disconnect()"),this.api.disconnect(),this}getCurrentTime(){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqCurrentTime()},void 0,[[o.EventName.currentTime,this.onCurrentTime]],"getCurrentTime").pipe((0,i.map)(e=>e.all)),{defaultValue:0})}getManagedAccounts(){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqManagedAccts()},void 0,[[o.EventName.managedAccounts,this.onManagedAccts]],"getManagedAccounts").pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getAccountSummary(e,t){return this.subscriptions.register(r=>{this.api.reqAccountSummary(r,e,t)},e=>{this.api.cancelAccountSummary(e)},[[o.EventName.accountSummary,this.onAccountSummary],[o.EventName.accountSummaryEnd,this.onAccountSummaryEnd]],`getAccountSummary+${e}:${t}`)}getAccountUpdates(e){return this.subscriptions.register(()=>{this.api.reqAccountUpdates(!0,e)},()=>{this.api.reqAccountUpdates(!1,e)},[[o.EventName.updateAccountValue,this.onUpdateAccountValue],[o.EventName.updatePortfolio,this.onUpdatePortfolio],[o.EventName.accountDownloadEnd,this.onAccountDownloadEnd],[o.EventName.updateAccountTime,this.onUpdateAccountTime]],e?`getAccountUpdates+${e}`:"getAccountUpdates")}getPositions(){return this.subscriptions.register(()=>{this.api.reqPositions()},()=>{this.api.cancelPositions()},[[o.EventName.position,this.onPosition],[o.EventName.positionEnd,this.onPositionEnd]],"getPositions")}getContractDetails(e){return(0,n.lastValueFrom)(this.subscriptions.register(t=>{this.api.reqContractDetails(t,e)},void 0,[[o.EventName.contractDetails,this.onContractDetails],[o.EventName.bondContractDetails,this.onContractDetails],[o.EventName.contractDetailsEnd,this.onContractDetailsEnd]]).pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getSecDefOptParams(e,t,r,s){return(0,n.lastValueFrom)(this.subscriptions.register(n=>{this.api.reqSecDefOptParams(n,e,t,r,s)},void 0,[[o.EventName.securityDefinitionOptionParameter,this.onSecurityDefinitionOptionParameter],[o.EventName.securityDefinitionOptionParameterEnd,this.onSecurityDefinitionOptionParameterEnd]]).pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getPnL(e,t){return this.subscriptions.register(r=>{this.api.reqPnL(r,e,t)},e=>{this.api.cancelPnL(e)},[[o.EventName.pnl,this.onPnL]],`getPnl+${e}:${t}`).pipe((0,i.map)(e=>e.all))}getPnLSingle(e,t,r){return this.subscriptions.register(n=>{this.api.reqPnLSingle(n,e,t,r)},e=>{this.api.cancelPnLSingle(e)},[[o.EventName.pnlSingle,this.onPnLSingle]],`getPnLSingle+${e}:${t}:${r}`).pipe((0,i.map)(e=>e.all))}setMarketDataType(e){this.api.reqMarketDataType(e)}getMarketData(e,t,r,n){return this.subscriptions.register(i=>{this.api.reqMktData(i,e,t,r,n)},e=>{r||n||this.api.cancelMktData(e)},[[o.EventName.tickPrice,this.onTick],[o.EventName.tickSize,this.onTick],[o.EventName.tickGeneric,this.onTick],[o.EventName.tickOptionComputation,this.onTickOptionComputation],[o.EventName.tickSnapshotEnd,this.onTickSnapshotEnd]],`getMarketData+${JSON.stringify(e)}:${t}:${r}:${n}`)}getMarketDataSnapshot(e,t,r){return(0,n.lastValueFrom)(this.getMarketData(e,t,!0,r).pipe((0,i.map)(e=>e.all)),{defaultValue:new u.MutableMarketData})}getHeadTimestamp(e,t,r,s){return(0,n.lastValueFrom)(this.subscriptions.register(n=>{this.api.reqHeadTimestamp(n,e,t,r,s)},e=>{this.api.cancelHeadTimestamp(e)},[[o.EventName.headTimestamp,this.onHeadTimestamp]],`getHeadTimestamp+${JSON.stringify(e)}:${t}:${r}:${s}`).pipe((0,i.map)(e=>e.all)),{defaultValue:""})}getHistoricalData(e,t,r,s,a,u,c){return(0,n.lastValueFrom)(this.subscriptions.register(n=>{this.api.reqHistoricalData(n,e,t,r,s,a,u,c,!1)},void 0,[[o.EventName.historicalData,this.onHistoricalData]]).pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getHistoricalDataUpdates(e,t,r,n){return this.subscriptions.register(i=>{this.api.reqHistoricalData(i,e,"","1 D",t,r,0,n,!0)},e=>{this.api.cancelHistoricalData(e)},[[o.EventName.historicalDataUpdate,this.onHistoricalDataUpdate]],`${JSON.stringify(e)}:${t}:${r}:${n}`).pipe((0,i.map)(e=>e.all))}getHistoricalTicksMid(e,t,r,n,s){return this.subscriptions.register(i=>{this.api.reqHistoricalTicks(i,e,t,r,n,o.WhatToShow.MIDPOINT,s,!1)},void 0,[[o.EventName.historicalTicks,this.onHistoricalTicks]]).pipe((0,i.map)(e=>e.all))}getHistoricalTicksBidAsk(e,t,r,n,s,a){return this.subscriptions.register(i=>{this.api.reqHistoricalTicks(i,e,t,r,n,o.WhatToShow.BID_ASK,s,a)},void 0,[[o.EventName.historicalTicksBidAsk,this.onHistoricalTicksBidAsk]]).pipe((0,i.map)(e=>e.all))}getHistoricalTicksLast(e,t,r,n,s){return this.subscriptions.register(i=>{this.api.reqHistoricalTicks(i,e,t,r,n,o.WhatToShow.TRADES,s,!1)},void 0,[[o.EventName.historicalTicksLast,this.onHistoricalTicksLast]]).pipe((0,i.map)(e=>e.all))}getMarketDepthExchanges(){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqMktDepthExchanges()},void 0,[[o.EventName.mktDepthExchanges,this.onMktDepthExchanges]],"getMarketDepthExchanges").pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}insertAtMapIndex(e,t,r,n){let i=Array.from(n);return i.splice(e,0,[t,r]),n.clear(),i.forEach(([e,t])=>n.set(e,t)),n}getMarketDepth(e,t,r,n){return this.subscriptions.register(i=>{this.api.reqMktDepth(i,e,t,r,n)},e=>{this.api.cancelMktDepth(e,r)},[[o.EventName.updateMktDepth,this.onUpdateMktDepth],[o.EventName.updateMktDepthL2,this.onUpdateMktDepthL2]],`${JSON.stringify(e)}:${t}:${r}:${n}`)}getScannerParameters(){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqScannerParameters()},void 0,[[o.EventName.scannerParameters,this.onScannerParameters]],"getScannerParameters").pipe((0,i.map)(e=>e.all)),{defaultValue:""})}getMarketScanner(e,t,r){return this.subscriptions.register(n=>{this.api.reqScannerSubscription(n,e,t,r)},e=>{this.api.cancelScannerSubscription(e)},[[o.EventName.scannerData,this.onScannerData],[o.EventName.scannerDataEnd,this.onScannerDataEnd]])}getHistogramData(e,t,r,s){return(0,n.lastValueFrom)(this.subscriptions.register(n=>{this.api.reqHistogramData(n,e,t,r,s)},e=>{this.api.cancelHistogramData(e)},[[o.EventName.histogramData,this.onHistogramData]],`getHistogramData+${JSON.stringify(e)}:${t}:${r}:${s}`).pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getAllOpenOrders(){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqAllOpenOrders()},void 0,[[o.EventName.openOrder,this.onOpenOrder],[o.EventName.orderStatus,this.onOrderStatus],[o.EventName.orderBound,this.onOrderBound],[o.EventName.openOrderEnd,this.onOpenOrderComplete]],"getAllOpenOrders").pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getOpenOrders(){return this.subscriptions.register(()=>{this.api.reqOpenOrders()},void 0,[[o.EventName.openOrder,this.onOpenOrder],[o.EventName.orderStatus,this.onOrderStatus],[o.EventName.orderBound,this.onOrderBound],[o.EventName.openOrderEnd,this.onOpenOrderEnd]],"getOpenOrders")}getAutoOpenOrders(e){return this.subscriptions.register(()=>{this.api.reqAutoOpenOrders(e)},void 0,[[o.EventName.openOrder,this.onOpenOrder],[o.EventName.orderStatus,this.onOrderStatus],[o.EventName.orderBound,this.onOrderBound],[o.EventName.openOrderEnd,this.onOpenOrderEnd]],"getAutoOpenOrders")}getNextValidOrderId(){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqIds()},void 0,[[o.EventName.nextValidId,this.onNextValidId]]).pipe((0,i.map)(e=>e.all)),{defaultValue:-1})}placeOrder(e,t,r){this.api.placeOrder(e,t,r)}async placeNewOrder(e,t){let r=await this.getNextValidOrderId();return this.placeOrder(r,e,t),r}modifyOrder(e,t,r){this.api.placeOrder(e,t,r)}cancelOrder(e,t){this.api.cancelOrder(e,t)}cancelAllOrders(e){this.api.reqGlobalCancel(e)}getExecutionDetails(e){return(0,n.lastValueFrom)(this.subscriptions.register(t=>{this.api.reqExecutions(t,e)},void 0,[[o.EventName.execDetails,this.onExecDetails],[o.EventName.execDetailsEnd,this.onExecDetailsEnd]]).pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getCommissionReport(e){return(0,n.lastValueFrom)(this.subscriptions.register(t=>{this.api.reqExecutions(t,e)},void 0,[[o.EventName.commissionReport,this.onComissionReport],[o.EventName.execDetailsEnd,this.onExecDetailsEnd]]).pipe((0,i.map)(e=>e.all)),{defaultValue:[]})}getMatchingSymbols(e){return(0,n.lastValueFrom)(this.subscriptions.register(t=>{this.api.reqMatchingSymbols(t,e)},void 0,[[o.EventName.symbolSamples,this.onSymbolSamples]]).pipe((0,i.map)(e=>e.all)))}getUserInfo(){return(0,n.lastValueFrom)(this.subscriptions.register(e=>{this.api.reqUserInfo(e)},void 0,[[o.EventName.userInfo,this.onUserInfo]],"getUserInfo").pipe((0,i.map)(e=>e.all)),{defaultValue:void 0})}getMarketRule(e){return(0,n.lastValueFrom)(this.subscriptions.register(()=>{this.api.reqMarketRule(e)},void 0,[[o.EventName.marketRule,this.onMarketRule]],`getMarketRule+${e}`).pipe((0,i.map)(e=>e.all)),{defaultValue:void 0})}getTickByTickAllLastDataUpdates(e,t=0,r=!1){return this.subscriptions.register(n=>{this.api.reqTickByTickData(n,e,o.TickByTickDataType.Last,t,r)},e=>{this.api.cancelTickByTickData(e)},[[o.EventName.tickByTickAllLast,this.onTickByTickAllLastDataUpdates(e)]],`${JSON.stringify(e)}:${t}:${r}`).pipe((0,i.map)(e=>e.all))}}t.IBApiNext=T},63345:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DurationUnit=void 0,function(e){e.SECOND="SECOND",e.DAY="DAY",e.WEEK="WEEK",e.MONTH="MONTH",e.YEAR="YEAR"}(r||(t.DurationUnit=r={})),t.default=r},63410:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.noop=void 0,t.noop=function(){}},63701:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.observable=void 0,t.observable="function"==typeof Symbol&&Symbol.observable||"@@observable"},63759:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.mergeWith=void 0;var o=r(86059);t.mergeWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.merge.apply(void 0,i([],n(e)))}},63908:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.joinAllInternals=void 0;var n=r(56032),i=r(42605),o=r(58538),s=r(24071),a=r(28013);t.joinAllInternals=function(e,t){return o.pipe(a.toArray(),s.mergeMap(function(t){return e(t)}),t?i.mapOneOrManyArgs(t):n.identity)}},64491:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleObservable=void 0;var n=r(81773),i=r(95628),o=r(81700);t.scheduleObservable=function(e,t){return n.innerFrom(e).pipe(o.subscribeOn(t),i.observeOn(t))}},64517:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportUnhandledError=void 0;var n=r(87273),i=r(78417);t.reportUnhandledError=function(e){i.timeoutProvider.setTimeout(function(){var t=n.config.onUnhandledError;if(t)t(e);else throw e})}},65014:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.every=void 0;var n=r(96829),i=r(28719);t.every=function(e,t){return n.operate(function(r,n){var o=0;r.subscribe(i.createOperatorSubscriber(n,function(i){e.call(t,i,o++,r)||(n.next(!1),n.complete())},function(){n.next(!0),n.complete()}))})}},65043:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNextMap=void 0;class r extends Map{getOrAdd(e,t){let r=this.get(e);return void 0===r&&(r=t(e),this.set(e,r)),r}}t.IBApiNextMap=r},65186:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MarketCloseOrder=void 0;let n=r(55332);class i{constructor(e,t,r=!0){this.action=e,this.totalQuantity=t,this.transmit=r,this.orderType=n.OrderType.MOC}}t.MarketCloseOrder=i,t.default=i},66193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exhaust=void 0,t.exhaust=r(45870).exhaustAll},66389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.popNumber=t.popScheduler=t.popResultSelector=void 0;var n=r(17886),i=r(90597);function o(e){return e[e.length-1]}t.popResultSelector=function(e){return n.isFunction(o(e))?e.pop():void 0},t.popScheduler=function(e){return i.isScheduler(o(e))?e.pop():void 0},t.popNumber=function(e,t){return"number"==typeof o(e)?e.pop():t}},66707:(e,t,r)=>{"use strict";var n=r(21820),i=r(54972),o=process.env,s=void 0;function a(e){var t;return 0!==(t=function(e){if(!1===s)return 0;if(i("color=16m")||i("color=full")||i("color=truecolor"))return 3;if(i("color=256"))return 2;if(e&&!e.isTTY&&!0!==s)return 0;var t=+!!s;if("win32"===process.platform){var r=n.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in o)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(e){return e in o})||"codeship"===o.CI_NAME?1:t;if("TEAMCITY_VERSION"in o)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION);if("TERM_PROGRAM"in o){var a=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(o.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)||"COLORTERM"in o?1:(o.TERM,t)}(e))&&{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}i("no-color")||i("no-colors")||i("color=false")?s=!1:(i("color")||i("colors")||i("color=true")||i("color=always"))&&(s=!0),"FORCE_COLOR"in o&&(s=0===o.FORCE_COLOR.length||0!==parseInt(o.FORCE_COLOR,10)),e.exports={supportsColor:a,stdout:a(process.stdout),stderr:a(process.stderr)}},66811:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNextItemListUpdate=void 0;class r{constructor(e,t,r,n){this.all=e,this.added=t,this.changed=r,this.removed=n}}t.IBApiNextItemListUpdate=r},66921:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isPromise=void 0;var n=r(17886);t.isPromise=function(e){return n.isFunction(null==e?void 0:e.then)}},67152:e=>{e.exports=function(e){var t=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(r,n,i){return" "===r?r:e[t[Math.round(Math.random()*(t.length-2))]](r)}}},67165:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.partition=void 0;var n=r(1697),i=r(45007);t.partition=function(e,t){return function(r){return[i.filter(e,t)(r),i.filter(n.not(e,t))(r)]}}},68300:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createFind=t.find=void 0;var n=r(96829),i=r(28719);function o(e,t,r){var n="index"===r;return function(r,o){var s=0;r.subscribe(i.createOperatorSubscriber(o,function(i){var a=s++;e.call(t,i,a,r)&&(o.next(n?a:i),o.complete())},function(){o.next(n?-1:void 0),o.complete()}))}}t.find=function(e,t){return n.operate(o(e,t,"value"))},t.createFind=o},68468:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.takeLast=void 0;var i=r(25042),o=r(96829),s=r(28719);t.takeLast=function(e){return e<=0?function(){return i.EMPTY}:o.operate(function(t,r){var i=[];t.subscribe(s.createOperatorSubscriber(r,function(t){i.push(t),e<i.length&&i.shift()},function(){var e,t;try{for(var o=n(i),s=o.next();!s.done;s=o.next()){var a=s.value;r.next(a)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}r.complete()},void 0,function(){i=null}))})}},69105:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pairwise=void 0;var n=r(96829),i=r(28719);t.pairwise=function(){return n.operate(function(e,t){var r,n=!1;e.subscribe(i.createOperatorSubscriber(t,function(e){var i=r;r=e,n&&t.next([i,e]),n=!0}))})}},69325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleReadableStreamLike=void 0;var n=r(93638),i=r(70791);t.scheduleReadableStreamLike=function(e,t){return n.scheduleAsyncIterable(i.readableStreamLikeToAsyncGenerator(e),t)}},69998:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.IN_MSG_ID=void 0,function(e){e[e.UNDEFINED=-1]="UNDEFINED",e[e.TICK_PRICE=1]="TICK_PRICE",e[e.TICK_SIZE=2]="TICK_SIZE",e[e.ORDER_STATUS=3]="ORDER_STATUS",e[e.ERR_MSG=4]="ERR_MSG",e[e.OPEN_ORDER=5]="OPEN_ORDER",e[e.ACCT_VALUE=6]="ACCT_VALUE",e[e.PORTFOLIO_VALUE=7]="PORTFOLIO_VALUE",e[e.ACCT_UPDATE_TIME=8]="ACCT_UPDATE_TIME",e[e.NEXT_VALID_ID=9]="NEXT_VALID_ID",e[e.CONTRACT_DATA=10]="CONTRACT_DATA",e[e.EXECUTION_DATA=11]="EXECUTION_DATA",e[e.MARKET_DEPTH=12]="MARKET_DEPTH",e[e.MARKET_DEPTH_L2=13]="MARKET_DEPTH_L2",e[e.NEWS_BULLETINS=14]="NEWS_BULLETINS",e[e.MANAGED_ACCTS=15]="MANAGED_ACCTS",e[e.RECEIVE_FA=16]="RECEIVE_FA",e[e.HISTORICAL_DATA=17]="HISTORICAL_DATA",e[e.BOND_CONTRACT_DATA=18]="BOND_CONTRACT_DATA",e[e.SCANNER_PARAMETERS=19]="SCANNER_PARAMETERS",e[e.SCANNER_DATA=20]="SCANNER_DATA",e[e.TICK_OPTION_COMPUTATION=21]="TICK_OPTION_COMPUTATION",e[e.TICK_GENERIC=45]="TICK_GENERIC",e[e.TICK_STRING=46]="TICK_STRING",e[e.TICK_EFP=47]="TICK_EFP",e[e.CURRENT_TIME=49]="CURRENT_TIME",e[e.REAL_TIME_BARS=50]="REAL_TIME_BARS",e[e.FUNDAMENTAL_DATA=51]="FUNDAMENTAL_DATA",e[e.CONTRACT_DATA_END=52]="CONTRACT_DATA_END",e[e.OPEN_ORDER_END=53]="OPEN_ORDER_END",e[e.ACCT_DOWNLOAD_END=54]="ACCT_DOWNLOAD_END",e[e.EXECUTION_DATA_END=55]="EXECUTION_DATA_END",e[e.DELTA_NEUTRAL_VALIDATION=56]="DELTA_NEUTRAL_VALIDATION",e[e.TICK_SNAPSHOT_END=57]="TICK_SNAPSHOT_END",e[e.MARKET_DATA_TYPE=58]="MARKET_DATA_TYPE",e[e.COMMISSION_REPORT=59]="COMMISSION_REPORT",e[e.POSITION=61]="POSITION",e[e.POSITION_END=62]="POSITION_END",e[e.ACCOUNT_SUMMARY=63]="ACCOUNT_SUMMARY",e[e.ACCOUNT_SUMMARY_END=64]="ACCOUNT_SUMMARY_END",e[e.VERIFY_MESSAGE_API=65]="VERIFY_MESSAGE_API",e[e.VERIFY_COMPLETED=66]="VERIFY_COMPLETED",e[e.DISPLAY_GROUP_LIST=67]="DISPLAY_GROUP_LIST",e[e.DISPLAY_GROUP_UPDATED=68]="DISPLAY_GROUP_UPDATED",e[e.VERIFY_AND_AUTH_MESSAGE_API=69]="VERIFY_AND_AUTH_MESSAGE_API",e[e.VERIFY_AND_AUTH_COMPLETED=70]="VERIFY_AND_AUTH_COMPLETED",e[e.POSITION_MULTI=71]="POSITION_MULTI",e[e.POSITION_MULTI_END=72]="POSITION_MULTI_END",e[e.ACCOUNT_UPDATE_MULTI=73]="ACCOUNT_UPDATE_MULTI",e[e.ACCOUNT_UPDATE_MULTI_END=74]="ACCOUNT_UPDATE_MULTI_END",e[e.SECURITY_DEFINITION_OPTION_PARAMETER=75]="SECURITY_DEFINITION_OPTION_PARAMETER",e[e.SECURITY_DEFINITION_OPTION_PARAMETER_END=76]="SECURITY_DEFINITION_OPTION_PARAMETER_END",e[e.SOFT_DOLLAR_TIERS=77]="SOFT_DOLLAR_TIERS",e[e.FAMILY_CODES=78]="FAMILY_CODES",e[e.SYMBOL_SAMPLES=79]="SYMBOL_SAMPLES",e[e.MKT_DEPTH_EXCHANGES=80]="MKT_DEPTH_EXCHANGES",e[e.TICK_REQ_PARAMS=81]="TICK_REQ_PARAMS",e[e.SMART_COMPONENTS=82]="SMART_COMPONENTS",e[e.NEWS_ARTICLE=83]="NEWS_ARTICLE",e[e.TICK_NEWS=84]="TICK_NEWS",e[e.NEWS_PROVIDERS=85]="NEWS_PROVIDERS",e[e.HISTORICAL_NEWS=86]="HISTORICAL_NEWS",e[e.HISTORICAL_NEWS_END=87]="HISTORICAL_NEWS_END",e[e.HEAD_TIMESTAMP=88]="HEAD_TIMESTAMP",e[e.HISTOGRAM_DATA=89]="HISTOGRAM_DATA",e[e.HISTORICAL_DATA_UPDATE=90]="HISTORICAL_DATA_UPDATE",e[e.REROUTE_MKT_DATA=91]="REROUTE_MKT_DATA",e[e.REROUTE_MKT_DEPTH=92]="REROUTE_MKT_DEPTH",e[e.MARKET_RULE=93]="MARKET_RULE",e[e.PNL=94]="PNL",e[e.PNL_SINGLE=95]="PNL_SINGLE",e[e.HISTORICAL_TICKS=96]="HISTORICAL_TICKS",e[e.HISTORICAL_TICKS_BID_ASK=97]="HISTORICAL_TICKS_BID_ASK",e[e.HISTORICAL_TICKS_LAST=98]="HISTORICAL_TICKS_LAST",e[e.TICK_BY_TICK=99]="TICK_BY_TICK",e[e.ORDER_BOUND=100]="ORDER_BOUND",e[e.COMPLETED_ORDER=101]="COMPLETED_ORDER",e[e.COMPLETED_ORDERS_END=102]="COMPLETED_ORDERS_END",e[e.REPLACE_FA_END=103]="REPLACE_FA_END",e[e.WSH_META_DATA=104]="WSH_META_DATA",e[e.WSH_EVENT_DATA=105]="WSH_EVENT_DATA",e[e.HISTORICAL_SCHEDULE=106]="HISTORICAL_SCHEDULE",e[e.USER_INFO=107]="USER_INFO"}(r||(t.IN_MSG_ID=r={}))},70236:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.skipLast=void 0;var n=r(56032),i=r(96829),o=r(28719);t.skipLast=function(e){return e<=0?n.identity:i.operate(function(t,r){var n=Array(e),i=0;return t.subscribe(o.createOperatorSubscriber(r,function(t){var o=i++;if(o<e)n[o]=t;else{var s=o%e,a=n[s];n[s]=t,r.next(a)}})),function(){n=null}})}},70671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.min=void 0;var n=r(4759),i=r(17886);t.min=function(e){return n.reduce(i.isFunction(e)?function(t,r){return 0>e(t,r)?t:r}:function(e,t){return e<t?e:t})}},70791:(e,t,r)=>{"use strict";var n=function(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){var u=[o,a];if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&u[0]?n.return:u[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,u[1])).done)return i;switch(n=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,n=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){s=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){s.label=u[1];break}if(6===u[0]&&s.label<i[1]){s.label=i[1],i=u;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(u);break}i[2]&&s.ops.pop(),s.trys.pop();continue}u=t.call(e,s)}catch(e){u=[6,e],n=0}finally{r=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},i=function(e){return this instanceof i?(this.v=e,this):new i(e)},o=function(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),s=[];return n={},a("next"),a("throw"),a("return"),n[Symbol.asyncIterator]=function(){return this},n;function a(e){o[e]&&(n[e]=function(t){return new Promise(function(r,n){s.push([e,t,r,n])>1||u(e,t)})})}function u(e,t){try{var r;(r=o[e](t)).value instanceof i?Promise.resolve(r.value.v).then(c,l):d(s[0][2],r)}catch(e){d(s[0][3],e)}}function c(e){u("next",e)}function l(e){u("throw",e)}function d(e,t){e(t),s.shift(),s.length&&u(s[0][0],s[0][1])}};Object.defineProperty(t,"__esModule",{value:!0}),t.isReadableStreamLike=t.readableStreamLikeToAsyncGenerator=void 0;var s=r(17886);t.readableStreamLikeToAsyncGenerator=function(e){return o(this,arguments,function(){var t,r,o;return n(this,function(n){switch(n.label){case 0:t=e.getReader(),n.label=1;case 1:n.trys.push([1,,9,10]),n.label=2;case 2:return[4,i(t.read())];case 3:if(o=(r=n.sent()).value,!r.done)return[3,5];return[4,i(void 0)];case 4:return[2,n.sent()];case 5:return[4,i(o)];case 6:return[4,n.sent()];case 7:return n.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})},t.isReadableStreamLike=function(e){return s.isFunction(null==e?void 0:e.getReader)}},71344:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.raceInit=t.race=void 0;var n=r(57248),i=r(81773),o=r(19029),s=r(28719);function a(e){return function(t){for(var r=[],n=function(n){r.push(i.innerFrom(e[n]).subscribe(s.createOperatorSubscriber(t,function(e){if(r){for(var i=0;i<r.length;i++)i!==n&&r[i].unsubscribe();r=null}t.next(e)})))},o=0;r&&!t.closed&&o<e.length;o++)n(o)}}t.race=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 1===(e=o.argsOrArgArray(e)).length?i.innerFrom(e[0]):new n.Observable(a(e))},t.raceInit=a},71485:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNext=t.MutableMarketData=t.IBApiNextTickType=t.IBApiTickType=t.MarketDataType=t.IBApiNextError=t.ConnectionState=void 0;var n=r(11);Object.defineProperty(t,"ConnectionState",{enumerable:!0,get:function(){return n.ConnectionState}});var i=r(99515);Object.defineProperty(t,"IBApiNextError",{enumerable:!0,get:function(){return i.IBApiNextError}});var o=r(35488);Object.defineProperty(t,"MarketDataType",{enumerable:!0,get:function(){return o.MarketDataType}});var s=r(19743);Object.defineProperty(t,"IBApiTickType",{enumerable:!0,get:function(){return s.TickType}});var a=r(78554);Object.defineProperty(t,"IBApiNextTickType",{enumerable:!0,get:function(){return a.TickType}});var u=r(56457);Object.defineProperty(t,"MutableMarketData",{enumerable:!0,get:function(){return u.MutableMarketData}});var c=r(62773);Object.defineProperty(t,"IBApiNext",{enumerable:!0,get:function(){return c.IBApiNext}})},72785:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TimeInForce=void 0,t.TimeInForce={DAY:"DAY",GTC:"GTC",OPG:"OPG",IOC:"IOC",GTD:"GTD",GTT:"GTT",AUC:"AUC",FOK:"FOK",GTX:"GTX",DTC:"DTC",Minutes:"Minutes"}},72836:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.onErrorResumeNext=void 0;var n=r(57248),i=r(19029),o=r(28719),s=r(63410),a=r(81773);t.onErrorResumeNext=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=i.argsOrArgArray(e);return new n.Observable(function(e){var t=0,n=function(){if(t<r.length){var i=void 0;try{i=a.innerFrom(r[t++])}catch(e){n();return}var u=new o.OperatorSubscriber(e,void 0,s.noop,s.noop);i.subscribe(u),u.add(n)}else e.complete()};n()})}},73783:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatestInit=t.combineLatest=void 0;var n=r(57248),i=r(35228),o=r(6747),s=r(56032),a=r(42605),u=r(66389),c=r(21099),l=r(28719),d=r(88840);function h(e,t,r){return void 0===r&&(r=s.identity),function(n){_(t,function(){for(var i=e.length,s=Array(i),a=i,u=i,c=function(i){_(t,function(){var c=o.from(e[i],t),d=!1;c.subscribe(l.createOperatorSubscriber(n,function(e){s[i]=e,!d&&(d=!0,u--),u||n.next(r(s.slice()))},function(){--a||n.complete()}))},n)},d=0;d<i;d++)c(d)},n)}}function _(e,t,r){e?d.executeSchedule(r,e,t):t()}t.combineLatest=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=u.popScheduler(e),l=u.popResultSelector(e),d=i.argsArgArrayOrObject(e),_=d.args,f=d.keys;if(0===_.length)return o.from([],r);var E=new n.Observable(h(_,r,f?function(e){return c.createObject(f,e)}:s.identity));return l?E.pipe(a.mapOneOrManyArgs(l)):E},t.combineLatestInit=h},74661:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncAction=void 0;var i=r(18043),o=r(79197),s=r(1071);t.AsyncAction=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return n(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r,n=this.id,i=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(i,n,t)),this.pending=!0,this.delay=t,this.id=null!=(r=this.id)?r:this.requestAsyncId(i,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),o.intervalProvider.setInterval(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&o.intervalProvider.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r,n=!1;try{this.work(e)}catch(e){n=!0,r=e||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,s.arrRemove(n,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(i.Action)},74711:e=>{e.exports=function(e){return function(t,r,n){return r%2==0?t:e.inverse(t)}}},75643:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.TickByTickDataType=void 0,function(e){e.Last="Last",e.AllLast="AllLast",e.BidAsk="BidAsk",e.MidPoint="MidPoint"}(r||(t.TickByTickDataType=r={})),t.default=r},76046:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sequenceEqual=void 0;var n=r(96829),i=r(28719),o=r(81773);function s(){return{buffer:[],complete:!1}}t.sequenceEqual=function(e,t){return void 0===t&&(t=function(e,t){return e===t}),n.operate(function(r,n){var a=s(),u=s(),c=function(e){n.next(e),n.complete()},l=function(e,r){var o=i.createOperatorSubscriber(n,function(n){var i=r.buffer,o=r.complete;0===i.length?o?c(!1):e.buffer.push(n):t(n,i.shift())||c(!1)},function(){e.complete=!0;var t=r.complete,n=r.buffer;t&&c(0===n.length),null==o||o.unsubscribe()});return o};r.subscribe(l(a,u)),o.innerFrom(e).subscribe(l(u,a))})}},76523:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.zipAll=void 0;var n=r(59452),i=r(63908);t.zipAll=function(e){return i.joinAllInternals(n.zip,e)}},77685:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatest=void 0;var o=r(73783),s=r(96829),a=r(19029),u=r(42605),c=r(58538),l=r(66389);t.combineLatest=function e(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var d=l.popResultSelector(t);return d?c.pipe(e.apply(void 0,i([],n(t))),u.mapOneOrManyArgs(d)):s.operate(function(e,r){o.combineLatestInit(i([e],n(a.argsOrArgArray(t))))(r)})}},77945:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ArgumentOutOfRangeError=void 0,t.ArgumentOutOfRangeError=r(23008).createErrorClass(function(e){return function(){e(this),this.name="ArgumentOutOfRangeError",this.message="argument out of range"}})},77982:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatAll=void 0;var n=r(87820);t.concatAll=function(){return n.mergeAll(1)}},78231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleArray=void 0;var n=r(57248);t.scheduleArray=function(e,t){return new n.Observable(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})})}},78281:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Option=void 0;let i=n(r(15850));class o{constructor(e,t,r,n,o,s){this.symbol=e,this.expiry=t,this.strike=r,this.right=n,this.exchange=o,this.currency=s,this.secType=i.default.OPT,this.multiplier=100,this.currency=this.currency??"USD",this.exchange=this.exchange??"SMART"}get lastTradeDateOrContractMonth(){return this.expiry}}t.Option=o,t.default=o},78417:(e,t)=>{"use strict";var r=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},n=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.timeoutProvider=void 0,t.timeoutProvider={setTimeout:function(e,i){for(var o=[],s=2;s<arguments.length;s++)o[s-2]=arguments[s];var a=t.timeoutProvider.delegate;return(null==a?void 0:a.setTimeout)?a.setTimeout.apply(a,n([e,i],r(o))):setTimeout.apply(void 0,n([e,i],r(o)))},clearTimeout:function(e){var r=t.timeoutProvider.delegate;return((null==r?void 0:r.clearTimeout)||clearTimeout)(e)},delegate:void 0}},78554:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.TickType=void 0,function(e){e[e.API_NEXT_FIRST_TICK_ID=1e4]="API_NEXT_FIRST_TICK_ID",e[e.OPTION_PV_DIVIDEND=10001]="OPTION_PV_DIVIDEND",e[e.OPTION_UNDERLYING=10002]="OPTION_UNDERLYING",e[e.BID_OPTION_IV=10003]="BID_OPTION_IV",e[e.BID_OPTION_PRICE=10004]="BID_OPTION_PRICE",e[e.BID_OPTION_DELTA=10005]="BID_OPTION_DELTA",e[e.BID_OPTION_GAMMA=10006]="BID_OPTION_GAMMA",e[e.BID_OPTION_VEGA=10007]="BID_OPTION_VEGA",e[e.BID_OPTION_THETA=10008]="BID_OPTION_THETA",e[e.DELAYED_BID_OPTION_IV=10009]="DELAYED_BID_OPTION_IV",e[e.DELAYED_BID_OPTION_PRICE=10010]="DELAYED_BID_OPTION_PRICE",e[e.DELAYED_BID_OPTION_DELTA=10011]="DELAYED_BID_OPTION_DELTA",e[e.DELAYED_BID_OPTION_GAMMA=10012]="DELAYED_BID_OPTION_GAMMA",e[e.DELAYED_BID_OPTION_VEGA=10013]="DELAYED_BID_OPTION_VEGA",e[e.DELAYED_BID_OPTION_THETA=10014]="DELAYED_BID_OPTION_THETA",e[e.ASK_OPTION_IV=10015]="ASK_OPTION_IV",e[e.ASK_OPTION_PRICE=10016]="ASK_OPTION_PRICE",e[e.ASK_OPTION_DELTA=10017]="ASK_OPTION_DELTA",e[e.ASK_OPTION_GAMMA=10018]="ASK_OPTION_GAMMA",e[e.ASK_OPTION_VEGA=10019]="ASK_OPTION_VEGA",e[e.ASK_OPTION_THETA=10020]="ASK_OPTION_THETA",e[e.DELAYED_ASK_OPTION_IV=10021]="DELAYED_ASK_OPTION_IV",e[e.DELAYED_ASK_OPTION_PRICE=10022]="DELAYED_ASK_OPTION_PRICE",e[e.DELAYED_ASK_OPTION_DELTA=10023]="DELAYED_ASK_OPTION_DELTA",e[e.DELAYED_ASK_OPTION_GAMMA=10024]="DELAYED_ASK_OPTION_GAMMA",e[e.DELAYED_ASK_OPTION_VEGA=10025]="DELAYED_ASK_OPTION_VEGA",e[e.DELAYED_ASK_OPTION_THETA=10026]="DELAYED_ASK_OPTION_THETA",e[e.LAST_OPTION_IV=10027]="LAST_OPTION_IV",e[e.LAST_OPTION_PRICE=10028]="LAST_OPTION_PRICE",e[e.LAST_OPTION_DELTA=10029]="LAST_OPTION_DELTA",e[e.LAST_OPTION_GAMMA=10030]="LAST_OPTION_GAMMA",e[e.LAST_OPTION_VEGA=10031]="LAST_OPTION_VEGA",e[e.LAST_OPTION_THETA=10032]="LAST_OPTION_THETA",e[e.DELAYED_LAST_OPTION_IV=10033]="DELAYED_LAST_OPTION_IV",e[e.DELAYED_LAST_OPTION_PRICE=10034]="DELAYED_LAST_OPTION_PRICE",e[e.DELAYED_LAST_OPTION_DELTA=10035]="DELAYED_LAST_OPTION_DELTA",e[e.DELAYED_LAST_OPTION_GAMMA=10036]="DELAYED_LAST_OPTION_GAMMA",e[e.DELAYED_LAST_OPTION_VEGA=10037]="DELAYED_LAST_OPTION_VEGA",e[e.DELAYED_LAST_OPTION_THETA=10038]="DELAYED_LAST_OPTION_THETA",e[e.MODEL_OPTION_IV=10039]="MODEL_OPTION_IV",e[e.MODEL_OPTION_PRICE=10040]="MODEL_OPTION_PRICE",e[e.MODEL_OPTION_DELTA=10041]="MODEL_OPTION_DELTA",e[e.MODEL_OPTION_GAMMA=10042]="MODEL_OPTION_GAMMA",e[e.MODEL_OPTION_VEGA=10043]="MODEL_OPTION_VEGA",e[e.MODEL_OPTION_THETA=10044]="MODEL_OPTION_THETA",e[e.DELAYED_MODEL_OPTION_IV=10045]="DELAYED_MODEL_OPTION_IV",e[e.DELAYED_MODEL_OPTION_PRICE=10046]="DELAYED_MODEL_OPTION_PRICE",e[e.DELAYED_MODEL_OPTION_DELTA=10047]="DELAYED_MODEL_OPTION_DELTA",e[e.DELAYED_MODEL_OPTION_GAMMA=10048]="DELAYED_MODEL_OPTION_GAMMA",e[e.DELAYED_MODEL_OPTION_VEGA=10049]="DELAYED_MODEL_OPTION_VEGA",e[e.DELAYED_MODEL_OPTION_THETA=10050]="DELAYED_MODEL_OPTION_THETA"}(r||(t.TickType=r={}))},78650:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatestAll=void 0;var n=r(73783),i=r(63908);t.combineLatestAll=function(e){return i.joinAllInternals(n.combineLatest,e)}},79197:(e,t)=>{"use strict";var r=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},n=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.intervalProvider=void 0,t.intervalProvider={setInterval:function(e,i){for(var o=[],s=2;s<arguments.length;s++)o[s-2]=arguments[s];var a=t.intervalProvider.delegate;return(null==a?void 0:a.setInterval)?a.setInterval.apply(a,n([e,i],r(o))):setInterval.apply(void 0,n([e,i],r(o)))},clearInterval:function(e){var r=t.intervalProvider.delegate;return((null==r?void 0:r.clearInterval)||clearInterval)(e)},delegate:void 0}},79249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ArgumentOutOfRangeError:()=>e6,AsyncSubject:()=>eo,BehaviorSubject:()=>er,ConnectableObservable:()=>Q,EMPTY:()=>eg,EmptyError:()=>e4,NEVER:()=>tF,NotFoundError:()=>te,Notification:()=>e8,NotificationKind:()=>i,ObjectUnsubscribedError:()=>J,Observable:()=>H,ReplaySubject:()=>ei,Scheduler:()=>eT,SequenceError:()=>tt,Subject:()=>ee,Subscriber:()=>P,Subscription:()=>p,TimeoutError:()=>tn,UnsubscriptionError:()=>f,VirtualAction:()=>eC,VirtualTimeScheduler:()=>eR,animationFrame:()=>eP,animationFrameScheduler:()=>ey,animationFrames:()=>X,asap:()=>ev,asapScheduler:()=>eI,async:()=>eS,asyncScheduler:()=>eO,audit:()=>t$,auditTime:()=>tZ,bindCallback:()=>tl,bindNodeCallback:()=>td,buffer:()=>tJ,bufferCount:()=>t0,bufferTime:()=>t1,bufferToggle:()=>t2,bufferWhen:()=>t8,catchError:()=>function e(t){return j(function(r,n){var i,o=null,s=!1;o=r.subscribe(K(n,void 0,void 0,function(a){i=eY(t(a,e(t)(r))),o?(o.unsubscribe(),o=null,i.subscribe(n)):s=!0})),s&&(o.unsubscribe(),o=null,i.subscribe(n))})},combineAll:()=>re,combineLatest:()=>tA,combineLatestAll:()=>t6,combineLatestWith:()=>rr,concat:()=>tm,concatAll:()=>tb,concatMap:()=>rn,concatMapTo:()=>ri,concatWith:()=>rs,config:()=>v,connect:()=>ru,connectable:()=>ty,count:()=>rc,debounce:()=>rl,debounceTime:()=>rd,defaultIfEmpty:()=>rh,defer:()=>tD,delay:()=>rT,delayWhen:()=>rp,dematerialize:()=>rA,distinct:()=>rI,distinctUntilChanged:()=>rv,distinctUntilKeyChanged:()=>rS,elementAt:()=>rD,empty:()=>eL,endWith:()=>rN,every:()=>ry,exhaust:()=>rC,exhaustAll:()=>rR,exhaustMap:()=>rP,expand:()=>rg,filter:()=>tK,finalize:()=>rL,find:()=>rM,findIndex:()=>rV,first:()=>rx,firstValueFrom:()=>e3,flatMap:()=>rj,forkJoin:()=>tP,from:()=>e0,fromEvent:()=>function e(t,r,n,i){if(h(n)&&(i=n,n=void 0),i)return e(t,r,n).pipe(tu(i));var o,s,a,u=c(h((o=t).addEventListener)&&h(o.removeEventListener)?tC.map(function(e){return function(i){return t[e](r,i,n)}}):h((s=t).addListener)&&h(s.removeListener)?tR.map(tL(t,r)):h((a=t).on)&&h(a.off)?tg.map(tL(t,r)):[],2),l=u[0],d=u[1];if(!l&&eF(t))return tO(function(t){return e(t,r,n)})(eY(t));if(!l)throw TypeError("Invalid event target");return new H(function(e){var t=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.next(1<t.length?t:t[0])};return l(t),function(){return d(t)}})},fromEventPattern:()=>function e(t,r,n){return n?e(t,r).pipe(tu(n)):new H(function(e){var n=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.next(1===t.length?t[0]:t)},i=t(n);return h(r)?function(){return r(n,i)}:void 0})},generate:()=>tM,groupBy:()=>rw,identity:()=>w,ignoreElements:()=>rf,iif:()=>tU,interval:()=>tx,isEmpty:()=>rF,isObservable:()=>e7,last:()=>rH,lastValueFrom:()=>e5,map:()=>ts,mapTo:()=>rE,materialize:()=>rG,max:()=>rk,merge:()=>tw,mergeAll:()=>tS,mergeMap:()=>tO,mergeMapTo:()=>rK,mergeScan:()=>rW,mergeWith:()=>rQ,min:()=>rq,multicast:()=>rz,never:()=>tB,noop:()=>b,observable:()=>x,observeOn:()=>ez,of:()=>e1,onErrorResumeNext:()=>tk,onErrorResumeNextWith:()=>rX,pairs:()=>tj,pairwise:()=>r$,partition:()=>tW,pipe:()=>F,pluck:()=>rZ,publish:()=>rJ,publishBehavior:()=>r0,publishLast:()=>r1,publishReplay:()=>r2,queue:()=>eD,queueScheduler:()=>em,race:()=>tY,raceWith:()=>r8,range:()=>tq,reduce:()=>t7,refCount:()=>Y,repeat:()=>r9,repeatWhen:()=>r7,retry:()=>r4,retryWhen:()=>r5,sample:()=>r3,sampleTime:()=>r6,scan:()=>ne,scheduled:()=>eJ,sequenceEqual:()=>nt,share:()=>nn,shareReplay:()=>no,single:()=>ns,skip:()=>na,skipLast:()=>nu,skipUntil:()=>nc,skipWhile:()=>nl,startWith:()=>nd,subscribeOn:()=>eX,switchAll:()=>n_,switchMap:()=>nh,switchMapTo:()=>nf,switchScan:()=>nE,take:()=>r_,takeLast:()=>rB,takeUntil:()=>np,takeWhile:()=>nT,tap:()=>nA,throttle:()=>nI,throttleTime:()=>nv,throwError:()=>e2,throwIfEmpty:()=>rb,timeInterval:()=>nO,timeout:()=>ti,timeoutWith:()=>nb,timer:()=>tV,timestamp:()=>nm,toArray:()=>t5,using:()=>tz,window:()=>nD,windowCount:()=>nN,windowTime:()=>ny,windowToggle:()=>nP,windowWhen:()=>nR,withLatestFrom:()=>nC,zip:()=>tX,zipAll:()=>ng,zipWith:()=>nM});var n,i,o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function s(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function a(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(u){var c=[a,u];if(r)throw TypeError("Generator is already executing.");for(;s&&(s=0,c[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&c[0]?n.return:c[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,c[1])).done)return i;switch(n=0,i&&(c=[2&c[0],i.value]),c[0]){case 0:case 1:i=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,n=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===c[0]||2===c[0])){o=0;continue}if(3===c[0]&&(!i||c[1]>i[0]&&c[1]<i[3])){o.label=c[1];break}if(6===c[0]&&o.label<i[1]){o.label=i[1],i=c;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(c);break}i[2]&&o.ops.pop(),o.trys.pop();continue}c=t.call(e,o)}catch(e){c=[6,e],n=0}finally{r=i=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}}function u(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function c(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function l(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}function d(e){return this instanceof d?(this.v=e,this):new d(e)}function h(e){return"function"==typeof e}function _(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var f=_(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}});function E(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var p=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,n,i,o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var s=u(o),a=s.next();!a.done;a=s.next())a.value.remove(this)}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}else o.remove(this);var d=this.initialTeardown;if(h(d))try{d()}catch(e){i=e instanceof f?e.errors:[e]}var _=this._finalizers;if(_){this._finalizers=null;try{for(var E=u(_),p=E.next();!p.done;p=E.next()){var T=p.value;try{I(T)}catch(e){i=null!=i?i:[],e instanceof f?i=l(l([],c(i)),c(e.errors)):i.push(e)}}}catch(e){r={error:e}}finally{try{p&&!p.done&&(n=E.return)&&n.call(E)}finally{if(r)throw r.error}}}if(i)throw new f(i)}},t.prototype.add=function(e){var r;if(e&&e!==this)if(this.closed)I(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!=(r=this._finalizers)?r:[]).push(e)}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&E(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&E(r,e),e instanceof t&&e._removeParent(this)},(e=new t).closed=!0,t.EMPTY=e,t}(),T=p.EMPTY;function A(e){return e instanceof p||e&&"closed"in e&&h(e.remove)&&h(e.add)&&h(e.unsubscribe)}function I(e){h(e)?e():e.unsubscribe()}var v={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},O={setTimeout:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=O.delegate;return(null==i?void 0:i.setTimeout)?i.setTimeout.apply(i,l([e,t],c(r))):setTimeout.apply(void 0,l([e,t],c(r)))},clearTimeout:function(e){var t=O.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function S(e){O.setTimeout(function(){var t=v.onUnhandledError;if(t)t(e);else throw e})}function b(){}var m=D("C",void 0,void 0);function D(e,t,r){return{kind:e,value:t,error:r}}var N=null;function y(e){if(v.useDeprecatedSynchronousErrorHandling){var t=!N;if(t&&(N={errorThrown:!1,error:null}),e(),t){var r=N,n=r.errorThrown,i=r.error;if(N=null,n)throw i}}else e()}var P=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,A(t)&&t.add(r)):r.destination=V,r}return s(t,e),t.create=function(e,t,r){return new L(e,t,r)},t.prototype.next=function(e){this.isStopped?U(D("N",e,void 0),this):this._next(e)},t.prototype.error=function(e){this.isStopped?U(D("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?U(m,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(p),R=Function.prototype.bind;function C(e,t){return R.call(e,t)}var g=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){M(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){M(e)}else M(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){M(e)}},e}(),L=function(e){function t(t,r,n){var i,o,s=e.call(this)||this;return h(t)||!t?i={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:s&&v.useDeprecatedNextContext?((o=Object.create(t)).unsubscribe=function(){return s.unsubscribe()},i={next:t.next&&C(t.next,o),error:t.error&&C(t.error,o),complete:t.complete&&C(t.complete,o)}):i=t,s.destination=new g(i),s}return s(t,e),t}(P);function M(e){if(v.useDeprecatedSynchronousErrorHandling)v.useDeprecatedSynchronousErrorHandling&&N&&(N.errorThrown=!0,N.error=e);else S(e)}function U(e,t){var r=v.onStoppedNotification;r&&O.setTimeout(function(){return r(e,t)})}var V={closed:!0,next:b,error:function(e){throw e},complete:b},x="function"==typeof Symbol&&Symbol.observable||"@@observable";function w(e){return e}function F(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return B(e)}function B(e){return 0===e.length?w:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)}}var H=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var n=this,i=!function(e){return e&&e instanceof P||e&&h(e.next)&&h(e.error)&&h(e.complete)&&A(e)}(e)?new L(e,t,r):e;return y(function(){var e=n.operator,t=n.source;i.add(e?e.call(i,t):t?n._subscribe(i):n._trySubscribe(i))}),i},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=G(t))(function(t,n){var i=new L({next:function(t){try{e(t)}catch(e){n(e),i.unsubscribe()}},error:n,complete:t});r.subscribe(i)})},e.prototype._subscribe=function(e){var t;return null==(t=this.source)?void 0:t.subscribe(e)},e.prototype[x]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return B(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=G(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function G(e){var t;return null!=(t=null!=e?e:v.Promise)?t:Promise}function k(e){return h(null==e?void 0:e.lift)}function j(e){return function(t){if(k(t))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}function K(e,t,r,n,i){return new W(e,t,r,n,i)}var W=function(e){function t(t,r,n,i,o,s){var a=e.call(this,t)||this;return a.onFinalize=o,a.shouldUnsubscribe=s,a._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=i?function(e){try{i(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return s(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null==(t=this.onFinalize)||t.call(this)}},t}(P);function Y(){return j(function(e,t){var r=null;e._refCount++;var n=K(t,void 0,void 0,void 0,function(){if(!e||e._refCount<=0||0<--e._refCount){r=null;return}var n=e._connection,i=r;r=null,n&&(!i||n===i)&&n.unsubscribe(),t.unsubscribe()});e.subscribe(n),n.closed||(r=e.connect())})}var Q=function(e){function t(t,r){var n=e.call(this)||this;return n.source=t,n.subjectFactory=r,n._subject=null,n._refCount=0,n._connection=null,k(t)&&(n.lift=t.lift),n}return s(t,e),t.prototype._subscribe=function(e){return this.getSubject().subscribe(e)},t.prototype.getSubject=function(){var e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject},t.prototype._teardown=function(){this._refCount=0;var e=this._connection;this._subject=this._connection=null,null==e||e.unsubscribe()},t.prototype.connect=function(){var e=this,t=this._connection;if(!t){t=this._connection=new p;var r=this.getSubject();t.add(this.source.subscribe(K(r,void 0,function(){e._teardown(),r.complete()},function(t){e._teardown(),r.error(t)},function(){return e._teardown()}))),t.closed&&(this._connection=null,t=p.EMPTY)}return t},t.prototype.refCount=function(){return Y()(this)},t}(H),q={now:function(){return(q.delegate||performance).now()},delegate:void 0},z={schedule:function(e){var t=requestAnimationFrame,r=cancelAnimationFrame,n=z.delegate;n&&(t=n.requestAnimationFrame,r=n.cancelAnimationFrame);var i=t(function(t){r=void 0,e(t)});return new p(function(){return null==r?void 0:r(i)})},requestAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=z.delegate;return((null==r?void 0:r.requestAnimationFrame)||requestAnimationFrame).apply(void 0,l([],c(e)))},cancelAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=z.delegate;return((null==r?void 0:r.cancelAnimationFrame)||cancelAnimationFrame).apply(void 0,l([],c(e)))},delegate:void 0};function X(e){return e?$(e):Z}function $(e){return new H(function(t){var r=e||q,n=r.now(),i=0,o=function(){t.closed||(i=z.requestAnimationFrame(function(s){i=0;var a=r.now();t.next({timestamp:e?a:s,elapsed:a-n}),o()}))};return o(),function(){i&&z.cancelAnimationFrame(i)}})}var Z=$(),J=_(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),ee=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return s(t,e),t.prototype.lift=function(e){var t=new et(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new J},t.prototype.next=function(e){var t=this;y(function(){var r,n;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var i=u(t.currentObservers),o=i.next();!o.done;o=i.next())o.value.next(e)}catch(e){r={error:e}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}})},t.prototype.error=function(e){var t=this;y(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}})},t.prototype.complete=function(){var e=this;y(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null==(e=this.observers)?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,r=this.hasError,n=this.isStopped,i=this.observers;return r||n?T:(this.currentObservers=null,i.push(e),new p(function(){t.currentObservers=null,E(i,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this.thrownError,n=this.isStopped;t?e.error(r):n&&e.complete()},t.prototype.asObservable=function(){var e=new H;return e.source=this,e},t.create=function(e,t){return new et(e,t)},t}(H),et=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return s(t,e),t.prototype.next=function(e){var t,r;null==(r=null==(t=this.destination)?void 0:t.next)||r.call(t,e)},t.prototype.error=function(e){var t,r;null==(r=null==(t=this.destination)?void 0:t.error)||r.call(t,e)},t.prototype.complete=function(){var e,t;null==(t=null==(e=this.destination)?void 0:e.complete)||t.call(e)},t.prototype._subscribe=function(e){var t,r;return null!=(r=null==(t=this.source)?void 0:t.subscribe(e))?r:T},t}(ee),er=function(e){function t(t){var r=e.call(this)||this;return r._value=t,r}return s(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),t.prototype._subscribe=function(t){var r=e.prototype._subscribe.call(this,t);return r.closed||t.next(this._value),r},t.prototype.getValue=function(){var e=this.hasError,t=this.thrownError,r=this._value;if(e)throw t;return this._throwIfClosed(),r},t.prototype.next=function(t){e.prototype.next.call(this,this._value=t)},t}(ee),en={now:function(){return(en.delegate||Date).now()},delegate:void 0},ei=function(e){function t(t,r,n){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===n&&(n=en);var i=e.call(this)||this;return i._bufferSize=t,i._windowTime=r,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=r===1/0,i._bufferSize=Math.max(1,t),i._windowTime=Math.max(1,r),i}return s(t,e),t.prototype.next=function(t){var r=this.isStopped,n=this._buffer,i=this._infiniteTimeWindow,o=this._timestampProvider,s=this._windowTime;!r&&(n.push(t),i||n.push(o.now()+s)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,n=this._buffer.slice(),i=0;i<n.length&&!e.closed;i+=r?1:2)e.next(n[i]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,i=(n?1:2)*e;if(e<1/0&&i<r.length&&r.splice(0,r.length-i),!n){for(var o=t.now(),s=0,a=1;a<r.length&&r[a]<=o;a+=2)s=a;s&&r.splice(0,s+1)}},t}(ee),eo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._value=null,t._hasValue=!1,t._isComplete=!1,t}return s(t,e),t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this._hasValue,n=this._value,i=this.thrownError,o=this.isStopped,s=this._isComplete;t?e.error(i):(o||s)&&(r&&e.next(n),e.complete())},t.prototype.next=function(e){this.isStopped||(this._value=e,this._hasValue=!0)},t.prototype.complete=function(){var t=this._hasValue,r=this._value;this._isComplete||(this._isComplete=!0,t&&e.prototype.next.call(this,r),e.prototype.complete.call(this))},t}(ee),es=function(e){function t(t,r){return e.call(this)||this}return s(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(p),ea={setInterval:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=ea.delegate;return(null==i?void 0:i.setInterval)?i.setInterval.apply(i,l([e,t],c(r))):setInterval.apply(void 0,l([e,t],c(r)))},clearInterval:function(e){var t=ea.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},eu=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return s(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r,n=this.id,i=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(i,n,t)),this.pending=!0,this.delay=t,this.id=null!=(r=this.id)?r:this.requestAsyncId(i,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),ea.setInterval(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&ea.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r,n=!1;try{this.work(e)}catch(e){n=!0,r=e||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,E(n,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(es),ec=1,el={};function ed(e){return e in el&&(delete el[e],!0)}var eh={setImmediate:function(e){var t=ec++;return el[t]=!0,n||(n=Promise.resolve()),n.then(function(){return ed(t)&&e()}),t},clearImmediate:function(e){ed(e)}},e_=eh.setImmediate,ef=eh.clearImmediate,eE={setImmediate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=eE.delegate;return((null==r?void 0:r.setImmediate)||e_).apply(void 0,l([],c(e)))},clearImmediate:function(e){var t=eE.delegate;return((null==t?void 0:t.clearImmediate)||ef)(e)},delegate:void 0},ep=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n}return s(t,e),t.prototype.requestAsyncId=function(t,r,n){return(void 0===n&&(n=0),null!==n&&n>0)?e.prototype.requestAsyncId.call(this,t,r,n):(t.actions.push(this),t._scheduled||(t._scheduled=eE.setImmediate(t.flush.bind(t,void 0))))},t.prototype.recycleAsyncId=function(t,r,n){if(void 0===n&&(n=0),null!=n?n>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,t,r,n);var i,o=t.actions;null!=r&&(null==(i=o[o.length-1])?void 0:i.id)!==r&&(eE.clearImmediate(r),t._scheduled===r&&(t._scheduled=void 0))},t}(eu),eT=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=en.now,e}(),eA=function(e){function t(t,r){void 0===r&&(r=eT.now);var n=e.call(this,t,r)||this;return n.actions=[],n._active=!1,n}return s(t,e),t.prototype.flush=function(e){var t,r=this.actions;if(this._active)return void r.push(e);this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,t){for(;e=r.shift();)e.unsubscribe();throw t}},t}(eT),eI=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return s(t,e),t.prototype.flush=function(e){this._active=!0;var t,r=this._scheduled;this._scheduled=void 0;var n=this.actions;e=e||n.shift();do if(t=e.execute(e.state,e.delay))break;while((e=n[0])&&e.id===r&&n.shift());if(this._active=!1,t){for(;(e=n[0])&&e.id===r&&n.shift();)e.unsubscribe();throw t}},t}(eA))(ep),ev=eI,eO=new eA(eu),eS=eO,eb=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n}return s(t,e),t.prototype.schedule=function(t,r){return(void 0===r&&(r=0),r>0)?e.prototype.schedule.call(this,t,r):(this.delay=r,this.state=t,this.scheduler.flush(this),this)},t.prototype.execute=function(t,r){return r>0||this.closed?e.prototype.execute.call(this,t,r):this._execute(t,r)},t.prototype.requestAsyncId=function(t,r,n){return(void 0===n&&(n=0),null!=n&&n>0||null==n&&this.delay>0)?e.prototype.requestAsyncId.call(this,t,r,n):(t.flush(this),0)},t}(eu),em=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return s(t,e),t}(eA))(eb),eD=em,eN=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n}return s(t,e),t.prototype.requestAsyncId=function(t,r,n){return(void 0===n&&(n=0),null!==n&&n>0)?e.prototype.requestAsyncId.call(this,t,r,n):(t.actions.push(this),t._scheduled||(t._scheduled=z.requestAnimationFrame(function(){return t.flush(void 0)})))},t.prototype.recycleAsyncId=function(t,r,n){if(void 0===n&&(n=0),null!=n?n>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,t,r,n);var i,o=t.actions;null!=r&&r===t._scheduled&&(null==(i=o[o.length-1])?void 0:i.id)!==r&&(z.cancelAnimationFrame(r),t._scheduled=void 0)},t}(eu),ey=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return s(t,e),t.prototype.flush=function(e){this._active=!0,e?t=e.id:(t=this._scheduled,this._scheduled=void 0);var t,r,n=this.actions;e=e||n.shift();do if(r=e.execute(e.state,e.delay))break;while((e=n[0])&&e.id===t&&n.shift());if(this._active=!1,r){for(;(e=n[0])&&e.id===t&&n.shift();)e.unsubscribe();throw r}},t}(eA))(eN),eP=ey,eR=function(e){function t(t,r){void 0===t&&(t=eC),void 0===r&&(r=1/0);var n=e.call(this,t,function(){return n.frame})||this;return n.maxFrames=r,n.frame=0,n.index=-1,n}return s(t,e),t.prototype.flush=function(){for(var e,t,r=this.actions,n=this.maxFrames;(t=r[0])&&t.delay<=n&&(r.shift(),this.frame=t.delay,!(e=t.execute(t.state,t.delay))););if(e){for(;t=r.shift();)t.unsubscribe();throw e}},t.frameTimeFactor=10,t}(eA),eC=function(e){function t(t,r,n){void 0===n&&(n=t.index+=1);var i=e.call(this,t,r)||this;return i.scheduler=t,i.work=r,i.index=n,i.active=!0,i.index=t.index=n,i}return s(t,e),t.prototype.schedule=function(r,n){if(void 0===n&&(n=0),!Number.isFinite(n))return p.EMPTY;if(!this.id)return e.prototype.schedule.call(this,r,n);this.active=!1;var i=new t(this.scheduler,this.work);return this.add(i),i.schedule(r,n)},t.prototype.requestAsyncId=function(e,r,n){void 0===n&&(n=0),this.delay=e.frame+n;var i=e.actions;return i.push(this),i.sort(t.sortActions),1},t.prototype.recycleAsyncId=function(e,t,r){void 0===r&&(r=0)},t.prototype._execute=function(t,r){if(!0===this.active)return e.prototype._execute.call(this,t,r)},t.sortActions=function(e,t){if(e.delay===t.delay)if(e.index===t.index)return 0;else if(e.index>t.index)return 1;else return -1;return e.delay>t.delay?1:-1},t}(eu),eg=new H(function(e){return e.complete()});function eL(e){var t;return e?(t=e,new H(function(e){return t.schedule(function(){return e.complete()})})):eg}function eM(e){return e&&h(e.schedule)}function eU(e){return e[e.length-1]}function eV(e){return h(eU(e))?e.pop():void 0}function ex(e){return eM(eU(e))?e.pop():void 0}function ew(e,t){return"number"==typeof eU(e)?e.pop():t}var eF=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function eB(e){return h(null==e?void 0:e.then)}function eH(e){return Symbol.asyncIterator&&h(null==e?void 0:e[Symbol.asyncIterator])}function eG(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var ek="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function ej(e){return h(null==e?void 0:e[ek])}function eK(e){return function(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(e,t||[]),o=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",function(e){return function(t){return Promise.resolve(t).then(e,c)}}),n[Symbol.asyncIterator]=function(){return this},n;function s(e,t){i[e]&&(n[e]=function(t){return new Promise(function(r,n){o.push([e,t,r,n])>1||a(e,t)})},t&&(n[e]=t(n[e])))}function a(e,t){try{var r;(r=i[e](t)).value instanceof d?Promise.resolve(r.value.v).then(u,c):l(o[0][2],r)}catch(e){l(o[0][3],e)}}function u(e){a("next",e)}function c(e){a("throw",e)}function l(e,t){e(t),o.shift(),o.length&&a(o[0][0],o[0][1])}}(this,arguments,function(){var t,r,n;return a(this,function(i){switch(i.label){case 0:t=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,d(t.read())];case 3:if(n=(r=i.sent()).value,!r.done)return[3,5];return[4,d(void 0)];case 4:return[2,i.sent()];case 5:return[4,d(n)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function eW(e){return h(null==e?void 0:e.getReader)}function eY(e){if(e instanceof H)return e;if(null!=e){var t,r,n,i;if(h(e[x])){return t=e,new H(function(e){var r=t[x]();if(h(r.subscribe))return r.subscribe(e);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if(eF(e)){return r=e,new H(function(e){for(var t=0;t<r.length&&!e.closed;t++)e.next(r[t]);e.complete()})}if(eB(e)){return n=e,new H(function(e){n.then(function(t){e.closed||(e.next(t),e.complete())},function(t){return e.error(t)}).then(null,S)})}if(eH(e))return eQ(e);if(ej(e)){return i=e,new H(function(e){var t,r;try{for(var n=u(i),o=n.next();!o.done;o=n.next()){var s=o.value;if(e.next(s),e.closed)return}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}e.complete()})}if(eW(e))return eQ(eK(e))}throw eG(e)}function eQ(e){return new H(function(t){(function(e,t){var r,n,i,o,s,c,l,d;return s=this,c=void 0,l=void 0,d=function(){var s;return a(this,function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),r=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=u(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,i){var o,s,a;o=n,s=i,a=(t=e[r](t)).done,Promise.resolve(t.value).then(function(e){o({value:e,done:a})},s)})}}}(e),a.label=1;case 1:return[4,r.next()];case 2:if((n=a.sent()).done)return[3,4];if(s=n.value,t.next(s),t.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return i={error:a.sent()},[3,11];case 6:if(a.trys.push([6,,9,10]),!(n&&!n.done&&(o=r.return)))return[3,8];return[4,o.call(r)];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})},new(l||(l=Promise))(function(e,t){function r(e){try{i(d.next(e))}catch(e){t(e)}}function n(e){try{i(d.throw(e))}catch(e){t(e)}}function i(t){var i;t.done?e(t.value):((i=t.value)instanceof l?i:new l(function(e){e(i)})).then(r,n)}i((d=d.apply(s,c||[])).next())})})(e,t).catch(function(e){return t.error(e)})})}function eq(e,t,r,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var o=t.schedule(function(){r(),i?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(o),!i)return o}function ez(e,t){return void 0===t&&(t=0),j(function(r,n){r.subscribe(K(n,function(r){return eq(n,e,function(){return n.next(r)},t)},function(){return eq(n,e,function(){return n.complete()},t)},function(r){return eq(n,e,function(){return n.error(r)},t)}))})}function eX(e,t){return void 0===t&&(t=0),j(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}function e$(e,t){return new H(function(r){var n;return eq(r,t,function(){n=e[ek](),eq(r,t,function(){var e,t,i;try{t=(e=n.next()).value,i=e.done}catch(e){r.error(e);return}i?r.complete():r.next(t)},0,!0)}),function(){return h(null==n?void 0:n.return)&&n.return()}})}function eZ(e,t){if(!e)throw Error("Iterable cannot be null");return new H(function(r){eq(r,t,function(){var n=e[Symbol.asyncIterator]();eq(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}function eJ(e,t){if(null!=e){if(h(e[x]))return eY(e).pipe(eX(t),ez(t));if(eF(e))return new H(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})});if(eB(e))return eY(e).pipe(eX(t),ez(t));if(eH(e))return eZ(e,t);if(ej(e))return e$(e,t);if(eW(e))return eZ(eK(e),t)}throw eG(e)}function e0(e,t){return t?eJ(e,t):eY(e)}function e1(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ex(e);return e0(e,r)}function e2(e,t){var r=h(e)?e:function(){return e},n=function(e){return e.error(r())};return new H(t?function(e){return t.schedule(n,0,e)}:n)}!function(e){e.NEXT="N",e.ERROR="E",e.COMPLETE="C"}(i||(i={}));var e8=function(){function e(e,t,r){this.kind=e,this.value=t,this.error=r,this.hasValue="N"===e}return e.prototype.observe=function(e){return e9(this,e)},e.prototype.do=function(e,t,r){var n=this.kind,i=this.value,o=this.error;return"N"===n?null==e?void 0:e(i):"E"===n?null==t?void 0:t(o):null==r?void 0:r()},e.prototype.accept=function(e,t,r){return h(null==e?void 0:e.next)?this.observe(e):this.do(e,t,r)},e.prototype.toObservable=function(){var e=this.kind,t=this.value,r=this.error,n="N"===e?e1(t):"E"===e?e2(function(){return r}):"C"===e?eg:0;if(!n)throw TypeError("Unexpected notification kind "+e);return n},e.createNext=function(t){return new e("N",t)},e.createError=function(t){return new e("E",void 0,t)},e.createComplete=function(){return e.completeNotification},e.completeNotification=new e("C"),e}();function e9(e,t){var r,n,i,o=e.kind,s=e.value,a=e.error;if("string"!=typeof o)throw TypeError('Invalid notification, missing "kind"');"N"===o?null==(r=t.next)||r.call(t,s):"E"===o?null==(n=t.error)||n.call(t,a):null==(i=t.complete)||i.call(t)}function e7(e){return!!e&&(e instanceof H||h(e.lift)&&h(e.subscribe))}var e4=_(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function e5(e,t){var r="object"==typeof t;return new Promise(function(n,i){var o,s=!1;e.subscribe({next:function(e){o=e,s=!0},error:i,complete:function(){s?n(o):r?n(t.defaultValue):i(new e4)}})})}function e3(e,t){var r="object"==typeof t;return new Promise(function(n,i){var o=new L({next:function(e){n(e),o.unsubscribe()},error:i,complete:function(){r?n(t.defaultValue):i(new e4)}});e.subscribe(o)})}var e6=_(function(e){return function(){e(this),this.name="ArgumentOutOfRangeError",this.message="argument out of range"}}),te=_(function(e){return function(t){e(this),this.name="NotFoundError",this.message=t}}),tt=_(function(e){return function(t){e(this),this.name="SequenceError",this.message=t}});function tr(e){return e instanceof Date&&!isNaN(e)}var tn=_(function(e){return function(t){void 0===t&&(t=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t}});function ti(e,t){var r=tr(e)?{first:e}:"number"==typeof e?{each:e}:e,n=r.first,i=r.each,o=r.with,s=void 0===o?to:o,a=r.scheduler,u=void 0===a?null!=t?t:eO:a,c=r.meta,l=void 0===c?null:c;if(null==n&&null==i)throw TypeError("No timeout provided.");return j(function(e,t){var r,o,a=null,c=0,d=function(e){o=eq(t,u,function(){try{r.unsubscribe(),eY(s({meta:l,lastValue:a,seen:c})).subscribe(t)}catch(e){t.error(e)}},e)};r=e.subscribe(K(t,function(e){null==o||o.unsubscribe(),c++,t.next(a=e),i>0&&d(i)},void 0,void 0,function(){(null==o?void 0:o.closed)||null==o||o.unsubscribe(),a=null})),c||d(null!=n?"number"==typeof n?n:n-u.now():i)})}function to(e){throw new tn(e)}function ts(e,t){return j(function(r,n){var i=0;r.subscribe(K(n,function(r){n.next(e.call(t,r,i++))}))})}var ta=Array.isArray;function tu(e){return ts(function(t){return ta(t)?e.apply(void 0,l([],c(t))):e(t)})}function tc(e,t,r,n){if(r)if(!eM(r))return function(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];return tc(e,t,n).apply(this,i).pipe(tu(r))};else n=r;return n?function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return tc(e,t).apply(this,r).pipe(eX(n),ez(n))}:function(){for(var r=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=new eo,s=!0;return new H(function(i){var a=o.subscribe(i);if(s){s=!1;var u=!1,d=!1;t.apply(r,l(l([],c(n)),[function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(e){var n=t.shift();if(null!=n)return void o.error(n)}o.next(1<t.length?t:t[0]),d=!0,u&&o.complete()}])),d&&o.complete(),u=!0}return a})}}function tl(e,t,r){return tc(!1,e,t,r)}function td(e,t,r){return tc(!0,e,t,r)}var th=Array.isArray,t_=Object.getPrototypeOf,tf=Object.prototype,tE=Object.keys;function tp(e){if(1===e.length){var t,r=e[0];if(th(r))return{args:r,keys:null};if((t=r)&&"object"==typeof t&&t_(t)===tf){var n=tE(r);return{args:n.map(function(e){return r[e]}),keys:n}}}return{args:e,keys:null}}function tT(e,t){return e.reduce(function(e,r,n){return e[r]=t[n],e},{})}function tA(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ex(e),n=eV(e),i=tp(e),o=i.args,s=i.keys;if(0===o.length)return e0([],r);var a=new H(tI(o,r,s?function(e){return tT(s,e)}:w));return n?a.pipe(tu(n)):a}function tI(e,t,r){return void 0===r&&(r=w),function(n){var i,o,s;i=t,o=function(){for(var i=e.length,o=Array(i),s=i,a=i,u=function(i){var u,c,l;u=t,c=function(){var u=e0(e[i],t),c=!1;u.subscribe(K(n,function(e){o[i]=e,!c&&(c=!0,a--),a||n.next(r(o.slice()))},function(){--s||n.complete()}))},l=n,u?eq(l,u,c):c()},c=0;c<i;c++)u(c)},s=n,i?eq(s,i,o):o()}}function tv(e,t,r,n,i,o,s,a){var u=[],c=0,l=0,d=!1,h=function(){!d||u.length||c||t.complete()},_=function(e){return c<n?f(e):u.push(e)},f=function(e){o&&t.next(e),c++;var a=!1;eY(r(e,l++)).subscribe(K(t,function(e){null==i||i(e),o?_(e):t.next(e)},function(){a=!0},void 0,function(){if(a)try{for(c--;u.length&&c<n;)!function(){var e=u.shift();s?eq(t,s,function(){return f(e)}):f(e)}();h()}catch(e){t.error(e)}}))};return e.subscribe(K(t,_,function(){d=!0,h()})),function(){null==a||a()}}function tO(e,t,r){return(void 0===r&&(r=1/0),h(t))?tO(function(r,n){return ts(function(e,i){return t(r,e,n,i)})(eY(e(r,n)))},r):("number"==typeof t&&(r=t),j(function(t,n){return tv(t,n,e,r)}))}function tS(e){return void 0===e&&(e=1/0),tO(w,e)}function tb(){return tS(1)}function tm(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return tb()(e0(e,ex(e)))}function tD(e){return new H(function(t){eY(e()).subscribe(t)})}var tN={connector:function(){return new ee},resetOnDisconnect:!0};function ty(e,t){void 0===t&&(t=tN);var r=null,n=t.connector,i=t.resetOnDisconnect,o=void 0===i||i,s=n(),a=new H(function(e){return s.subscribe(e)});return a.connect=function(){return(!r||r.closed)&&(r=tD(function(){return e}).subscribe(s),o&&r.add(function(){return s=n()})),r},a}function tP(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=eV(e),n=tp(e),i=n.args,o=n.keys,s=new H(function(e){var t=i.length;if(!t)return void e.complete();for(var r=Array(t),n=t,s=t,a=function(t){var a=!1;eY(i[t]).subscribe(K(e,function(e){!a&&(a=!0,s--),r[t]=e},function(){return n--},void 0,function(){n&&a||(s||e.next(o?tT(o,r):r),e.complete())}))},u=0;u<t;u++)a(u)});return r?s.pipe(tu(r)):s}var tR=["addListener","removeListener"],tC=["addEventListener","removeEventListener"],tg=["on","off"];function tL(e,t){return function(r){return function(n){return e[r](t,n)}}}function tM(e,t,r,n,i){var o,s,u;function c(){var e;return a(this,function(n){switch(n.label){case 0:e=u,n.label=1;case 1:if(!(!t||t(e)))return[3,4];return[4,s(e)];case 2:n.sent(),n.label=3;case 3:return e=r(e),[3,1];case 4:return[2]}})}return 1==arguments.length?(u=e.initialState,t=e.condition,r=e.iterate,s=void 0===(o=e.resultSelector)?w:o,i=e.scheduler):(u=e,!n||eM(n)?(s=w,i=n):s=n),tD(i?function(){return e$(c(),i)}:c)}function tU(e,t,r){return tD(function(){return e()?t:r})}function tV(e,t,r){void 0===e&&(e=0),void 0===r&&(r=eS);var n=-1;return null!=t&&(eM(t)?r=t:n=t),new H(function(t){var i=tr(e)?e-r.now():e;i<0&&(i=0);var o=0;return r.schedule(function(){t.closed||(t.next(o++),0<=n?this.schedule(void 0,n):t.complete())},i)})}function tx(e,t){return void 0===e&&(e=0),void 0===t&&(t=eO),e<0&&(e=0),tV(e,e,t)}function tw(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ex(e),n=ew(e,1/0);return e.length?1===e.length?eY(e[0]):tS(n)(e0(e,r)):eg}var tF=new H(b);function tB(){return tF}var tH=Array.isArray;function tG(e){return 1===e.length&&tH(e[0])?e[0]:e}function tk(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=tG(e);return new H(function(e){var t=0,n=function(){if(t<r.length){var i=void 0;try{i=eY(r[t++])}catch(e){n();return}var o=new W(e,void 0,b,b);i.subscribe(o),o.add(n)}else e.complete()};n()})}function tj(e,t){return e0(Object.entries(e),t)}function tK(e,t){return j(function(r,n){var i=0;r.subscribe(K(n,function(r){return e.call(t,r,i++)&&n.next(r)}))})}function tW(e,t,r){return[tK(t,r)(eY(e)),tK(function(e,n){return!t.call(r,e,n)})(eY(e))]}function tY(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 1===(e=tG(e)).length?eY(e[0]):new H(tQ(e))}function tQ(e){return function(t){for(var r=[],n=function(n){r.push(eY(e[n]).subscribe(K(t,function(e){if(r){for(var i=0;i<r.length;i++)i!==n&&r[i].unsubscribe();r=null}t.next(e)})))},i=0;r&&!t.closed&&i<e.length;i++)n(i)}}function tq(e,t,r){if(null==t&&(t=e,e=0),t<=0)return eg;var n=t+e;return new H(r?function(t){var i=e;return r.schedule(function(){i<n?(t.next(i++),this.schedule()):t.complete()})}:function(t){for(var r=e;r<n&&!t.closed;)t.next(r++);t.complete()})}function tz(e,t){return new H(function(r){var n=e(),i=t(n);return(i?eY(i):eg).subscribe(r),function(){n&&n.unsubscribe()}})}function tX(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=eV(e),n=tG(e);return n.length?new H(function(e){var t=n.map(function(){return[]}),i=n.map(function(){return!1});e.add(function(){t=i=null});for(var o=function(o){eY(n[o]).subscribe(K(e,function(n){if(t[o].push(n),t.every(function(e){return e.length})){var s=t.map(function(e){return e.shift()});e.next(r?r.apply(void 0,l([],c(s))):s),t.some(function(e,t){return!e.length&&i[t]})&&e.complete()}},function(){i[o]=!0,t[o].length||e.complete()}))},s=0;!e.closed&&s<n.length;s++)o(s);return function(){t=i=null}}):eg}function t$(e){return j(function(t,r){var n=!1,i=null,o=null,s=!1,a=function(){if(null==o||o.unsubscribe(),o=null,n){n=!1;var e=i;i=null,r.next(e)}s&&r.complete()},u=function(){o=null,s&&r.complete()};t.subscribe(K(r,function(t){n=!0,i=t,o||eY(e(t)).subscribe(o=K(r,a,u))},function(){s=!0,n&&o&&!o.closed||r.complete()}))})}function tZ(e,t){return void 0===t&&(t=eO),t$(function(){return tV(e,t)})}function tJ(e){return j(function(t,r){var n=[];return t.subscribe(K(r,function(e){return n.push(e)},function(){r.next(n),r.complete()})),eY(e).subscribe(K(r,function(){var e=n;n=[],r.next(e)},b)),function(){n=null}})}function t0(e,t){return void 0===t&&(t=null),t=null!=t?t:e,j(function(r,n){var i=[],o=0;r.subscribe(K(n,function(r){var s,a,c,l,d=null;o++%t==0&&i.push([]);try{for(var h=u(i),_=h.next();!_.done;_=h.next()){var f=_.value;f.push(r),e<=f.length&&(d=null!=d?d:[]).push(f)}}catch(e){s={error:e}}finally{try{_&&!_.done&&(a=h.return)&&a.call(h)}finally{if(s)throw s.error}}if(d)try{for(var p=u(d),T=p.next();!T.done;T=p.next()){var f=T.value;E(i,f),n.next(f)}}catch(e){c={error:e}}finally{try{T&&!T.done&&(l=p.return)&&l.call(p)}finally{if(c)throw c.error}}},function(){var e,t;try{for(var r=u(i),o=r.next();!o.done;o=r.next()){var s=o.value;n.next(s)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}n.complete()},void 0,function(){i=null}))})}function t1(e){for(var t,r,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];var o=null!=(t=ex(n))?t:eO,s=null!=(r=n[0])?r:null,a=n[1]||1/0;return j(function(t,r){var n=[],i=!1,c=function(e){var t=e.buffer;e.subs.unsubscribe(),E(n,e),r.next(t),i&&l()},l=function(){if(n){var t=new p;r.add(t);var i={buffer:[],subs:t};n.push(i),eq(t,o,function(){return c(i)},e)}};null!==s&&s>=0?eq(r,o,l,s,!0):i=!0,l();var d=K(r,function(e){var t,r,i=n.slice();try{for(var o=u(i),s=o.next();!s.done;s=o.next()){var l=s.value,d=l.buffer;d.push(e),a<=d.length&&c(l)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}},function(){for(;null==n?void 0:n.length;)r.next(n.shift().buffer);null==d||d.unsubscribe(),r.complete(),r.unsubscribe()},void 0,function(){return n=null});t.subscribe(d)})}function t2(e,t){return j(function(r,n){var i=[];eY(e).subscribe(K(n,function(e){var r=[];i.push(r);var o=new p;o.add(eY(t(e)).subscribe(K(n,function(){E(i,r),n.next(r),o.unsubscribe()},b)))},b)),r.subscribe(K(n,function(e){var t,r;try{for(var n=u(i),o=n.next();!o.done;o=n.next())o.value.push(e)}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},function(){for(;i.length>0;)n.next(i.shift());n.complete()}))})}function t8(e){return j(function(t,r){var n=null,i=null,o=function(){null==i||i.unsubscribe();var t=n;n=[],t&&r.next(t),eY(e()).subscribe(i=K(r,o,b))};o(),t.subscribe(K(r,function(e){return null==n?void 0:n.push(e)},function(){n&&r.next(n),r.complete()},void 0,function(){return n=i=null}))})}function t9(e,t,r,n,i){return function(o,s){var a=r,u=t,c=0;o.subscribe(K(s,function(t){var r=c++;u=a?e(u,t,r):(a=!0,t),n&&s.next(u)},i&&function(){a&&s.next(u),s.complete()}))}}function t7(e,t){return j(t9(e,t,arguments.length>=2,!1,!0))}var t4=function(e,t){return e.push(t),e};function t5(){return j(function(e,t){t7(t4,[])(e).subscribe(t)})}function t3(e,t){return F(t5(),tO(function(t){return e(t)}),t?tu(t):w)}function t6(e){return t3(tA,e)}var re=t6;function rt(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=eV(e);return r?F(rt.apply(void 0,l([],c(e))),tu(r)):j(function(t,r){tI(l([t],c(tG(e))))(r)})}function rr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return rt.apply(void 0,l([],c(e)))}function rn(e,t){return h(t)?tO(e,t,1):tO(e,1)}function ri(e,t){return h(t)?rn(function(){return e},t):rn(function(){return e})}function ro(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ex(e);return j(function(t,n){tb()(e0(l([t],c(e)),r)).subscribe(n)})}function rs(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ro.apply(void 0,l([],c(e)))}var ra={connector:function(){return new ee}};function ru(e,t){void 0===t&&(t=ra);var r=t.connector;return j(function(t,n){var i=r();eY(e(new H(function(e){return i.subscribe(e)}))).subscribe(n),n.add(t.subscribe(i))})}function rc(e){return t7(function(t,r,n){return!e||e(r,n)?t+1:t},0)}function rl(e){return j(function(t,r){var n=!1,i=null,o=null,s=function(){if(null==o||o.unsubscribe(),o=null,n){n=!1;var e=i;i=null,r.next(e)}};t.subscribe(K(r,function(t){null==o||o.unsubscribe(),n=!0,i=t,o=K(r,s,b),eY(e(t)).subscribe(o)},function(){s(),r.complete()},void 0,function(){i=o=null}))})}function rd(e,t){return void 0===t&&(t=eO),j(function(r,n){var i=null,o=null,s=null,a=function(){if(i){i.unsubscribe(),i=null;var e=o;o=null,n.next(e)}};function u(){var r=s+e,o=t.now();if(o<r){i=this.schedule(void 0,r-o),n.add(i);return}a()}r.subscribe(K(n,function(r){o=r,s=t.now(),i||(i=t.schedule(u,e),n.add(i))},function(){a(),n.complete()},void 0,function(){o=i=null}))})}function rh(e){return j(function(t,r){var n=!1;t.subscribe(K(r,function(e){n=!0,r.next(e)},function(){n||r.next(e),r.complete()}))})}function r_(e){return e<=0?function(){return eg}:j(function(t,r){var n=0;t.subscribe(K(r,function(t){++n<=e&&(r.next(t),e<=n&&r.complete())}))})}function rf(){return j(function(e,t){e.subscribe(K(t,b))})}function rE(e){return ts(function(){return e})}function rp(e,t){return t?function(r){return tm(t.pipe(r_(1),rf()),r.pipe(rp(e)))}:tO(function(t,r){return eY(e(t,r)).pipe(r_(1),rE(t))})}function rT(e,t){void 0===t&&(t=eO);var r=tV(e,t);return rp(function(){return r})}function rA(){return j(function(e,t){e.subscribe(K(t,function(e){return e9(e,t)}))})}function rI(e,t){return j(function(r,n){var i=new Set;r.subscribe(K(n,function(t){var r=e?e(t):t;i.has(r)||(i.add(r),n.next(t))})),t&&eY(t).subscribe(K(n,function(){return i.clear()},b))})}function rv(e,t){return void 0===t&&(t=w),e=null!=e?e:rO,j(function(r,n){var i,o=!0;r.subscribe(K(n,function(r){var s=t(r);(o||!e(i,s))&&(o=!1,i=s,n.next(r))}))})}function rO(e,t){return e===t}function rS(e,t){return rv(function(r,n){return t?t(r[e],n[e]):r[e]===n[e]})}function rb(e){return void 0===e&&(e=rm),j(function(t,r){var n=!1;t.subscribe(K(r,function(e){n=!0,r.next(e)},function(){return n?r.complete():r.error(e())}))})}function rm(){return new e4}function rD(e,t){if(e<0)throw new e6;var r=arguments.length>=2;return function(n){return n.pipe(tK(function(t,r){return r===e}),r_(1),r?rh(t):rb(function(){return new e6}))}}function rN(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){return tm(t,e1.apply(void 0,l([],c(e))))}}function ry(e,t){return j(function(r,n){var i=0;r.subscribe(K(n,function(o){e.call(t,o,i++,r)||(n.next(!1),n.complete())},function(){n.next(!0),n.complete()}))})}function rP(e,t){return t?function(r){return r.pipe(rP(function(r,n){return eY(e(r,n)).pipe(ts(function(e,i){return t(r,e,n,i)}))}))}:j(function(t,r){var n=0,i=null,o=!1;t.subscribe(K(r,function(t){i||(i=K(r,void 0,function(){i=null,o&&r.complete()}),eY(e(t,n++)).subscribe(i))},function(){o=!0,i||r.complete()}))})}function rR(){return rP(w)}var rC=rR;function rg(e,t,r){return void 0===t&&(t=1/0),t=1>(t||0)?1/0:t,j(function(n,i){return tv(n,i,e,t,void 0,!0,r)})}function rL(e){return j(function(t,r){try{t.subscribe(r)}finally{r.add(e)}})}function rM(e,t){return j(rU(e,t,"value"))}function rU(e,t,r){var n="index"===r;return function(r,i){var o=0;r.subscribe(K(i,function(s){var a=o++;e.call(t,s,a,r)&&(i.next(n?a:s),i.complete())},function(){i.next(n?-1:void 0),i.complete()}))}}function rV(e,t){return j(rU(e,t,"index"))}function rx(e,t){var r=arguments.length>=2;return function(n){return n.pipe(e?tK(function(t,r){return e(t,r,n)}):w,r_(1),r?rh(t):rb(function(){return new e4}))}}function rw(e,t,r,n){return j(function(i,o){t&&"function"!=typeof t?(r=t.duration,s=t.element,n=t.connector):s=t;var s,a=new Map,u=function(e){a.forEach(e),e(o)},c=function(e){return u(function(t){return t.error(e)})},l=0,d=!1,h=new W(o,function(t){try{var i=e(t),u=a.get(i);if(!u){a.set(i,u=n?n():new ee);var _,f,E,p=(_=i,f=u,(E=new H(function(e){l++;var t=f.subscribe(e);return function(){t.unsubscribe(),0==--l&&d&&h.unsubscribe()}})).key=_,E);if(o.next(p),r){var T=K(u,function(){u.complete(),null==T||T.unsubscribe()},void 0,void 0,function(){return a.delete(i)});h.add(eY(r(p)).subscribe(T))}}u.next(s?s(t):t)}catch(e){c(e)}},function(){return u(function(e){return e.complete()})},c,function(){return a.clear()},function(){return d=!0,0===l});i.subscribe(h)})}function rF(){return j(function(e,t){e.subscribe(K(t,function(){t.next(!1),t.complete()},function(){t.next(!0),t.complete()}))})}function rB(e){return e<=0?function(){return eg}:j(function(t,r){var n=[];t.subscribe(K(r,function(t){n.push(t),e<n.length&&n.shift()},function(){var e,t;try{for(var i=u(n),o=i.next();!o.done;o=i.next()){var s=o.value;r.next(s)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}r.complete()},void 0,function(){n=null}))})}function rH(e,t){var r=arguments.length>=2;return function(n){return n.pipe(e?tK(function(t,r){return e(t,r,n)}):w,rB(1),r?rh(t):rb(function(){return new e4}))}}function rG(){return j(function(e,t){e.subscribe(K(t,function(e){t.next(e8.createNext(e))},function(){t.next(e8.createComplete()),t.complete()},function(e){t.next(e8.createError(e)),t.complete()}))})}function rk(e){return t7(h(e)?function(t,r){return e(t,r)>0?t:r}:function(e,t){return e>t?e:t})}var rj=tO;function rK(e,t,r){return(void 0===r&&(r=1/0),h(t))?tO(function(){return e},t,r):("number"==typeof t&&(r=t),tO(function(){return e},r))}function rW(e,t,r){return void 0===r&&(r=1/0),j(function(n,i){var o=t;return tv(n,i,function(t,r){return e(o,t,r)},r,function(e){o=e},!1,void 0,function(){return o=null})})}function rY(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ex(e),n=ew(e,1/0);return j(function(t,i){tS(n)(e0(l([t],c(e)),r)).subscribe(i)})}function rQ(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return rY.apply(void 0,l([],c(e)))}function rq(e){return t7(h(e)?function(t,r){return 0>e(t,r)?t:r}:function(e,t){return e<t?e:t})}function rz(e,t){var r=h(e)?e:function(){return e};return h(t)?ru(t,{connector:r}):function(e){return new Q(e,r)}}function rX(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=tG(e);return function(e){return tk.apply(void 0,l([e],c(r)))}}function r$(){return j(function(e,t){var r,n=!1;e.subscribe(K(t,function(e){var i=r;r=e,n&&t.next([i,e]),n=!0}))})}function rZ(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.length;if(0===r)throw Error("list of properties cannot be empty.");return ts(function(t){for(var n=t,i=0;i<r;i++){var o=null==n?void 0:n[e[i]];if(void 0===o)return;n=o}return n})}function rJ(e){return e?function(t){return ru(e)(t)}:function(e){return rz(new ee)(e)}}function r0(e){return function(t){var r=new er(e);return new Q(t,function(){return r})}}function r1(){return function(e){var t=new eo;return new Q(e,function(){return t})}}function r2(e,t,r,n){r&&!h(r)&&(n=r);var i=h(r)?r:void 0;return function(r){return rz(new ei(e,t,n),i)(r)}}function r8(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length?j(function(t,r){tQ(l([t],c(e)))(r)}):w}function r9(e){var t,r,n=1/0;return null!=e&&("object"==typeof e?(n=void 0===(t=e.count)?1/0:t,r=e.delay):n=e),n<=0?function(){return eg}:j(function(e,t){var i,o=0,s=function(){if(null==i||i.unsubscribe(),i=null,null!=r){var e="number"==typeof r?tV(r):eY(r(o)),n=K(t,function(){n.unsubscribe(),a()});e.subscribe(n)}else a()},a=function(){var r=!1;i=e.subscribe(K(t,void 0,function(){++o<n?i?s():r=!0:t.complete()})),r&&s()};a()})}function r7(e){return j(function(t,r){var n,i,o=!1,s=!1,a=!1,u=function(){return a&&s&&(r.complete(),!0)},c=function(){a=!1,n=t.subscribe(K(r,void 0,function(){a=!0,u()||(!i&&eY(e(i=new ee)).subscribe(K(r,function(){n?c():o=!0},function(){s=!0,u()})),i).next()})),o&&(n.unsubscribe(),n=null,o=!1,c())};c()})}function r4(e){void 0===e&&(e=1/0);var t=e&&"object"==typeof e?e:{count:e},r=t.count,n=void 0===r?1/0:r,i=t.delay,o=t.resetOnSuccess,s=void 0!==o&&o;return n<=0?w:j(function(e,t){var r,o=0,a=function(){var u=!1;r=e.subscribe(K(t,function(e){s&&(o=0),t.next(e)},void 0,function(e){if(o++<n){var s=function(){r?(r.unsubscribe(),r=null,a()):u=!0};if(null!=i){var c="number"==typeof i?tV(i):eY(i(e,o)),l=K(t,function(){l.unsubscribe(),s()},function(){t.complete()});c.subscribe(l)}else s()}else t.error(e)})),u&&(r.unsubscribe(),r=null,a())};a()})}function r5(e){return j(function(t,r){var n,i,o=!1,s=function(){n=t.subscribe(K(r,void 0,void 0,function(t){i||eY(e(i=new ee)).subscribe(K(r,function(){return n?s():o=!0})),i&&i.next(t)})),o&&(n.unsubscribe(),n=null,o=!1,s())};s()})}function r3(e){return j(function(t,r){var n=!1,i=null;t.subscribe(K(r,function(e){n=!0,i=e})),eY(e).subscribe(K(r,function(){if(n){n=!1;var e=i;i=null,r.next(e)}},b))})}function r6(e,t){return void 0===t&&(t=eO),r3(tx(e,t))}function ne(e,t){return j(t9(e,t,arguments.length>=2,!0))}function nt(e,t){return void 0===t&&(t=function(e,t){return e===t}),j(function(r,n){var i=nr(),o=nr(),s=function(e){n.next(e),n.complete()},a=function(e,r){var i=K(n,function(n){var i=r.buffer,o=r.complete;0===i.length?o?s(!1):e.buffer.push(n):t(n,i.shift())||s(!1)},function(){e.complete=!0;var t=r.complete,n=r.buffer;t&&s(0===n.length),null==i||i.unsubscribe()});return i};r.subscribe(a(i,o)),eY(e).subscribe(a(o,i))})}function nr(){return{buffer:[],complete:!1}}function nn(e){void 0===e&&(e={});var t=e.connector,r=void 0===t?function(){return new ee}:t,n=e.resetOnError,i=void 0===n||n,o=e.resetOnComplete,s=void 0===o||o,a=e.resetOnRefCountZero,u=void 0===a||a;return function(e){var t,n,o,a=0,c=!1,l=!1,d=function(){null==n||n.unsubscribe(),n=void 0},h=function(){d(),t=o=void 0,c=l=!1},_=function(){var e=t;h(),null==e||e.unsubscribe()};return j(function(e,f){a++,l||c||d();var E=o=null!=o?o:r();f.add(function(){0!=--a||l||c||(n=ni(_,u))}),E.subscribe(f),!t&&a>0&&(t=new L({next:function(e){return E.next(e)},error:function(e){l=!0,d(),n=ni(h,i,e),E.error(e)},complete:function(){c=!0,d(),n=ni(h,s),E.complete()}}),eY(e).subscribe(t))})(e)}}function ni(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!0===t)return void e();if(!1!==t){var i=new L({next:function(){i.unsubscribe(),e()}});return eY(t.apply(void 0,l([],c(r)))).subscribe(i)}}function no(e,t,r){var n,i,o,s,a=!1;return e&&"object"==typeof e?(s=void 0===(n=e.bufferSize)?1/0:n,t=void 0===(i=e.windowTime)?1/0:i,a=void 0!==(o=e.refCount)&&o,r=e.scheduler):s=null!=e?e:1/0,nn({connector:function(){return new ei(s,t,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:a})}function ns(e){return j(function(t,r){var n,i=!1,o=!1,s=0;t.subscribe(K(r,function(a){o=!0,(!e||e(a,s++,t))&&(i&&r.error(new tt("Too many matching values")),i=!0,n=a)},function(){i?(r.next(n),r.complete()):r.error(o?new te("No matching values"):new e4)}))})}function na(e){return tK(function(t,r){return e<=r})}function nu(e){return e<=0?w:j(function(t,r){var n=Array(e),i=0;return t.subscribe(K(r,function(t){var o=i++;if(o<e)n[o]=t;else{var s=o%e,a=n[s];n[s]=t,r.next(a)}})),function(){n=null}})}function nc(e){return j(function(t,r){var n=!1,i=K(r,function(){null==i||i.unsubscribe(),n=!0},b);eY(e).subscribe(i),t.subscribe(K(r,function(e){return n&&r.next(e)}))})}function nl(e){return j(function(t,r){var n=!1,i=0;t.subscribe(K(r,function(t){return(n||(n=!e(t,i++)))&&r.next(t)}))})}function nd(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=ex(e);return j(function(t,n){(r?tm(e,t,r):tm(e,t)).subscribe(n)})}function nh(e,t){return j(function(r,n){var i=null,o=0,s=!1,a=function(){return s&&!i&&n.complete()};r.subscribe(K(n,function(r){null==i||i.unsubscribe();var s=0,u=o++;eY(e(r,u)).subscribe(i=K(n,function(e){return n.next(t?t(r,e,u,s++):e)},function(){i=null,a()}))},function(){s=!0,a()}))})}function n_(){return nh(w)}function nf(e,t){return h(t)?nh(function(){return e},t):nh(function(){return e})}function nE(e,t){return j(function(r,n){var i=t;return nh(function(t,r){return e(i,t,r)},function(e,t){return i=t,t})(r).subscribe(n),function(){i=null}})}function np(e){return j(function(t,r){eY(e).subscribe(K(r,function(){return r.complete()},b)),r.closed||t.subscribe(r)})}function nT(e,t){return void 0===t&&(t=!1),j(function(r,n){var i=0;r.subscribe(K(n,function(r){var o=e(r,i++);(o||t)&&n.next(r),o||n.complete()}))})}function nA(e,t,r){var n=h(e)||t||r?{next:e,error:t,complete:r}:e;return n?j(function(e,t){null==(r=n.subscribe)||r.call(n);var r,i=!0;e.subscribe(K(t,function(e){var r;null==(r=n.next)||r.call(n,e),t.next(e)},function(){var e;i=!1,null==(e=n.complete)||e.call(n),t.complete()},function(e){var r;i=!1,null==(r=n.error)||r.call(n,e),t.error(e)},function(){var e,t;i&&(null==(e=n.unsubscribe)||e.call(n)),null==(t=n.finalize)||t.call(n)}))}):w}function nI(e,t){return j(function(r,n){var i=null!=t?t:{},o=i.leading,s=void 0===o||o,a=i.trailing,u=void 0!==a&&a,c=!1,l=null,d=null,h=!1,_=function(){null==d||d.unsubscribe(),d=null,u&&(p(),h&&n.complete())},f=function(){d=null,h&&n.complete()},E=function(t){return d=eY(e(t)).subscribe(K(n,_,f))},p=function(){if(c){c=!1;var e=l;l=null,n.next(e),h||E(e)}};r.subscribe(K(n,function(e){c=!0,l=e,d&&!d.closed||(s?p():E(e))},function(){h=!0,u&&c&&d&&!d.closed||n.complete()}))})}function nv(e,t,r){void 0===t&&(t=eO);var n=tV(e,t);return nI(function(){return n},r)}function nO(e){return void 0===e&&(e=eO),j(function(t,r){var n=e.now();t.subscribe(K(r,function(t){var i=e.now(),o=i-n;n=i,r.next(new nS(t,o))}))})}var nS=function(e,t){this.value=e,this.interval=t};function nb(e,t,r){var n,i,o;if(r=null!=r?r:eS,tr(e)?n=e:"number"==typeof e&&(i=e),t)o=function(){return t};else throw TypeError("No observable provided to switch to");if(null==n&&null==i)throw TypeError("No timeout provided.");return ti({first:n,each:i,scheduler:r,with:o})}function nm(e){return void 0===e&&(e=en),ts(function(t){return{value:t,timestamp:e.now()}})}function nD(e){return j(function(t,r){var n=new ee;r.next(n.asObservable());var i=function(e){n.error(e),r.error(e)};return t.subscribe(K(r,function(e){return null==n?void 0:n.next(e)},function(){n.complete(),r.complete()},i)),eY(e).subscribe(K(r,function(){n.complete(),r.next(n=new ee)},b,i)),function(){null==n||n.unsubscribe(),n=null}})}function nN(e,t){void 0===t&&(t=0);var r=t>0?t:e;return j(function(t,n){var i=[new ee],o=0;n.next(i[0].asObservable()),t.subscribe(K(n,function(t){try{for(var s,a,c=u(i),l=c.next();!l.done;l=c.next())l.value.next(t)}catch(e){s={error:e}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(s)throw s.error}}var d=o-e+1;if(d>=0&&d%r==0&&i.shift().complete(),++o%r==0){var h=new ee;i.push(h),n.next(h.asObservable())}},function(){for(;i.length>0;)i.shift().complete();n.complete()},function(e){for(;i.length>0;)i.shift().error(e);n.error(e)},function(){i=null}))})}function ny(e){for(var t,r,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];var o=null!=(t=ex(n))?t:eO,s=null!=(r=n[0])?r:null,a=n[1]||1/0;return j(function(t,r){var n=[],i=!1,u=function(e){var t=e.window,r=e.subs;t.complete(),r.unsubscribe(),E(n,e),i&&c()},c=function(){if(n){var t=new p;r.add(t);var i=new ee,s={window:i,subs:t,seen:0};n.push(s),r.next(i.asObservable()),eq(t,o,function(){return u(s)},e)}};null!==s&&s>=0?eq(r,o,c,s,!0):i=!0,c();var l=function(e){return n.slice().forEach(e)},d=function(e){l(function(t){return e(t.window)}),e(r),r.unsubscribe()};return t.subscribe(K(r,function(e){l(function(t){t.window.next(e),a<=++t.seen&&u(t)})},function(){return d(function(e){return e.complete()})},function(e){return d(function(t){return t.error(e)})})),function(){n=null}})}function nP(e,t){return j(function(r,n){var i=[],o=function(e){for(;0<i.length;)i.shift().error(e);n.error(e)};eY(e).subscribe(K(n,function(e){var r,s=new ee;i.push(s);var a=new p;try{r=eY(t(e))}catch(e){o(e);return}n.next(s.asObservable()),a.add(r.subscribe(K(n,function(){E(i,s),s.complete(),a.unsubscribe()},b,o)))},b)),r.subscribe(K(n,function(e){var t,r,n=i.slice();try{for(var o=u(n),s=o.next();!s.done;s=o.next())s.value.next(e)}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}},function(){for(;0<i.length;)i.shift().complete();n.complete()},o,function(){for(;0<i.length;)i.shift().unsubscribe()}))})}function nR(e){return j(function(t,r){var n,i,o=function(e){n.error(e),r.error(e)},s=function(){var t;null==i||i.unsubscribe(),null==n||n.complete(),n=new ee,r.next(n.asObservable());try{t=eY(e())}catch(e){o(e);return}t.subscribe(i=K(r,s,s,o))};s(),t.subscribe(K(r,function(e){return n.next(e)},function(){n.complete(),r.complete()},o,function(){null==i||i.unsubscribe(),n=null}))})}function nC(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=eV(e);return j(function(t,n){for(var i=e.length,o=Array(i),s=e.map(function(){return!1}),a=!1,u=function(t){eY(e[t]).subscribe(K(n,function(e){o[t]=e,!a&&!s[t]&&(s[t]=!0,(a=s.every(w))&&(s=null))},b))},d=0;d<i;d++)u(d);t.subscribe(K(n,function(e){if(a){var t=l([e],c(o));n.next(r?r.apply(void 0,l([],c(t))):t)}}))})}function ng(e){return t3(tX,e)}function nL(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return j(function(t,r){tX.apply(void 0,l([t],c(e))).subscribe(r)})}function nM(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return nL.apply(void 0,l([],c(e)))}},79898:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.EventName=void 0,function(e){e.all="all",e.connected="connected",e.disconnected="disconnected",e.server="server",e.error="error",e.info="info",e.received="received",e.sent="sent",e.result="result",e.accountDownloadEnd="accountDownloadEnd",e.accountSummary="accountSummary",e.accountSummaryEnd="accountSummaryEnd",e.accountUpdateMulti="accountUpdateMulti",e.accountUpdateMultiEnd="accountUpdateMultiEnd",e.bondContractDetails="bondContractDetails",e.commissionReport="commissionReport",e.completedOrder="completedOrder",e.completedOrdersEnd="completedOrdersEnd",e.connectionClosed="connectionClosed",e.contractDetails="contractDetails",e.contractDetailsEnd="contractDetailsEnd",e.currentTime="currentTime",e.deltaNeutralValidation="deltaNeutralValidation",e.tickSnapshotEnd="tickSnapshotEnd",e.marketDataType="marketDataType",e.displayGroupList="displayGroupList",e.displayGroupUpdated="displayGroupUpdated",e.execDetails="execDetails",e.execDetailsEnd="execDetailsEnd",e.familyCodes="familyCodes",e.contractDescriptions="contractDescriptions",e.fundamentalData="fundamentalData",e.headTimestamp="headTimestamp",e.histogramData="histogramData",e.historicalDataUpdate="historicalDataUpdate",e.historicalNews="historicalNews",e.historicalNewsEnd="historicalNewsEnd",e.historicalTicks="historicalTicks",e.historicalTicksBidAsk="historicalTicksBidAsk",e.historicalTicksLast="historicalTicksLast",e.managedAccounts="managedAccounts",e.marketRule="marketRule",e.mktDepthExchanges="mktDepthExchanges",e.newsArticle="newsArticle",e.newsProviders="newsProviders",e.nextValidId="nextValidId",e.openOrder="openOrder",e.openOrderEnd="openOrderEnd",e.orderBound="orderBound",e.orderStatus="orderStatus",e.pnl="pnl",e.pnlSingle="pnlSingle",e.position="position",e.positionEnd="positionEnd",e.positionMulti="positionMulti",e.positionMultiEnd="positionMultiEnd",e.realtimeBar="realtimeBar",e.receiveFA="receiveFA",e.replaceFAEnd="replaceFAEnd",e.rerouteMktDataReq="rerouteMktDataReq",e.rerouteMktDepthReq="rerouteMktDepthReq",e.scannerData="scannerData",e.scannerDataEnd="scannerDataEnd",e.scannerParameters="scannerParameters",e.securityDefinitionOptionParameter="securityDefinitionOptionParameter",e.securityDefinitionOptionParameterEnd="securityDefinitionOptionParameterEnd",e.smartComponents="smartComponents",e.softDollarTiers="softDollarTiers",e.symbolSamples="symbolSamples",e.tickByTickAllLast="tickByTickAllLast",e.tickByTickBidAsk="tickByTickBidAsk",e.tickByTickMidPoint="tickByTickMidPoint",e.tickEFP="tickEFP",e.tickGeneric="tickGeneric",e.tickNews="tickNews",e.tickOptionComputation="tickOptionComputation",e.tickPrice="tickPrice",e.tickReqParams="tickReqParams",e.tickSize="tickSize",e.tickString="tickString",e.updateAccountTime="updateAccountTime",e.updateAccountValue="updateAccountValue",e.updatePortfolio="updatePortfolio",e.updateMktDepth="updateMktDepth",e.updateMktDepthL2="updateMktDepthL2",e.updateNewsBulletin="updateNewsBulletin",e.historicalData="historicalData",e.wshMetaData="wshMetaData",e.wshEventData="wshEventData",e.historicalSchedule="historicalSchedule",e.userInfo="userInfo"}(r||(t.EventName=r={}))},80127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNextLogger=void 0;let n=r(90742);class i{constructor(e){this.logger=e,this._logLevel=n.LogLevel.SYSTEM}get logLevel(){return this._logLevel}set logLevel(e){this._logLevel=e}debug(e,t){this._logLevel>=n.LogLevel.DETAIL&&this.logger.debug(e,t)}info(e,t){this._logLevel>=n.LogLevel.INFO&&this.logger.info(e,t)}warn(e,t){this._logLevel>=n.LogLevel.WARN&&this.logger.warn(e,t)}error(e,t){this._logLevel>=n.LogLevel.ERROR&&this.logger.error(e,t)}}t.IBApiNextLogger=i},80848:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.of=void 0;var n=r(66389),i=r(6747);t.of=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n.popScheduler(e);return i.from(e,r)}},81054:e=>{"use strict";e.exports=JSON.parse('{"name":"dotenv","version":"16.5.0","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}')},81700:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.subscribeOn=void 0;var n=r(96829);t.subscribeOn=function(e,t){return void 0===t&&(t=0),n.operate(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}},81773:(e,t,r)=>{"use strict";var n=function(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){var u=[o,a];if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&u[0]?n.return:u[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,u[1])).done)return i;switch(n=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,n=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){s=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){s.label=u[1];break}if(6===u[0]&&s.label<i[1]){s.label=i[1],i=u;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(u);break}i[2]&&s.ops.pop(),s.trys.pop();continue}u=t.call(e,s)}catch(e){u=[6,e],n=0}finally{r=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},i=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof o?o(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,i){var o,s,a;o=n,s=i,a=(t=e[r](t)).done,Promise.resolve(t.value).then(function(e){o({value:e,done:a})},s)})}}},o=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.fromReadableStreamLike=t.fromAsyncIterable=t.fromIterable=t.fromPromise=t.fromArrayLike=t.fromInteropObservable=t.innerFrom=void 0;var s=r(44868),a=r(66921),u=r(57248),c=r(91602),l=r(14092),d=r(61854),h=r(82952),_=r(70791),f=r(17886),E=r(64517),p=r(63701);function T(e){return new u.Observable(function(t){var r=e[p.observable]();if(f.isFunction(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")})}function A(e){return new u.Observable(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function I(e){return new u.Observable(function(t){e.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,E.reportUnhandledError)})}function v(e){return new u.Observable(function(t){var r,n;try{for(var i=o(e),s=i.next();!s.done;s=i.next()){var a=s.value;if(t.next(a),t.closed)return}}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}t.complete()})}function O(e){return new u.Observable(function(t){(function(e,t){var r,o,s,a,u,c,l,d;return u=this,c=void 0,l=void 0,d=function(){var u;return n(this,function(n){switch(n.label){case 0:n.trys.push([0,5,6,11]),r=i(e),n.label=1;case 1:return[4,r.next()];case 2:if((o=n.sent()).done)return[3,4];if(u=o.value,t.next(u),t.closed)return[2];n.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return s={error:n.sent()},[3,11];case 6:if(n.trys.push([6,,9,10]),!(o&&!o.done&&(a=r.return)))return[3,8];return[4,a.call(r)];case 7:n.sent(),n.label=8;case 8:return[3,10];case 9:if(s)throw s.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})},new(l||(l=Promise))(function(e,t){function r(e){try{i(d.next(e))}catch(e){t(e)}}function n(e){try{i(d.throw(e))}catch(e){t(e)}}function i(t){var i;t.done?e(t.value):((i=t.value)instanceof l?i:new l(function(e){e(i)})).then(r,n)}i((d=d.apply(u,c||[])).next())})})(e,t).catch(function(e){return t.error(e)})})}function S(e){return O(_.readableStreamLikeToAsyncGenerator(e))}t.innerFrom=function(e){if(e instanceof u.Observable)return e;if(null!=e){if(c.isInteropObservable(e))return T(e);if(s.isArrayLike(e))return A(e);if(a.isPromise(e))return I(e);if(l.isAsyncIterable(e))return O(e);if(h.isIterable(e))return v(e);if(_.isReadableStreamLike(e))return S(e)}throw d.createInvalidObservableTypeError(e)},t.fromInteropObservable=T,t.fromArrayLike=A,t.fromPromise=I,t.fromIterable=v,t.fromAsyncIterable=O,t.fromReadableStreamLike=S},82258:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.raceWith=void 0;var o=r(71344),s=r(96829),a=r(56032);t.raceWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length?s.operate(function(t,r){o.raceInit(i([t],n(e)))(r)}):a.identity}},82570:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.count=void 0;var n=r(4759);t.count=function(e){return n.reduce(function(t,r,n){return!e||e(r,n)?t+1:t},0)}},82794:(e,t,r)=>{"use strict";var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.ReplaySubject=void 0;var i=r(1937),o=r(87816);t.ReplaySubject=function(e){function t(t,r,n){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===n&&(n=o.dateTimestampProvider);var i=e.call(this)||this;return i._bufferSize=t,i._windowTime=r,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=r===1/0,i._bufferSize=Math.max(1,t),i._windowTime=Math.max(1,r),i}return n(t,e),t.prototype.next=function(t){var r=this.isStopped,n=this._buffer,i=this._infiniteTimeWindow,o=this._timestampProvider,s=this._windowTime;!r&&(n.push(t),i||n.push(o.now()+s)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,n=this._buffer.slice(),i=0;i<n.length&&!e.closed;i+=r?1:2)e.next(n[i]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,i=(n?1:2)*e;if(e<1/0&&i<r.length&&r.splice(0,r.length-i),!n){for(var o=t.now(),s=0,a=1;a<r.length&&r[a]<=o;a+=2)s=a;s&&r.splice(0,s+1)}},t}(i.Subject)},82952:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isIterable=void 0;var n=r(84968),i=r(17886);t.isIterable=function(e){return i.isFunction(null==e?void 0:e[n.iterator])}},83364:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PercentChangeCondition=void 0;let n=r(48909);class i{constructor(e,t,r,i,o){this.percent=e,this.conId=t,this.exchange=r,this.isMore=i,this.conjunctionConnection=o,this.type=n.OrderConditionType.PercentChange}get strValue(){return""+this.percent}}t.PercentChangeCondition=i,t.default=i},83979:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.bufferToggle=void 0;var i=r(46124),o=r(96829),s=r(81773),a=r(28719),u=r(63410),c=r(1071);t.bufferToggle=function(e,t){return o.operate(function(r,o){var l=[];s.innerFrom(e).subscribe(a.createOperatorSubscriber(o,function(e){var r=[];l.push(r);var n=new i.Subscription;n.add(s.innerFrom(t(e)).subscribe(a.createOperatorSubscriber(o,function(){c.arrRemove(l,r),o.next(r),n.unsubscribe()},u.noop)))},u.noop)),r.subscribe(a.createOperatorSubscriber(o,function(e){var t,r;try{for(var i=n(l),o=i.next();!o.done;o=i.next())o.value.push(e)}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},function(){for(;l.length>0;)o.next(l.shift());o.complete()}))})}},84310:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ExecutionCondition=void 0;let n=r(48909);class i{constructor(e,t,r,i){this.exchange=e,this.secType=t,this.symbol=r,this.conjunctionConnection=i,this.type=n.OrderConditionType.Execution}}t.ExecutionCondition=i,t.default=i},84805:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.retryWhen=void 0;var n=r(81773),i=r(1937),o=r(96829),s=r(28719);t.retryWhen=function(e){return o.operate(function(t,r){var o,a,u=!1,c=function(){o=t.subscribe(s.createOperatorSubscriber(r,void 0,void 0,function(t){a||(a=new i.Subject,n.innerFrom(e(a)).subscribe(s.createOperatorSubscriber(r,function(){return o?c():u=!0}))),a&&a.next(t)})),u&&(o.unsubscribe(),o=null,u=!1,c())};c()})}},84968:(e,t)=>{"use strict";function r(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}Object.defineProperty(t,"__esModule",{value:!0}),t.iterator=t.getSymbolIterator=void 0,t.getSymbolIterator=r,t.iterator=r()},85249:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.concat=void 0;var o=r(96829),s=r(77982),a=r(66389),u=r(6747);t.concat=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a.popScheduler(e);return o.operate(function(t,o){s.concatAll()(u.from(i([t],n(e)),r)).subscribe(o)})}},85632:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.interval=void 0;var n=r(97847),i=r(21574);t.interval=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=n.asyncScheduler),e<0&&(e=0),i.timer(e,e,t)}},85819:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.FADataType=void 0,function(e){e[e.NA=0]="NA",e[e.GROUPS=1]="GROUPS",e[e.PROFILES=2]="PROFILES",e[e.ALIASES=3]="ALIASES"}(r||(t.FADataType=r={})),t.default=r},86059:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.merge=void 0;var o=r(96829),s=r(87820),a=r(66389),u=r(6747);t.merge=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a.popScheduler(e),c=a.popNumber(e,1/0);return o.operate(function(t,o){s.mergeAll(c)(u.from(i([t],n(e)),r)).subscribe(o)})}},86190:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OptionType=void 0,function(e){e.Put="P",e.Call="C"}(r||(t.OptionType=r={})),t.default=r},86281:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.multicast=void 0;var n=r(55100),i=r(17886),o=r(94165);t.multicast=function(e,t){var r=i.isFunction(e)?e:function(){return e};return i.isFunction(t)?o.connect(t,{connector:r}):function(e){return new n.ConnectableObservable(e,r)}}},86525:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.first=void 0;var n=r(46889),i=r(45007),o=r(90580),s=r(17476),a=r(98193),u=r(56032);t.first=function(e,t){var r=arguments.length>=2;return function(c){return c.pipe(e?i.filter(function(t,r){return e(t,r,c)}):u.identity,o.take(1),r?s.defaultIfEmpty(t):a.throwIfEmpty(function(){return new n.EmptyError}))}}},86767:function(e,t,r){"use strict";var n,i=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return(n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&i(t,e,r[s]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiAutoConnection=void 0;let a=r(79249),u=s(r(90742)),c="IBApiAutoConnection";class l extends u.default{constructor(e,t,r,n){super(n),this.reconnectInterval=e,this.watchdogInterval=t,this.logger=r,this.options=n,this.autoReconnectEnabled=!0,this._connectionState=new a.BehaviorSubject(u.ConnectionState.Disconnected),this.on(u.EventName.connected,()=>this.onConnected()),this.on(u.EventName.disconnected,()=>this.onDisconnected()),this.on(u.EventName.received,()=>this.lastDataIngressTm=Date.now()),this.on(u.EventName.error,(e,t)=>{t===u.ErrorCode.CONNECT_FAIL&&this.onDisconnected()}),this.on(u.EventName.currentTime,()=>this.lastDataIngressTm=Date.now())}get connectionState(){return this._connectionState}connect(e){return this.autoReconnectEnabled=!0,this.fixedClientId=e,this.currentClientId=(void 0===e?this.options?.clientId:e)??Math.floor(32766*Math.random())+1,this._connectionState.getValue()===u.ConnectionState.Disconnected&&(this._connectionState.next(u.ConnectionState.Connecting),this.logger.info(c,`Connecting to TWS with client id ${this.currentClientId}`),super.connect(this.currentClientId)),this}disconnect(){return this.autoReconnectEnabled=!1,this._connectionState.getValue()!==u.ConnectionState.Disconnected&&(this.logger.info(c,`Disconnecting client id ${this.currentClientId} from TWS.`),this._connectionState.next(u.ConnectionState.Disconnected),this.isConnected&&super.disconnect()),this}onConnected(){this._connectionState.getValue()!==u.ConnectionState.Connected&&(this._connectionState.next(u.ConnectionState.Connected),this.logger.info(c,`Successfully connected to TWS with client id ${this.currentClientId}.`),this.stopReConnectTimer(),this.runWatchdog())}reConnect(){this._connectionState.getValue()===u.ConnectionState.Disconnected&&this.autoReconnectEnabled&&(this._connectionState.next(u.ConnectionState.Connecting),this.currentClientId=void 0!==this.fixedClientId?this.fixedClientId:this.currentClientId+1,this.logger.info(c,`Re-Connecting to TWS with client id ${this.currentClientId}`),super.disconnect(),super.connect(this.currentClientId))}runReConnectTimer(){this.reconnectInterval&&this.autoReconnectEnabled&&(this.logger.info(c,`Re-Connecting to TWS in ${this.reconnectInterval/1e3}s...`),this.reconnectionTimeout&&clearTimeout(this.reconnectionTimeout),this.reconnectionTimeout=setTimeout(()=>{this.reConnect()},this.reconnectInterval))}stopReConnectTimer(){void 0!==this.reconnectionTimeout&&(clearTimeout(this.reconnectionTimeout),delete this.reconnectionTimeout)}runWatchdog(){this.watchdogInterval&&!this.connectionWatchdogTimeout&&(this.logger.debug(c,`Starting connection watchdog with ${this.watchdogInterval}ms interval.`),this.connectionWatchdogTimeout=setInterval(()=>{let e=!1;void 0===this.lastDataIngressTm?e=!0:Date.now()-this.lastDataIngressTm>this.watchdogInterval&&(e=!0),e&&!this.isConnected&&(this.logger.debug(c,"Connection watchdog timeout. Dropping connection."),this.onDisconnected()),this.reqCurrentTime()},this.watchdogInterval/2))}stopWatchdog(){void 0!==this.connectionWatchdogTimeout&&(clearInterval(this.connectionWatchdogTimeout),delete this.connectionWatchdogTimeout)}onDisconnected(){this.logger.debug(c,"onDisconnected()"),this.isConnected&&(this.logger.debug(c,`Disconnecting client id ${this.currentClientId} from TWS (state-sync).`),this.disconnect(),this.autoReconnectEnabled=!0),this._connectionState.getValue()!==u.ConnectionState.Disconnected&&this._connectionState.next(u.ConnectionState.Disconnected),this.stopWatchdog(),this.runReConnectTimer()}}t.IBApiAutoConnection=l},86940:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.BarSizeSetting=void 0,function(e){e.SECONDS_ONE="1 secs",e.SECONDS_FIVE="5 secs",e.SECONDS_TEN="10 secs",e.SECONDS_FIFTEEN="15 secs",e.SECONDS_THIRTY="30 secs",e.MINUTES_ONE="1 min",e.MINUTES_TWO="2 mins",e.MINUTES_THREE="3 mins",e.MINUTES_FIVE="5 mins",e.MINUTES_TEN="10 mins",e.MINUTES_FIFTEEN="15 mins",e.MINUTES_TWENTY="20 mins",e.MINUTES_THIRTY="30 mins",e.HOURS_ONE="1 hour",e.HOURS_TWO="2 hours",e.HOURS_THREE="3 hours",e.HOURS_FOUR="4 hours",e.HOURS_EIGHT="8 hours",e.DAYS_ONE="1 day",e.WEEKS_ONE="1 week",e.MONTHS_ONE="1 month"}(r||(t.BarSizeSetting=r={})),t.default=r},87198:(e,t)=>{"use strict";var r,n,i;Object.defineProperty(t,"__esModule",{value:!0}),t.ScanCode=t.Instrument=t.LocationCode=void 0,function(e){e.BOND_US="BOND.US",e.EFP="EFP",e.FUT_EU_BELFOX="FUT.EU.BELFOX",e.FUT_EU_FTA="FUT.EU.FTA",e.FUT_EU_IDEM="FUT.EU.IDEM",e.FUT_EU_LIFFE="FUT.EU.LIFFE",e.FUT_EU_MEFFRV="FUT.EU.MEFFRV",e.FUT_EU_MONEP="FUT.EU.MONEP",e.FUT_EU="FUT.EU",e.FUT_HK_HKFE="FUT.HK.HKFE",e.FUT_HK_JAPAN="FUT.HK.JAPAN",e.FUT_HK_KSE="FUT.HK.KSE",e.FUT_HK_NSE="FUT.HK.NSE",e.FUT_HK_OSE_JPN="FUT.HK.OSE.JPN",e.FUT_HK_SGX="FUT.HK.SGX",e.FUT_HK_SNFE="FUT.HK.SNFE",e.FUT_HK_TSE_JPN="FUT.HK.TSE.JPN",e.FUT_HK="FUT.HK",e.FUT_IPE="FUT.IPE",e.FUT_NA_CDE="FUT.NA.CDE",e.FUT_NA="FUT.NA",e.FUT_NYBOT="FUT.NYBOT",e.FUT_NYSELIFFE="FUT.NYSELIFFE",e.FUT_US="FUT.US",e.IND_EU_BELFOX="IND.EU.BELFOX",e.IND_EU_FTA="IND.EU.FTA",e.IND_EU_LIFFE="IND.EU.LIFFE",e.IND_EU_MONEP="IND.EU.MONEP",e.IND_EU="IND.EU",e.IND_HK_HKFE="IND.HK.HKFE",e.IND_HK_JAPAN="IND.HK.JAPAN",e.IND_HK_KSE="IND.HK.KSE",e.IND_HK_NSE="IND.HK.NSE",e.IND_HK_OSE_JPN="IND.HK.OSE.JPN",e.IND_HK_SGX="IND.HK.SGX",e.IND_HK_SNFE="IND.HK.SNFE",e.IND_HK_TSE_JPN="IND.HK.TSE.JPN",e.IND_HK="IND.HK",e.IND_US="IND.US",e.SLB_AQS="SLB.AQS",e.STK_AMEX="STK.AMEX",e.STK_ARCA="STK.ARCA",e.STK_EU_AEB="STK.EU.AEB",e.STK_EU_BM="STK.EU.BM",e.STK_EU_BVME="STK.EU.BVME",e.STK_EU_EBS="STK.EU.EBS",e.STK_EU_IBIS="STK.EU.IBIS",e.STK_EU_IBIS_ETF="STK.EU.IBIS-ETF",e.STK_EU_IBIS_EUSTARS="STK.EU.IBIS-EUSTARS",e.STK_EU_IBIS_NEWX="STK.EU.IBIS-NEWX",e.STK_EU_IBIS_USSTARS="STK.EU.IBIS-USSTARS",e.STK_EU_IBIS_XETRA="STK.EU.IBIS-XETRA",e.STK_EU_LSE="STK.EU.LSE",e.STK_EU_SBF="STK.EU.SBF",e.STK_EU_SBVM="STK.EU.SBVM",e.STK_EU_SFB="STK.EU.SFB",e.STK_EU_SWISS="STK.EU.SWISS",e.STK_EU_VIRTX="STK.EU.VIRTX",e.STK_EU="STK.EU",e.STK_HK_ASX="STK.HK.ASX",e.STK_HK_NSE="STK.HK.NSE",e.STK_HK_SEHK="STK.HK.SEHK",e.STK_HK_SGX="STK.HK.SGX",e.STK_HK_TSE_JPN="STK.HK.TSE.JPN",e.STK_HK="STK.HK",e.STK_NA_CANADA="STK.NA.CANADA",e.STK_NA_TSE="STK.NA.TSE",e.STK_NA_VENTURE="STK.NA.VENTURE",e.STK_NA="STK.NA",e.STK_NASDAQ_NMS="STK.NASDAQ.NMS",e.STK_NASDAQ_SCM="STK.NASDAQ.SCM",e.STK_NASDAQ="STK.NASDAQ",e.STK_NYSE="STK.NYSE",e.STK_OTCBB="STK.OTCBB",e.STK_PINK="STK.PINK",e.STK_US_MAJOR="STK.US.MAJOR",e.STK_US_MINOR="STK.US.MINOR",e.STK_US="STK.US",e.WAR_EU_ALL="WAR.EU.ALL"}(r||(t.LocationCode=r={})),function(e){e.STK="STK",e.BOND="BOND",e.EFP="EFP",e.FUT_EU="FUT.EU",e.FUT_HK="FUT.HK",e.FUT_NA="FUT.NA",e.FUT_US="FUT.US",e.IND_EU="IND.EU",e.IND_HK="IND.HK",e.IND_US="IND.US",e.PMONITOR="PMONITOR",e.PMONITORM="PMONITORM",e.SLB_US="SLB.US",e.STOCK_EU="STOCK.EU",e.STOCK_HK="STOCK.HK",e.STOCK_NA="STOCK.NA",e.WAR_EU="WAR.EU"}(n||(t.Instrument=n={})),function(e){e[e.TOP_PERC_GAIN=0]="TOP_PERC_GAIN",e[e.TOP_PERC_LOSE=1]="TOP_PERC_LOSE",e[e.MOST_ACTIVE=2]="MOST_ACTIVE",e[e.ALL_SYMBOLS_ASC=3]="ALL_SYMBOLS_ASC",e[e.ALL_SYMBOLS_DESC=4]="ALL_SYMBOLS_DESC",e[e.BOND_CUSIP_AZ=5]="BOND_CUSIP_AZ",e[e.BOND_CUSIP_ZA=6]="BOND_CUSIP_ZA",e[e.FAR_MATURITY_DATE=7]="FAR_MATURITY_DATE",e[e.HALTED=8]="HALTED",e[e.HIGH_BOND_ASK_CURRENT_YIELD_ALL=9]="HIGH_BOND_ASK_CURRENT_YIELD_ALL",e[e.HIGH_BOND_ASK_YIELD_ALL=10]="HIGH_BOND_ASK_YIELD_ALL",e[e.HIGH_BOND_DEBT_2_BOOK_RATIO=11]="HIGH_BOND_DEBT_2_BOOK_RATIO",e[e.HIGH_BOND_DEBT_2_EQUITY_RATIO=12]="HIGH_BOND_DEBT_2_EQUITY_RATIO",e[e.HIGH_BOND_DEBT_2_TAN_BOOK_RATIO=13]="HIGH_BOND_DEBT_2_TAN_BOOK_RATIO",e[e.HIGH_BOND_EQUITY_2_BOOK_RATIO=14]="HIGH_BOND_EQUITY_2_BOOK_RATIO",e[e.HIGH_BOND_EQUITY_2_TAN_BOOK_RATIO=15]="HIGH_BOND_EQUITY_2_TAN_BOOK_RATIO",e[e.HIGH_BOND_NET_ASK_CURRENT_YIELD_ALL=16]="HIGH_BOND_NET_ASK_CURRENT_YIELD_ALL",e[e.HIGH_BOND_NET_ASK_YIELD_ALL=17]="HIGH_BOND_NET_ASK_YIELD_ALL",e[e.HIGH_BOND_NET_SPREAD_ALL=18]="HIGH_BOND_NET_SPREAD_ALL",e[e.HIGH_BOND_SPREAD_ALL=19]="HIGH_BOND_SPREAD_ALL",e[e.HIGH_COUPON_RATE=20]="HIGH_COUPON_RATE",e[e.HIGH_DIVIDEND_YIELD=21]="HIGH_DIVIDEND_YIELD",e[e.HIGH_DIVIDEND_YIELD_IB=22]="HIGH_DIVIDEND_YIELD_IB",e[e.HIGHEST_SLB_BID=23]="HIGHEST_SLB_BID",e[e.HIGH_GROWTH_RATE=24]="HIGH_GROWTH_RATE",e[e.HIGH_MOODY_RATING_ALL=25]="HIGH_MOODY_RATING_ALL",e[e.HIGH_OPEN_GAP=26]="HIGH_OPEN_GAP",e[e.HIGH_OPT_IMP_VOLAT=27]="HIGH_OPT_IMP_VOLAT",e[e.HIGH_OPT_IMP_VOLAT_OVER_HIST=28]="HIGH_OPT_IMP_VOLAT_OVER_HIST",e[e.HIGH_OPT_OPEN_INTEREST_PUT_CALL_RATIO=29]="HIGH_OPT_OPEN_INTEREST_PUT_CALL_RATIO",e[e.HIGH_OPT_VOLUME_PUT_CALL_RATIO=30]="HIGH_OPT_VOLUME_PUT_CALL_RATIO",e[e.HIGH_PE_RATIO=31]="HIGH_PE_RATIO",e[e.HIGH_PRICE_2_BOOK_RATIO=32]="HIGH_PRICE_2_BOOK_RATIO",e[e.HIGH_PRICE_2_TAN_BOOK_RATIO=33]="HIGH_PRICE_2_TAN_BOOK_RATIO",e[e.HIGH_QUICK_RATIO=34]="HIGH_QUICK_RATIO",e[e.HIGH_RETURN_ON_EQUITY=35]="HIGH_RETURN_ON_EQUITY",e[e.HIGH_SYNTH_BID_REV_NAT_YIELD=36]="HIGH_SYNTH_BID_REV_NAT_YIELD",e[e.HIGH_VS_13W_HL=37]="HIGH_VS_13W_HL",e[e.HIGH_VS_26W_HL=38]="HIGH_VS_26W_HL",e[e.HIGH_VS_52W_HL=39]="HIGH_VS_52W_HL",e[e.HOT_BY_OPT_VOLUME=40]="HOT_BY_OPT_VOLUME",e[e.HOT_BY_PRICE=41]="HOT_BY_PRICE",e[e.HOT_BY_PRICE_RANGE=42]="HOT_BY_PRICE_RANGE",e[e.HOT_BY_VOLUME=43]="HOT_BY_VOLUME",e[e.LIMIT_UP_DOWN=44]="LIMIT_UP_DOWN",e[e.LOW_BOND_BID_CURRENT_YIELD_ALL=45]="LOW_BOND_BID_CURRENT_YIELD_ALL",e[e.LOW_BOND_BID_YIELD_ALL=46]="LOW_BOND_BID_YIELD_ALL",e[e.LOW_BOND_DEBT_2_BOOK_RATIO=47]="LOW_BOND_DEBT_2_BOOK_RATIO",e[e.LOW_BOND_DEBT_2_EQUITY_RATIO=48]="LOW_BOND_DEBT_2_EQUITY_RATIO",e[e.LOW_BOND_DEBT_2_TAN_BOOK_RATIO=49]="LOW_BOND_DEBT_2_TAN_BOOK_RATIO",e[e.LOW_BOND_EQUITY_2_BOOK_RATIO=50]="LOW_BOND_EQUITY_2_BOOK_RATIO",e[e.LOW_BOND_EQUITY_2_TAN_BOOK_RATIO=51]="LOW_BOND_EQUITY_2_TAN_BOOK_RATIO",e[e.LOW_BOND_NET_BID_CURRENT_YIELD_ALL=52]="LOW_BOND_NET_BID_CURRENT_YIELD_ALL",e[e.LOW_BOND_NET_BID_YIELD_ALL=53]="LOW_BOND_NET_BID_YIELD_ALL",e[e.LOW_BOND_NET_SPREAD_ALL=54]="LOW_BOND_NET_SPREAD_ALL",e[e.LOW_BOND_SPREAD_ALL=55]="LOW_BOND_SPREAD_ALL",e[e.LOW_COUPON_RATE=56]="LOW_COUPON_RATE",e[e.LOWEST_SLB_ASK=57]="LOWEST_SLB_ASK",e[e.LOW_GROWTH_RATE=58]="LOW_GROWTH_RATE",e[e.LOW_MOODY_RATING_ALL=59]="LOW_MOODY_RATING_ALL",e[e.LOW_OPEN_GAP=60]="LOW_OPEN_GAP",e[e.LOW_OPT_IMP_VOLAT=61]="LOW_OPT_IMP_VOLAT",e[e.LOW_OPT_IMP_VOLAT_OVER_HIST=62]="LOW_OPT_IMP_VOLAT_OVER_HIST",e[e.LOW_OPT_OPEN_INTEREST_PUT_CALL_RATIO=63]="LOW_OPT_OPEN_INTEREST_PUT_CALL_RATIO",e[e.LOW_OPT_VOLUME_PUT_CALL_RATIO=64]="LOW_OPT_VOLUME_PUT_CALL_RATIO",e[e.LOW_PE_RATIO=65]="LOW_PE_RATIO",e[e.LOW_PRICE_2_BOOK_RATIO=66]="LOW_PRICE_2_BOOK_RATIO",e[e.LOW_PRICE_2_TAN_BOOK_RATIO=67]="LOW_PRICE_2_TAN_BOOK_RATIO",e[e.LOW_QUICK_RATIO=68]="LOW_QUICK_RATIO",e[e.LOW_RETURN_ON_EQUITY=69]="LOW_RETURN_ON_EQUITY",e[e.LOW_SYNTH_ASK_REV_NAT_YIELD=70]="LOW_SYNTH_ASK_REV_NAT_YIELD",e[e.LOW_VS_13W_HL=71]="LOW_VS_13W_HL",e[e.LOW_VS_26W_HL=72]="LOW_VS_26W_HL",e[e.LOW_VS_52W_HL=73]="LOW_VS_52W_HL",e[e.LOW_WAR_REL_IMP_VOLAT=74]="LOW_WAR_REL_IMP_VOLAT",e[e.MARKET_CAP_USD_ASC=75]="MARKET_CAP_USD_ASC",e[e.MARKET_CAP_USD_DESC=76]="MARKET_CAP_USD_DESC",e[e.MOST_ACTIVE_AVG_USD=77]="MOST_ACTIVE_AVG_USD",e[e.MOST_ACTIVE_USD=78]="MOST_ACTIVE_USD",e[e.NEAR_MATURITY_DATE=79]="NEAR_MATURITY_DATE",e[e.NOT_OPEN=80]="NOT_OPEN",e[e.OPT_OPEN_INTEREST_MOST_ACTIVE=81]="OPT_OPEN_INTEREST_MOST_ACTIVE",e[e.OPT_VOLUME_MOST_ACTIVE=82]="OPT_VOLUME_MOST_ACTIVE",e[e.PMONITOR_AVAIL_CONTRACTS=83]="PMONITOR_AVAIL_CONTRACTS",e[e.PMONITOR_CTT=84]="PMONITOR_CTT",e[e.PMONITOR_IBOND=85]="PMONITOR_IBOND",e[e.PMONITOR_RFQ=86]="PMONITOR_RFQ",e[e.TOP_OPEN_PERC_GAIN=87]="TOP_OPEN_PERC_GAIN",e[e.TOP_OPEN_PERC_LOSE=88]="TOP_OPEN_PERC_LOSE",e[e.TOP_OPT_IMP_VOLAT_GAIN=89]="TOP_OPT_IMP_VOLAT_GAIN",e[e.TOP_OPT_IMP_VOLAT_LOSE=90]="TOP_OPT_IMP_VOLAT_LOSE",e[e.TOP_PRICE_RANGE=91]="TOP_PRICE_RANGE",e[e.TOP_STOCK_BUY_IMBALANCE_ADV_RATIO=92]="TOP_STOCK_BUY_IMBALANCE_ADV_RATIO",e[e.TOP_STOCK_SELL_IMBALANCE_ADV_RATIO=93]="TOP_STOCK_SELL_IMBALANCE_ADV_RATIO",e[e.TOP_TRADE_COUNT=94]="TOP_TRADE_COUNT",e[e.TOP_TRADE_RATE=95]="TOP_TRADE_RATE",e[e.TOP_VOLUME_RATE=96]="TOP_VOLUME_RATE",e[e.WSH_NEXT_ANALYST_MEETING=97]="WSH_NEXT_ANALYST_MEETING",e[e.WSH_NEXT_EARNINGS=98]="WSH_NEXT_EARNINGS",e[e.WSH_NEXT_EVENT=99]="WSH_NEXT_EVENT",e[e.WSH_NEXT_MAJOR_EVENT=100]="WSH_NEXT_MAJOR_EVENT",e[e.WSH_PREV_ANALYST_MEETING=101]="WSH_PREV_ANALYST_MEETING",e[e.WSH_PREV_EARNINGS=102]="WSH_PREV_EARNINGS",e[e.WSH_PREV_EVENT=103]="WSH_PREV_EVENT"}(i||(t.ScanCode=i={}))},87273:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.config=void 0,t.config={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},87816:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dateTimestampProvider=void 0,t.dateTimestampProvider={now:function(){return(t.dateTimestampProvider.delegate||Date).now()},delegate:void 0}},87820:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeAll=void 0;var n=r(24071),i=r(56032);t.mergeAll=function(e){return void 0===e&&(e=1/0),n.mergeMap(i.identity,e)}},87834:e=>{e.exports=function(e,t,r){var n=[],i=e;return function(){var e=Array.prototype.slice.call(arguments);if(i<=0)return void n.push([this,e]);i-=1,function e(o){setTimeout(function(){n.length>0?e():i+=1},t);var s=n.shift();if(!s&&o)return void r.apply(o[0],o[1]);r.apply(s[0],s[1])}([this,e])}}},88210:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.TriggerMethod=void 0,function(e){e[e.Default=0]="Default",e[e.DoubleBidAsk=1]="DoubleBidAsk",e[e.Last=2]="Last",e[e.DoubleLast=3]="DoubleLast",e[e.BidAsk=4]="BidAsk",e[e.LastOfBidAsk=7]="LastOfBidAsk",e[e.MidPoint=8]="MidPoint"}(r||(t.TriggerMethod=r={})),t.default=r},88840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.executeSchedule=void 0,t.executeSchedule=function(e,t,r,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var o=t.schedule(function(){r(),i?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(o),!i)return o}},89123:function(e,t,r){"use strict";var n,i,o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Encoder=t.OUT_MSG_ID=void 0;let s=r(25648),a=o(r(85819)),u=o(r(48369)),c=o(r(15850)),l=r(48909),d=r(55332),h=r(19654),_=r(7133);function f(e){return e===Number.MAX_VALUE?null:e}(n=i||(t.OUT_MSG_ID=i={}))[n.REQ_MKT_DATA=1]="REQ_MKT_DATA",n[n.CANCEL_MKT_DATA=2]="CANCEL_MKT_DATA",n[n.PLACE_ORDER=3]="PLACE_ORDER",n[n.CANCEL_ORDER=4]="CANCEL_ORDER",n[n.REQ_OPEN_ORDERS=5]="REQ_OPEN_ORDERS",n[n.REQ_ACCOUNT_DATA=6]="REQ_ACCOUNT_DATA",n[n.REQ_EXECUTIONS=7]="REQ_EXECUTIONS",n[n.REQ_IDS=8]="REQ_IDS",n[n.REQ_CONTRACT_DATA=9]="REQ_CONTRACT_DATA",n[n.REQ_MKT_DEPTH=10]="REQ_MKT_DEPTH",n[n.CANCEL_MKT_DEPTH=11]="CANCEL_MKT_DEPTH",n[n.REQ_NEWS_BULLETINS=12]="REQ_NEWS_BULLETINS",n[n.CANCEL_NEWS_BULLETINS=13]="CANCEL_NEWS_BULLETINS",n[n.SET_SERVER_LOGLEVEL=14]="SET_SERVER_LOGLEVEL",n[n.REQ_AUTO_OPEN_ORDERS=15]="REQ_AUTO_OPEN_ORDERS",n[n.REQ_ALL_OPEN_ORDERS=16]="REQ_ALL_OPEN_ORDERS",n[n.REQ_MANAGED_ACCTS=17]="REQ_MANAGED_ACCTS",n[n.REQ_FA=18]="REQ_FA",n[n.REPLACE_FA=19]="REPLACE_FA",n[n.REQ_HISTORICAL_DATA=20]="REQ_HISTORICAL_DATA",n[n.EXERCISE_OPTIONS=21]="EXERCISE_OPTIONS",n[n.REQ_SCANNER_SUBSCRIPTION=22]="REQ_SCANNER_SUBSCRIPTION",n[n.CANCEL_SCANNER_SUBSCRIPTION=23]="CANCEL_SCANNER_SUBSCRIPTION",n[n.REQ_SCANNER_PARAMETERS=24]="REQ_SCANNER_PARAMETERS",n[n.CANCEL_HISTORICAL_DATA=25]="CANCEL_HISTORICAL_DATA",n[n.REQ_CURRENT_TIME=49]="REQ_CURRENT_TIME",n[n.REQ_REAL_TIME_BARS=50]="REQ_REAL_TIME_BARS",n[n.CANCEL_REAL_TIME_BARS=51]="CANCEL_REAL_TIME_BARS",n[n.REQ_FUNDAMENTAL_DATA=52]="REQ_FUNDAMENTAL_DATA",n[n.CANCEL_FUNDAMENTAL_DATA=53]="CANCEL_FUNDAMENTAL_DATA",n[n.REQ_CALC_IMPLIED_VOLAT=54]="REQ_CALC_IMPLIED_VOLAT",n[n.REQ_CALC_OPTION_PRICE=55]="REQ_CALC_OPTION_PRICE",n[n.CANCEL_CALC_IMPLIED_VOLAT=56]="CANCEL_CALC_IMPLIED_VOLAT",n[n.CANCEL_CALC_OPTION_PRICE=57]="CANCEL_CALC_OPTION_PRICE",n[n.REQ_GLOBAL_CANCEL=58]="REQ_GLOBAL_CANCEL",n[n.REQ_MARKET_DATA_TYPE=59]="REQ_MARKET_DATA_TYPE",n[n.REQ_POSITIONS=61]="REQ_POSITIONS",n[n.REQ_ACCOUNT_SUMMARY=62]="REQ_ACCOUNT_SUMMARY",n[n.CANCEL_ACCOUNT_SUMMARY=63]="CANCEL_ACCOUNT_SUMMARY",n[n.CANCEL_POSITIONS=64]="CANCEL_POSITIONS",n[n.VERIFY_REQUEST=65]="VERIFY_REQUEST",n[n.VERIFY_MESSAGE=66]="VERIFY_MESSAGE",n[n.QUERY_DISPLAY_GROUPS=67]="QUERY_DISPLAY_GROUPS",n[n.SUBSCRIBE_TO_GROUP_EVENTS=68]="SUBSCRIBE_TO_GROUP_EVENTS",n[n.UPDATE_DISPLAY_GROUP=69]="UPDATE_DISPLAY_GROUP",n[n.UNSUBSCRIBE_FROM_GROUP_EVENTS=70]="UNSUBSCRIBE_FROM_GROUP_EVENTS",n[n.START_API=71]="START_API",n[n.VERIFY_AND_AUTH_REQUEST=72]="VERIFY_AND_AUTH_REQUEST",n[n.VERIFY_AND_AUTH_MESSAGE=73]="VERIFY_AND_AUTH_MESSAGE",n[n.REQ_POSITIONS_MULTI=74]="REQ_POSITIONS_MULTI",n[n.CANCEL_POSITIONS_MULTI=75]="CANCEL_POSITIONS_MULTI",n[n.REQ_ACCOUNT_UPDATES_MULTI=76]="REQ_ACCOUNT_UPDATES_MULTI",n[n.CANCEL_ACCOUNT_UPDATES_MULTI=77]="CANCEL_ACCOUNT_UPDATES_MULTI",n[n.REQ_SEC_DEF_OPT_PARAMS=78]="REQ_SEC_DEF_OPT_PARAMS",n[n.REQ_SOFT_DOLLAR_TIERS=79]="REQ_SOFT_DOLLAR_TIERS",n[n.REQ_FAMILY_CODES=80]="REQ_FAMILY_CODES",n[n.REQ_MATCHING_SYMBOLS=81]="REQ_MATCHING_SYMBOLS",n[n.REQ_MKT_DEPTH_EXCHANGES=82]="REQ_MKT_DEPTH_EXCHANGES",n[n.REQ_SMART_COMPONENTS=83]="REQ_SMART_COMPONENTS",n[n.REQ_NEWS_ARTICLE=84]="REQ_NEWS_ARTICLE",n[n.REQ_NEWS_PROVIDERS=85]="REQ_NEWS_PROVIDERS",n[n.REQ_HISTORICAL_NEWS=86]="REQ_HISTORICAL_NEWS",n[n.REQ_HEAD_TIMESTAMP=87]="REQ_HEAD_TIMESTAMP",n[n.REQ_HISTOGRAM_DATA=88]="REQ_HISTOGRAM_DATA",n[n.CANCEL_HISTOGRAM_DATA=89]="CANCEL_HISTOGRAM_DATA",n[n.CANCEL_HEAD_TIMESTAMP=90]="CANCEL_HEAD_TIMESTAMP",n[n.REQ_MARKET_RULE=91]="REQ_MARKET_RULE",n[n.REQ_PNL=92]="REQ_PNL",n[n.CANCEL_PNL=93]="CANCEL_PNL",n[n.REQ_PNL_SINGLE=94]="REQ_PNL_SINGLE",n[n.CANCEL_PNL_SINGLE=95]="CANCEL_PNL_SINGLE",n[n.REQ_HISTORICAL_TICKS=96]="REQ_HISTORICAL_TICKS",n[n.REQ_TICK_BY_TICK_DATA=97]="REQ_TICK_BY_TICK_DATA",n[n.CANCEL_TICK_BY_TICK_DATA=98]="CANCEL_TICK_BY_TICK_DATA",n[n.REQ_COMPLETED_ORDERS=99]="REQ_COMPLETED_ORDERS",n[n.REQ_WSH_META_DATA=100]="REQ_WSH_META_DATA",n[n.CANCEL_WSH_META_DATA=101]="CANCEL_WSH_META_DATA",n[n.REQ_WSH_EVENT_DATA=102]="REQ_WSH_EVENT_DATA",n[n.CANCEL_WSH_EVENT_DATA=103]="CANCEL_WSH_EVENT_DATA",n[n.REQ_USER_INFO=104]="REQ_USER_INFO";class E{constructor(e){this.callback=e}get serverVersion(){return this.callback.serverVersion}sendMsg(...e){this.callback.sendMsg(e)}emitError(e,t,r){this.callback.emitError(`Server Version ${this.serverVersion}: ${e}`,t,r??_.ErrorCode.NO_VALID_ID)}encodeContract(e){return[e.conId,e.symbol,e.secType,e.lastTradeDateOrContractMonth,e.strike,e.right,e.multiplier,e.exchange,e.primaryExch,e.currency,e.localSymbol,e.tradingClass,+!!e.includeExpired]}encodeTagValues(e){let t="";return e?.forEach(e=>{t+=`${e.tag}=${e.value};`}),t}calculateImpliedVolatility(e,t,r,n,o){if(this.serverVersion<u.default.REQ_CALC_IMPLIED_VOLAT)return this.emitError("It does not support calculate implied volatility requests.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&(void 0===t.tradingClass||""===t.tradingClass))return this.emitError("It does not support tradingClass parameter in calculateImpliedVolatility.",_.ErrorCode.UPDATE_TWS,e);let s=[i.REQ_CALC_IMPLIED_VOLAT,2,e];s.push(t.conId),s.push(t.symbol),s.push(t.secType),s.push(t.lastTradeDateOrContractMonth),s.push(t.strike),s.push(t.right),s.push(t.multiplier),s.push(t.exchange),s.push(t.primaryExch),s.push(t.currency),s.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&s.push(t.tradingClass),s.push(r),s.push(n),this.serverVersion>=u.default.LINKING&&s.push(this.encodeTagValues(o)),this.sendMsg(s)}calculateOptionPrice(e,t,r,n,o){if(this.serverVersion<u.default.REQ_CALC_OPTION_PRICE)return this.emitError("It does not support calculate option price requests.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&t.tradingClass)return this.emitError("It does not support tradingClass parameter in calculateOptionPrice.",_.ErrorCode.UPDATE_TWS,e);let s=[i.REQ_CALC_OPTION_PRICE,2,e];s.push(t.conId),s.push(t.symbol),s.push(t.secType),s.push(t.lastTradeDateOrContractMonth),s.push(t.strike),s.push(t.right),s.push(t.multiplier),s.push(t.exchange),s.push(t.primaryExch),s.push(t.currency),s.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&s.push(t.tradingClass),s.push(r),s.push(n),this.serverVersion>=u.default.LINKING&&s.push(this.encodeTagValues(o)),this.sendMsg(s)}cancelAccountSummary(e){if(this.serverVersion<u.default.ACCT_SUMMARY)return this.emitError("It not support account summary cancellation.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_ACCOUNT_SUMMARY,1,e)}cancelAccountUpdatesMulti(e){this.sendMsg(i.CANCEL_ACCOUNT_UPDATES_MULTI,2,e)}cancelCalculateImpliedVolatility(e){if(this.serverVersion<u.default.CANCEL_CALC_IMPLIED_VOLAT)return this.emitError("It does not support calculate implied volatility cancellation.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_CALC_IMPLIED_VOLAT,1,e)}cancelCalculateOptionPrice(e){if(this.serverVersion<u.default.CANCEL_CALC_OPTION_PRICE)return this.emitError("It does not support calculate option price cancellation.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_CALC_OPTION_PRICE,1,e)}cancelFundamentalData(e){if(this.serverVersion<u.default.FUNDAMENTAL_DATA)return this.emitError("It does not support fundamental data requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_FUNDAMENTAL_DATA,1,e)}cancelHistoricalData(e){if(this.serverVersion<24)return this.emitError("It does not support historical data query cancellation.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_HISTORICAL_DATA,1,e)}cancelMktData(e){this.sendMsg(i.CANCEL_MKT_DATA,1,e)}cancelMktDepth(e,t){if(this.serverVersion<6)return this.emitError("This feature is only available for versions of TWS >=6.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SMART_DEPTH&&t)return this.emitError("It does not support SMART depth cancel.",_.ErrorCode.UPDATE_TWS,e);let r=[i.CANCEL_MKT_DEPTH,1,e];this.serverVersion>=u.default.SMART_DEPTH&&r.push(t),this.sendMsg(r)}cancelNewsBulletins(){this.sendMsg(i.CANCEL_NEWS_BULLETINS,1)}cancelOrder(e,t){if(this.serverVersion<u.default.MANUAL_ORDER_TIME&&t.manualOrderCancelTime?.length)return this.emitError("It does not support manual order cancel time attribute",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.CME_TAGGING_FIELDS&&(t.extOperator?.length||void 0!=t.manualOrderIndicator))return this.emitError("It does not support ext operator and manual order indicator parameters",_.ErrorCode.UPDATE_TWS,e);let r=[i.CANCEL_ORDER];this.serverVersion<u.default.CME_TAGGING_FIELDS&&r.push(1),r.push(e),this.serverVersion>=u.default.MANUAL_ORDER_TIME&&r.push(t.manualOrderCancelTime),this.serverVersion>=u.default.RFQ_FIELDS&&this.serverVersion<u.default.UNDO_RFQ_FIELDS&&(r.push(""),r.push(""),r.push(0x7fffffff)),this.serverVersion>=u.default.CME_TAGGING_FIELDS&&(r.push(t.extOperator),r.push(t.manualOrderIndicator??0x7fffffff)),this.sendMsg(r)}cancelPositions(){if(this.serverVersion<u.default.ACCT_SUMMARY)return this.emitError("It does not support position cancellation.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.CANCEL_POSITIONS,1)}cancelRealTimeBars(e){if(this.serverVersion<u.default.REAL_TIME_BARS)return this.emitError("It does not support realtime bar data query cancellation.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_REAL_TIME_BARS,1,e)}cancelScannerSubscription(e){if(this.serverVersion<24)return this.emitError("It does not support API scanner subscription.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_SCANNER_SUBSCRIPTION,1,e)}exerciseOptions(e,t,r,n,o,s,a="",c="",l=!1){if(this.serverVersion<21)return this.emitError("It does not support options exercise from the API.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&(t.tradingClass||void 0!=t.conId))return this.emitError("It does not support conId and tradingClass parameters in exerciseOptions.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.MANUAL_ORDER_TIME_EXERCISE_OPTIONS&&a)return this.emitError("It does not support manual order time parameter in exerciseOptions.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.CUSTOMER_ACCOUNT&&c)return this.emitError("It does not support customer account parameter in exerciseOptions.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.PROFESSIONAL_CUSTOMER&&l)return this.emitError("It does not support professional customer parameter in exerciseOptions.",_.ErrorCode.UPDATE_TWS,e);let d=[i.EXERCISE_OPTIONS,2,e];this.serverVersion>=u.default.TRADING_CLASS&&d.push(t.conId),d.push(t.symbol),d.push(t.secType),d.push(t.lastTradeDateOrContractMonth),d.push(t.strike),d.push(t.right),d.push(t.multiplier),d.push(t.exchange),d.push(t.currency),d.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&d.push(t.tradingClass),d.push(r),d.push(n),d.push(o),d.push(s),this.serverVersion>=u.default.MANUAL_ORDER_TIME_EXERCISE_OPTIONS&&d.push(a),this.serverVersion>=u.default.CUSTOMER_ACCOUNT&&d.push(c),this.serverVersion>=u.default.PROFESSIONAL_CUSTOMER&&d.push(l),this.sendMsg(d)}placeOrder(e,t,r){if(this.serverVersion<u.default.SCALE_ORDERS&&(void 0!==r.scaleInitLevelSize||void 0!==r.scalePriceIncrement))return this.emitError("It does not support Scale orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SSHORT_COMBO_LEGS&&t.comboLegs?.forEach(t=>{if(t.shortSaleSlot||t.designatedLocation)return this.emitError("It does not support SSHORT flag for combo legs.",_.ErrorCode.UPDATE_TWS,e)}),this.serverVersion<u.default.WHAT_IF_ORDERS&&r.whatIf)return this.emitError("It does not support what-if orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.DELTA_NEUTRAL&&t.deltaNeutralContract)return this.emitError("It does not support delta-neutral orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SCALE_ORDERS2&&void 0!==r.scaleSubsLevelSize)return this.emitError("It does not support Subsequent Level Size for Scale orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.ALGO_ORDERS&&r.algoStrategy)return this.emitError("It does not support algo orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.NOT_HELD&&r.notHeld)return this.emitError("It does not support notHeld parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SEC_ID_TYPE&&(t.secIdType||t.secId))return this.emitError("It does not support secIdType and secId parameters.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.PLACE_ORDER_CONID&&void 0!=t.conId)return this.emitError("It does not support conId parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SSHORTX&&-1!==r.exemptCode)return this.emitError("It does not support exemptCode parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SSHORTX&&t.comboLegs?.forEach(t=>{if(-1!==t.exemptCode)return this.emitError("It does not support exemptCode parameter.",_.ErrorCode.UPDATE_TWS,e)}),this.serverVersion<u.default.HEDGE_ORDERS&&r.hedgeType)return this.emitError("It does not support hedge orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.OPT_OUT_SMART_ROUTING&&r.optOutSmartRouting)return this.emitError("It does not support optOutSmartRouting parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.DELTA_NEUTRAL_CONID&&(void 0!=r.deltaNeutralConId||r.deltaNeutralSettlingFirm||r.deltaNeutralClearingAccount||r.deltaNeutralClearingIntent))return this.emitError("It does not support deltaNeutral parameters: ConId, SettlingFirm, ClearingAccount, ClearingIntent.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.DELTA_NEUTRAL_OPEN_CLOSE&&(r.deltaNeutralOpenClose||r.deltaNeutralShortSale||void 0!=r.deltaNeutralShortSaleSlot||r.deltaNeutralDesignatedLocation))return this.emitError("It does not support deltaNeutral parameters: OpenClose, ShortSale, ShortSaleSlot, DesignatedLocation.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SCALE_ORDERS3&&void 0!=r.scalePriceIncrement&&void 0!==r.scalePriceIncrement&&(void 0!==r.scalePriceAdjustValue||void 0!==r.scalePriceAdjustInterval||void 0!==r.scaleProfitOffset||r.scaleAutoReset||void 0!==r.scaleInitPosition||void 0!==r.scaleInitFillQty||r.scaleRandomPercent))return this.emitError("It does not support Scale order parameters: PriceAdjustValue, PriceAdjustInterval, ProfitOffset, AutoReset, InitPosition, InitFillQty and RandomPercent",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.ORDER_COMBO_LEGS_PRICE&&c.default.BAG===t.secType?.toUpperCase()&&r.orderComboLegs?.forEach(t=>{if(void 0!==t.price)return this.emitError("It does not support per-leg prices for order combo legs.",_.ErrorCode.UPDATE_TWS,e)}),this.serverVersion<u.default.TRAILING_PERCENT&&void 0!==r.trailingPercent)return this.emitError("It does not support trailing percent parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&t.tradingClass)return this.emitError("It does not support tradingClass parameters in placeOrder.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SCALE_TABLE&&(r.scaleTable||r.activeStartTime||r.activeStopTime))return this.emitError("It does not support scaleTable, activeStartTime and activeStopTime parameters.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.ALGO_ID&&r.algoId)return this.emitError("It does not support algoId parameter",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SCALE_TABLE&&(r.scaleTable||r.activeStartTime?.length||r.activeStopTime?.length))return this.emitError("It does not support scaleTable, activeStartTime and activeStopTime parameters.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.ORDER_SOLICITED&&r.solicited)return this.emitError("It does not support order solicited parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.MODELS_SUPPORT&&r.modelCode)return this.emitError("It does not support model code parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.EXT_OPERATOR&&r.extOperator)return this.emitError("It does not support ext operator",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SOFT_DOLLAR_TIER&&(r.softDollarTier?.name||r.softDollarTier?.value))return this.emitError("It does not support soft dollar tier",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.CASH_QTY&&void 0!==r.cashQty)return this.emitError("It does not support cash quantity parameter",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.DECISION_MAKER&&(r.mifid2DecisionMaker||r.mifid2DecisionAlgo))return this.emitError("It does not support MIFID II decision maker parameters",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.MIFID_EXECUTION&&(r.mifid2ExecutionTrader||r.mifid2ExecutionAlgo))return this.emitError("It does not support MIFID II execution parameters",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.AUTO_PRICE_FOR_HEDGE&&r.dontUseAutoPriceForHedge)return this.emitError("It does not support don't use auto price for hedge parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.ORDER_CONTAINER&&r.isOmsContainer)return this.emitError("It does not support oms container parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.D_PEG_ORDERS&&r.discretionaryUpToLimitPrice)return this.emitError("It does not support D-Peg orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.PRICE_MGMT_ALGO&&null!=r.usePriceMgmtAlgo)return this.emitError("It does not support price management algo parameter",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.DURATION&&void 0!=r.duration)return this.emitError("It does not support duration attribute",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.POST_TO_ATS&&void 0!=r.postToAts)return this.emitError("It does not support postToAts attribute",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.AUTO_CANCEL_PARENT&&null!=r.autoCancelParent)return this.emitError("It does not support autoCancelParent attribute",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.ADVANCED_ORDER_REJECT&&void 0!=r.advancedErrorOverride)return this.emitError("It does not support advanced error override attribute",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.MANUAL_ORDER_TIME&&r.manualOrderTime?.length)return this.emitError("It does not support manual order time attribute",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.PEGBEST_PEGMID_OFFSETS&&(void 0!==r.minTradeQty||void 0!==r.minCompeteSize||void 0!==r.competeAgainstBestOffset||void 0!==r.midOffsetAtWhole||void 0!==r.midOffsetAtHalf))return this.emitError("It does not support PEG BEST / PEG MID order parameters: minTradeQty, minCompeteSize, competeAgainstBestOffset, midOffsetAtWhole and midOffsetAtHalf",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.CUSTOMER_ACCOUNT&&r.customerAccount)return this.emitError("It does not support customer account parameter",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.PROFESSIONAL_CUSTOMER&&r.professionalCustomer)return this.emitError("It does not support professional customer parameter",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.INCLUDE_OVERNIGHT&&r.includeOvernight)return this.emitError("It does not support include overnight parameter",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.CME_TAGGING_FIELDS&&r.manualOrderIndicator)return this.emitError("It does not support manual order indicator parameter",_.ErrorCode.UPDATE_TWS,e);let n=this.serverVersion<u.default.NOT_HELD?27:45,o=[i.PLACE_ORDER];if(this.serverVersion<u.default.ORDER_CONTAINER&&o.push(n),o.push(e),this.serverVersion>=u.default.PLACE_ORDER_CONID&&o.push(t.conId),o.push(t.symbol),o.push(t.secType),o.push(t.lastTradeDateOrContractMonth),o.push(t.strike),o.push(t.right),this.serverVersion>=15&&o.push(t.multiplier),o.push(t.exchange),this.serverVersion>=14&&o.push(t.primaryExch),o.push(t.currency),this.serverVersion>=2&&o.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&o.push(t.tradingClass),this.serverVersion>=u.default.SEC_ID_TYPE&&(o.push(t.secIdType),o.push(t.secId)),o.push(r.action),this.serverVersion>=u.default.FRACTIONAL_POSITIONS?o.push(r.totalQuantity):o.push(r.totalQuantity?Math.round(r.totalQuantity):void 0),o.push(r.orderType),this.serverVersion<u.default.ORDER_COMBO_LEGS_PRICE?o.push(r.lmtPrice??0):o.push(f(r.lmtPrice)),this.serverVersion<u.default.TRAILING_PERCENT?o.push(r.auxPrice??0):o.push(f(r.auxPrice)),o.push(r.tif),o.push(r.ocaGroup),o.push(r.account),o.push(r.openClose),o.push(r.origin),o.push(r.orderRef),o.push(r.transmit),this.serverVersion>=4&&o.push(r.parentId),this.serverVersion>=5&&(o.push(r.blockOrder),o.push(r.sweepToFill),o.push(r.displaySize),o.push(r.triggerMethod),this.serverVersion<38?o.push(!1):o.push(r.outsideRth)),this.serverVersion>=7&&o.push(r.hidden),this.serverVersion>=8&&c.default.BAG===t.secType?.toUpperCase()&&(t.comboLegs?.length?(o.push(t.comboLegs.length),t.comboLegs.forEach(e=>{o.push(e.conId),o.push(e.ratio),o.push(e.action),o.push(e.exchange),o.push(e.openClose),this.serverVersion>=u.default.SSHORT_COMBO_LEGS&&(o.push(e.shortSaleSlot),o.push(e.designatedLocation)),this.serverVersion>=u.default.SSHORTX_OLD&&o.push(e.exemptCode)})):o.push(0)),this.serverVersion>=u.default.ORDER_COMBO_LEGS_PRICE&&c.default.BAG===t.secType?.toUpperCase()&&(r.orderComboLegs?.length?(o.push(r.orderComboLegs.length),r.orderComboLegs.forEach(function(e){o.push(f(e.price))})):o.push(0)),this.serverVersion>=u.default.SMART_COMBO_ROUTING_PARAMS&&c.default.BAG===t.secType?.toUpperCase()){let e=r.smartComboRoutingParams?r.smartComboRoutingParams.length:0;o.push(e),e>0&&r.smartComboRoutingParams?.forEach(e=>{o.push(e.tag),o.push(e.value)})}if(this.serverVersion>=9&&o.push(""),this.serverVersion>=10&&o.push(r.discretionaryAmt),this.serverVersion>=11&&o.push(r.goodAfterTime),this.serverVersion>=12&&o.push(r.goodTillDate),this.serverVersion>=13&&(o.push(r.faGroup),o.push(r.faMethod),o.push(r.faPercentage),this.serverVersion<u.default.FA_PROFILE_DESUPPORT&&o.push("")),this.serverVersion>=u.default.MODELS_SUPPORT&&o.push(r.modelCode),this.serverVersion>=18&&(o.push(r.shortSaleSlot),o.push(r.designatedLocation)),this.serverVersion>=u.default.SSHORTX_OLD&&o.push(r.exemptCode),this.serverVersion>=19){o.push(r.ocaType),this.serverVersion<38&&o.push(!1),o.push(r.rule80A),o.push(r.settlingFirm),o.push(r.allOrNone),o.push(f(r.minQty)),o.push(f(r.percentOffset)),o.push(r.eTradeOnly),o.push(r.firmQuoteOnly),o.push(f(r.nbboPriceCap)),o.push(f(r.auctionStrategy)),o.push(f(r.startingPrice)),o.push(f(r.stockRefPrice)),o.push(f(r.delta));let e=26===this.serverVersion&&(0,d.isVolOrder)(r.orderType)?void 0:r.stockRangeLower,t=26===this.serverVersion&&(0,d.isVolOrder)(r.orderType)?void 0:r.stockRangeUpper;o.push(e),o.push(t)}if(this.serverVersion>=22&&o.push(r.overridePercentageConstraints),this.serverVersion>=26){if(o.push(f(r.volatility)),o.push(f(r.volatilityType)),this.serverVersion<28?o.push(r.deltaNeutralOrderType?.toUpperCase()==="MKT"):(o.push(r.deltaNeutralOrderType),o.push(f(r.deltaNeutralAuxPrice)),this.serverVersion>=u.default.DELTA_NEUTRAL_CONID&&r.deltaNeutralOrderType&&(o.push(r.deltaNeutralConId),o.push(r.deltaNeutralSettlingFirm),o.push(r.deltaNeutralClearingAccount),o.push(r.deltaNeutralClearingIntent)),this.serverVersion>=u.default.DELTA_NEUTRAL_OPEN_CLOSE&&r.deltaNeutralOrderType&&(o.push(r.deltaNeutralOpenClose),o.push(r.deltaNeutralShortSale),o.push(r.deltaNeutralShortSaleSlot),o.push(r.deltaNeutralDesignatedLocation))),o.push(r.continuousUpdate),26===this.serverVersion){let e=(0,d.isVolOrder)(r.orderType)?r.stockRangeLower:void 0,t=(0,d.isVolOrder)(r.orderType)?r.stockRangeUpper:void 0;o.push(f(e)),o.push(f(t))}o.push(f(r.referencePriceType))}if(this.serverVersion>=30&&o.push(f(r.trailStopPrice)),this.serverVersion>=u.default.TRAILING_PERCENT&&o.push(f(r.trailingPercent)),this.serverVersion>=u.default.SCALE_ORDERS&&(this.serverVersion>=u.default.SCALE_ORDERS2?(o.push(f(r.scaleInitLevelSize)),o.push(f(r.scaleSubsLevelSize))):(o.push(""),o.push(f(r.scaleInitLevelSize))),o.push(f(r.scalePriceIncrement))),this.serverVersion>=u.default.SCALE_ORDERS3&&void 0!==r.scalePriceIncrement&&(o.push(f(r.scalePriceAdjustValue)),o.push(f(r.scalePriceAdjustInterval)),o.push(f(r.scaleProfitOffset)),o.push(r.scaleAutoReset),o.push(f(r.scaleInitPosition)),o.push(f(r.scaleInitFillQty)),o.push(r.scaleRandomPercent)),this.serverVersion>=u.default.SCALE_TABLE&&(o.push(r.scaleTable),o.push(r.activeStartTime),o.push(r.activeStopTime)),this.serverVersion>=u.default.HEDGE_ORDERS&&(o.push(r.hedgeType),r.hedgeType&&o.push(r.hedgeParam)),this.serverVersion>=u.default.OPT_OUT_SMART_ROUTING&&o.push(r.optOutSmartRouting),this.serverVersion>=u.default.PTA_ORDERS&&(o.push(r.clearingAccount),o.push(r.clearingIntent)),this.serverVersion>=u.default.NOT_HELD&&o.push(r.notHeld),this.serverVersion>=u.default.DELTA_NEUTRAL&&(t.deltaNeutralContract?(o.push(!0),o.push(t.deltaNeutralContract.conId),o.push(t.deltaNeutralContract.delta),o.push(t.deltaNeutralContract.price)):o.push(!1)),this.serverVersion>=u.default.ALGO_ORDERS&&(o.push(r.algoStrategy),r.algoStrategy)){let e=r.algoParams?.length?r.algoParams.length:0;o.push(e),e>0&&r.algoParams?.forEach(e=>{o.push(e.tag),o.push(e.value)})}if(this.serverVersion>=u.default.ALGO_ID&&o.push(r.algoId),this.serverVersion>=u.default.WHAT_IF_ORDERS&&o.push(r.whatIf),this.serverVersion>=u.default.LINKING&&o.push(r.orderMiscOptions),this.serverVersion>=u.default.ORDER_SOLICITED&&o.push(r.solicited),this.serverVersion>=u.default.RANDOMIZE_SIZE_AND_PRICE&&(o.push(r.randomizeSize),o.push(r.randomizePrice)),this.serverVersion>=u.default.PEGGED_TO_BENCHMARK){(0,d.isPegBenchOrder)(r.orderType)&&(o.push(r.referenceContractId),o.push(r.isPeggedChangeAmountDecrease),o.push(r.peggedChangeAmount),o.push(r.referenceChangeAmount),o.push(r.referenceExchangeId));let e=r.conditions?r.conditions.length:0;o.push(e),e>0&&(r.conditions?.forEach(e=>{switch(o.push(e.type),o.push(e.conjunctionConnection),e.type){case l.OrderConditionType.Execution:o.push(e.secType),o.push(e.exchange),o.push(e.symbol);break;case l.OrderConditionType.Margin:o.push(e.isMore),o.push(e.strValue);break;case l.OrderConditionType.PercentChange:o.push(e.isMore),o.push(e.strValue),o.push(e.conId),o.push(e.exchange);break;case l.OrderConditionType.Price:o.push(e.isMore),o.push(e.strValue),o.push(e.conId),o.push(e.exchange),o.push(e.triggerMethod);break;case l.OrderConditionType.Time:o.push(e.isMore),o.push(e.strValue);break;case l.OrderConditionType.Volume:o.push(e.isMore),o.push(e.strValue),o.push(e.conId),o.push(e.exchange)}}),o.push(r.conditionsIgnoreRth),o.push(r.conditionsCancelOrder)),o.push(r.adjustedOrderType),o.push(r.triggerPrice),o.push(r.lmtPriceOffset),o.push(r.adjustedStopPrice),o.push(r.adjustedStopLimitPrice),o.push(r.adjustedTrailingAmount),o.push(r.adjustableTrailingUnit)}if(this.serverVersion>=u.default.EXT_OPERATOR&&o.push(r.extOperator),this.serverVersion>=u.default.SOFT_DOLLAR_TIER&&(o.push(r.softDollarTier?.name?r.softDollarTier.name:""),o.push(r.softDollarTier?.value?r.softDollarTier.value:"")),this.serverVersion>=u.default.CASH_QTY&&o.push(f(r.cashQty)),this.serverVersion>=u.default.DECISION_MAKER&&(o.push(r.mifid2DecisionMaker),o.push(r.mifid2DecisionAlgo)),this.serverVersion>=u.default.MIFID_EXECUTION&&(o.push(r.mifid2ExecutionTrader),o.push(r.mifid2ExecutionAlgo)),this.serverVersion>=u.default.AUTO_PRICE_FOR_HEDGE&&o.push(r.dontUseAutoPriceForHedge),this.serverVersion>=u.default.ORDER_CONTAINER&&o.push(r.isOmsContainer),this.serverVersion>=u.default.D_PEG_ORDERS&&o.push(r.discretionaryUpToLimitPrice),this.serverVersion>=u.default.PRICE_MGMT_ALGO&&o.push(r.usePriceMgmtAlgo),this.serverVersion>=u.default.DURATION&&o.push(r.duration),this.serverVersion>=u.default.POST_TO_ATS&&o.push(r.postToAts),this.serverVersion>=u.default.AUTO_CANCEL_PARENT&&o.push(r.autoCancelParent),this.serverVersion>=u.default.ADVANCED_ORDER_REJECT&&o.push(r.advancedErrorOverride),this.serverVersion>=u.default.MANUAL_ORDER_TIME&&o.push(r.manualOrderTime),this.serverVersion>=u.default.PEGBEST_PEGMID_OFFSETS){let e=!1;"IBKRATS"==t.exchange&&o.push(r.minTradeQty),(0,d.isPegBestOrder)(r.orderType)?(o.push(r.minCompeteSize),o.push(r.competeAgainstBestOffset),(0,h.isCompeteAgainstBestOffsetUpToMid)(r)&&(e=!0)):(0,d.isPegMidOrder)(r.orderType)&&(e=!0),e&&(o.push(r.midOffsetAtWhole),o.push(r.midOffsetAtHalf))}this.serverVersion>=u.default.CUSTOMER_ACCOUNT&&o.push(r.customerAccount),this.serverVersion>=u.default.PROFESSIONAL_CUSTOMER&&o.push(r.professionalCustomer),this.serverVersion>=u.default.RFQ_FIELDS&&this.serverVersion<u.default.UNDO_RFQ_FIELDS&&(o.push(""),o.push(0x7fffffff)),this.serverVersion>=u.default.INCLUDE_OVERNIGHT&&o.push(r.includeOvernight),this.serverVersion>=u.default.CME_TAGGING_FIELDS&&o.push(r.manualOrderIndicator),this.sendMsg(o)}replaceFA(e,t,r){if(this.serverVersion>=u.default.FA_PROFILE_DESUPPORT&&t==a.default.PROFILES)return this.emitError("FA Profile is not supported anymore, use FA Group instead.",_.ErrorCode.UPDATE_TWS,e);let n=[i.REPLACE_FA,1,t,r];this.serverVersion>=u.default.REPLACE_FA_END&&n.push(e),this.sendMsg(n)}reqAccountSummary(e,t,r){if(this.serverVersion<u.default.ACCT_SUMMARY)return this.emitError("It does not support account summary requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_ACCOUNT_SUMMARY,1,e,t,r)}reqPnL(e,t,r){if(this.serverVersion<u.default.PNL)return this.emitError("It does not support pnl requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_PNL,e,t,r)}cancelPnL(e){if(this.serverVersion<u.default.PNL)return this.emitError("It does not support pnl requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_PNL,e)}reqPnLSingle(e,t,r,n){if(this.serverVersion<u.default.PNL)return this.emitError("It does not support pnl requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_PNL_SINGLE,e,t,r,n)}cancelPnLSingle(e){if(this.serverVersion<u.default.PNL)return this.emitError("It does not support pnl requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_PNL_SINGLE,e)}reqAccountUpdates(e,t){let r=[i.REQ_ACCOUNT_DATA,2,e];this.serverVersion>=9&&r.push(t),this.sendMsg(r)}reqAccountUpdatesMulti(e,t,r,n){this.sendMsg(i.REQ_ACCOUNT_UPDATES_MULTI,2,e,t,r,n)}reqAllOpenOrders(){this.sendMsg(i.REQ_ALL_OPEN_ORDERS,1)}reqAutoOpenOrders(e){this.sendMsg(i.REQ_AUTO_OPEN_ORDERS,1,e)}reqHeadTimestamp(e,t,r,n,o){if(this.serverVersion<u.default.REQ_HEAD_TIMESTAMP)return this.emitError("It does not support reqHeadTimeStamp",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_HEAD_TIMESTAMP,e,this.encodeContract(t),+!!n,r,o)}reqContractDetails(e,t){if(this.serverVersion<4)return this.emitError("This feature is only available for versions of TWS >=4",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SEC_ID_TYPE&&(t.secIdType||t.secId))return this.emitError("It does not support secIdType and secId parameters.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&t.tradingClass)return this.emitError("It does not support tradingClass parameter in reqContractDetails.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.LINKING&&t.primaryExch)return this.emitError("It does not support primaryExchange parameter in reqContractDetails.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.BOND_ISSUERID&&t.issuerId)return this.emitError("It does not support issuerId parameter in reqContractDetails.",_.ErrorCode.UPDATE_TWS,e);let r=[i.REQ_CONTRACT_DATA,8];this.serverVersion>=u.default.CONTRACT_DATA_CHAIN&&r.push(e),this.serverVersion>=u.default.CONTRACT_CONID&&r.push(t.conId),r.push(t.symbol),r.push(t.secType),r.push(t.lastTradeDateOrContractMonth),r.push(t.strike),r.push(t.right),this.serverVersion>=15&&r.push(t.multiplier),this.serverVersion>=u.default.PRIMARYEXCH?(r.push(t.exchange),r.push(t.primaryExch)):this.serverVersion>=u.default.LINKING&&(t.primaryExch&&("BEST"===t.exchange||"SMART"===t.exchange)?r.push(t.exchange+":"+t.primaryExch):r.push(t.exchange)),r.push(t.currency),r.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&r.push(t.tradingClass),this.serverVersion>=31&&r.push(t.includeExpired),this.serverVersion>=u.default.SEC_ID_TYPE&&(r.push(t.secIdType),r.push(t.secId)),this.serverVersion>=u.default.BOND_ISSUERID&&r.push(t.issuerId),this.sendMsg(r)}reqCurrentTime(){if(this.serverVersion<33)return this.emitError("It does not support current time requests.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_CURRENT_TIME,1)}reqExecutions(e,t){let r=[i.REQ_EXECUTIONS,3];this.serverVersion>=u.default.EXECUTION_DATA_CHAIN&&r.push(e),this.serverVersion>=9&&(r.push(t.clientId),r.push(t.acctCode),r.push(t.time),r.push(t.symbol),r.push(t.secType),r.push(t.exchange),r.push(t.side)),this.sendMsg(r)}reqFundamentalData(e,t,r,n){if(this.serverVersion<u.default.FUNDAMENTAL_DATA)return this.emitError("It does not support fundamental data requests.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&void 0!=t.conId)return this.emitError("It does not support conId parameter in reqFundamentalData.",_.ErrorCode.UPDATE_TWS,e);let o=[i.REQ_FUNDAMENTAL_DATA,2,e];this.serverVersion>=u.default.TRADING_CLASS&&o.push(t.conId),o.push(t.symbol),o.push(t.secType),o.push(t.exchange),o.push(t.primaryExch),o.push(t.currency),o.push(t.localSymbol),o.push(r),this.serverVersion>=u.default.LINKING&&o.push(this.encodeTagValues(n)),this.sendMsg(o)}reqGlobalCancel(e){if(this.serverVersion<u.default.REQ_GLOBAL_CANCEL)return this.emitError("It does not support globalCancel requests.",_.ErrorCode.UPDATE_TWS);if(this.serverVersion<u.default.CME_TAGGING_FIELDS&&(e.extOperator?.length||void 0!=e.manualOrderIndicator))return this.emitError("It does not support ext operator and manual order indicator parameters",_.ErrorCode.UPDATE_TWS);let t=[i.REQ_GLOBAL_CANCEL];this.serverVersion<u.default.CME_TAGGING_FIELDS&&t.push(1),this.serverVersion>=u.default.CME_TAGGING_FIELDS&&(t.push(e.extOperator),t.push(e.manualOrderIndicator??0x7fffffff)),this.sendMsg(t)}reqHistoricalData(e,t,r,n,o,s,a,l,d,h){if(this.serverVersion<16)return this.emitError("It does not support historical data backfill.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&(t.tradingClass||void 0!=t.conId))return this.emitError("It does not support conId and tradingClass parameters in reqHistoricalData.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.HISTORICAL_SCHEDULE&&"string"==typeof s&&"SCHEDULE"===s.toUpperCase())return this.emitError("It does not support requesting of historical schedule.",_.ErrorCode.UPDATE_TWS,e);let f=[i.REQ_HISTORICAL_DATA];this.serverVersion<u.default.SYNT_REALTIME_BARS&&f.push(6),f.push(e),this.serverVersion>=u.default.TRADING_CLASS&&f.push(t.conId),f.push(t.symbol),f.push(t.secType),f.push(t.lastTradeDateOrContractMonth),f.push(t.strike),f.push(t.right),f.push(t.multiplier),f.push(t.exchange),f.push(t.primaryExch),f.push(t.currency),f.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&f.push(t.tradingClass),this.serverVersion>=31&&f.push(!!t.includeExpired),this.serverVersion>=20&&(f.push(r),f.push(o)),f.push(n),f.push(a),f.push(s),this.serverVersion>16&&f.push(l),c.default.BAG===t.secType?.toUpperCase()&&(t.comboLegs?(f.push(t.comboLegs.length),t.comboLegs.forEach(e=>{f.push(e.conId),f.push(e.ratio),f.push(e.action),f.push(e.exchange)})):f.push(0)),this.serverVersion>=u.default.SYNT_REALTIME_BARS&&f.push(d),this.serverVersion>=u.default.LINKING&&f.push(this.encodeTagValues(h)),this.sendMsg(f)}reqHistoricalTicks(e,t,r,n,o,s,a,c,l){if(this.serverVersion<u.default.HISTORICAL_TICKS)return this.emitError("It does not support historical ticks request.",_.ErrorCode.UPDATE_TWS,e);let d=[i.REQ_HISTORICAL_TICKS,e,...this.encodeContract(t)];d.push(r),d.push(n),d.push(o),d.push(s),d.push(a),d.push(c),d.push(this.encodeTagValues(l)),this.sendMsg(d)}encodeReqTickByTickData(e,t,r,n,o){let s=[i.REQ_TICK_BY_TICK_DATA,e];return s.push(t.conId),s.push(t.symbol),s.push(t.secType),s.push(t.lastTradeDateOrContractMonth),s.push(t.strike),s.push(t.right),s.push(t.multiplier),s.push(t.exchange),s.push(t.primaryExch),s.push(t.currency),s.push(t.localSymbol),s.push(t.tradingClass),s.push(r),this.serverVersion>=u.default.TICK_BY_TICK_IGNORE_SIZE&&(s.push(n),s.push(o)),s}reqTickByTickData(e,t,r,n,i){if(this.serverVersion<u.default.TICK_BY_TICK)this.emitError("It does not support tick-by-tick data requests.",_.ErrorCode.UPDATE_TWS,e);else if(this.serverVersion<u.default.TICK_BY_TICK_IGNORE_SIZE&&(0!=n||i))this.emitError("It does not support ignoreSize and numberOfTicks parameters in tick-by-tick data requests.",_.ErrorCode.UPDATE_TWS,e);else{let o=this.encodeReqTickByTickData(e,t,r,n,i);this.sendMsg(o)}}cancelTickByTickData(e){if(this.serverVersion<u.default.TICK_BY_TICK)return this.emitError("It does not support tick-by-tick data cancels.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_TICK_BY_TICK_DATA,e)}reqIds(e){this.sendMsg(i.REQ_IDS,1,e)}reqManagedAccts(){this.sendMsg(i.REQ_MANAGED_ACCTS,1)}reqMarketDataType(e){if(this.serverVersion<u.default.REQ_MARKET_DATA_TYPE)return this.emitError("It does not support marketDataType requests.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_MARKET_DATA_TYPE,1,e)}reqMktData(e,t,r,n,o){if(this.serverVersion<u.default.SNAPSHOT_MKT_DATA&&n)return this.emitError("It does not support snapshot market data requests.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.DELTA_NEUTRAL)return this.emitError("It does not support delta-neutral orders.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.REQ_MKT_DATA_CONID)return this.emitError("It does not support conId parameter.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&t.tradingClass)return this.emitError("It does not support tradingClass parameter in reqMarketData.",_.ErrorCode.UPDATE_TWS,e);let s=[i.REQ_MKT_DATA,11,e];this.serverVersion>=u.default.REQ_MKT_DATA_CONID&&s.push(t.conId),s.push(t.symbol),s.push(t.secType),s.push(t.lastTradeDateOrContractMonth),s.push(t.strike),s.push(t.right),this.serverVersion>=15&&s.push(t.multiplier),s.push(t.exchange),this.serverVersion>=14&&s.push(t.primaryExch),s.push(t.currency),this.serverVersion>=2&&s.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&s.push(t.tradingClass),this.serverVersion>=8&&c.default.BAG===t.secType?.toUpperCase()&&(t.comboLegs?(s.push(t.comboLegs.length),t.comboLegs.forEach(e=>{s.push(e.conId),s.push(e.ratio),s.push(e.action),s.push(e.exchange)})):s.push(0)),this.serverVersion>=u.default.DELTA_NEUTRAL&&(t.deltaNeutralContract?(s.push(!0),s.push(t.deltaNeutralContract.conId),s.push(t.deltaNeutralContract.delta),s.push(t.deltaNeutralContract.price)):s.push(!1)),this.serverVersion>=31&&s.push(r),this.serverVersion>=u.default.SNAPSHOT_MKT_DATA&&s.push(n),this.serverVersion>=u.default.REQ_SMART_COMPONENTS&&s.push(o),this.serverVersion>=u.default.LINKING&&s.push(""),this.sendMsg(s)}reqMktDepth(e,t,r,n,o){if(this.serverVersion<6)return this.emitError("This feature is only available for versions of TWS >=6",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&(t.tradingClass||void 0!=t.conId))return this.emitError("It does not support conId and tradingClass parameters in reqMktDepth.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SMART_DEPTH&&n)return this.emitError("It does not support SMART depth request.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.MKT_DEPTH_PRIM_EXCHANGE&&t.primaryExch)return this.emitError("It does not support primaryExch parameter in reqMktDepth.",_.ErrorCode.UPDATE_TWS,e);let s=[i.REQ_MKT_DEPTH,5,e];this.serverVersion>=u.default.TRADING_CLASS&&s.push(t.conId),s.push(t.symbol),s.push(t.secType),s.push(t.lastTradeDateOrContractMonth),s.push(t.strike),s.push(t.right),this.serverVersion>=15&&s.push(t.multiplier),s.push(t.exchange),this.serverVersion>=u.default.MKT_DEPTH_PRIM_EXCHANGE&&s.push(t.primaryExch),s.push(t.currency),s.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&s.push(t.tradingClass),this.serverVersion>=19&&s.push(r),this.serverVersion>=u.default.SMART_DEPTH&&s.push(n),this.serverVersion>=u.default.LINKING&&s.push(this.encodeTagValues(o)),this.sendMsg(s)}reqNewsBulletins(e){this.sendMsg(i.REQ_NEWS_BULLETINS,1,e)}reqOpenOrders(){this.sendMsg(i.REQ_OPEN_ORDERS,1)}reqPositions(){if(this.serverVersion<u.default.ACCT_SUMMARY)return this.emitError("It does not support position requests.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_POSITIONS,1)}reqPositionsMulti(e,t,r){if(this.serverVersion<u.default.MODELS_SUPPORT)return this.emitError("It does not support position requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_POSITIONS_MULTI,1,e,t,r)}cancelPositionsMulti(e){if(this.serverVersion<u.default.MODELS_SUPPORT)return this.emitError("It does not support positions multi cancellation.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_POSITIONS_MULTI,1,e)}reqRealTimeBars(e,t,r,n,o,s){if(this.serverVersion<u.default.REAL_TIME_BARS)return this.emitError("It does not support real time bars.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.TRADING_CLASS&&(t.tradingClass||void 0!=t.conId))return this.emitError("It does not support conId and tradingClass parameters in reqRealTimeBars.",_.ErrorCode.UPDATE_TWS,e);let a=[i.REQ_REAL_TIME_BARS,3,e];this.serverVersion>=u.default.TRADING_CLASS&&a.push(t.conId),a.push(t.symbol),a.push(t.secType),a.push(t.lastTradeDateOrContractMonth),a.push(t.strike),a.push(t.right),a.push(t.multiplier),a.push(t.exchange),a.push(t.primaryExch),a.push(t.currency),a.push(t.localSymbol),this.serverVersion>=u.default.TRADING_CLASS&&a.push(t.tradingClass),a.push(r),a.push(n),a.push(o),this.serverVersion>=u.default.LINKING&&a.push(this.encodeTagValues(s)),this.sendMsg(a)}reqScannerParameters(){if(this.serverVersion<24)return this.emitError("It does not support API scanner subscription.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_SCANNER_PARAMETERS,1)}reqScannerSubscription(e,t,r,n){if(this.serverVersion<24)return this.emitError("It does not support API scanner subscription.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.SCANNER_GENERIC_OPTS&&n)return this.emitError("It does not support API scanner subscription generic filter options.",_.ErrorCode.UPDATE_TWS,e);let o=[i.REQ_SCANNER_SUBSCRIPTION];this.serverVersion<u.default.SCANNER_GENERIC_OPTS&&o.push(4),o.push(e),o.push(f(t.numberOfRows)),o.push(t.instrument),o.push(t.locationCode),o.push(s.ScanCode[t.scanCode]),o.push(f(t.abovePrice)),o.push(f(t.belowPrice)),o.push(f(t.aboveVolume)),o.push(f(t.marketCapAbove)),o.push(f(t.marketCapBelow)),o.push(t.moodyRatingAbove),o.push(t.moodyRatingBelow),o.push(t.spRatingAbove),o.push(t.spRatingBelow),o.push(t.maturityDateAbove),o.push(t.maturityDateBelow),o.push(f(t.couponRateAbove)),o.push(f(t.couponRateBelow)),o.push(t.excludeConvertible),this.serverVersion>=25&&(o.push(f(t.averageOptionVolumeAbove)),o.push(t.scannerSettingPairs)),this.serverVersion>=27&&o.push(t.stockTypeFilter),this.serverVersion>=u.default.SCANNER_GENERIC_OPTS&&o.push(this.encodeTagValues(n)),this.serverVersion>=u.default.LINKING&&o.push(this.encodeTagValues(r)),this.sendMsg(o)}requestFA(e){if(this.serverVersion>=u.default.FA_PROFILE_DESUPPORT&&e==a.default.PROFILES)return this.emitError("FA Profile is not supported anymore, use FA Group instead.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_FA,1,e)}setServerLogLevel(e){this.sendMsg(i.SET_SERVER_LOGLEVEL,1,e)}queryDisplayGroups(e){this.sendMsg(i.QUERY_DISPLAY_GROUPS,1,e)}updateDisplayGroup(e,t){this.sendMsg(i.UPDATE_DISPLAY_GROUP,1,e,t)}subscribeToGroupEvents(e,t){this.sendMsg(i.SUBSCRIBE_TO_GROUP_EVENTS,1,e,t)}unsubscribeToGroupEvents(e){this.sendMsg(i.UNSUBSCRIBE_FROM_GROUP_EVENTS,1,e)}reqSecDefOptParams(e,t,r,n,o){if(this.serverVersion<u.default.SEC_DEF_OPT_PARAMS_REQ)return this.emitError("It does not support reqSecDefOptParams.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_SEC_DEF_OPT_PARAMS,e,t,r,n,o)}reqSoftDollarTiers(e){if(this.serverVersion<u.default.SOFT_DOLLAR_TIER)return this.emitError("It does not support soft dollar tier requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_SOFT_DOLLAR_TIERS,e)}reqFamilyCodes(){if(this.serverVersion<u.default.REQ_FAMILY_CODES)return this.emitError("It does not support family codes request.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_FAMILY_CODES)}reqMatchingSymbols(e,t){if(this.serverVersion<u.default.REQ_MATCHING_SYMBOLS)return this.emitError("It does not support matching symbols request.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_MATCHING_SYMBOLS,e,t)}reqMktDepthExchanges(){if(this.serverVersion<u.default.REQ_MKT_DEPTH_EXCHANGES)return this.emitError("It does not support market depth exchanges request.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_MKT_DEPTH_EXCHANGES)}reqSmartComponents(e,t){if(this.serverVersion<u.default.REQ_SMART_COMPONENTS)return this.emitError("It does not support smart components request.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_SMART_COMPONENTS,e,t)}reqNewsArticle(e,t,r,n){if(this.serverVersion<u.default.REQ_NEWS_ARTICLE)return this.emitError("It does not support news article request.",_.ErrorCode.UPDATE_TWS,e);let o=[i.REQ_NEWS_ARTICLE,e,t,r];this.serverVersion>=u.default.NEWS_QUERY_ORIGINS&&o.push(this.encodeTagValues(n)),this.sendMsg(o)}reqNewsProviders(){if(this.serverVersion<u.default.REQ_SMART_COMPONENTS)return this.emitError("It does not support smart components request.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_NEWS_PROVIDERS)}reqHistoricalNews(e,t,r,n,o,s,a){if(this.serverVersion<u.default.REQ_HISTORICAL_NEWS)return this.emitError("It does not support historical news request.",_.ErrorCode.UPDATE_TWS,e);let c=[i.REQ_HISTORICAL_NEWS,e,t,r,n,o,s];this.serverVersion>=u.default.NEWS_QUERY_ORIGINS&&c.push(this.encodeTagValues(a)),this.sendMsg(c)}reqHistogramData(e,t,r,n){if(this.serverVersion<u.default.REQ_HISTOGRAM)return this.emitError("It does not support histogram requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_HISTOGRAM_DATA,e,this.encodeContract(t),+!!r,n)}cancelHistogramData(e){if(this.serverVersion<u.default.REQ_HISTOGRAM)return this.emitError("It does not support head time stamp requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_HISTOGRAM_DATA,e)}cancelHeadTimestamp(e){if(this.serverVersion<u.default.CANCEL_HEADTIMESTAMP)return this.emitError("It does not support head time stamp requests canceling.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_HEAD_TIMESTAMP,e)}reqMarketRule(e){if(this.serverVersion<u.default.MARKET_RULES)return this.emitError("It does not support market rule requests.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_MARKET_RULE,e)}reqCompletedOrders(e){if(this.serverVersion<u.default.REQ_COMPLETED_ORDERS)return this.emitError("It does not support completed orders requests.",_.ErrorCode.UPDATE_TWS);this.sendMsg(i.REQ_COMPLETED_ORDERS,e)}reqWshMetaData(e){if(this.serverVersion<u.default.WSHE_CALENDAR)return this.emitError("It does not support WSHE Calendar API.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_WSH_META_DATA,e)}reqCancelWshMetaData(e){if(this.serverVersion<u.default.WSHE_CALENDAR)return this.emitError("It does not support WSHE Calendar API.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_WSH_META_DATA,e)}reqWshEventData(e,t){if(this.serverVersion<u.default.WSHE_CALENDAR)return this.emitError("It does not support WSHE Calendar API.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.WSH_EVENT_DATA_FILTERS&&(t.filter?.length||t.fillWatchlist||t.fillPortfolio||t.fillCompetitors))return this.emitError("It does not support WSH event data filters.",_.ErrorCode.UPDATE_TWS,e);if(this.serverVersion<u.default.WSH_EVENT_DATA_FILTERS_DATE&&(t.startDate||t.endDate||t.totalLimit))return this.emitError("It does not support WSH event data date filters.",_.ErrorCode.UPDATE_TWS,e);let r=[i.REQ_WSH_EVENT_DATA,e,t.conId];this.serverVersion>=u.default.WSH_EVENT_DATA_FILTERS&&(r.push(t.filter),r.push(t.fillWatchlist),r.push(t.fillPortfolio),r.push(t.fillCompetitors)),this.serverVersion>=u.default.WSH_EVENT_DATA_FILTERS_DATE&&(r.push(t.startDate),r.push(t.endDate),r.push(t.totalLimit)),this.sendMsg(r)}reqCancelWshEventData(e){if(this.serverVersion<u.default.WSHE_CALENDAR)return this.emitError("It does not support WSHE Calendar API.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.CANCEL_WSH_EVENT_DATA,e)}reqUserInfo(e){if(this.serverVersion<u.default.USER_INFO)return this.emitError("It does not support user info requests.",_.ErrorCode.UPDATE_TWS,e);this.sendMsg(i.REQ_USER_INFO,e)}}t.Encoder=E},89519:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Decoder=t.UnderrunError=void 0;let i=r(79898),o=n(r(48369)),s=n(r(86190)),a=n(r(15850)),u=r(19743),c=n(r(84310)),l=n(r(52974)),d=n(r(83364)),h=n(r(98491)),_=n(r(12143)),f=n(r(22410)),E=r(48909),p=r(55332),T=r(7133),A=r(69998);function I(e){return -1!==Object.values(s.default).indexOf(e)?e:void 0}class v extends Error{constructor(e="An underrun error has occurred"){super(),this.message=e,this.stack=Error().stack,this.name="UnderrunError"}}t.UnderrunError=v;class O{constructor(e){this.callback=e,this.dataQueue=[],this.emitQueue=[],this.readBoolFromInt=this.readBool,this.readIntMax=this.readIntOrUndefined}enqueueMessage(e){this.dataQueue.push(void 0),this.dataQueue=this.dataQueue.concat(e),this.dataQueue.push(void 0)}enqueueTokens(e){this.dataQueue=this.dataQueue.concat(e)}processMsg(e){switch(e){case A.IN_MSG_ID.TICK_PRICE:return this.decodeMsg_TICK_PRICE();case A.IN_MSG_ID.TICK_SIZE:return this.decodeMsg_TICK_SIZE();case A.IN_MSG_ID.ORDER_STATUS:return this.decodeMsg_ORDER_STATUS();case A.IN_MSG_ID.ERR_MSG:return this.decodeMsg_ERR_MSG();case A.IN_MSG_ID.OPEN_ORDER:return this.decodeMsg_OPEN_ORDER();case A.IN_MSG_ID.ACCT_VALUE:return this.decodeMsg_ACCT_VALUE();case A.IN_MSG_ID.PORTFOLIO_VALUE:return this.decodeMsg_PORTFOLIO_VALUE();case A.IN_MSG_ID.ACCT_UPDATE_TIME:return this.decodeMsg_ACCT_UPDATE_TIME();case A.IN_MSG_ID.NEXT_VALID_ID:return this.decodeMsg_NEXT_VALID_ID();case A.IN_MSG_ID.CONTRACT_DATA:return this.decodeMsg_CONTRACT_DATA();case A.IN_MSG_ID.EXECUTION_DATA:return this.decodeMsg_EXECUTION_DATA();case A.IN_MSG_ID.MARKET_DEPTH:return this.decodeMsg_MARKET_DEPTH();case A.IN_MSG_ID.MARKET_DEPTH_L2:return this.decodeMsg_MARKET_DEPTH_L2();case A.IN_MSG_ID.NEWS_BULLETINS:return this.decodeMsg_NEWS_BULLETINS();case A.IN_MSG_ID.MANAGED_ACCTS:return this.decodeMsg_MANAGED_ACCTS();case A.IN_MSG_ID.RECEIVE_FA:return this.decodeMsg_RECEIVE_FA();case A.IN_MSG_ID.HISTORICAL_DATA:return this.decodeMsg_HISTORICAL_DATA();case A.IN_MSG_ID.BOND_CONTRACT_DATA:return this.decodeMsg_BOND_CONTRACT_DATA();case A.IN_MSG_ID.SCANNER_PARAMETERS:return this.decodeMsg_SCANNER_PARAMETERS();case A.IN_MSG_ID.SCANNER_DATA:return this.decodeMsg_SCANNER_DATA();case A.IN_MSG_ID.TICK_OPTION_COMPUTATION:return this.decodeMsg_TICK_OPTION_COMPUTATION();case A.IN_MSG_ID.TICK_GENERIC:return this.decodeMsg_TICK_GENERIC();case A.IN_MSG_ID.TICK_STRING:return this.decodeMsg_TICK_STRING();case A.IN_MSG_ID.TICK_EFP:return this.decodeMsg_TICK_EFP();case A.IN_MSG_ID.CURRENT_TIME:return this.decodeMsg_CURRENT_TIME();case A.IN_MSG_ID.REAL_TIME_BARS:return this.decodeMsg_REAL_TIME_BARS();case A.IN_MSG_ID.FUNDAMENTAL_DATA:return this.decodeMsg_FUNDAMENTAL_DATA();case A.IN_MSG_ID.CONTRACT_DATA_END:return this.decodeMsg_CONTRACT_DATA_END();case A.IN_MSG_ID.OPEN_ORDER_END:return this.decodeMsg_OPEN_ORDER_END();case A.IN_MSG_ID.ACCT_DOWNLOAD_END:return this.decodeMsg_ACCT_DOWNLOAD_END();case A.IN_MSG_ID.EXECUTION_DATA_END:return this.decodeMsg_EXECUTION_DATA_END();case A.IN_MSG_ID.DELTA_NEUTRAL_VALIDATION:return this.decodeMsg_DELTA_NEUTRAL_VALIDATION();case A.IN_MSG_ID.TICK_SNAPSHOT_END:return this.decodeMsg_TICK_SNAPSHOT_END();case A.IN_MSG_ID.MARKET_DATA_TYPE:return this.decodeMsg_MARKET_DATA_TYPE();case A.IN_MSG_ID.COMMISSION_REPORT:return this.decodeMsg_COMMISSION_REPORT();case A.IN_MSG_ID.POSITION:return this.decodeMsg_POSITION();case A.IN_MSG_ID.POSITION_END:return this.decodeMsg_POSITION_END();case A.IN_MSG_ID.ACCOUNT_SUMMARY:return this.decodeMsg_ACCOUNT_SUMMARY();case A.IN_MSG_ID.ACCOUNT_SUMMARY_END:return this.decodeMsg_ACCOUNT_SUMMARY_END();case A.IN_MSG_ID.DISPLAY_GROUP_LIST:return this.decodeMsg_DISPLAY_GROUP_LIST();case A.IN_MSG_ID.DISPLAY_GROUP_UPDATED:return this.decodeMsg_DISPLAY_GROUP_UPDATED();case A.IN_MSG_ID.POSITION_MULTI:return this.decodeMsg_POSITION_MULTI();case A.IN_MSG_ID.POSITION_MULTI_END:return this.decodeMsg_POSITION_MULTI_END();case A.IN_MSG_ID.ACCOUNT_UPDATE_MULTI:return this.decodeMsg_ACCOUNT_UPDATE_MULTI();case A.IN_MSG_ID.ACCOUNT_UPDATE_MULTI_END:return this.decodeMsg_ACCOUNT_UPDATE_MULTI_END();case A.IN_MSG_ID.SECURITY_DEFINITION_OPTION_PARAMETER:return this.decodeMsg_SECURITY_DEFINITION_OPTION_PARAMETER();case A.IN_MSG_ID.SECURITY_DEFINITION_OPTION_PARAMETER_END:return this.decodeMsg_SECURITY_DEFINITION_OPTION_PARAMETER_END();case A.IN_MSG_ID.SOFT_DOLLAR_TIERS:return this.decodeMsg_SOFT_DOLLAR_TIERS();case A.IN_MSG_ID.FAMILY_CODES:return this.decodeMsg_FAMILY_CODES();case A.IN_MSG_ID.SYMBOL_SAMPLES:return this.decodeMsg_SYMBOL_SAMPLES();case A.IN_MSG_ID.MKT_DEPTH_EXCHANGES:return this.decodeMsg_MKT_DEPTH_EXCHANGES();case A.IN_MSG_ID.TICK_REQ_PARAMS:return this.decodeMsg_TICK_REQ_PARAMS();case A.IN_MSG_ID.SMART_COMPONENTS:return this.decodeMsg_SMART_COMPONENTS();case A.IN_MSG_ID.NEWS_ARTICLE:return this.decodeMsg_NEWS_ARTICLE();case A.IN_MSG_ID.TICK_NEWS:return this.decodeMsg_TICK_NEWS();case A.IN_MSG_ID.NEWS_PROVIDERS:return this.decodeMsg_NEWS_PROVIDERS();case A.IN_MSG_ID.HISTORICAL_NEWS:return this.decodeMsg_HISTORICAL_NEWS();case A.IN_MSG_ID.HISTORICAL_NEWS_END:return this.decodeMsg_HISTORICAL_NEWS_END();case A.IN_MSG_ID.HEAD_TIMESTAMP:return this.decodeMsg_HEAD_TIMESTAMP();case A.IN_MSG_ID.HISTOGRAM_DATA:return this.decodeMsg_HISTOGRAM_DATA();case A.IN_MSG_ID.HISTORICAL_DATA_UPDATE:return this.decodeMsg_HISTORICAL_DATA_UPDATE();case A.IN_MSG_ID.REROUTE_MKT_DATA:return this.decodeMsg_REROUTE_MKT_DATA();case A.IN_MSG_ID.REROUTE_MKT_DEPTH:return this.decodeMsg_REROUTE_MKT_DEPTH();case A.IN_MSG_ID.MARKET_RULE:return this.decodeMsg_MARKET_RULE();case A.IN_MSG_ID.PNL:return this.decodeMsg_PNL();case A.IN_MSG_ID.PNL_SINGLE:return this.decodeMsg_PNL_SINGLE();case A.IN_MSG_ID.HISTORICAL_TICKS:return this.decodeMsg_HISTORICAL_TICKS();case A.IN_MSG_ID.HISTORICAL_TICKS_BID_ASK:return this.decodeMsg_HISTORICAL_TICKS_BID_ASK();case A.IN_MSG_ID.HISTORICAL_TICKS_LAST:return this.decodeMsg_HISTORICAL_TICKS_LAST();case A.IN_MSG_ID.TICK_BY_TICK:return this.decodeMsg_TICK_BY_TICK();case A.IN_MSG_ID.ORDER_BOUND:return this.decodeMsg_ORDER_BOUND();case A.IN_MSG_ID.COMPLETED_ORDER:return this.decodeMsg_COMPLETED_ORDER();case A.IN_MSG_ID.COMPLETED_ORDERS_END:return this.decodeMsg_COMPLETED_ORDERS_END();case A.IN_MSG_ID.REPLACE_FA_END:return this.decodeMsg_REPLACE_FA_END();case A.IN_MSG_ID.WSH_META_DATA:return this.decodeMsg_WSH_META_DATA();case A.IN_MSG_ID.WSH_EVENT_DATA:return this.decodeMsg_WSH_EVENT_DATA();case A.IN_MSG_ID.HISTORICAL_SCHEDULE:return this.decodeMsg_HISTORICAL_SCHEDULE();case A.IN_MSG_ID.USER_INFO:return this.decodeMsg_USER_INFO();default:this.callback.emitError(`No parser implementation found for token: ${A.IN_MSG_ID[e]} (${e}).`,T.ErrorCode.UNKNOWN_ID)}}process(){for(;this.dataQueue.length;){this.emitQueue=[];let e=!1;void 0===this.dataQueue[0]&&(e=!0,this.dataQueue.shift());let t=A.IN_MSG_ID.UNDEFINED;try{t=this.readInt(),this.processMsg(t),e&&(void 0!==this.dataQueue[0]&&this.callback.emitError(`Decoding error on ${A.IN_MSG_ID[t]}: unprocessed data left on queue (${JSON.stringify(this.dataQueue)}). Please report to https://github.com/stoqey/ib`,T.ErrorCode.UNKNOWN_ID),this.drainQueue())}catch(r){if("UnderrunError"!==r.name)throw r;e&&this.callback.emitError(`Underrun error on ${A.IN_MSG_ID[t]}: ${r.message} Please report to https://github.com/stoqey/ib`,T.ErrorCode.UNKNOWN_ID),this.drainQueue()}let r=this.emitQueue;this.emitQueue=[],r.forEach(e=>this.callback.emitEvent(e.name,...e.args))}}get serverVersion(){return this.callback.serverVersion}decodeUnicodeEscapedString(e){let t=e;try{for(;;){let e=t.indexOf("\\u");if(-1==e||t.length-e<6)break;let r=t.substring(e,e+6),n=parseInt(r.replace("\\u",""),16);t=t.replace(r,String.fromCharCode(n))}}catch(e){}return t}readStr(){if(0===this.dataQueue.length)throw new v;let e=this.dataQueue.shift();if(void 0===e)throw new v("End of message reached.");return e}readBool(){return!!parseInt(this.readStr())}readDouble(){let e=this.readStr();if(""===e)return 0;let t=parseFloat(e);return t===Number.MAX_VALUE?void 0:t}readDecimal(){let e=this.readStr();if(""===e)return;let t=parseFloat(e.replaceAll(",",""));return t===Number.MAX_VALUE||t===1/0?void 0:t}readDoubleOrUndefined(){let e=this.readStr();if(""===e)return;let t=parseFloat(e);return t===Number.MAX_VALUE?void 0:t}readInt(){let e=this.readStr();return""===e?0:parseInt(e,10)}readIntOrUndefined(){let e=this.readStr();if(""===e)return;let t=parseInt(e,10);return 0x7fffffff===t?void 0:t}drainQueue(){for(;this.dataQueue.length&&void 0!==this.dataQueue[0];)this.dataQueue.shift();this.dataQueue.length&&this.dataQueue.shift()}emit(e,...t){this.emitQueue.push({name:e,args:t})}decodeMsg_TICK_PRICE(){let e,t,r,n=this.readInt(),o=this.readInt(),s=this.readInt(),a=this.readDouble();if(n>=2&&(e=this.readDecimal()),n>=3&&(t=this.readBool()),this.emit(i.EventName.tickPrice,o,s,a,t),n>=2)switch(s){case u.TickType.BID:r=u.TickType.BID_SIZE;break;case u.TickType.ASK:r=u.TickType.ASK_SIZE;break;case u.TickType.LAST:r=u.TickType.LAST_SIZE;break;case u.TickType.DELAYED_BID:r=u.TickType.DELAYED_BID_SIZE;break;case u.TickType.DELAYED_ASK:r=u.TickType.DELAYED_ASK_SIZE;break;case u.TickType.DELAYED_LAST:r=u.TickType.DELAYED_LAST_SIZE}r&&this.emit(i.EventName.tickSize,o,r,e)}decodeMsg_TICK_SIZE(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readDecimal();this.emit(i.EventName.tickSize,e,t,r)}decodeMsg_ORDER_STATUS(){let e,t,r,n,s,a,u=this.serverVersion>=o.default.MARKET_CAP_PRICE?Number.MAX_SAFE_INTEGER:this.readInt(),c=this.readInt(),l=this.readStr(),d=this.readDecimal(),h=this.readDecimal(),_=this.readDouble();u>=2&&(e=this.readInt()),u>=3&&(t=this.readInt()),u>=4&&(r=this.readDouble()),u>=5&&(n=this.readInt()),u>=6&&(s=this.readStr()),this.serverVersion>=o.default.MARKET_CAP_PRICE&&(a=this.readDouble()),this.emit(i.EventName.orderStatus,c,l,d,h,_,e,t,r,n,s,a)}decodeMsg_ERR_MSG(){if(2>this.readInt()){let e=this.readStr();this.callback.emitError(e,T.ErrorCode.UNKNOWN_ID)}else{let e,t=this.readInt(),r=this.readInt(),n=this.readStr();if(this.serverVersion>=o.default.ENCODE_MSG_ASCII7&&(n=this.decodeUnicodeEscapedString(n)),this.serverVersion>=o.default.ADVANCED_ORDER_REJECT){let t=this.readStr();t?.length>0&&(e=JSON.parse(this.decodeUnicodeEscapedString(t)))}t===T.ErrorCode.NO_VALID_ID?this.callback.emitInfo(n,r):this.callback.emitError(n,r,t,e)}}decodeMsg_OPEN_ORDER(){let e=this.serverVersion<o.default.ORDER_CONTAINER?this.readInt():this.serverVersion,t={},r={},n={},s=new S(this,t,r,n,e,this.serverVersion);s.readOrderId(),s.readContractFields(),s.readAction(),s.readTotalQuantity(),s.readOrderType(),s.readLmtPrice(),s.readAuxPrice(),s.readTIF(),s.readOcaGroup(),s.readAccount(),s.readOpenClose(),s.readOrigin(),s.readOrderRef(),s.readClientId(),s.readPermId(),s.readOutsideRth(),s.readHidden(),s.readDiscretionaryAmount(),s.readGoodAfterTime(),s.skipSharesAllocation(),s.readFAParams(),s.readModelCode(),s.readGoodTillDate(),s.readRule80A(),s.readPercentOffset(),s.readSettlingFirm(),s.readShortSaleParams(),s.readAuctionStrategy(),s.readBoxOrderParams(),s.readPegToStkOrVolOrderParams(),s.readDisplaySize(),s.readOldStyleOutsideRth(),s.readBlockOrder(),s.readSweepToFill(),s.readAllOrNone(),s.readMinQty(),s.readOcaType(),s.readETradeOnly(),s.readFirmQuoteOnly(),s.readNbboPriceCap(),s.readParentId(),s.readTriggerMethod(),s.readVolOrderParams(!0),s.readTrailParams(),s.readBasisPoints(),s.readComboLegs(),s.readSmartComboRoutingParams(),s.readScaleOrderParams(),s.readHedgeParams(),s.readOptOutSmartRouting(),s.readClearingParams(),s.readNotHeld(),s.readDeltaNeutral(),s.readAlgoParams(),s.readSolicited(),s.readWhatIfInfoAndCommission(),s.readVolRandomizeFlags(),s.readPegToBenchParams(),s.readConditions(),s.readAdjustedOrderParams(),s.readSoftDollarTier(),s.readCashQty(),s.readDontUseAutoPriceForHedge(),s.readIsOmsContainer(),s.readDiscretionaryUpToLimitPrice(),s.readUsePriceMgmtAlgo(),s.readDuration(),s.readPostToAts(),s.readAutoCancelParent(o.default.AUTO_CANCEL_PARENT),s.readPegBestPegMidOrderAttributes(),s.readCustomerAccount(),s.readProfessionalCustomer(),s.readBondAccruedInterest(),s.readIncludeOvernight(),s.readCMETaggingFields(),this.emit(i.EventName.openOrder,r.orderId,t,r,n)}decodeMsg_ACCT_VALUE(){this.readInt();let e=this.readStr(),t=this.readStr(),r=this.readStr(),n=this.readStr();this.emit(i.EventName.updateAccountValue,e,t,r,n)}decodeMsg_PORTFOLIO_VALUE(){let e,t,r,n,o=this.readInt(),s={};o>=6&&(s.conId=this.readInt()),s.symbol=this.readStr(),s.secType=this.readStr(),s.lastTradeDateOrContractMonth=this.readStr(),s.strike=this.readDouble(),s.right=I(this.readStr()),o>=7&&(s.multiplier=this.readDouble(),s.primaryExch=this.readStr()),s.currency=this.readStr(),o>=2&&(s.localSymbol=this.readStr()),o>=8&&(s.tradingClass=this.readStr());let a=this.readDecimal(),u=this.readDouble(),c=this.readDouble();o>=3&&(e=this.readDouble(),t=this.readDouble(),r=this.readDouble()),o>=4&&(n=this.readStr()),6===o&&39===this.serverVersion&&(s.primaryExch=this.readStr()),this.emit(i.EventName.updatePortfolio,s,a,u,c,e,t,r,n)}decodeMsg_ACCT_UPDATE_TIME(){this.readInt();let e=this.readStr();this.emit(i.EventName.updateAccountTime,e)}decodeMsg_NEXT_VALID_ID(){this.readInt();let e=this.readInt();this.emit(i.EventName.nextValidId,e)}decodeMsg_CONTRACT_DATA(){let e=8;this.serverVersion<o.default.SIZE_RULES&&(e=this.readInt());let t=-1;e>=3&&(t=this.readInt());let r={contract:{}};if(r.contract.symbol=this.readStr(),r.contract.secType=this.readStr(),this.readLastTradeDate(r,!1),this.serverVersion>=o.default.LAST_TRADE_DATE&&(r.contract.lastTradeDate=this.readStr()),r.contract.strike=this.readDouble(),r.contract.right=I(this.readStr()),r.contract.exchange=this.readStr(),r.contract.currency=this.readStr(),r.contract.localSymbol=this.readStr(),r.marketName=this.readStr(),r.contract.tradingClass=this.readStr(),r.contract.conId=this.readInt(),r.minTick=this.readDouble(),this.serverVersion>=o.default.MD_SIZE_MULTIPLIER&&this.serverVersion<o.default.SIZE_RULES&&this.readInt(),r.contract.multiplier=this.readDouble(),r.orderTypes=this.readStr(),r.validExchanges=this.readStr(),e>=2&&(r.priceMagnifier=this.readInt()),e>=4&&(r.underConId=this.readInt()),e>=5&&(r.longName=this.readStr(),r.contract.primaryExch=this.readStr(),this.serverVersion>=o.default.ENCODE_MSG_ASCII7&&(r.longName=this.decodeUnicodeEscapedString(r.longName))),e>=6&&(r.contractMonth=this.readStr(),r.industry=this.readStr(),r.category=this.readStr(),r.subcategory=this.readStr(),r.timeZoneId=this.readStr(),r.tradingHours=this.readStr(),r.liquidHours=this.readStr()),e>=8&&(r.evRule=this.readStr(),r.evMultiplier=this.readDouble()),e>=7){let e=this.readInt();if(e>0){r.secIdList=[];for(let t=0;t<e;++t){let e={tag:this.readStr(),value:this.readStr()};r.secIdList.push(e)}}}if(this.serverVersion>=o.default.AGG_GROUP&&(r.aggGroup=this.readInt()),this.serverVersion>=o.default.UNDERLYING_INFO&&(r.underSymbol=this.readStr(),r.underSecType=this.readStr()),this.serverVersion>=o.default.MARKET_RULES&&(r.marketRuleIds=this.readStr()),this.serverVersion>=o.default.REAL_EXPIRATION_DATE&&(r.realExpirationDate=this.readStr()),this.serverVersion>=o.default.STOCK_TYPE&&(r.stockType=this.readStr()),this.serverVersion>=o.default.FRACTIONAL_SIZE_SUPPORT&&this.serverVersion<o.default.SIZE_RULES&&this.readDecimal(),this.serverVersion>=o.default.SIZE_RULES&&(r.minSize=this.readDecimal(),r.sizeIncrement=this.readDecimal(),r.suggestedSizeIncrement=this.readDecimal()),this.serverVersion>=o.default.FUND_DATA_FIELDS&&r.contract.secType==a.default.FUND&&(r.fundName=this.readStr(),r.fundFamily=this.readStr(),r.fundType=this.readStr(),r.fundFrontLoad=this.readStr(),r.fundBackLoad=this.readStr(),r.fundBackLoadTimeInterval=this.readStr(),r.fundManagementFee=this.readStr(),r.fundClosed=this.readBool(),r.fundClosedForNewInvestors=this.readBool(),r.fundClosedForNewMoney=this.readBool(),r.fundNotifyAmount=this.readStr(),r.fundMinimumInitialPurchase=this.readStr(),r.fundSubsequentMinimumPurchase=this.readStr(),r.fundBlueSkyStates=this.readStr(),r.fundBlueSkyTerritories=this.readStr(),r.fundDistributionPolicyIndicator=this.readStr(),r.fundAssetType=this.readStr()),this.serverVersion>=o.default.INELIGIBILITY_REASONS){let e=this.readInt(),t=[];for(let r=0;r<e;r++){let e=this.readStr(),r=this.readStr();t.push({id:e,description:r})}r.ineligibilityReasonList=t}this.emit(i.EventName.contractDetails,t,r)}decodeMsg_EXECUTION_DATA(){let e=this.serverVersion;e<o.default.LAST_LIQUIDITY&&(e=this.readInt());let t=-1;e>=7&&(t=this.readInt());let r=this.readInt(),n={};e>=5&&(n.conId=this.readInt()),n.symbol=this.readStr(),n.secType=this.readStr(),n.lastTradeDateOrContractMonth=this.readStr(),n.strike=this.readDouble(),n.right=I(this.readStr()),e>=9&&(n.multiplier=this.readDouble()),n.exchange=this.readStr(),n.currency=this.readStr(),n.localSymbol=this.readStr(),e>=10&&(n.tradingClass=this.readStr());let s={};s.orderId=r,s.execId=this.readStr(),s.time=this.readStr(),s.acctNumber=this.readStr(),s.exchange=this.readStr(),s.side=this.readStr(),s.shares=this.readDecimal(),s.price=this.readDouble(),e>=2&&(s.permId=this.readInt()),e>=3&&(s.clientId=this.readInt()),e>=4&&(s.liquidation=this.readInt()),e>=6&&(s.cumQty=this.readDecimal(),s.avgPrice=this.readDouble()),e>=8&&(s.orderRef=this.readStr()),e>=9&&(s.evRule=this.readStr(),s.evMultiplier=this.readDouble()),this.serverVersion>=o.default.MODELS_SUPPORT&&(s.modelCode=this.readStr()),this.serverVersion>=o.default.LAST_LIQUIDITY&&(s.lastLiquidity=this.readInt()),this.serverVersion>=o.default.PENDING_PRICE_REVISION&&(s.pendingPriceRevision=this.readBool()),this.emit(i.EventName.execDetails,t,n,s)}decodeMsg_MARKET_DEPTH(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readInt(),n=this.readInt(),o=this.readDouble(),s=this.readDecimal();this.emit(i.EventName.updateMktDepth,e,t,r,n,o,s)}decodeMsg_MARKET_DEPTH_L2(){let e;this.readInt();let t=this.readInt(),r=this.readInt(),n=this.readStr(),s=this.readInt(),a=this.readInt(),u=this.readDouble(),c=this.readDecimal();this.serverVersion>=o.default.SMART_DEPTH&&(e=this.readBool()),this.emit(i.EventName.updateMktDepthL2,t,r,n,s,a,u,c,e)}decodeMsg_NEWS_BULLETINS(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readStr(),n=this.readStr();this.emit(i.EventName.updateNewsBulletin,e,t,r,n)}decodeMsg_MANAGED_ACCTS(){this.readInt();let e=this.readStr();this.emit(i.EventName.managedAccounts,e)}decodeMsg_RECEIVE_FA(){this.readInt();let e=this.readInt(),t=this.readStr();this.emit(i.EventName.receiveFA,e,t)}decodeMsg_HISTORICAL_DATA(){let e=Number.MAX_SAFE_INTEGER;this.serverVersion<o.default.SYNT_REALTIME_BARS&&(e=this.readInt());let t=this.readInt(),r="finished",n="",s="";e>=2&&(r+="-"+(n=this.readStr())+"-"+this.readStr());let a=this.readInt();for(;a--;){let r,n,s=this.readStr(),a=this.readDouble(),u=this.readDouble(),c=this.readDouble(),l=this.readDouble(),d=this.readDecimal(),h=this.readDecimal();this.serverVersion<o.default.SYNT_REALTIME_BARS&&(r=this.readBool()),e>=3&&(n=this.readInt()),this.emit(i.EventName.historicalData,t,s,a,u,c,l,d,n,h,r)}this.emit(i.EventName.historicalData,t,r,-1,-1,-1,-1,-1,-1,-1,!1)}decodeMsg_HISTORICAL_DATA_UPDATE(){let e=this.readInt(),t=this.readInt(),r=this.readStr(),n=this.readDouble(),o=this.readDouble(),s=this.readDouble(),a=this.readDouble(),u=this.readDecimal(),c=this.readDecimal();this.emit(i.EventName.historicalDataUpdate,e,r,n,s,a,o,c,t,u)}decodeMsg_REROUTE_MKT_DATA(){let e=this.readInt(),t=this.readInt(),r=this.readStr();this.emit(i.EventName.rerouteMktDataReq,e,t,r)}decodeMsg_REROUTE_MKT_DEPTH(){let e=this.readInt(),t=this.readInt(),r=this.readStr();this.emit(i.EventName.rerouteMktDepthReq,e,t,r)}decodeMsg_MARKET_RULE(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++)r[e]={lowEdge:this.readDouble(),increment:this.readDouble()};this.emit(i.EventName.marketRule,e,r)}decodeMsg_BOND_CONTRACT_DATA(){let e=6;this.serverVersion<o.default.SIZE_RULES&&(e=this.readInt());let t=-1;e>=3&&(t=this.readInt());let r={contract:{}};if(r.contract.symbol=this.readStr(),r.contract.secType=this.readStr(),r.cusip=this.readStr(),r.coupon=this.readDouble(),this.readLastTradeDate(r,!0),r.issueDate=this.readStr(),r.ratings=this.readStr(),r.bondType=this.readStr(),r.couponType=this.readStr(),r.convertible=this.readBool(),r.callable=this.readBool(),r.putable=this.readBool(),r.descAppend=this.readStr(),r.contract.exchange=this.readStr(),r.contract.currency=this.readStr(),r.marketName=this.readStr(),r.contract.tradingClass=this.readStr(),r.contract.conId=this.readInt(),r.minTick=this.readDouble(),this.serverVersion>=o.default.MD_SIZE_MULTIPLIER&&this.serverVersion<o.default.SIZE_RULES&&this.readInt(),r.orderTypes=this.readStr(),r.validExchanges=this.readStr(),e>=2&&(r.nextOptionDate=this.readStr(),r.nextOptionType=this.readStr(),r.nextOptionPartial=this.readBool(),r.notes=this.readStr()),e>=4&&(r.longName=this.readStr()),this.serverVersion>=o.default.BOND_TRADING_HOURS&&(r.timeZoneId=this.readStr(),r.tradingHours=this.readStr(),r.liquidHours=this.readStr()),e>=6&&(r.evRule=this.readStr(),r.evMultiplier=this.readDouble()),e>=5){let e=this.readInt();if(e>0)for(r.secIdList=[];e--;){let e={tag:this.readStr(),value:this.readStr()};r.secIdList.push(e)}}this.serverVersion>=o.default.AGG_GROUP&&(r.aggGroup=this.readInt()),this.serverVersion>=o.default.MARKET_RULES&&(r.marketRuleIds=this.readStr()),this.serverVersion>=o.default.SIZE_RULES&&(r.minSize=this.readDecimal(),r.sizeIncrement=this.readDecimal(),r.suggestedSizeIncrement=this.readDecimal()),this.emit(i.EventName.bondContractDetails,t,r)}decodeMsg_SCANNER_PARAMETERS(){this.readInt();let e=this.readStr();this.emit(i.EventName.scannerParameters,e)}decodeMsg_SCANNER_DATA(){let e=this.readInt(),t=this.readInt(),r=this.readInt();for(;r--;){let r,n={contract:{}},o=this.readInt();e>=3&&(n.contract.conId=this.readInt()),n.contract.symbol=this.readStr(),n.contract.secType=this.readStr(),this.readLastTradeDate(n,!1),n.contract.strike=this.readDouble(),n.contract.right=I(this.readStr()),n.contract.exchange=this.readStr(),n.contract.currency=this.readStr(),n.contract.localSymbol=this.readStr(),n.marketName=this.readStr(),n.contract.tradingClass=this.readStr();let s=this.readStr(),a=this.readStr(),u=this.readStr();e>=2&&(r=this.readStr()),this.emit(i.EventName.scannerData,t,o,n,s,a,u,r)}this.emit(i.EventName.scannerDataEnd,t)}decodeMsg_TICK_OPTION_COMPUTATION(){let e,t,r,n,s,a,c=this.serverVersion>=o.default.PRICE_BASED_VOLATILITY?Number.MAX_VALUE:this.readInt(),l=this.readInt(),d=this.readInt();this.serverVersion>=o.default.PRICE_BASED_VOLATILITY&&this.readInt();let h=this.readDouble();-1==h&&(h=void 0);let _=this.readDouble();-2==_&&(_=void 0),(c>=6||d===u.TickType.MODEL_OPTION||d===u.TickType.DELAYED_MODEL_OPTION)&&(-1==(e=this.readDouble())&&(e=void 0),-1==(t=this.readDouble())&&(t=void 0)),c>=6&&(-2==(r=this.readDouble())&&(r=void 0),-2==(n=this.readDouble())&&(n=void 0),-2==(s=this.readDouble())&&(s=void 0),-1==(a=this.readDouble())&&(a=void 0)),this.emit(i.EventName.tickOptionComputation,l,d,h,_,e,t,r,n,s,a)}decodeMsg_TICK_GENERIC(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readDouble();this.emit(i.EventName.tickGeneric,e,t,r)}decodeMsg_TICK_STRING(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readStr();this.emit(i.EventName.tickString,e,t,r)}decodeMsg_TICK_EFP(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readDouble(),n=this.readStr(),o=this.readDouble(),s=this.readInt(),a=this.readStr(),u=this.readDouble(),c=this.readDouble();this.emit(i.EventName.tickEFP,e,t,r,n,o,s,a,u,c)}decodeMsg_CURRENT_TIME(){this.readInt();let e=this.readInt();this.emit(i.EventName.currentTime,e)}decodeMsg_REAL_TIME_BARS(){this.readInt();let e=this.readInt(),t=this.readInt(),r=this.readDouble(),n=this.readDouble(),o=this.readDouble(),s=this.readDouble(),a=this.readDecimal(),u=this.readDecimal(),c=this.readInt();this.emit(i.EventName.realtimeBar,e,t,r,n,o,s,a,u,c)}decodeMsg_FUNDAMENTAL_DATA(){this.readInt();let e=this.readInt(),t=this.readStr();this.emit(i.EventName.fundamentalData,e,t)}decodeMsg_CONTRACT_DATA_END(){this.readInt();let e=this.readInt();this.emit(i.EventName.contractDetailsEnd,e)}decodeMsg_OPEN_ORDER_END(){this.readInt(),this.emit(i.EventName.openOrderEnd)}decodeMsg_ACCT_DOWNLOAD_END(){this.readInt();let e=this.readStr();this.emit(i.EventName.accountDownloadEnd,e)}decodeMsg_EXECUTION_DATA_END(){this.readInt();let e=this.readInt();this.emit(i.EventName.execDetailsEnd,e)}decodeMsg_DELTA_NEUTRAL_VALIDATION(){this.readInt();let e=this.readInt(),t={conId:this.readInt(),delta:this.readDouble(),price:this.readDouble()};this.emit(i.EventName.deltaNeutralValidation,e,t)}decodeMsg_TICK_SNAPSHOT_END(){this.readInt();let e=this.readInt();this.emit(i.EventName.tickSnapshotEnd,e)}decodeMsg_MARKET_DATA_TYPE(){this.readInt();let e=this.readInt(),t=this.readInt();this.emit(i.EventName.marketDataType,e,t)}decodeMsg_COMMISSION_REPORT(){this.readInt();let e={};e.execId=this.readStr(),e.commission=this.readDouble(),e.currency=this.readStr(),e.realizedPNL=this.readDouble(),e.yield=this.readDouble(),e.yieldRedemptionDate=this.readInt(),this.emit(i.EventName.commissionReport,e)}decodeMsg_POSITION(){let e=this.readInt(),t=this.readStr(),r={};r.conId=this.readInt(),r.symbol=this.readStr(),r.secType=this.readStr(),r.lastTradeDateOrContractMonth=this.readStr(),r.strike=this.readDouble(),r.right=I(this.readStr()),r.multiplier=this.readDouble(),r.exchange=this.readStr(),r.currency=this.readStr(),r.localSymbol=this.readStr(),e>=2&&(r.tradingClass=this.readStr());let n=this.readDecimal(),o=0;e>=3&&(o=this.readDouble()),this.emit(i.EventName.position,t,r,n,o)}decodeMsg_POSITION_END(){this.readInt(),this.emit(i.EventName.positionEnd)}decodeMsg_ACCOUNT_SUMMARY(){this.readInt();let e=this.readInt(),t=this.readStr(),r=this.readStr(),n=this.readStr(),o=this.readStr();this.emit(i.EventName.accountSummary,e,t,r,n,o)}decodeMsg_ACCOUNT_SUMMARY_END(){this.readInt();let e=this.readInt();this.emit(i.EventName.accountSummaryEnd,e)}decodeMsg_DISPLAY_GROUP_LIST(){this.readInt();let e=this.readInt(),t=this.readStr();this.emit(i.EventName.displayGroupList,e,t)}decodeMsg_DISPLAY_GROUP_UPDATED(){this.readInt();let e=this.readInt(),t=this.readStr();this.emit(i.EventName.displayGroupUpdated,e,t)}decodeMsg_POSITION_MULTI(){this.readInt();let e=this.readInt(),t=this.readStr(),r={};r.conId=this.readInt(),r.symbol=this.readStr(),r.secType=this.readStr(),r.lastTradeDateOrContractMonth=this.readStr(),r.strike=this.readDouble(),r.right=I(this.readStr()),r.multiplier=this.readDouble(),r.exchange=this.readStr(),r.currency=this.readStr(),r.localSymbol=this.readStr(),r.tradingClass=this.readStr();let n=this.readDecimal(),o=this.readDouble(),s=this.readStr();this.emit(i.EventName.positionMulti,e,t,s,r,n,o)}decodeMsg_POSITION_MULTI_END(){this.readInt();let e=this.readInt();this.emit(i.EventName.positionMultiEnd,e)}decodeMsg_ACCOUNT_UPDATE_MULTI(){this.readInt();let e=this.readInt(),t=this.readStr(),r=this.readStr(),n=this.readStr(),o=this.readStr(),s=this.readStr();this.emit(i.EventName.accountUpdateMulti,e,t,r,n,o,s)}decodeMsg_ACCOUNT_UPDATE_MULTI_END(){this.readInt();let e=this.readStr();this.emit(i.EventName.accountUpdateMultiEnd,e)}decodeMsg_SECURITY_DEFINITION_OPTION_PARAMETER(){let e=this.readInt(),t=this.readStr(),r=this.readInt(),n=this.readStr(),o=this.readDouble(),s=this.readInt(),a=[];for(let e=0;e<s;e++)a.push(this.readStr());let u=this.readInt(),c=[];for(let e=0;e<u;e++)c.push(this.readDouble());this.emit(i.EventName.securityDefinitionOptionParameter,e,t,r,n,o,a,c)}decodeMsg_SECURITY_DEFINITION_OPTION_PARAMETER_END(){let e=this.readInt();this.emit(i.EventName.securityDefinitionOptionParameterEnd,e)}decodeMsg_SOFT_DOLLAR_TIERS(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++)r[e]={name:this.readStr(),value:this.readStr(),displayName:this.readStr()};this.emit(i.EventName.softDollarTiers,e,r)}decodeMsg_FAMILY_CODES(){let e=this.readInt(),t=Array(e);for(let r=0;r<e;r++)t[r]={accountID:this.readStr(),familyCode:this.readStr()};this.emit(i.EventName.familyCodes,t)}decodeMsg_SYMBOL_SAMPLES(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++){let t={conId:this.readInt(),symbol:this.readStr(),secType:this.readStr(),primaryExch:this.readStr(),currency:this.readStr()},n=this.readInt(),i=Array(n);for(let e=0;e<n;e++)i[e]=this.readStr();this.serverVersion>=o.default.BOND_ISSUERID&&(t.description=this.readStr(),t.issuerId=this.readStr()),r[e]={contract:t,derivativeSecTypes:i}}this.emit(i.EventName.symbolSamples,e,r)}decodeMsg_MKT_DEPTH_EXCHANGES(){let e=this.readInt(),t=Array(e);for(let r=0;r<e;r++)this.serverVersion>=o.default.SERVICE_DATA_TYPE?t[r]={exchange:this.readStr(),secType:this.readStr(),listingExch:this.readStr(),serviceDataType:this.readStr(),aggGroup:this.readIntOrUndefined()}:t[r]={exchange:this.readStr(),secType:this.readStr(),listingExch:"",serviceDataType:this.readBool()?"Deep2":"Deep",aggGroup:void 0};this.emit(i.EventName.mktDepthExchanges,t)}decodeMsg_TICK_REQ_PARAMS(){let e=this.readInt(),t=this.readInt(),r=this.readStr(),n=this.readInt();this.emit(i.EventName.tickReqParams,e,t,r,n)}decodeMsg_SMART_COMPONENTS(){let e=this.readInt(),t=this.readInt(),r=new Map;for(let e=0;e<t;e++){let e=this.readInt(),t=this.readStr(),n=this.readStr();r.set(e,[t,n])}this.emit(i.EventName.smartComponents,e,r)}decodeMsg_NEWS_ARTICLE(){let e=this.readInt(),t=this.readInt(),r=this.readStr();this.emit(i.EventName.newsArticle,e,t,r)}decodeMsg_TICK_NEWS(){let e=this.readInt(),t=this.readInt(),r=this.readStr(),n=this.readStr(),o=this.readStr(),s=this.readStr();this.emit(i.EventName.tickNews,e,t,r,n,o,s)}decodeMsg_NEWS_PROVIDERS(){let e=this.readInt(),t=Array(e);for(let r=0;r<e;r++)t[r]={providerCode:this.readStr(),providerName:this.readStr()};this.emit(i.EventName.newsProviders,t)}decodeMsg_HISTORICAL_NEWS(){let e=this.readInt(),t=this.readStr(),r=this.readStr(),n=this.readStr(),o=this.readStr();this.emit(i.EventName.historicalNews,e,t,r,n,o)}decodeMsg_HISTORICAL_NEWS_END(){let e=this.readInt(),t=this.readBool();this.emit(i.EventName.historicalNewsEnd,e,t)}decodeMsg_HEAD_TIMESTAMP(){let e=this.readInt(),t=this.readStr();this.emit(i.EventName.headTimestamp,e,t)}decodeMsg_HISTOGRAM_DATA(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++)r[e]={price:this.readDouble(),size:this.readDecimal()};this.emit(i.EventName.histogramData,e,r)}decodeMsg_PNL(){let e,t,r=this.readInt(),n=this.readDouble();this.serverVersion>=o.default.UNREALIZED_PNL&&(e=this.readDouble()),this.serverVersion>=o.default.REALIZED_PNL&&(t=this.readDouble()),this.emit(i.EventName.pnl,r,n,e,t)}decodeMsg_PNL_SINGLE(){let e,t,r=this.readInt(),n=this.readDecimal(),s=this.readDouble();this.serverVersion>=o.default.UNREALIZED_PNL&&(e=this.readDouble()),this.serverVersion>=o.default.REALIZED_PNL&&(t=this.readDouble());let a=this.readDouble();this.emit(i.EventName.pnlSingle,r,n,s,e,t,a)}decodeMsg_HISTORICAL_TICKS(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++){let t=this.readInt();this.readInt();let n=this.readDouble(),i=this.readDecimal();r[e]={time:t,price:n,size:i}}let n=this.readBool();this.emit(i.EventName.historicalTicks,e,r,n)}decodeMsg_HISTORICAL_TICKS_BID_ASK(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++){let t=this.readInt(),n=this.readInt(),i=this.readDouble(),o=this.readDouble(),s=this.readDecimal(),a=this.readDecimal();r[e]={time:t,tickAttribBidAsk:{bidPastLow:(1&n)!=0,askPastHigh:(2&n)!=0},priceBid:i,priceAsk:o,sizeBid:s,sizeAsk:a}}let n=this.readBool();this.emit(i.EventName.historicalTicksBidAsk,e,r,n)}decodeMsg_HISTORICAL_TICKS_LAST(){let e=this.readInt(),t=this.readInt(),r=Array(t);for(let e=0;e<t;e++){let t=this.readInt(),n=this.readInt(),i=this.readDouble(),o=this.readDecimal(),s=this.readStr(),a=this.readStr();r[e]={time:t,tickAttribLast:{pastLimit:(1&n)!=0,unreported:(2&n)!=0},price:i,size:o,exchange:s,specialConditions:a}}let n=this.readBool();this.emit(i.EventName.historicalTicksLast,e,r,n)}decodeMsg_TICK_BY_TICK(){let e=this.readInt(),t=this.readInt(),r=this.readStr();switch(t){case 0:break;case 1:case 2:{let n=this.readDouble(),o=this.readDecimal(),s=this.readInt(),a=this.readStr(),u=this.readStr();this.emit(i.EventName.tickByTickAllLast,e,t,r,n,o,{pastLimit:(1&s)!=0,unreported:(2&s)!=0},a,u);break}case 3:{let t=this.readDouble(),n=this.readDouble(),o=this.readDecimal(),s=this.readDecimal(),a=this.readInt();this.emit(i.EventName.tickByTickBidAsk,e,r,t,n,o,s,{bidPastLow:(1&a)!=0,askPastHigh:(2&a)!=0});break}case 4:{let t=this.readDouble();this.emit(i.EventName.tickByTickMidPoint,e,r,t)}}}decodeMsg_ORDER_BOUND(){let e=this.readInt(),t=this.readDouble(),r=this.readInt();this.emit(i.EventName.orderBound,e,t,r)}decodeMsg_COMPLETED_ORDER(){let e={},t={},r={},n=new S(this,e,t,r,Number.MAX_VALUE,this.serverVersion);n.readContractFields(),n.readAction(),n.readTotalQuantity(),n.readOrderType(),n.readLmtPrice(),n.readAuxPrice(),n.readTIF(),n.readOcaGroup(),n.readAccount(),n.readOpenClose(),n.readOrigin(),n.readOrderRef(),n.readPermId(),n.readOutsideRth(),n.readHidden(),n.readDiscretionaryAmount(),n.readGoodAfterTime(),n.readFAParams(),n.readModelCode(),n.readGoodTillDate(),n.readRule80A(),n.readPercentOffset(),n.readSettlingFirm(),n.readShortSaleParams(),n.readBoxOrderParams(),n.readPegToStkOrVolOrderParams(),n.readDisplaySize(),n.readSweepToFill(),n.readAllOrNone(),n.readMinQty(),n.readOcaType(),n.readTriggerMethod(),n.readVolOrderParams(!1),n.readTrailParams(),n.readComboLegs(),n.readSmartComboRoutingParams(),n.readScaleOrderParams(),n.readHedgeParams(),n.readClearingParams(),n.readNotHeld(),n.readDeltaNeutral(),n.readAlgoParams(),n.readSolicited(),n.readOrderStatus(),n.readVolRandomizeFlags(),n.readPegToBenchParams(),n.readConditions(),n.readStopPriceAndLmtPriceOffset(),n.readCashQty(),n.readDontUseAutoPriceForHedge(),n.readIsOmsContainer(),n.readAutoCancelDate(),n.readFilledQuantity(),n.readRefFuturesConId(),n.readAutoCancelParent(),n.readShareholder(),n.readImbalanceOnly(),n.readRouteMarketableToBbo(),n.readParentPermId(),n.readCompletedTime(),n.readCompletedStatus(),n.readPegBestPegMidOrderAttributes(),n.readCustomerAccount(),n.readProfessionalCustomer(),this.emit(i.EventName.completedOrder,e,t,r)}decodeMsg_COMPLETED_ORDERS_END(){this.emit(i.EventName.completedOrdersEnd)}decodeMsg_REPLACE_FA_END(){let e=this.readInt(),t=this.readStr();this.emit(i.EventName.replaceFAEnd,e,t)}decodeMsg_WSH_META_DATA(){let e=this.readInt(),t=this.readStr();this.emit(i.EventName.wshMetaData,e,t)}decodeMsg_WSH_EVENT_DATA(){let e=this.readInt(),t=this.readStr();this.emit(i.EventName.wshEventData,e,t)}decodeMsg_HISTORICAL_SCHEDULE(){let e=this.readInt(),t=this.readStr(),r=this.readStr(),n=this.readStr(),o=this.readInt(),s=Array(o);for(let e=0;e<o;e++){let t=this.readStr(),r=this.readStr(),n=this.readStr();s[e]={startDateTime:t,endDateTime:r,refDate:n}}this.emit(i.EventName.historicalSchedule,e,t,r,n,s)}decodeMsg_USER_INFO(){let e=this.readInt(),t=this.readStr();this.emit(i.EventName.userInfo,e,t)}readLastTradeDate(e,t){let r=this.readStr();if(r.length){let n=r.indexOf("-")>0?r.split("-"):r.split("\\s+");n.length>0&&(t?e.maturity=n[0]:e.contract.lastTradeDateOrContractMonth=n[0]),n.length>1&&(e.lastTradeTime=n[1]),t&&n.length>2&&(e.timeZoneId=n[2])}}}t.Decoder=O;class S{constructor(e,t,r,n,i,o){this.decoder=e,this.contract=t,this.order=r,this.orderState=n,this.version=i,this.serverVersion=o}readOrderId(){this.order.orderId=this.decoder.readInt()}readContractFields(){this.version>=17&&(this.contract.conId=this.decoder.readInt()),this.contract.symbol=this.decoder.readStr(),this.contract.secType=this.decoder.readStr(),this.contract.lastTradeDateOrContractMonth=this.decoder.readStr(),this.contract.strike=this.decoder.readDouble(),this.contract.right=I(this.decoder.readStr()),this.version>=32&&(this.contract.multiplier=+this.decoder.readStr()),this.contract.exchange=this.decoder.readStr(),this.contract.currency=this.decoder.readStr(),this.version>=2&&(this.contract.localSymbol=this.decoder.readStr()),this.version>=32&&(this.contract.tradingClass=this.decoder.readStr())}readAction(){this.order.action=this.decoder.readStr()}readTotalQuantity(){this.order.totalQuantity=this.decoder.readDecimal()}readOrderType(){this.order.orderType=this.decoder.readStr()}readLmtPrice(){this.version<29?this.order.lmtPrice=this.decoder.readDouble():this.order.lmtPrice=this.decoder.readDoubleOrUndefined()}readAuxPrice(){this.version<30?this.order.auxPrice=this.decoder.readDouble():this.order.auxPrice=this.decoder.readDoubleOrUndefined()}readTIF(){this.order.tif=this.decoder.readStr()}readOcaGroup(){this.order.ocaGroup=this.decoder.readStr()}readAccount(){this.order.account=this.decoder.readStr()}readOpenClose(){this.order.openClose=this.decoder.readStr()}readOrigin(){this.order.origin=this.decoder.readInt()}readOrderRef(){this.order.orderRef=this.decoder.readStr()}readClientId(){this.version>=3&&(this.order.clientId=this.decoder.readInt())}readPermId(){this.version>=4&&(this.order.permId=this.decoder.readInt())}readOutsideRth(){this.version>=4&&(this.version<18?this.decoder.readBool():this.order.outsideRth=this.decoder.readBool())}readHidden(){this.version>=4&&(this.order.hidden=1==this.decoder.readInt())}readDiscretionaryAmount(){this.version>=4&&(this.order.discretionaryAmt=this.decoder.readDouble())}readGoodAfterTime(){this.version>=5&&(this.order.goodAfterTime=this.decoder.readStr())}skipSharesAllocation(){this.version>=6&&this.decoder.readStr()}readFAParams(){this.version>=7&&(this.order.faGroup=this.decoder.readStr(),this.order.faMethod=this.decoder.readStr(),this.order.faPercentage=this.decoder.readStr(),this.version<o.default.FA_PROFILE_DESUPPORT&&(this.order.faProfile=this.decoder.readStr()))}readModelCode(){this.version>=o.default.MODELS_SUPPORT&&(this.order.modelCode=this.decoder.readStr())}readGoodTillDate(){this.version>=8&&(this.order.goodTillDate=this.decoder.readStr())}readRule80A(){this.version>=9&&(this.order.rule80A=this.decoder.readStr())}readPercentOffset(){this.version>=9&&(this.order.percentOffset=this.decoder.readDoubleOrUndefined())}readSettlingFirm(){this.version>=9&&(this.order.settlingFirm=this.decoder.readStr())}readShortSaleParams(){this.version>=9&&(this.order.shortSaleSlot=this.decoder.readInt(),this.order.designatedLocation=this.decoder.readStr(),51==this.version?this.decoder.readInt():this.version>=23&&(this.order.exemptCode=this.decoder.readInt()))}readAuctionStrategy(){this.version>=9&&(this.order.auctionStrategy=this.decoder.readInt())}readBoxOrderParams(){this.version>=9&&(this.order.startingPrice=this.decoder.readDoubleOrUndefined(),this.order.stockRefPrice=this.decoder.readDoubleOrUndefined(),this.order.delta=this.decoder.readDoubleOrUndefined())}readPegToStkOrVolOrderParams(){this.version>=9&&(this.order.stockRangeLower=this.decoder.readDoubleOrUndefined(),this.order.stockRangeUpper=this.decoder.readDoubleOrUndefined())}readDisplaySize(){this.version>=9&&(this.order.displaySize=this.decoder.readIntOrUndefined())}readOldStyleOutsideRth(){this.version>=9&&this.version<18&&this.decoder.readBool()}readBlockOrder(){this.version>=9&&(this.order.blockOrder=this.decoder.readBool())}readSweepToFill(){this.version>=9&&(this.order.sweepToFill=this.decoder.readBool())}readAllOrNone(){this.version>=9&&(this.order.allOrNone=this.decoder.readBool())}readMinQty(){this.version>=9&&(this.order.minQty=this.decoder.readIntOrUndefined())}readOcaType(){this.version>=9&&(this.order.ocaType=this.decoder.readInt())}readETradeOnly(){this.version>=9&&(this.order.eTradeOnly=this.decoder.readBool())}readFirmQuoteOnly(){this.version>=9&&(this.order.firmQuoteOnly=this.decoder.readBool())}readNbboPriceCap(){this.version>=9&&(this.order.nbboPriceCap=this.decoder.readDoubleOrUndefined())}readParentId(){this.version>=10&&(this.order.parentId=this.decoder.readInt())}readTriggerMethod(){this.version>=10&&(this.order.triggerMethod=this.decoder.readInt())}readVolOrderParams(e){if(this.version>=11){if(this.order.volatility=this.decoder.readDoubleOrUndefined(),this.order.volatilityType=this.decoder.readInt(),11==this.version){let e=this.decoder.readInt();this.order.deltaNeutralOrderType=0==e?"NONE":"MKT"}else this.order.deltaNeutralOrderType=this.decoder.readStr(),this.order.deltaNeutralAuxPrice=this.decoder.readDoubleOrUndefined(),this.version>=27&&this.order.deltaNeutralOrderType&&""!==this.order.deltaNeutralOrderType&&(this.order.deltaNeutralConId=this.decoder.readInt(),e&&(this.order.deltaNeutralSettlingFirm=this.decoder.readStr(),this.order.deltaNeutralClearingAccount=this.decoder.readStr(),this.order.deltaNeutralClearingIntent=this.decoder.readStr())),this.version>=31&&this.order.deltaNeutralOrderType&&""!==this.order.deltaNeutralOrderType&&(e&&(this.order.deltaNeutralOpenClose=this.decoder.readStr()),this.order.deltaNeutralShortSale=this.decoder.readBool(),this.order.deltaNeutralShortSaleSlot=this.decoder.readInt(),this.order.deltaNeutralDesignatedLocation=this.decoder.readStr());this.order.continuousUpdate=this.decoder.readInt(),26==this.version&&(this.order.stockRangeLower=this.decoder.readDouble(),this.order.stockRangeUpper=this.decoder.readDouble()),this.order.referencePriceType=this.decoder.readInt()}}readTrailParams(){this.version>=13&&(this.order.trailStopPrice=this.decoder.readDoubleOrUndefined()),this.version>=30&&(this.order.trailingPercent=this.decoder.readDoubleOrUndefined())}readBasisPoints(){this.version>=14&&(this.order.basisPoints=this.decoder.readDoubleOrUndefined(),this.order.basisPointsType=this.decoder.readIntOrUndefined())}readComboLegs(){if(this.version>=14&&(this.contract.comboLegsDescription=this.decoder.readStr()),this.version>=29){let e=this.decoder.readInt();if(e>0){this.contract.comboLegs=[];for(let t=0;t<e;++t){let e=this.decoder.readInt(),t=this.decoder.readInt(),r=this.decoder.readStr(),n=this.decoder.readStr(),i=this.decoder.readInt(),o=this.decoder.readInt(),s=this.decoder.readStr(),a=this.decoder.readInt();this.contract.comboLegs.push({conId:e,ratio:t,action:r,exchange:n,openClose:i,shortSaleSlot:o,designatedLocation:s,exemptCode:a})}}let t=this.decoder.readInt();if(t>0){this.order.orderComboLegs=[];for(let e=0;e<t;++e){let e=this.decoder.readDoubleOrUndefined();this.order.orderComboLegs.push({price:e})}}}}readSmartComboRoutingParams(){if(this.version>=26){let e=this.decoder.readInt();if(e>0){this.order.smartComboRoutingParams=[];for(let t=0;t<e;++t){let e=this.decoder.readStr(),t=this.decoder.readStr();this.order.smartComboRoutingParams.push({tag:e,value:t})}}}}readScaleOrderParams(){this.version>=15&&(this.version>=20?(this.order.scaleInitLevelSize=this.decoder.readIntOrUndefined(),this.order.scaleSubsLevelSize=this.decoder.readIntOrUndefined()):(this.decoder.readIntOrUndefined(),this.order.scaleInitLevelSize=this.decoder.readIntOrUndefined()),this.order.scalePriceIncrement=this.decoder.readDoubleOrUndefined()),this.version>=28&&this.order.scalePriceIncrement&&this.order.scalePriceIncrement>0&&(this.order.scalePriceAdjustValue=this.decoder.readDoubleOrUndefined(),this.order.scalePriceAdjustInterval=this.decoder.readIntOrUndefined(),this.order.scaleProfitOffset=this.decoder.readDoubleOrUndefined(),this.order.scaleAutoReset=this.decoder.readBool(),this.order.scaleInitPosition=this.decoder.readIntOrUndefined(),this.order.scaleInitFillQty=this.decoder.readIntOrUndefined(),this.order.scaleRandomPercent=this.decoder.readBool())}readHedgeParams(){this.version>=24&&(this.order.hedgeType=this.decoder.readStr(),this.order.hedgeType&&""!==this.order.hedgeType&&(this.order.hedgeParam=this.decoder.readStr()))}readOptOutSmartRouting(){this.version>=25&&(this.order.optOutSmartRouting=this.decoder.readBool())}readClearingParams(){this.version>=19&&(this.order.clearingAccount=this.decoder.readStr(),this.order.clearingIntent=this.decoder.readStr())}readNotHeld(){this.version>=22&&(this.order.notHeld=this.decoder.readBool())}readDeltaNeutral(){if(this.version>=20&&this.decoder.readBool()){let e=this.decoder.readInt(),t=this.decoder.readDouble(),r=this.decoder.readDouble();this.contract.deltaNeutralContract={conId:e,delta:t,price:r}}}readAlgoParams(){if(this.version>=21&&(this.order.algoStrategy=this.decoder.readStr(),this.order.algoStrategy&&""!==this.order.algoStrategy)){let e=this.decoder.readInt();if(e>0){this.order.algoParams=[];for(let t=0;t<e;++t){let e=this.decoder.readStr(),t=this.decoder.readStr();this.order.algoParams.push({tag:e,value:t})}}}}readSolicited(){this.version>=33&&(this.order.solicited=this.decoder.readBool())}readWhatIfInfoAndCommission(){this.version>=16&&(this.order.whatIf=this.decoder.readBool(),this.readOrderStatus(),this.serverVersion>=o.default.WHAT_IF_EXT_FIELDS&&(this.orderState.initMarginBefore=this.decoder.readDoubleOrUndefined(),this.orderState.maintMarginBefore=this.decoder.readDoubleOrUndefined(),this.orderState.equityWithLoanBefore=this.decoder.readDoubleOrUndefined(),this.orderState.initMarginChange=this.decoder.readDoubleOrUndefined(),this.orderState.maintMarginChange=this.decoder.readDoubleOrUndefined(),this.orderState.equityWithLoanChange=this.decoder.readDoubleOrUndefined()),this.orderState.initMarginAfter=this.decoder.readDoubleOrUndefined(),this.orderState.maintMarginAfter=this.decoder.readDoubleOrUndefined(),this.orderState.equityWithLoanAfter=this.decoder.readDoubleOrUndefined(),this.orderState.commission=this.decoder.readDoubleOrUndefined(),this.orderState.minCommission=this.decoder.readDoubleOrUndefined(),this.orderState.maxCommission=this.decoder.readDoubleOrUndefined(),this.orderState.commissionCurrency=this.decoder.readStr(),this.orderState.warningText=this.decoder.readStr())}readOrderStatus(){this.orderState.status=this.decoder.readStr()}readVolRandomizeFlags(){this.version>=34&&(this.order.randomizeSize=this.decoder.readBool(),this.order.randomizePrice=this.decoder.readBool())}readPegToBenchParams(){this.serverVersion>=o.default.PEGGED_TO_BENCHMARK&&(0,p.isPegBenchOrder)(this.order.orderType)&&(this.order.referenceContractId=this.decoder.readInt(),this.order.isPeggedChangeAmountDecrease=this.decoder.readBool(),this.order.peggedChangeAmount=this.decoder.readDouble(),this.order.referenceChangeAmount=this.decoder.readDouble(),this.order.referenceExchangeId=this.decoder.readStr())}readConditions(){if(this.serverVersion>=o.default.PEGGED_TO_BENCHMARK){let e=this.decoder.readInt();if(e>0){this.order.conditions=Array(e);for(let t=0;t<e;t++){let e=this.decoder.readInt(),r=this.decoder.readStr()?.toLocaleLowerCase();switch(e){case E.OrderConditionType.Execution:{let e=this.decoder.readStr(),n=this.decoder.readStr(),i=this.decoder.readStr();this.order.conditions[t]=new c.default(n,e,i,r);break}case E.OrderConditionType.Margin:{let e=this.decoder.readBool(),n=this.decoder.readInt();this.order.conditions[t]=new l.default(n,e,r);break}case E.OrderConditionType.PercentChange:{let e=this.decoder.readBool(),n=this.decoder.readDouble(),i=this.decoder.readInt(),o=this.decoder.readStr();this.order.conditions[t]=new d.default(n,i,o,e,r);break}case E.OrderConditionType.Price:{let e=this.decoder.readBool(),n=this.decoder.readDouble(),i=this.decoder.readInt(),o=this.decoder.readStr(),s=this.decoder.readInt();this.order.conditions[t]=new h.default(n,s,i,o,e,r);break}case E.OrderConditionType.Time:{let e=this.decoder.readBool(),n=this.decoder.readStr();this.order.conditions[t]=new _.default(n,e,r);break}case E.OrderConditionType.Volume:{let e=this.decoder.readBool(),n=this.decoder.readInt(),i=this.decoder.readInt(),o=this.decoder.readStr();this.order.conditions[t]=new f.default(n,i,o,e,r)}}}this.order.conditionsIgnoreRth=this.decoder.readBool(),this.order.conditionsCancelOrder=this.decoder.readBool()}}}readAdjustedOrderParams(){this.serverVersion>=o.default.PEGGED_TO_BENCHMARK&&(this.order.adjustedOrderType=this.decoder.readStr(),this.order.triggerPrice=this.decoder.readDoubleOrUndefined(),this.readStopPriceAndLmtPriceOffset(),this.order.adjustedStopPrice=this.decoder.readDoubleOrUndefined(),this.order.adjustedStopLimitPrice=this.decoder.readDoubleOrUndefined(),this.order.adjustedTrailingAmount=this.decoder.readDoubleOrUndefined(),this.order.adjustableTrailingUnit=this.decoder.readInt())}readStopPriceAndLmtPriceOffset(){this.order.trailStopPrice=this.decoder.readDoubleOrUndefined(),this.order.lmtPriceOffset=this.decoder.readDoubleOrUndefined()}readSoftDollarTier(){if(this.serverVersion>=o.default.SOFT_DOLLAR_TIER){let e=this.decoder.readStr(),t=this.decoder.readStr(),r=this.decoder.readStr();this.order.softDollarTier={name:e,value:t,displayName:r}}}readCashQty(){this.serverVersion>=o.default.CASH_QTY&&(this.order.cashQty=this.decoder.readDoubleOrUndefined())}readDontUseAutoPriceForHedge(){this.serverVersion>=o.default.AUTO_PRICE_FOR_HEDGE&&(this.order.dontUseAutoPriceForHedge=this.decoder.readBool())}readIsOmsContainer(){this.serverVersion>=o.default.ORDER_CONTAINER&&(this.order.isOmsContainer=this.decoder.readBool())}readDiscretionaryUpToLimitPrice(){this.serverVersion>=o.default.D_PEG_ORDERS&&(this.order.discretionaryUpToLimitPrice=this.decoder.readBool())}readAutoCancelDate(){this.order.autoCancelDate=this.decoder.readStr()}readFilledQuantity(){this.order.filledQuantity=this.decoder.readDecimal()}readRefFuturesConId(){this.order.refFuturesConId=this.decoder.readInt()}readAutoCancelParent(e){(void 0===e||this.serverVersion>=e)&&(this.order.autoCancelParent=this.decoder.readBool())}readShareholder(){this.order.shareholder=this.decoder.readStr()}readImbalanceOnly(){this.order.imbalanceOnly=this.decoder.readBool()}readRouteMarketableToBbo(){this.order.routeMarketableToBbo=this.decoder.readBool()}readParentPermId(){this.order.parentPermId=this.decoder.readInt()}readCompletedTime(){this.orderState.completedTime=this.decoder.readStr()}readCompletedStatus(){this.orderState.completedStatus=this.decoder.readStr()}readUsePriceMgmtAlgo(){this.serverVersion>=o.default.PRICE_MGMT_ALGO&&(this.order.usePriceMgmtAlgo=this.decoder.readBool())}readDuration(){this.serverVersion>=o.default.DURATION&&(this.order.duration=this.decoder.readInt())}readPostToAts(){this.serverVersion>=o.default.POST_TO_ATS&&(this.order.postToAts=this.decoder.readIntOrUndefined())}readPegBestPegMidOrderAttributes(){this.serverVersion>=o.default.PEGBEST_PEGMID_OFFSETS&&(this.order.minTradeQty=this.decoder.readIntOrUndefined(),this.order.minCompeteSize=this.decoder.readIntOrUndefined(),this.order.competeAgainstBestOffset=this.decoder.readDoubleOrUndefined(),this.order.midOffsetAtWhole=this.decoder.readDoubleOrUndefined(),this.order.midOffsetAtHalf=this.decoder.readDoubleOrUndefined())}readCustomerAccount(){this.serverVersion>=o.default.CUSTOMER_ACCOUNT&&(this.order.customerAccount=this.decoder.readStr())}readProfessionalCustomer(){this.serverVersion>=o.default.PROFESSIONAL_CUSTOMER&&(this.order.professionalCustomer=this.decoder.readBool())}readBondAccruedInterest(){this.serverVersion>=o.default.BOND_ACCRUED_INTEREST&&(this.order.bondAccruedInterest=this.decoder.readStr())}readIncludeOvernight(){this.serverVersion>=o.default.INCLUDE_OVERNIGHT&&(this.order.includeOvernight=this.decoder.readBool())}readCMETaggingFields(){this.serverVersion>=o.default.CME_TAGGING_FIELDS_IN_OPEN_ORDER&&(this.order.extOperator=this.decoder.readStr(),this.order.manualOrderIndicator=this.decoder.readIntOrUndefined())}}},90580:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.take=void 0;var n=r(25042),i=r(96829),o=r(28719);t.take=function(e){return e<=0?function(){return n.EMPTY}:i.operate(function(t,r){var n=0;t.subscribe(o.createOperatorSubscriber(r,function(t){++n<=e&&(r.next(t),e<=n&&r.complete())}))})}},90597:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isScheduler=void 0;var n=r(17886);t.isScheduler=function(e){return e&&n.isFunction(e.schedule)}},90742:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.ScanCode=t.LocationCode=t.Instrument=t.TrailingStopOrder=t.StopLimitOrder=t.StopOrder=t.MarketCloseOrder=t.MarketOrder=t.Liquidities=t.LimitOrder=t.TriggerMethod=t.OrderType=t.OrderStatus=t.OrderConditionType=t.OrderAction=t.ConjunctionConnection=t.VolumeCondition=t.TimeCondition=t.PriceCondition=t.PercentChangeCondition=t.MarginCondition=t.ExecutionCondition=t.TickByTickDataType=t.BarSizeSetting=t.DurationUnit=t.SecType=t.OptionType=t.OptionExerciseAction=t.MIN_SERVER_VER=t.LogLevel=t.FADataType=t.EventName=t.WshEventData=t.Stock=t.Option=t.Index=t.Future=t.Forex=t.FOP=t.Combo=t.CFD=t.Bond=t.isNonFatalError=t.ErrorCode=t.IBApi=void 0;let o=r(59643);var s=r(59643);Object.defineProperty(t,"IBApi",{enumerable:!0,get:function(){return s.IBApi}});var a=r(7133);Object.defineProperty(t,"ErrorCode",{enumerable:!0,get:function(){return a.ErrorCode}}),Object.defineProperty(t,"isNonFatalError",{enumerable:!0,get:function(){return a.isNonFatalError}});var u=r(35561);Object.defineProperty(t,"Bond",{enumerable:!0,get:function(){return u.Bond}});var c=r(37431);Object.defineProperty(t,"CFD",{enumerable:!0,get:function(){return c.CFD}});var l=r(93700);Object.defineProperty(t,"Combo",{enumerable:!0,get:function(){return l.Combo}});var d=r(55293);Object.defineProperty(t,"FOP",{enumerable:!0,get:function(){return d.FOP}});var h=r(260);Object.defineProperty(t,"Forex",{enumerable:!0,get:function(){return h.Forex}});var _=r(37495);Object.defineProperty(t,"Future",{enumerable:!0,get:function(){return _.Future}});var f=r(96045);Object.defineProperty(t,"Index",{enumerable:!0,get:function(){return f.Index}});var E=r(78281);Object.defineProperty(t,"Option",{enumerable:!0,get:function(){return E.Option}});var p=r(62524);Object.defineProperty(t,"Stock",{enumerable:!0,get:function(){return p.Stock}});var T=r(14028);Object.defineProperty(t,"WshEventData",{enumerable:!0,get:function(){return T.WshEventData}});var A=r(79898);Object.defineProperty(t,"EventName",{enumerable:!0,get:function(){return A.EventName}});var I=r(85819);Object.defineProperty(t,"FADataType",{enumerable:!0,get:function(){return I.FADataType}});var v=r(18397);Object.defineProperty(t,"LogLevel",{enumerable:!0,get:function(){return v.LogLevel}});var O=r(48369);Object.defineProperty(t,"MIN_SERVER_VER",{enumerable:!0,get:function(){return O.MIN_SERVER_VER}});var S=r(12515);Object.defineProperty(t,"OptionExerciseAction",{enumerable:!0,get:function(){return S.OptionExerciseAction}});var b=r(86190);Object.defineProperty(t,"OptionType",{enumerable:!0,get:function(){return b.OptionType}});var m=r(15850);Object.defineProperty(t,"SecType",{enumerable:!0,get:function(){return m.SecType}});var D=r(63345);Object.defineProperty(t,"DurationUnit",{enumerable:!0,get:function(){return D.DurationUnit}});var N=r(86940);Object.defineProperty(t,"BarSizeSetting",{enumerable:!0,get:function(){return N.BarSizeSetting}}),i(r(98102),t);var y=r(75643);Object.defineProperty(t,"TickByTickDataType",{enumerable:!0,get:function(){return y.TickByTickDataType}});var P=r(84310);Object.defineProperty(t,"ExecutionCondition",{enumerable:!0,get:function(){return P.ExecutionCondition}});var R=r(52974);Object.defineProperty(t,"MarginCondition",{enumerable:!0,get:function(){return R.MarginCondition}});var C=r(83364);Object.defineProperty(t,"PercentChangeCondition",{enumerable:!0,get:function(){return C.PercentChangeCondition}});var g=r(98491);Object.defineProperty(t,"PriceCondition",{enumerable:!0,get:function(){return g.PriceCondition}});var L=r(12143);Object.defineProperty(t,"TimeCondition",{enumerable:!0,get:function(){return L.TimeCondition}});var M=r(22410);Object.defineProperty(t,"VolumeCondition",{enumerable:!0,get:function(){return M.VolumeCondition}});var U=r(11429);Object.defineProperty(t,"ConjunctionConnection",{enumerable:!0,get:function(){return U.ConjunctionConnection}});var V=r(40575);Object.defineProperty(t,"OrderAction",{enumerable:!0,get:function(){return V.OrderAction}});var x=r(48909);Object.defineProperty(t,"OrderConditionType",{enumerable:!0,get:function(){return x.OrderConditionType}});var w=r(13971);Object.defineProperty(t,"OrderStatus",{enumerable:!0,get:function(){return w.OrderStatus}});var F=r(55332);Object.defineProperty(t,"OrderType",{enumerable:!0,get:function(){return F.OrderType}}),i(r(72785),t);var B=r(88210);Object.defineProperty(t,"TriggerMethod",{enumerable:!0,get:function(){return B.TriggerMethod}});var H=r(94015);Object.defineProperty(t,"LimitOrder",{enumerable:!0,get:function(){return H.LimitOrder}});var G=r(20566);Object.defineProperty(t,"Liquidities",{enumerable:!0,get:function(){return G.Liquidities}});var k=r(44310);Object.defineProperty(t,"MarketOrder",{enumerable:!0,get:function(){return k.MarketOrder}});var j=r(65186);Object.defineProperty(t,"MarketCloseOrder",{enumerable:!0,get:function(){return j.MarketCloseOrder}});var K=r(23814);Object.defineProperty(t,"StopOrder",{enumerable:!0,get:function(){return K.StopOrder}});var W=r(98263);Object.defineProperty(t,"StopLimitOrder",{enumerable:!0,get:function(){return W.StopLimitOrder}});var Y=r(4510);Object.defineProperty(t,"TrailingStopOrder",{enumerable:!0,get:function(){return Y.TrailingStopOrder}});var Q=r(87198);Object.defineProperty(t,"Instrument",{enumerable:!0,get:function(){return Q.Instrument}}),Object.defineProperty(t,"LocationCode",{enumerable:!0,get:function(){return Q.LocationCode}}),Object.defineProperty(t,"ScanCode",{enumerable:!0,get:function(){return Q.ScanCode}}),t.default=o.IBApi,i(r(71485),t)},91168:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scanInternals=void 0;var n=r(28719);t.scanInternals=function(e,t,r,i,o){return function(s,a){var u=r,c=t,l=0;s.subscribe(n.createOperatorSubscriber(a,function(t){var r=l++;c=u?e(c,t,r):(u=!0,t),i&&a.next(c)},o&&function(){u&&a.next(c),a.complete()}))}}},91204:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ignoreElements=void 0;var n=r(96829),i=r(28719),o=r(63410);t.ignoreElements=function(){return n.operate(function(e,t){e.subscribe(i.createOperatorSubscriber(t,o.noop))})}},91602:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isInteropObservable=void 0;var n=r(63701),i=r(17886);t.isInteropObservable=function(e){return i.isFunction(e[n.observable])}},91802:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.audit=void 0;var n=r(96829),i=r(81773),o=r(28719);t.audit=function(e){return n.operate(function(t,r){var n=!1,s=null,a=null,u=!1,c=function(){if(null==a||a.unsubscribe(),a=null,n){n=!1;var e=s;s=null,r.next(e)}u&&r.complete()},l=function(){a=null,u&&r.complete()};t.subscribe(o.createOperatorSubscriber(r,function(t){n=!0,s=t,a||i.innerFrom(e(t)).subscribe(a=o.createOperatorSubscriber(r,c,l))},function(){u=!0,n&&a&&!a.closed||r.complete()}))})}},92772:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.withLatestFrom=void 0;var o=r(96829),s=r(28719),a=r(81773),u=r(56032),c=r(63410),l=r(66389);t.withLatestFrom=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l.popResultSelector(e);return o.operate(function(t,o){for(var l=e.length,d=Array(l),h=e.map(function(){return!1}),_=!1,f=function(t){a.innerFrom(e[t]).subscribe(s.createOperatorSubscriber(o,function(e){d[t]=e,!_&&!h[t]&&(h[t]=!0,(_=h.every(u.identity))&&(h=null))},c.noop))},E=0;E<l;E++)f(E);t.subscribe(s.createOperatorSubscriber(o,function(e){if(_){var t=i([e],n(d));o.next(r?r.apply(void 0,i([],n(t))):t)}}))})}},93440:e=>{e.exports=function(e,t){e=e||"   he is here   ";var r={up:["̍","̎","̄","̅","̿","̑","̆","̐","͒","͗","͑","̇","̈","̊","͂","̓","̈","͊","͋","͌","̃","̂","̌","͐","̀","́","̋","̏","̒","̓","̔","̽","̉","ͣ","ͤ","ͥ","ͦ","ͧ","ͨ","ͩ","ͪ","ͫ","ͬ","ͭ","ͮ","ͯ","̾","͛","͆","̚"],down:["̖","̗","̘","̙","̜","̝","̞","̟","̠","̤","̥","̦","̩","̪","̫","̬","̭","̮","̯","̰","̱","̲","̳","̹","̺","̻","̼","ͅ","͇","͈","͉","͍","͎","͓","͔","͕","͖","͙","͚","̣"],mid:["̕","̛","̀","́","͘","̡","̢","̧","̨","̴","̵","̶","͜","͝","͞","͟","͠","͢","̸","̷","͡"," ҉"]},n=[].concat(r.up,r.down,r.mid);function i(e){return Math.floor(Math.random()*e)}return function(e,t){var o,s,a="";for(s in(t=t||{}).up=void 0===t.up||t.up,t.mid=void 0===t.mid||t.mid,t.down=void 0===t.down||t.down,t.size=void 0!==t.size?t.size:"maxi",e=e.split(""))if(!function(e){var t=!1;return n.filter(function(r){t=r===e}),t}(s)){switch(a+=e[s],o={up:0,down:0,mid:0},t.size){case"mini":o.up=i(8),o.mid=i(2),o.down=i(8);break;case"maxi":o.up=i(16)+3,o.mid=i(4)+1,o.down=i(64)+3;break;default:o.up=i(8)+1,o.mid=i(6)/2,o.down=i(8)+1}var u=["up","mid","down"];for(var c in u)for(var l=u[c],d=0;d<=o[l];d++)t[l]&&(a+=r[l][i(r[l].length)])}return a}(e,t)}},93638:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleAsyncIterable=void 0;var n=r(57248),i=r(88840);t.scheduleAsyncIterable=function(e,t){if(!e)throw Error("Iterable cannot be null");return new n.Observable(function(r){i.executeSchedule(r,t,function(){var n=e[Symbol.asyncIterator]();i.executeSchedule(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}},93700:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Combo=void 0;let i=n(r(15850));class o{constructor(e,t,r,n){this.symbol=e,this.comboLegs=t,this.currency=r,this.exchange=n,this.secType=i.default.BAG,this.currency=this.currency??"USD",this.exchange=this.exchange??"SMART"}}t.Combo=o,t.default=o},94015:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LimitOrder=void 0;let n=r(55332);class i{constructor(e,t,r,i=!0){this.action=e,this.lmtPrice=t,this.totalQuantity=r,this.transmit=i,this.orderType=n.OrderType.LMT}}t.LimitOrder=i,t.default=i},94022:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.race=void 0;var o=r(19029),s=r(82258);t.race=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return s.raceWith.apply(void 0,i([],n(o.argsOrArgArray(e))))}},94108:(e,t,r)=>{"use strict";var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.bufferCount=void 0;var i=r(96829),o=r(28719),s=r(1071);t.bufferCount=function(e,t){return void 0===t&&(t=null),t=null!=t?t:e,i.operate(function(r,i){var a=[],u=0;r.subscribe(o.createOperatorSubscriber(i,function(r){var o,c,l,d,h=null;u++%t==0&&a.push([]);try{for(var _=n(a),f=_.next();!f.done;f=_.next()){var E=f.value;E.push(r),e<=E.length&&(h=null!=h?h:[]).push(E)}}catch(e){o={error:e}}finally{try{f&&!f.done&&(c=_.return)&&c.call(_)}finally{if(o)throw o.error}}if(h)try{for(var p=n(h),T=p.next();!T.done;T=p.next()){var E=T.value;s.arrRemove(a,E),i.next(E)}}catch(e){l={error:e}}finally{try{T&&!T.done&&(d=p.return)&&d.call(p)}finally{if(l)throw l.error}}},function(){var e,t;try{for(var r=n(a),o=r.next();!o.done;o=r.next()){var s=o.value;i.next(s)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}i.complete()},void 0,function(){a=null}))})}},94165:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.connect=void 0;var n=r(1937),i=r(81773),o=r(96829),s=r(96324),a={connector:function(){return new n.Subject}};t.connect=function(e,t){void 0===t&&(t=a);var r=t.connector;return o.operate(function(t,n){var o=r();i.innerFrom(e(s.fromSubscribable(o))).subscribe(n),n.add(t.subscribe(o))})}},94498:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.timeoutWith=void 0;var n=r(97847),i=r(8742),o=r(59798);t.timeoutWith=function(e,t,r){var s,a,u;if(r=null!=r?r:n.async,i.isValidDate(e)?s=e:"number"==typeof e&&(a=e),t)u=function(){return t};else throw TypeError("No observable provided to switch to");if(null==s&&null==a)throw TypeError("No timeout provided.");return o.timeout({first:s,each:a,scheduler:r,with:u})}},95369:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.captureError=t.errorContext=void 0;var n=r(87273),i=null;t.errorContext=function(e){if(n.config.useDeprecatedSynchronousErrorHandling){var t=!i;if(t&&(i={errorThrown:!1,error:null}),e(),t){var r=i,o=r.errorThrown,s=r.error;if(i=null,o)throw s}}else e()},t.captureError=function(e){n.config.useDeprecatedSynchronousErrorHandling&&i&&(i.errorThrown=!0,i.error=e)}},95628:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.observeOn=void 0;var n=r(88840),i=r(96829),o=r(28719);t.observeOn=function(e,t){return void 0===t&&(t=0),i.operate(function(r,i){r.subscribe(o.createOperatorSubscriber(i,function(r){return n.executeSchedule(i,e,function(){return i.next(r)},t)},function(){return n.executeSchedule(i,e,function(){return i.complete()},t)},function(r){return n.executeSchedule(i,e,function(){return i.error(r)},t)}))})}},96045:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Index=void 0;let i=n(r(15850));class o{constructor(e,t,r){this.symbol=e,this.currency=t,this.exchange=r,this.secType=i.default.IND,this.currency=this.currency??"USD",this.exchange=this.exchange??"CME"}}t.Index=o,t.default=o},96324:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromSubscribable=void 0;var n=r(57248);t.fromSubscribable=function(e){return new n.Observable(function(t){return e.subscribe(t)})}},96634:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UnsubscriptionError=void 0,t.UnsubscriptionError=r(23008).createErrorClass(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}})},96829:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.operate=t.hasLift=void 0;var n=r(17886);function i(e){return n.isFunction(null==e?void 0:e.lift)}t.hasLift=i,t.operate=function(e){return function(t){if(i(t))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}},97118:(e,t,r)=>{"use strict";var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.onErrorResumeNext=t.onErrorResumeNextWith=void 0;var o=r(19029),s=r(72836);function a(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=o.argsOrArgArray(e);return function(e){return s.onErrorResumeNext.apply(void 0,i([e],n(r)))}}t.onErrorResumeNextWith=a,t.onErrorResumeNext=a},97847:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.async=t.asyncScheduler=void 0;var n=r(74661);t.asyncScheduler=new(r(34138)).AsyncScheduler(n.AsyncAction),t.async=t.asyncScheduler},98102:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WhatToShow=void 0,t.WhatToShow={None:"",TRADES:"TRADES",MIDPOINT:"MIDPOINT",BID:"BID",ASK:"ASK",BID_ASK:"BID_ASK",HISTORICAL_VOLATILITY:"HISTORICAL_VOLATILITY",OPTION_IMPLIED_VOLATILITY:"OPTION_IMPLIED_VOLATILITY",YIELD_ASK:"YIELD_ASK",YIELD_BID:"YIELD_BID",YIELD_BID_ASK:"YIELD_BID_ASK",YIELD_LAST:"YIELD_LAST",ADJUSTED_LAST:"ADJUSTED_LAST",SCHEDULE:"SCHEDULE",AGGTRADES:"AGGTRADES"}},98193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.throwIfEmpty=void 0;var n=r(46889),i=r(96829),o=r(28719);function s(){return new n.EmptyError}t.throwIfEmpty=function(e){return void 0===e&&(e=s),i.operate(function(t,r){var n=!1;t.subscribe(o.createOperatorSubscriber(r,function(e){n=!0,r.next(e)},function(){return n?r.complete():r.error(e())}))})}},98263:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StopLimitOrder=void 0;let n=r(55332),i=r(72785);class o{constructor(e,t,r,o,s,a,u){this.action=e,this.lmtPrice=t,this.auxPrice=r,this.totalQuantity=o,this.transmit=s,this.parentId=a,this.tif=u,this.orderType=n.OrderType.STP_LMT,this.transmit=this.transmit??!0,this.parentId=this.parentId??0,this.tif=this.tif??i.TimeInForce.DAY}}t.StopLimitOrder=o,t.default=o},98491:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PriceCondition=void 0;let n=r(48909);class i{constructor(e,t,r,i,o,s){this.price=e,this.triggerMethod=t,this.conId=r,this.exchange=i,this.isMore=o,this.conjunctionConnection=s,this.type=n.OrderConditionType.Price}get strValue(){return""+this.price}}t.PriceCondition=i,t.default=i},99515:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IBApiNextError=void 0;let n=r(90742);class i extends Error{constructor(e,t,r=n.ErrorCode.NO_VALID_ID,o){super(e.message),this.name="IBApiNextError",Object.setPrototypeOf(this,i.prototype),this.error=e,this.code=t,this.reqId=r,this.advancedOrderReject=o}}t.IBApiNextError=i}};