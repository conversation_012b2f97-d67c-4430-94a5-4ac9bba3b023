exports.id=7524,exports.ids=[7524],exports.modules={1608:(e,a,t)=>{"use strict";let o=t(33267),i=["local","example","invalid","localhost","test"],n=["localhost","invalid"];a.getPublicSuffix=function(e,a={}){let t=e.split("."),r=t[t.length-1],s=!!a.allowSpecialUseDomain,l=!!a.ignoreError;if(s&&i.includes(r)){if(t.length>1){let e=t[t.length-2];return`${e}.${r}`}else if(n.includes(r))return`${r}`}if(!l&&i.includes(r))throw Error(`<PERSON><PERSON> has domain set to the public suffix "${r}" which is a special use domain. To allow this, configure your CookieJar with {allowSpecialUseDomain:true, rejectPublicSuffixes: false}.`);return o.get(e)}},5837:(e,a)=>{"use strict";a.fromCallback=function(e){return Object.defineProperty(function(){if("function"!=typeof arguments[arguments.length-1])return new Promise((a,t)=>{arguments[arguments.length]=(e,o)=>{if(e)return t(e);a(o)},arguments.length++,e.apply(this,arguments)});e.apply(this,arguments)},"name",{value:e.name})},a.fromPromise=function(e){return Object.defineProperty(function(){let a=arguments[arguments.length-1];if("function"!=typeof a)return e.apply(this,arguments);delete arguments[arguments.length-1],arguments.length--,e.apply(this,arguments).then(e=>a(null,e),a)},"name",{value:e.name})}},10018:(e,a,t)=>{"use strict";let o=t(1608);a.$=function(e,a){let t=o.getPublicSuffix(e,{allowSpecialUseDomain:a});if(!t)return null;if(t==e)return[e];"."==e.slice(-1)&&(e=e.slice(0,-1));let i=e.slice(0,-(t.length+1)).split(".").reverse(),n=t,r=[n];for(;i.length;)r.push(n=`${i.shift()}.${n}`);return r}},12757:(e,a)=>{"use strict";let t=Object.prototype.toString;function o(e){return"function"==typeof e}function i(e){return n(e)&&""!==e}function n(e){return"string"==typeof e||e instanceof String}function r(e){return"[object Object]"===t.call(e)}function s(e,a){try{return e instanceof a}catch(e){return!1}}class l extends Error{constructor(...e){super(...e)}}a.ParameterError=l,a.isFunction=o,a.isNonEmptyString=i,a.isDate=function(e){var a;return s(e,Date)&&"number"==typeof(a=e.getTime())&&a%1==0},a.isEmptyString=function(e){return""===e||e instanceof String&&""===e.toString()},a.isString=n,a.isObject=r,a.isUrlStringOrObject=function(e){return i(e)||r(e)&&"hostname"in e&&"pathname"in e&&"protocol"in e||s(e,URL)},a.validate=function(e,a,t){if(o(a)||(t=a,a=null),r(t)||(t={Error:"Failed Check"}),!e)if(a)a(new l(t));else throw new l(t)}},16922:e=>{"use strict";e.exports=function(e,a){if(a=a.split(":")[0],!(e*=1))return!1;switch(a){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},33267:(e,a)=>{"use strict";var t,o;Object.defineProperties(a,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});let i=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function(){if(o)return t;o=1;let e=/^xn--/,a=/[^\0-\x7F]/,i=/[\x2E\u3002\uFF0E\uFF61]/g,n={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},r=Math.floor,s=String.fromCharCode;function l(e){throw RangeError(n[e])}function u(e,a){let t=e.split("@"),o="";return t.length>1&&(o=t[0]+"@",e=t[1]),o+(function(e,a){let t=[],o=e.length;for(;o--;)t[o]=a(e[o]);return t})((e=e.replace(i,".")).split("."),a).join(".")}function c(e){let a=[],t=0,o=e.length;for(;t<o;){let i=e.charCodeAt(t++);if(i>=55296&&i<=56319&&t<o){let o=e.charCodeAt(t++);(64512&o)==56320?a.push(((1023&i)<<10)+(1023&o)+65536):(a.push(i),t--)}else a.push(i)}return a}let m=function(e,a){return e+22+75*(e<26)-((0!=a)<<5)},p=function(e,a,t){let o=0;for(e=t?r(e/700):e>>1,e+=r(e/a);e>455;o+=36)e=r(e/35);return r(o+36*e/(e+38))},d=function(e){let a=[],t=e.length,o=0,i=128,n=72,s=e.lastIndexOf("-");s<0&&(s=0);for(let t=0;t<s;++t)e.charCodeAt(t)>=128&&l("not-basic"),a.push(e.charCodeAt(t));for(let c=s>0?s+1:0;c<t;){let s=o;for(let a=1,i=36;;i+=36){var u;c>=t&&l("invalid-input");let s=(u=e.charCodeAt(c++))>=48&&u<58?26+(u-48):u>=65&&u<91?u-65:u>=97&&u<123?u-97:36;s>=36&&l("invalid-input"),s>r((0x7fffffff-o)/a)&&l("overflow"),o+=s*a;let m=i<=n?1:i>=n+26?26:i-n;if(s<m)break;let p=36-m;a>r(0x7fffffff/p)&&l("overflow"),a*=p}let m=a.length+1;n=p(o-s,m,0==s),r(o/m)>0x7fffffff-i&&l("overflow"),i+=r(o/m),o%=m,a.splice(o++,0,i)}return String.fromCodePoint(...a)},g=function(e){let a=[],t=(e=c(e)).length,o=128,i=0,n=72;for(let t of e)t<128&&a.push(s(t));let u=a.length,d=u;for(u&&a.push("-");d<t;){let t=0x7fffffff;for(let a of e)a>=o&&a<t&&(t=a);let c=d+1;for(let g of(t-o>r((0x7fffffff-i)/c)&&l("overflow"),i+=(t-o)*c,o=t,e))if(g<o&&++i>0x7fffffff&&l("overflow"),g===o){let e=i;for(let t=36;;t+=36){let o=t<=n?1:t>=n+26?26:t-n;if(e<o)break;let i=e-o,l=36-o;a.push(s(m(o+i%l,0))),e=r(i/l)}a.push(s(m(e,0))),n=p(i,c,d===u),i=0,++d}++i,++o}return a.join("")};return t={version:"2.3.1",ucs2:{decode:c,encode:e=>String.fromCodePoint(...e)},decode:d,encode:g,toASCII:function(e){return u(e,function(e){return a.test(e)?"xn--"+g(e):e})},toUnicode:function(a){return u(a,function(a){return e.test(a)?d(a.slice(4).toLowerCase()):a})}}}()),n=["ac","com.ac","edu.ac","gov.ac","mil.ac","net.ac","org.ac","ad","ae","ac.ae","co.ae","gov.ae","mil.ae","net.ae","org.ae","sch.ae","aero","airline.aero","airport.aero","accident-investigation.aero","accident-prevention.aero","aerobatic.aero","aeroclub.aero","aerodrome.aero","agents.aero","air-surveillance.aero","air-traffic-control.aero","aircraft.aero","airtraffic.aero","ambulance.aero","association.aero","author.aero","ballooning.aero","broker.aero","caa.aero","cargo.aero","catering.aero","certification.aero","championship.aero","charter.aero","civilaviation.aero","club.aero","conference.aero","consultant.aero","consulting.aero","control.aero","council.aero","crew.aero","design.aero","dgca.aero","educator.aero","emergency.aero","engine.aero","engineer.aero","entertainment.aero","equipment.aero","exchange.aero","express.aero","federation.aero","flight.aero","freight.aero","fuel.aero","gliding.aero","government.aero","groundhandling.aero","group.aero","hanggliding.aero","homebuilt.aero","insurance.aero","journal.aero","journalist.aero","leasing.aero","logistics.aero","magazine.aero","maintenance.aero","marketplace.aero","media.aero","microlight.aero","modelling.aero","navigation.aero","parachuting.aero","paragliding.aero","passenger-association.aero","pilot.aero","press.aero","production.aero","recreation.aero","repbody.aero","res.aero","research.aero","rotorcraft.aero","safety.aero","scientist.aero","services.aero","show.aero","skydiving.aero","software.aero","student.aero","taxi.aero","trader.aero","trading.aero","trainer.aero","union.aero","workinggroup.aero","works.aero","af","com.af","edu.af","gov.af","net.af","org.af","ag","co.ag","com.ag","net.ag","nom.ag","org.ag","ai","com.ai","net.ai","off.ai","org.ai","al","com.al","edu.al","gov.al","mil.al","net.al","org.al","am","co.am","com.am","commune.am","net.am","org.am","ao","co.ao","ed.ao","edu.ao","gov.ao","gv.ao","it.ao","og.ao","org.ao","pb.ao","aq","ar","bet.ar","com.ar","coop.ar","edu.ar","gob.ar","gov.ar","int.ar","mil.ar","musica.ar","mutual.ar","net.ar","org.ar","senasa.ar","tur.ar","arpa","e164.arpa","home.arpa","in-addr.arpa","ip6.arpa","iris.arpa","uri.arpa","urn.arpa","as","gov.as","asia","at","ac.at","sth.ac.at","co.at","gv.at","or.at","au","asn.au","com.au","edu.au","gov.au","id.au","net.au","org.au","conf.au","oz.au","act.au","nsw.au","nt.au","qld.au","sa.au","tas.au","vic.au","wa.au","act.edu.au","catholic.edu.au","nsw.edu.au","nt.edu.au","qld.edu.au","sa.edu.au","tas.edu.au","vic.edu.au","wa.edu.au","qld.gov.au","sa.gov.au","tas.gov.au","vic.gov.au","wa.gov.au","schools.nsw.edu.au","aw","com.aw","ax","az","biz.az","com.az","edu.az","gov.az","info.az","int.az","mil.az","name.az","net.az","org.az","pp.az","pro.az","ba","com.ba","edu.ba","gov.ba","mil.ba","net.ba","org.ba","bb","biz.bb","co.bb","com.bb","edu.bb","gov.bb","info.bb","net.bb","org.bb","store.bb","tv.bb","*.bd","be","ac.be","bf","gov.bf","bg","0.bg","1.bg","2.bg","3.bg","4.bg","5.bg","6.bg","7.bg","8.bg","9.bg","a.bg","b.bg","c.bg","d.bg","e.bg","f.bg","g.bg","h.bg","i.bg","j.bg","k.bg","l.bg","m.bg","n.bg","o.bg","p.bg","q.bg","r.bg","s.bg","t.bg","u.bg","v.bg","w.bg","x.bg","y.bg","z.bg","bh","com.bh","edu.bh","gov.bh","net.bh","org.bh","bi","co.bi","com.bi","edu.bi","or.bi","org.bi","biz","bj","africa.bj","agro.bj","architectes.bj","assur.bj","avocats.bj","co.bj","com.bj","eco.bj","econo.bj","edu.bj","info.bj","loisirs.bj","money.bj","net.bj","org.bj","ote.bj","restaurant.bj","resto.bj","tourism.bj","univ.bj","bm","com.bm","edu.bm","gov.bm","net.bm","org.bm","bn","com.bn","edu.bn","gov.bn","net.bn","org.bn","bo","com.bo","edu.bo","gob.bo","int.bo","mil.bo","net.bo","org.bo","tv.bo","web.bo","academia.bo","agro.bo","arte.bo","blog.bo","bolivia.bo","ciencia.bo","cooperativa.bo","democracia.bo","deporte.bo","ecologia.bo","economia.bo","empresa.bo","indigena.bo","industria.bo","info.bo","medicina.bo","movimiento.bo","musica.bo","natural.bo","nombre.bo","noticias.bo","patria.bo","plurinacional.bo","politica.bo","profesional.bo","pueblo.bo","revista.bo","salud.bo","tecnologia.bo","tksat.bo","transporte.bo","wiki.bo","br","9guacu.br","abc.br","adm.br","adv.br","agr.br","aju.br","am.br","anani.br","aparecida.br","app.br","arq.br","art.br","ato.br","b.br","barueri.br","belem.br","bet.br","bhz.br","bib.br","bio.br","blog.br","bmd.br","boavista.br","bsb.br","campinagrande.br","campinas.br","caxias.br","cim.br","cng.br","cnt.br","com.br","contagem.br","coop.br","coz.br","cri.br","cuiaba.br","curitiba.br","def.br","des.br","det.br","dev.br","ecn.br","eco.br","edu.br","emp.br","enf.br","eng.br","esp.br","etc.br","eti.br","far.br","feira.br","flog.br","floripa.br","fm.br","fnd.br","fortal.br","fot.br","foz.br","fst.br","g12.br","geo.br","ggf.br","goiania.br","gov.br","ac.gov.br","al.gov.br","am.gov.br","ap.gov.br","ba.gov.br","ce.gov.br","df.gov.br","es.gov.br","go.gov.br","ma.gov.br","mg.gov.br","ms.gov.br","mt.gov.br","pa.gov.br","pb.gov.br","pe.gov.br","pi.gov.br","pr.gov.br","rj.gov.br","rn.gov.br","ro.gov.br","rr.gov.br","rs.gov.br","sc.gov.br","se.gov.br","sp.gov.br","to.gov.br","gru.br","imb.br","ind.br","inf.br","jab.br","jampa.br","jdf.br","joinville.br","jor.br","jus.br","leg.br","leilao.br","lel.br","log.br","londrina.br","macapa.br","maceio.br","manaus.br","maringa.br","mat.br","med.br","mil.br","morena.br","mp.br","mus.br","natal.br","net.br","niteroi.br","*.nom.br","not.br","ntr.br","odo.br","ong.br","org.br","osasco.br","palmas.br","poa.br","ppg.br","pro.br","psc.br","psi.br","pvh.br","qsl.br","radio.br","rec.br","recife.br","rep.br","ribeirao.br","rio.br","riobranco.br","riopreto.br","salvador.br","sampa.br","santamaria.br","santoandre.br","saobernardo.br","saogonca.br","seg.br","sjc.br","slg.br","slz.br","sorocaba.br","srv.br","taxi.br","tc.br","tec.br","teo.br","the.br","tmp.br","trd.br","tur.br","tv.br","udi.br","vet.br","vix.br","vlog.br","wiki.br","zlg.br","bs","com.bs","edu.bs","gov.bs","net.bs","org.bs","bt","com.bt","edu.bt","gov.bt","net.bt","org.bt","bv","bw","co.bw","org.bw","by","gov.by","mil.by","com.by","of.by","bz","co.bz","com.bz","edu.bz","gov.bz","net.bz","org.bz","ca","ab.ca","bc.ca","mb.ca","nb.ca","nf.ca","nl.ca","ns.ca","nt.ca","nu.ca","on.ca","pe.ca","qc.ca","sk.ca","yk.ca","gc.ca","cat","cc","cd","gov.cd","cf","cg","ch","ci","ac.ci","a\xe9roport.ci","asso.ci","co.ci","com.ci","ed.ci","edu.ci","go.ci","gouv.ci","int.ci","net.ci","or.ci","org.ci","*.ck","!www.ck","cl","co.cl","gob.cl","gov.cl","mil.cl","cm","co.cm","com.cm","gov.cm","net.cm","cn","ac.cn","com.cn","edu.cn","gov.cn","mil.cn","net.cn","org.cn","公司.cn","網絡.cn","网络.cn","ah.cn","bj.cn","cq.cn","fj.cn","gd.cn","gs.cn","gx.cn","gz.cn","ha.cn","hb.cn","he.cn","hi.cn","hk.cn","hl.cn","hn.cn","jl.cn","js.cn","jx.cn","ln.cn","mo.cn","nm.cn","nx.cn","qh.cn","sc.cn","sd.cn","sh.cn","sn.cn","sx.cn","tj.cn","tw.cn","xj.cn","xz.cn","yn.cn","zj.cn","co","com.co","edu.co","gov.co","mil.co","net.co","nom.co","org.co","com","coop","cr","ac.cr","co.cr","ed.cr","fi.cr","go.cr","or.cr","sa.cr","cu","com.cu","edu.cu","gob.cu","inf.cu","nat.cu","net.cu","org.cu","cv","com.cv","edu.cv","id.cv","int.cv","net.cv","nome.cv","org.cv","publ.cv","cw","com.cw","edu.cw","net.cw","org.cw","cx","gov.cx","cy","ac.cy","biz.cy","com.cy","ekloges.cy","gov.cy","ltd.cy","mil.cy","net.cy","org.cy","press.cy","pro.cy","tm.cy","cz","de","dj","dk","dm","co.dm","com.dm","edu.dm","gov.dm","net.dm","org.dm","do","art.do","com.do","edu.do","gob.do","gov.do","mil.do","net.do","org.do","sld.do","web.do","dz","art.dz","asso.dz","com.dz","edu.dz","gov.dz","net.dz","org.dz","pol.dz","soc.dz","tm.dz","ec","com.ec","edu.ec","fin.ec","gob.ec","gov.ec","info.ec","k12.ec","med.ec","mil.ec","net.ec","org.ec","pro.ec","edu","ee","aip.ee","com.ee","edu.ee","fie.ee","gov.ee","lib.ee","med.ee","org.ee","pri.ee","riik.ee","eg","ac.eg","com.eg","edu.eg","eun.eg","gov.eg","info.eg","me.eg","mil.eg","name.eg","net.eg","org.eg","sci.eg","sport.eg","tv.eg","*.er","es","com.es","edu.es","gob.es","nom.es","org.es","et","biz.et","com.et","edu.et","gov.et","info.et","name.et","net.et","org.et","eu","fi","aland.fi","fj","ac.fj","biz.fj","com.fj","gov.fj","info.fj","mil.fj","name.fj","net.fj","org.fj","pro.fj","*.fk","fm","com.fm","edu.fm","net.fm","org.fm","fo","fr","asso.fr","com.fr","gouv.fr","nom.fr","prd.fr","tm.fr","avoues.fr","cci.fr","greta.fr","huissier-justice.fr","ga","gb","gd","edu.gd","gov.gd","ge","com.ge","edu.ge","gov.ge","net.ge","org.ge","pvt.ge","school.ge","gf","gg","co.gg","net.gg","org.gg","gh","com.gh","edu.gh","gov.gh","mil.gh","org.gh","gi","com.gi","edu.gi","gov.gi","ltd.gi","mod.gi","org.gi","gl","co.gl","com.gl","edu.gl","net.gl","org.gl","gm","gn","ac.gn","com.gn","edu.gn","gov.gn","net.gn","org.gn","gov","gp","asso.gp","com.gp","edu.gp","mobi.gp","net.gp","org.gp","gq","gr","com.gr","edu.gr","gov.gr","net.gr","org.gr","gs","gt","com.gt","edu.gt","gob.gt","ind.gt","mil.gt","net.gt","org.gt","gu","com.gu","edu.gu","gov.gu","guam.gu","info.gu","net.gu","org.gu","web.gu","gw","gy","co.gy","com.gy","edu.gy","gov.gy","net.gy","org.gy","hk","com.hk","edu.hk","gov.hk","idv.hk","net.hk","org.hk","个人.hk","個人.hk","公司.hk","政府.hk","敎育.hk","教育.hk","箇人.hk","組織.hk","組织.hk","網絡.hk","網络.hk","组織.hk","组织.hk","网絡.hk","网络.hk","hm","hn","com.hn","edu.hn","gob.hn","mil.hn","net.hn","org.hn","hr","com.hr","from.hr","iz.hr","name.hr","ht","adult.ht","art.ht","asso.ht","com.ht","coop.ht","edu.ht","firm.ht","gouv.ht","info.ht","med.ht","net.ht","org.ht","perso.ht","pol.ht","pro.ht","rel.ht","shop.ht","hu","2000.hu","agrar.hu","bolt.hu","casino.hu","city.hu","co.hu","erotica.hu","erotika.hu","film.hu","forum.hu","games.hu","hotel.hu","info.hu","ingatlan.hu","jogasz.hu","konyvelo.hu","lakas.hu","media.hu","news.hu","org.hu","priv.hu","reklam.hu","sex.hu","shop.hu","sport.hu","suli.hu","szex.hu","tm.hu","tozsde.hu","utazas.hu","video.hu","id","ac.id","biz.id","co.id","desa.id","go.id","mil.id","my.id","net.id","or.id","ponpes.id","sch.id","web.id","ie","gov.ie","il","ac.il","co.il","gov.il","idf.il","k12.il","muni.il","net.il","org.il","ישראל","אקדמיה.ישראל","ישוב.ישראל","צהל.ישראל","ממשל.ישראל","im","ac.im","co.im","ltd.co.im","plc.co.im","com.im","net.im","org.im","tt.im","tv.im","in","5g.in","6g.in","ac.in","ai.in","am.in","bihar.in","biz.in","business.in","ca.in","cn.in","co.in","com.in","coop.in","cs.in","delhi.in","dr.in","edu.in","er.in","firm.in","gen.in","gov.in","gujarat.in","ind.in","info.in","int.in","internet.in","io.in","me.in","mil.in","net.in","nic.in","org.in","pg.in","post.in","pro.in","res.in","travel.in","tv.in","uk.in","up.in","us.in","info","int","eu.int","io","co.io","com.io","edu.io","gov.io","mil.io","net.io","nom.io","org.io","iq","com.iq","edu.iq","gov.iq","mil.iq","net.iq","org.iq","ir","ac.ir","co.ir","gov.ir","id.ir","net.ir","org.ir","sch.ir","ایران.ir","ايران.ir","is","it","edu.it","gov.it","abr.it","abruzzo.it","aosta-valley.it","aostavalley.it","bas.it","basilicata.it","cal.it","calabria.it","cam.it","campania.it","emilia-romagna.it","emiliaromagna.it","emr.it","friuli-v-giulia.it","friuli-ve-giulia.it","friuli-vegiulia.it","friuli-venezia-giulia.it","friuli-veneziagiulia.it","friuli-vgiulia.it","friuliv-giulia.it","friulive-giulia.it","friulivegiulia.it","friulivenezia-giulia.it","friuliveneziagiulia.it","friulivgiulia.it","fvg.it","laz.it","lazio.it","lig.it","liguria.it","lom.it","lombardia.it","lombardy.it","lucania.it","mar.it","marche.it","mol.it","molise.it","piedmont.it","piemonte.it","pmn.it","pug.it","puglia.it","sar.it","sardegna.it","sardinia.it","sic.it","sicilia.it","sicily.it","taa.it","tos.it","toscana.it","trentin-sud-tirol.it","trentin-s\xfcd-tirol.it","trentin-sudtirol.it","trentin-s\xfcdtirol.it","trentin-sued-tirol.it","trentin-suedtirol.it","trentino.it","trentino-a-adige.it","trentino-aadige.it","trentino-alto-adige.it","trentino-altoadige.it","trentino-s-tirol.it","trentino-stirol.it","trentino-sud-tirol.it","trentino-s\xfcd-tirol.it","trentino-sudtirol.it","trentino-s\xfcdtirol.it","trentino-sued-tirol.it","trentino-suedtirol.it","trentinoa-adige.it","trentinoaadige.it","trentinoalto-adige.it","trentinoaltoadige.it","trentinos-tirol.it","trentinostirol.it","trentinosud-tirol.it","trentinos\xfcd-tirol.it","trentinosudtirol.it","trentinos\xfcdtirol.it","trentinosued-tirol.it","trentinosuedtirol.it","trentinsud-tirol.it","trentins\xfcd-tirol.it","trentinsudtirol.it","trentins\xfcdtirol.it","trentinsued-tirol.it","trentinsuedtirol.it","tuscany.it","umb.it","umbria.it","val-d-aosta.it","val-daosta.it","vald-aosta.it","valdaosta.it","valle-aosta.it","valle-d-aosta.it","valle-daosta.it","valleaosta.it","valled-aosta.it","valledaosta.it","vallee-aoste.it","vall\xe9e-aoste.it","vallee-d-aoste.it","vall\xe9e-d-aoste.it","valleeaoste.it","vall\xe9eaoste.it","valleedaoste.it","vall\xe9edaoste.it","vao.it","vda.it","ven.it","veneto.it","ag.it","agrigento.it","al.it","alessandria.it","alto-adige.it","altoadige.it","an.it","ancona.it","andria-barletta-trani.it","andria-trani-barletta.it","andriabarlettatrani.it","andriatranibarletta.it","ao.it","aosta.it","aoste.it","ap.it","aq.it","aquila.it","ar.it","arezzo.it","ascoli-piceno.it","ascolipiceno.it","asti.it","at.it","av.it","avellino.it","ba.it","balsan.it","balsan-sudtirol.it","balsan-s\xfcdtirol.it","balsan-suedtirol.it","bari.it","barletta-trani-andria.it","barlettatraniandria.it","belluno.it","benevento.it","bergamo.it","bg.it","bi.it","biella.it","bl.it","bn.it","bo.it","bologna.it","bolzano.it","bolzano-altoadige.it","bozen.it","bozen-sudtirol.it","bozen-s\xfcdtirol.it","bozen-suedtirol.it","br.it","brescia.it","brindisi.it","bs.it","bt.it","bulsan.it","bulsan-sudtirol.it","bulsan-s\xfcdtirol.it","bulsan-suedtirol.it","bz.it","ca.it","cagliari.it","caltanissetta.it","campidano-medio.it","campidanomedio.it","campobasso.it","carbonia-iglesias.it","carboniaiglesias.it","carrara-massa.it","carraramassa.it","caserta.it","catania.it","catanzaro.it","cb.it","ce.it","cesena-forli.it","cesena-forl\xec.it","cesenaforli.it","cesenaforl\xec.it","ch.it","chieti.it","ci.it","cl.it","cn.it","co.it","como.it","cosenza.it","cr.it","cremona.it","crotone.it","cs.it","ct.it","cuneo.it","cz.it","dell-ogliastra.it","dellogliastra.it","en.it","enna.it","fc.it","fe.it","fermo.it","ferrara.it","fg.it","fi.it","firenze.it","florence.it","fm.it","foggia.it","forli-cesena.it","forl\xec-cesena.it","forlicesena.it","forl\xeccesena.it","fr.it","frosinone.it","ge.it","genoa.it","genova.it","go.it","gorizia.it","gr.it","grosseto.it","iglesias-carbonia.it","iglesiascarbonia.it","im.it","imperia.it","is.it","isernia.it","kr.it","la-spezia.it","laquila.it","laspezia.it","latina.it","lc.it","le.it","lecce.it","lecco.it","li.it","livorno.it","lo.it","lodi.it","lt.it","lu.it","lucca.it","macerata.it","mantova.it","massa-carrara.it","massacarrara.it","matera.it","mb.it","mc.it","me.it","medio-campidano.it","mediocampidano.it","messina.it","mi.it","milan.it","milano.it","mn.it","mo.it","modena.it","monza.it","monza-brianza.it","monza-e-della-brianza.it","monzabrianza.it","monzaebrianza.it","monzaedellabrianza.it","ms.it","mt.it","na.it","naples.it","napoli.it","no.it","novara.it","nu.it","nuoro.it","og.it","ogliastra.it","olbia-tempio.it","olbiatempio.it","or.it","oristano.it","ot.it","pa.it","padova.it","padua.it","palermo.it","parma.it","pavia.it","pc.it","pd.it","pe.it","perugia.it","pesaro-urbino.it","pesarourbino.it","pescara.it","pg.it","pi.it","piacenza.it","pisa.it","pistoia.it","pn.it","po.it","pordenone.it","potenza.it","pr.it","prato.it","pt.it","pu.it","pv.it","pz.it","ra.it","ragusa.it","ravenna.it","rc.it","re.it","reggio-calabria.it","reggio-emilia.it","reggiocalabria.it","reggioemilia.it","rg.it","ri.it","rieti.it","rimini.it","rm.it","rn.it","ro.it","roma.it","rome.it","rovigo.it","sa.it","salerno.it","sassari.it","savona.it","si.it","siena.it","siracusa.it","so.it","sondrio.it","sp.it","sr.it","ss.it","s\xfcdtirol.it","suedtirol.it","sv.it","ta.it","taranto.it","te.it","tempio-olbia.it","tempioolbia.it","teramo.it","terni.it","tn.it","to.it","torino.it","tp.it","tr.it","trani-andria-barletta.it","trani-barletta-andria.it","traniandriabarletta.it","tranibarlettaandria.it","trapani.it","trento.it","treviso.it","trieste.it","ts.it","turin.it","tv.it","ud.it","udine.it","urbino-pesaro.it","urbinopesaro.it","va.it","varese.it","vb.it","vc.it","ve.it","venezia.it","venice.it","verbania.it","vercelli.it","verona.it","vi.it","vibo-valentia.it","vibovalentia.it","vicenza.it","viterbo.it","vr.it","vs.it","vt.it","vv.it","je","co.je","net.je","org.je","*.jm","jo","agri.jo","ai.jo","com.jo","edu.jo","eng.jo","fm.jo","gov.jo","mil.jo","net.jo","org.jo","per.jo","phd.jo","sch.jo","tv.jo","jobs","jp","ac.jp","ad.jp","co.jp","ed.jp","go.jp","gr.jp","lg.jp","ne.jp","or.jp","aichi.jp","akita.jp","aomori.jp","chiba.jp","ehime.jp","fukui.jp","fukuoka.jp","fukushima.jp","gifu.jp","gunma.jp","hiroshima.jp","hokkaido.jp","hyogo.jp","ibaraki.jp","ishikawa.jp","iwate.jp","kagawa.jp","kagoshima.jp","kanagawa.jp","kochi.jp","kumamoto.jp","kyoto.jp","mie.jp","miyagi.jp","miyazaki.jp","nagano.jp","nagasaki.jp","nara.jp","niigata.jp","oita.jp","okayama.jp","okinawa.jp","osaka.jp","saga.jp","saitama.jp","shiga.jp","shimane.jp","shizuoka.jp","tochigi.jp","tokushima.jp","tokyo.jp","tottori.jp","toyama.jp","wakayama.jp","yamagata.jp","yamaguchi.jp","yamanashi.jp","三重.jp","京都.jp","佐賀.jp","兵庫.jp","北海道.jp","千葉.jp","和歌山.jp","埼玉.jp","大分.jp","大阪.jp","奈良.jp","宮城.jp","宮崎.jp","富山.jp","山口.jp","山形.jp","山梨.jp","岐阜.jp","岡山.jp","岩手.jp","島根.jp","広島.jp","徳島.jp","愛媛.jp","愛知.jp","新潟.jp","東京.jp","栃木.jp","沖縄.jp","滋賀.jp","熊本.jp","石川.jp","神奈川.jp","福井.jp","福岡.jp","福島.jp","秋田.jp","群馬.jp","茨城.jp","長崎.jp","長野.jp","青森.jp","静岡.jp","香川.jp","高知.jp","鳥取.jp","鹿児島.jp","*.kawasaki.jp","!city.kawasaki.jp","*.kitakyushu.jp","!city.kitakyushu.jp","*.kobe.jp","!city.kobe.jp","*.nagoya.jp","!city.nagoya.jp","*.sapporo.jp","!city.sapporo.jp","*.sendai.jp","!city.sendai.jp","*.yokohama.jp","!city.yokohama.jp","aisai.aichi.jp","ama.aichi.jp","anjo.aichi.jp","asuke.aichi.jp","chiryu.aichi.jp","chita.aichi.jp","fuso.aichi.jp","gamagori.aichi.jp","handa.aichi.jp","hazu.aichi.jp","hekinan.aichi.jp","higashiura.aichi.jp","ichinomiya.aichi.jp","inazawa.aichi.jp","inuyama.aichi.jp","isshiki.aichi.jp","iwakura.aichi.jp","kanie.aichi.jp","kariya.aichi.jp","kasugai.aichi.jp","kira.aichi.jp","kiyosu.aichi.jp","komaki.aichi.jp","konan.aichi.jp","kota.aichi.jp","mihama.aichi.jp","miyoshi.aichi.jp","nishio.aichi.jp","nisshin.aichi.jp","obu.aichi.jp","oguchi.aichi.jp","oharu.aichi.jp","okazaki.aichi.jp","owariasahi.aichi.jp","seto.aichi.jp","shikatsu.aichi.jp","shinshiro.aichi.jp","shitara.aichi.jp","tahara.aichi.jp","takahama.aichi.jp","tobishima.aichi.jp","toei.aichi.jp","togo.aichi.jp","tokai.aichi.jp","tokoname.aichi.jp","toyoake.aichi.jp","toyohashi.aichi.jp","toyokawa.aichi.jp","toyone.aichi.jp","toyota.aichi.jp","tsushima.aichi.jp","yatomi.aichi.jp","akita.akita.jp","daisen.akita.jp","fujisato.akita.jp","gojome.akita.jp","hachirogata.akita.jp","happou.akita.jp","higashinaruse.akita.jp","honjo.akita.jp","honjyo.akita.jp","ikawa.akita.jp","kamikoani.akita.jp","kamioka.akita.jp","katagami.akita.jp","kazuno.akita.jp","kitaakita.akita.jp","kosaka.akita.jp","kyowa.akita.jp","misato.akita.jp","mitane.akita.jp","moriyoshi.akita.jp","nikaho.akita.jp","noshiro.akita.jp","odate.akita.jp","oga.akita.jp","ogata.akita.jp","semboku.akita.jp","yokote.akita.jp","yurihonjo.akita.jp","aomori.aomori.jp","gonohe.aomori.jp","hachinohe.aomori.jp","hashikami.aomori.jp","hiranai.aomori.jp","hirosaki.aomori.jp","itayanagi.aomori.jp","kuroishi.aomori.jp","misawa.aomori.jp","mutsu.aomori.jp","nakadomari.aomori.jp","noheji.aomori.jp","oirase.aomori.jp","owani.aomori.jp","rokunohe.aomori.jp","sannohe.aomori.jp","shichinohe.aomori.jp","shingo.aomori.jp","takko.aomori.jp","towada.aomori.jp","tsugaru.aomori.jp","tsuruta.aomori.jp","abiko.chiba.jp","asahi.chiba.jp","chonan.chiba.jp","chosei.chiba.jp","choshi.chiba.jp","chuo.chiba.jp","funabashi.chiba.jp","futtsu.chiba.jp","hanamigawa.chiba.jp","ichihara.chiba.jp","ichikawa.chiba.jp","ichinomiya.chiba.jp","inzai.chiba.jp","isumi.chiba.jp","kamagaya.chiba.jp","kamogawa.chiba.jp","kashiwa.chiba.jp","katori.chiba.jp","katsuura.chiba.jp","kimitsu.chiba.jp","kisarazu.chiba.jp","kozaki.chiba.jp","kujukuri.chiba.jp","kyonan.chiba.jp","matsudo.chiba.jp","midori.chiba.jp","mihama.chiba.jp","minamiboso.chiba.jp","mobara.chiba.jp","mutsuzawa.chiba.jp","nagara.chiba.jp","nagareyama.chiba.jp","narashino.chiba.jp","narita.chiba.jp","noda.chiba.jp","oamishirasato.chiba.jp","omigawa.chiba.jp","onjuku.chiba.jp","otaki.chiba.jp","sakae.chiba.jp","sakura.chiba.jp","shimofusa.chiba.jp","shirako.chiba.jp","shiroi.chiba.jp","shisui.chiba.jp","sodegaura.chiba.jp","sosa.chiba.jp","tako.chiba.jp","tateyama.chiba.jp","togane.chiba.jp","tohnosho.chiba.jp","tomisato.chiba.jp","urayasu.chiba.jp","yachimata.chiba.jp","yachiyo.chiba.jp","yokaichiba.chiba.jp","yokoshibahikari.chiba.jp","yotsukaido.chiba.jp","ainan.ehime.jp","honai.ehime.jp","ikata.ehime.jp","imabari.ehime.jp","iyo.ehime.jp","kamijima.ehime.jp","kihoku.ehime.jp","kumakogen.ehime.jp","masaki.ehime.jp","matsuno.ehime.jp","matsuyama.ehime.jp","namikata.ehime.jp","niihama.ehime.jp","ozu.ehime.jp","saijo.ehime.jp","seiyo.ehime.jp","shikokuchuo.ehime.jp","tobe.ehime.jp","toon.ehime.jp","uchiko.ehime.jp","uwajima.ehime.jp","yawatahama.ehime.jp","echizen.fukui.jp","eiheiji.fukui.jp","fukui.fukui.jp","ikeda.fukui.jp","katsuyama.fukui.jp","mihama.fukui.jp","minamiechizen.fukui.jp","obama.fukui.jp","ohi.fukui.jp","ono.fukui.jp","sabae.fukui.jp","sakai.fukui.jp","takahama.fukui.jp","tsuruga.fukui.jp","wakasa.fukui.jp","ashiya.fukuoka.jp","buzen.fukuoka.jp","chikugo.fukuoka.jp","chikuho.fukuoka.jp","chikujo.fukuoka.jp","chikushino.fukuoka.jp","chikuzen.fukuoka.jp","chuo.fukuoka.jp","dazaifu.fukuoka.jp","fukuchi.fukuoka.jp","hakata.fukuoka.jp","higashi.fukuoka.jp","hirokawa.fukuoka.jp","hisayama.fukuoka.jp","iizuka.fukuoka.jp","inatsuki.fukuoka.jp","kaho.fukuoka.jp","kasuga.fukuoka.jp","kasuya.fukuoka.jp","kawara.fukuoka.jp","keisen.fukuoka.jp","koga.fukuoka.jp","kurate.fukuoka.jp","kurogi.fukuoka.jp","kurume.fukuoka.jp","minami.fukuoka.jp","miyako.fukuoka.jp","miyama.fukuoka.jp","miyawaka.fukuoka.jp","mizumaki.fukuoka.jp","munakata.fukuoka.jp","nakagawa.fukuoka.jp","nakama.fukuoka.jp","nishi.fukuoka.jp","nogata.fukuoka.jp","ogori.fukuoka.jp","okagaki.fukuoka.jp","okawa.fukuoka.jp","oki.fukuoka.jp","omuta.fukuoka.jp","onga.fukuoka.jp","onojo.fukuoka.jp","oto.fukuoka.jp","saigawa.fukuoka.jp","sasaguri.fukuoka.jp","shingu.fukuoka.jp","shinyoshitomi.fukuoka.jp","shonai.fukuoka.jp","soeda.fukuoka.jp","sue.fukuoka.jp","tachiarai.fukuoka.jp","tagawa.fukuoka.jp","takata.fukuoka.jp","toho.fukuoka.jp","toyotsu.fukuoka.jp","tsuiki.fukuoka.jp","ukiha.fukuoka.jp","umi.fukuoka.jp","usui.fukuoka.jp","yamada.fukuoka.jp","yame.fukuoka.jp","yanagawa.fukuoka.jp","yukuhashi.fukuoka.jp","aizubange.fukushima.jp","aizumisato.fukushima.jp","aizuwakamatsu.fukushima.jp","asakawa.fukushima.jp","bandai.fukushima.jp","date.fukushima.jp","fukushima.fukushima.jp","furudono.fukushima.jp","futaba.fukushima.jp","hanawa.fukushima.jp","higashi.fukushima.jp","hirata.fukushima.jp","hirono.fukushima.jp","iitate.fukushima.jp","inawashiro.fukushima.jp","ishikawa.fukushima.jp","iwaki.fukushima.jp","izumizaki.fukushima.jp","kagamiishi.fukushima.jp","kaneyama.fukushima.jp","kawamata.fukushima.jp","kitakata.fukushima.jp","kitashiobara.fukushima.jp","koori.fukushima.jp","koriyama.fukushima.jp","kunimi.fukushima.jp","miharu.fukushima.jp","mishima.fukushima.jp","namie.fukushima.jp","nango.fukushima.jp","nishiaizu.fukushima.jp","nishigo.fukushima.jp","okuma.fukushima.jp","omotego.fukushima.jp","ono.fukushima.jp","otama.fukushima.jp","samegawa.fukushima.jp","shimogo.fukushima.jp","shirakawa.fukushima.jp","showa.fukushima.jp","soma.fukushima.jp","sukagawa.fukushima.jp","taishin.fukushima.jp","tamakawa.fukushima.jp","tanagura.fukushima.jp","tenei.fukushima.jp","yabuki.fukushima.jp","yamato.fukushima.jp","yamatsuri.fukushima.jp","yanaizu.fukushima.jp","yugawa.fukushima.jp","anpachi.gifu.jp","ena.gifu.jp","gifu.gifu.jp","ginan.gifu.jp","godo.gifu.jp","gujo.gifu.jp","hashima.gifu.jp","hichiso.gifu.jp","hida.gifu.jp","higashishirakawa.gifu.jp","ibigawa.gifu.jp","ikeda.gifu.jp","kakamigahara.gifu.jp","kani.gifu.jp","kasahara.gifu.jp","kasamatsu.gifu.jp","kawaue.gifu.jp","kitagata.gifu.jp","mino.gifu.jp","minokamo.gifu.jp","mitake.gifu.jp","mizunami.gifu.jp","motosu.gifu.jp","nakatsugawa.gifu.jp","ogaki.gifu.jp","sakahogi.gifu.jp","seki.gifu.jp","sekigahara.gifu.jp","shirakawa.gifu.jp","tajimi.gifu.jp","takayama.gifu.jp","tarui.gifu.jp","toki.gifu.jp","tomika.gifu.jp","wanouchi.gifu.jp","yamagata.gifu.jp","yaotsu.gifu.jp","yoro.gifu.jp","annaka.gunma.jp","chiyoda.gunma.jp","fujioka.gunma.jp","higashiagatsuma.gunma.jp","isesaki.gunma.jp","itakura.gunma.jp","kanna.gunma.jp","kanra.gunma.jp","katashina.gunma.jp","kawaba.gunma.jp","kiryu.gunma.jp","kusatsu.gunma.jp","maebashi.gunma.jp","meiwa.gunma.jp","midori.gunma.jp","minakami.gunma.jp","naganohara.gunma.jp","nakanojo.gunma.jp","nanmoku.gunma.jp","numata.gunma.jp","oizumi.gunma.jp","ora.gunma.jp","ota.gunma.jp","shibukawa.gunma.jp","shimonita.gunma.jp","shinto.gunma.jp","showa.gunma.jp","takasaki.gunma.jp","takayama.gunma.jp","tamamura.gunma.jp","tatebayashi.gunma.jp","tomioka.gunma.jp","tsukiyono.gunma.jp","tsumagoi.gunma.jp","ueno.gunma.jp","yoshioka.gunma.jp","asaminami.hiroshima.jp","daiwa.hiroshima.jp","etajima.hiroshima.jp","fuchu.hiroshima.jp","fukuyama.hiroshima.jp","hatsukaichi.hiroshima.jp","higashihiroshima.hiroshima.jp","hongo.hiroshima.jp","jinsekikogen.hiroshima.jp","kaita.hiroshima.jp","kui.hiroshima.jp","kumano.hiroshima.jp","kure.hiroshima.jp","mihara.hiroshima.jp","miyoshi.hiroshima.jp","naka.hiroshima.jp","onomichi.hiroshima.jp","osakikamijima.hiroshima.jp","otake.hiroshima.jp","saka.hiroshima.jp","sera.hiroshima.jp","seranishi.hiroshima.jp","shinichi.hiroshima.jp","shobara.hiroshima.jp","takehara.hiroshima.jp","abashiri.hokkaido.jp","abira.hokkaido.jp","aibetsu.hokkaido.jp","akabira.hokkaido.jp","akkeshi.hokkaido.jp","asahikawa.hokkaido.jp","ashibetsu.hokkaido.jp","ashoro.hokkaido.jp","assabu.hokkaido.jp","atsuma.hokkaido.jp","bibai.hokkaido.jp","biei.hokkaido.jp","bifuka.hokkaido.jp","bihoro.hokkaido.jp","biratori.hokkaido.jp","chippubetsu.hokkaido.jp","chitose.hokkaido.jp","date.hokkaido.jp","ebetsu.hokkaido.jp","embetsu.hokkaido.jp","eniwa.hokkaido.jp","erimo.hokkaido.jp","esan.hokkaido.jp","esashi.hokkaido.jp","fukagawa.hokkaido.jp","fukushima.hokkaido.jp","furano.hokkaido.jp","furubira.hokkaido.jp","haboro.hokkaido.jp","hakodate.hokkaido.jp","hamatonbetsu.hokkaido.jp","hidaka.hokkaido.jp","higashikagura.hokkaido.jp","higashikawa.hokkaido.jp","hiroo.hokkaido.jp","hokuryu.hokkaido.jp","hokuto.hokkaido.jp","honbetsu.hokkaido.jp","horokanai.hokkaido.jp","horonobe.hokkaido.jp","ikeda.hokkaido.jp","imakane.hokkaido.jp","ishikari.hokkaido.jp","iwamizawa.hokkaido.jp","iwanai.hokkaido.jp","kamifurano.hokkaido.jp","kamikawa.hokkaido.jp","kamishihoro.hokkaido.jp","kamisunagawa.hokkaido.jp","kamoenai.hokkaido.jp","kayabe.hokkaido.jp","kembuchi.hokkaido.jp","kikonai.hokkaido.jp","kimobetsu.hokkaido.jp","kitahiroshima.hokkaido.jp","kitami.hokkaido.jp","kiyosato.hokkaido.jp","koshimizu.hokkaido.jp","kunneppu.hokkaido.jp","kuriyama.hokkaido.jp","kuromatsunai.hokkaido.jp","kushiro.hokkaido.jp","kutchan.hokkaido.jp","kyowa.hokkaido.jp","mashike.hokkaido.jp","matsumae.hokkaido.jp","mikasa.hokkaido.jp","minamifurano.hokkaido.jp","mombetsu.hokkaido.jp","moseushi.hokkaido.jp","mukawa.hokkaido.jp","muroran.hokkaido.jp","naie.hokkaido.jp","nakagawa.hokkaido.jp","nakasatsunai.hokkaido.jp","nakatombetsu.hokkaido.jp","nanae.hokkaido.jp","nanporo.hokkaido.jp","nayoro.hokkaido.jp","nemuro.hokkaido.jp","niikappu.hokkaido.jp","niki.hokkaido.jp","nishiokoppe.hokkaido.jp","noboribetsu.hokkaido.jp","numata.hokkaido.jp","obihiro.hokkaido.jp","obira.hokkaido.jp","oketo.hokkaido.jp","okoppe.hokkaido.jp","otaru.hokkaido.jp","otobe.hokkaido.jp","otofuke.hokkaido.jp","otoineppu.hokkaido.jp","oumu.hokkaido.jp","ozora.hokkaido.jp","pippu.hokkaido.jp","rankoshi.hokkaido.jp","rebun.hokkaido.jp","rikubetsu.hokkaido.jp","rishiri.hokkaido.jp","rishirifuji.hokkaido.jp","saroma.hokkaido.jp","sarufutsu.hokkaido.jp","shakotan.hokkaido.jp","shari.hokkaido.jp","shibecha.hokkaido.jp","shibetsu.hokkaido.jp","shikabe.hokkaido.jp","shikaoi.hokkaido.jp","shimamaki.hokkaido.jp","shimizu.hokkaido.jp","shimokawa.hokkaido.jp","shinshinotsu.hokkaido.jp","shintoku.hokkaido.jp","shiranuka.hokkaido.jp","shiraoi.hokkaido.jp","shiriuchi.hokkaido.jp","sobetsu.hokkaido.jp","sunagawa.hokkaido.jp","taiki.hokkaido.jp","takasu.hokkaido.jp","takikawa.hokkaido.jp","takinoue.hokkaido.jp","teshikaga.hokkaido.jp","tobetsu.hokkaido.jp","tohma.hokkaido.jp","tomakomai.hokkaido.jp","tomari.hokkaido.jp","toya.hokkaido.jp","toyako.hokkaido.jp","toyotomi.hokkaido.jp","toyoura.hokkaido.jp","tsubetsu.hokkaido.jp","tsukigata.hokkaido.jp","urakawa.hokkaido.jp","urausu.hokkaido.jp","uryu.hokkaido.jp","utashinai.hokkaido.jp","wakkanai.hokkaido.jp","wassamu.hokkaido.jp","yakumo.hokkaido.jp","yoichi.hokkaido.jp","aioi.hyogo.jp","akashi.hyogo.jp","ako.hyogo.jp","amagasaki.hyogo.jp","aogaki.hyogo.jp","asago.hyogo.jp","ashiya.hyogo.jp","awaji.hyogo.jp","fukusaki.hyogo.jp","goshiki.hyogo.jp","harima.hyogo.jp","himeji.hyogo.jp","ichikawa.hyogo.jp","inagawa.hyogo.jp","itami.hyogo.jp","kakogawa.hyogo.jp","kamigori.hyogo.jp","kamikawa.hyogo.jp","kasai.hyogo.jp","kasuga.hyogo.jp","kawanishi.hyogo.jp","miki.hyogo.jp","minamiawaji.hyogo.jp","nishinomiya.hyogo.jp","nishiwaki.hyogo.jp","ono.hyogo.jp","sanda.hyogo.jp","sannan.hyogo.jp","sasayama.hyogo.jp","sayo.hyogo.jp","shingu.hyogo.jp","shinonsen.hyogo.jp","shiso.hyogo.jp","sumoto.hyogo.jp","taishi.hyogo.jp","taka.hyogo.jp","takarazuka.hyogo.jp","takasago.hyogo.jp","takino.hyogo.jp","tamba.hyogo.jp","tatsuno.hyogo.jp","toyooka.hyogo.jp","yabu.hyogo.jp","yashiro.hyogo.jp","yoka.hyogo.jp","yokawa.hyogo.jp","ami.ibaraki.jp","asahi.ibaraki.jp","bando.ibaraki.jp","chikusei.ibaraki.jp","daigo.ibaraki.jp","fujishiro.ibaraki.jp","hitachi.ibaraki.jp","hitachinaka.ibaraki.jp","hitachiomiya.ibaraki.jp","hitachiota.ibaraki.jp","ibaraki.ibaraki.jp","ina.ibaraki.jp","inashiki.ibaraki.jp","itako.ibaraki.jp","iwama.ibaraki.jp","joso.ibaraki.jp","kamisu.ibaraki.jp","kasama.ibaraki.jp","kashima.ibaraki.jp","kasumigaura.ibaraki.jp","koga.ibaraki.jp","miho.ibaraki.jp","mito.ibaraki.jp","moriya.ibaraki.jp","naka.ibaraki.jp","namegata.ibaraki.jp","oarai.ibaraki.jp","ogawa.ibaraki.jp","omitama.ibaraki.jp","ryugasaki.ibaraki.jp","sakai.ibaraki.jp","sakuragawa.ibaraki.jp","shimodate.ibaraki.jp","shimotsuma.ibaraki.jp","shirosato.ibaraki.jp","sowa.ibaraki.jp","suifu.ibaraki.jp","takahagi.ibaraki.jp","tamatsukuri.ibaraki.jp","tokai.ibaraki.jp","tomobe.ibaraki.jp","tone.ibaraki.jp","toride.ibaraki.jp","tsuchiura.ibaraki.jp","tsukuba.ibaraki.jp","uchihara.ibaraki.jp","ushiku.ibaraki.jp","yachiyo.ibaraki.jp","yamagata.ibaraki.jp","yawara.ibaraki.jp","yuki.ibaraki.jp","anamizu.ishikawa.jp","hakui.ishikawa.jp","hakusan.ishikawa.jp","kaga.ishikawa.jp","kahoku.ishikawa.jp","kanazawa.ishikawa.jp","kawakita.ishikawa.jp","komatsu.ishikawa.jp","nakanoto.ishikawa.jp","nanao.ishikawa.jp","nomi.ishikawa.jp","nonoichi.ishikawa.jp","noto.ishikawa.jp","shika.ishikawa.jp","suzu.ishikawa.jp","tsubata.ishikawa.jp","tsurugi.ishikawa.jp","uchinada.ishikawa.jp","wajima.ishikawa.jp","fudai.iwate.jp","fujisawa.iwate.jp","hanamaki.iwate.jp","hiraizumi.iwate.jp","hirono.iwate.jp","ichinohe.iwate.jp","ichinoseki.iwate.jp","iwaizumi.iwate.jp","iwate.iwate.jp","joboji.iwate.jp","kamaishi.iwate.jp","kanegasaki.iwate.jp","karumai.iwate.jp","kawai.iwate.jp","kitakami.iwate.jp","kuji.iwate.jp","kunohe.iwate.jp","kuzumaki.iwate.jp","miyako.iwate.jp","mizusawa.iwate.jp","morioka.iwate.jp","ninohe.iwate.jp","noda.iwate.jp","ofunato.iwate.jp","oshu.iwate.jp","otsuchi.iwate.jp","rikuzentakata.iwate.jp","shiwa.iwate.jp","shizukuishi.iwate.jp","sumita.iwate.jp","tanohata.iwate.jp","tono.iwate.jp","yahaba.iwate.jp","yamada.iwate.jp","ayagawa.kagawa.jp","higashikagawa.kagawa.jp","kanonji.kagawa.jp","kotohira.kagawa.jp","manno.kagawa.jp","marugame.kagawa.jp","mitoyo.kagawa.jp","naoshima.kagawa.jp","sanuki.kagawa.jp","tadotsu.kagawa.jp","takamatsu.kagawa.jp","tonosho.kagawa.jp","uchinomi.kagawa.jp","utazu.kagawa.jp","zentsuji.kagawa.jp","akune.kagoshima.jp","amami.kagoshima.jp","hioki.kagoshima.jp","isa.kagoshima.jp","isen.kagoshima.jp","izumi.kagoshima.jp","kagoshima.kagoshima.jp","kanoya.kagoshima.jp","kawanabe.kagoshima.jp","kinko.kagoshima.jp","kouyama.kagoshima.jp","makurazaki.kagoshima.jp","matsumoto.kagoshima.jp","minamitane.kagoshima.jp","nakatane.kagoshima.jp","nishinoomote.kagoshima.jp","satsumasendai.kagoshima.jp","soo.kagoshima.jp","tarumizu.kagoshima.jp","yusui.kagoshima.jp","aikawa.kanagawa.jp","atsugi.kanagawa.jp","ayase.kanagawa.jp","chigasaki.kanagawa.jp","ebina.kanagawa.jp","fujisawa.kanagawa.jp","hadano.kanagawa.jp","hakone.kanagawa.jp","hiratsuka.kanagawa.jp","isehara.kanagawa.jp","kaisei.kanagawa.jp","kamakura.kanagawa.jp","kiyokawa.kanagawa.jp","matsuda.kanagawa.jp","minamiashigara.kanagawa.jp","miura.kanagawa.jp","nakai.kanagawa.jp","ninomiya.kanagawa.jp","odawara.kanagawa.jp","oi.kanagawa.jp","oiso.kanagawa.jp","sagamihara.kanagawa.jp","samukawa.kanagawa.jp","tsukui.kanagawa.jp","yamakita.kanagawa.jp","yamato.kanagawa.jp","yokosuka.kanagawa.jp","yugawara.kanagawa.jp","zama.kanagawa.jp","zushi.kanagawa.jp","aki.kochi.jp","geisei.kochi.jp","hidaka.kochi.jp","higashitsuno.kochi.jp","ino.kochi.jp","kagami.kochi.jp","kami.kochi.jp","kitagawa.kochi.jp","kochi.kochi.jp","mihara.kochi.jp","motoyama.kochi.jp","muroto.kochi.jp","nahari.kochi.jp","nakamura.kochi.jp","nankoku.kochi.jp","nishitosa.kochi.jp","niyodogawa.kochi.jp","ochi.kochi.jp","okawa.kochi.jp","otoyo.kochi.jp","otsuki.kochi.jp","sakawa.kochi.jp","sukumo.kochi.jp","susaki.kochi.jp","tosa.kochi.jp","tosashimizu.kochi.jp","toyo.kochi.jp","tsuno.kochi.jp","umaji.kochi.jp","yasuda.kochi.jp","yusuhara.kochi.jp","amakusa.kumamoto.jp","arao.kumamoto.jp","aso.kumamoto.jp","choyo.kumamoto.jp","gyokuto.kumamoto.jp","kamiamakusa.kumamoto.jp","kikuchi.kumamoto.jp","kumamoto.kumamoto.jp","mashiki.kumamoto.jp","mifune.kumamoto.jp","minamata.kumamoto.jp","minamioguni.kumamoto.jp","nagasu.kumamoto.jp","nishihara.kumamoto.jp","oguni.kumamoto.jp","ozu.kumamoto.jp","sumoto.kumamoto.jp","takamori.kumamoto.jp","uki.kumamoto.jp","uto.kumamoto.jp","yamaga.kumamoto.jp","yamato.kumamoto.jp","yatsushiro.kumamoto.jp","ayabe.kyoto.jp","fukuchiyama.kyoto.jp","higashiyama.kyoto.jp","ide.kyoto.jp","ine.kyoto.jp","joyo.kyoto.jp","kameoka.kyoto.jp","kamo.kyoto.jp","kita.kyoto.jp","kizu.kyoto.jp","kumiyama.kyoto.jp","kyotamba.kyoto.jp","kyotanabe.kyoto.jp","kyotango.kyoto.jp","maizuru.kyoto.jp","minami.kyoto.jp","minamiyamashiro.kyoto.jp","miyazu.kyoto.jp","muko.kyoto.jp","nagaokakyo.kyoto.jp","nakagyo.kyoto.jp","nantan.kyoto.jp","oyamazaki.kyoto.jp","sakyo.kyoto.jp","seika.kyoto.jp","tanabe.kyoto.jp","uji.kyoto.jp","ujitawara.kyoto.jp","wazuka.kyoto.jp","yamashina.kyoto.jp","yawata.kyoto.jp","asahi.mie.jp","inabe.mie.jp","ise.mie.jp","kameyama.mie.jp","kawagoe.mie.jp","kiho.mie.jp","kisosaki.mie.jp","kiwa.mie.jp","komono.mie.jp","kumano.mie.jp","kuwana.mie.jp","matsusaka.mie.jp","meiwa.mie.jp","mihama.mie.jp","minamiise.mie.jp","misugi.mie.jp","miyama.mie.jp","nabari.mie.jp","shima.mie.jp","suzuka.mie.jp","tado.mie.jp","taiki.mie.jp","taki.mie.jp","tamaki.mie.jp","toba.mie.jp","tsu.mie.jp","udono.mie.jp","ureshino.mie.jp","watarai.mie.jp","yokkaichi.mie.jp","furukawa.miyagi.jp","higashimatsushima.miyagi.jp","ishinomaki.miyagi.jp","iwanuma.miyagi.jp","kakuda.miyagi.jp","kami.miyagi.jp","kawasaki.miyagi.jp","marumori.miyagi.jp","matsushima.miyagi.jp","minamisanriku.miyagi.jp","misato.miyagi.jp","murata.miyagi.jp","natori.miyagi.jp","ogawara.miyagi.jp","ohira.miyagi.jp","onagawa.miyagi.jp","osaki.miyagi.jp","rifu.miyagi.jp","semine.miyagi.jp","shibata.miyagi.jp","shichikashuku.miyagi.jp","shikama.miyagi.jp","shiogama.miyagi.jp","shiroishi.miyagi.jp","tagajo.miyagi.jp","taiwa.miyagi.jp","tome.miyagi.jp","tomiya.miyagi.jp","wakuya.miyagi.jp","watari.miyagi.jp","yamamoto.miyagi.jp","zao.miyagi.jp","aya.miyazaki.jp","ebino.miyazaki.jp","gokase.miyazaki.jp","hyuga.miyazaki.jp","kadogawa.miyazaki.jp","kawaminami.miyazaki.jp","kijo.miyazaki.jp","kitagawa.miyazaki.jp","kitakata.miyazaki.jp","kitaura.miyazaki.jp","kobayashi.miyazaki.jp","kunitomi.miyazaki.jp","kushima.miyazaki.jp","mimata.miyazaki.jp","miyakonojo.miyazaki.jp","miyazaki.miyazaki.jp","morotsuka.miyazaki.jp","nichinan.miyazaki.jp","nishimera.miyazaki.jp","nobeoka.miyazaki.jp","saito.miyazaki.jp","shiiba.miyazaki.jp","shintomi.miyazaki.jp","takaharu.miyazaki.jp","takanabe.miyazaki.jp","takazaki.miyazaki.jp","tsuno.miyazaki.jp","achi.nagano.jp","agematsu.nagano.jp","anan.nagano.jp","aoki.nagano.jp","asahi.nagano.jp","azumino.nagano.jp","chikuhoku.nagano.jp","chikuma.nagano.jp","chino.nagano.jp","fujimi.nagano.jp","hakuba.nagano.jp","hara.nagano.jp","hiraya.nagano.jp","iida.nagano.jp","iijima.nagano.jp","iiyama.nagano.jp","iizuna.nagano.jp","ikeda.nagano.jp","ikusaka.nagano.jp","ina.nagano.jp","karuizawa.nagano.jp","kawakami.nagano.jp","kiso.nagano.jp","kisofukushima.nagano.jp","kitaaiki.nagano.jp","komagane.nagano.jp","komoro.nagano.jp","matsukawa.nagano.jp","matsumoto.nagano.jp","miasa.nagano.jp","minamiaiki.nagano.jp","minamimaki.nagano.jp","minamiminowa.nagano.jp","minowa.nagano.jp","miyada.nagano.jp","miyota.nagano.jp","mochizuki.nagano.jp","nagano.nagano.jp","nagawa.nagano.jp","nagiso.nagano.jp","nakagawa.nagano.jp","nakano.nagano.jp","nozawaonsen.nagano.jp","obuse.nagano.jp","ogawa.nagano.jp","okaya.nagano.jp","omachi.nagano.jp","omi.nagano.jp","ookuwa.nagano.jp","ooshika.nagano.jp","otaki.nagano.jp","otari.nagano.jp","sakae.nagano.jp","sakaki.nagano.jp","saku.nagano.jp","sakuho.nagano.jp","shimosuwa.nagano.jp","shinanomachi.nagano.jp","shiojiri.nagano.jp","suwa.nagano.jp","suzaka.nagano.jp","takagi.nagano.jp","takamori.nagano.jp","takayama.nagano.jp","tateshina.nagano.jp","tatsuno.nagano.jp","togakushi.nagano.jp","togura.nagano.jp","tomi.nagano.jp","ueda.nagano.jp","wada.nagano.jp","yamagata.nagano.jp","yamanouchi.nagano.jp","yasaka.nagano.jp","yasuoka.nagano.jp","chijiwa.nagasaki.jp","futsu.nagasaki.jp","goto.nagasaki.jp","hasami.nagasaki.jp","hirado.nagasaki.jp","iki.nagasaki.jp","isahaya.nagasaki.jp","kawatana.nagasaki.jp","kuchinotsu.nagasaki.jp","matsuura.nagasaki.jp","nagasaki.nagasaki.jp","obama.nagasaki.jp","omura.nagasaki.jp","oseto.nagasaki.jp","saikai.nagasaki.jp","sasebo.nagasaki.jp","seihi.nagasaki.jp","shimabara.nagasaki.jp","shinkamigoto.nagasaki.jp","togitsu.nagasaki.jp","tsushima.nagasaki.jp","unzen.nagasaki.jp","ando.nara.jp","gose.nara.jp","heguri.nara.jp","higashiyoshino.nara.jp","ikaruga.nara.jp","ikoma.nara.jp","kamikitayama.nara.jp","kanmaki.nara.jp","kashiba.nara.jp","kashihara.nara.jp","katsuragi.nara.jp","kawai.nara.jp","kawakami.nara.jp","kawanishi.nara.jp","koryo.nara.jp","kurotaki.nara.jp","mitsue.nara.jp","miyake.nara.jp","nara.nara.jp","nosegawa.nara.jp","oji.nara.jp","ouda.nara.jp","oyodo.nara.jp","sakurai.nara.jp","sango.nara.jp","shimoichi.nara.jp","shimokitayama.nara.jp","shinjo.nara.jp","soni.nara.jp","takatori.nara.jp","tawaramoto.nara.jp","tenkawa.nara.jp","tenri.nara.jp","uda.nara.jp","yamatokoriyama.nara.jp","yamatotakada.nara.jp","yamazoe.nara.jp","yoshino.nara.jp","aga.niigata.jp","agano.niigata.jp","gosen.niigata.jp","itoigawa.niigata.jp","izumozaki.niigata.jp","joetsu.niigata.jp","kamo.niigata.jp","kariwa.niigata.jp","kashiwazaki.niigata.jp","minamiuonuma.niigata.jp","mitsuke.niigata.jp","muika.niigata.jp","murakami.niigata.jp","myoko.niigata.jp","nagaoka.niigata.jp","niigata.niigata.jp","ojiya.niigata.jp","omi.niigata.jp","sado.niigata.jp","sanjo.niigata.jp","seiro.niigata.jp","seirou.niigata.jp","sekikawa.niigata.jp","shibata.niigata.jp","tagami.niigata.jp","tainai.niigata.jp","tochio.niigata.jp","tokamachi.niigata.jp","tsubame.niigata.jp","tsunan.niigata.jp","uonuma.niigata.jp","yahiko.niigata.jp","yoita.niigata.jp","yuzawa.niigata.jp","beppu.oita.jp","bungoono.oita.jp","bungotakada.oita.jp","hasama.oita.jp","hiji.oita.jp","himeshima.oita.jp","hita.oita.jp","kamitsue.oita.jp","kokonoe.oita.jp","kuju.oita.jp","kunisaki.oita.jp","kusu.oita.jp","oita.oita.jp","saiki.oita.jp","taketa.oita.jp","tsukumi.oita.jp","usa.oita.jp","usuki.oita.jp","yufu.oita.jp","akaiwa.okayama.jp","asakuchi.okayama.jp","bizen.okayama.jp","hayashima.okayama.jp","ibara.okayama.jp","kagamino.okayama.jp","kasaoka.okayama.jp","kibichuo.okayama.jp","kumenan.okayama.jp","kurashiki.okayama.jp","maniwa.okayama.jp","misaki.okayama.jp","nagi.okayama.jp","niimi.okayama.jp","nishiawakura.okayama.jp","okayama.okayama.jp","satosho.okayama.jp","setouchi.okayama.jp","shinjo.okayama.jp","shoo.okayama.jp","soja.okayama.jp","takahashi.okayama.jp","tamano.okayama.jp","tsuyama.okayama.jp","wake.okayama.jp","yakage.okayama.jp","aguni.okinawa.jp","ginowan.okinawa.jp","ginoza.okinawa.jp","gushikami.okinawa.jp","haebaru.okinawa.jp","higashi.okinawa.jp","hirara.okinawa.jp","iheya.okinawa.jp","ishigaki.okinawa.jp","ishikawa.okinawa.jp","itoman.okinawa.jp","izena.okinawa.jp","kadena.okinawa.jp","kin.okinawa.jp","kitadaito.okinawa.jp","kitanakagusuku.okinawa.jp","kumejima.okinawa.jp","kunigami.okinawa.jp","minamidaito.okinawa.jp","motobu.okinawa.jp","nago.okinawa.jp","naha.okinawa.jp","nakagusuku.okinawa.jp","nakijin.okinawa.jp","nanjo.okinawa.jp","nishihara.okinawa.jp","ogimi.okinawa.jp","okinawa.okinawa.jp","onna.okinawa.jp","shimoji.okinawa.jp","taketomi.okinawa.jp","tarama.okinawa.jp","tokashiki.okinawa.jp","tomigusuku.okinawa.jp","tonaki.okinawa.jp","urasoe.okinawa.jp","uruma.okinawa.jp","yaese.okinawa.jp","yomitan.okinawa.jp","yonabaru.okinawa.jp","yonaguni.okinawa.jp","zamami.okinawa.jp","abeno.osaka.jp","chihayaakasaka.osaka.jp","chuo.osaka.jp","daito.osaka.jp","fujiidera.osaka.jp","habikino.osaka.jp","hannan.osaka.jp","higashiosaka.osaka.jp","higashisumiyoshi.osaka.jp","higashiyodogawa.osaka.jp","hirakata.osaka.jp","ibaraki.osaka.jp","ikeda.osaka.jp","izumi.osaka.jp","izumiotsu.osaka.jp","izumisano.osaka.jp","kadoma.osaka.jp","kaizuka.osaka.jp","kanan.osaka.jp","kashiwara.osaka.jp","katano.osaka.jp","kawachinagano.osaka.jp","kishiwada.osaka.jp","kita.osaka.jp","kumatori.osaka.jp","matsubara.osaka.jp","minato.osaka.jp","minoh.osaka.jp","misaki.osaka.jp","moriguchi.osaka.jp","neyagawa.osaka.jp","nishi.osaka.jp","nose.osaka.jp","osakasayama.osaka.jp","sakai.osaka.jp","sayama.osaka.jp","sennan.osaka.jp","settsu.osaka.jp","shijonawate.osaka.jp","shimamoto.osaka.jp","suita.osaka.jp","tadaoka.osaka.jp","taishi.osaka.jp","tajiri.osaka.jp","takaishi.osaka.jp","takatsuki.osaka.jp","tondabayashi.osaka.jp","toyonaka.osaka.jp","toyono.osaka.jp","yao.osaka.jp","ariake.saga.jp","arita.saga.jp","fukudomi.saga.jp","genkai.saga.jp","hamatama.saga.jp","hizen.saga.jp","imari.saga.jp","kamimine.saga.jp","kanzaki.saga.jp","karatsu.saga.jp","kashima.saga.jp","kitagata.saga.jp","kitahata.saga.jp","kiyama.saga.jp","kouhoku.saga.jp","kyuragi.saga.jp","nishiarita.saga.jp","ogi.saga.jp","omachi.saga.jp","ouchi.saga.jp","saga.saga.jp","shiroishi.saga.jp","taku.saga.jp","tara.saga.jp","tosu.saga.jp","yoshinogari.saga.jp","arakawa.saitama.jp","asaka.saitama.jp","chichibu.saitama.jp","fujimi.saitama.jp","fujimino.saitama.jp","fukaya.saitama.jp","hanno.saitama.jp","hanyu.saitama.jp","hasuda.saitama.jp","hatogaya.saitama.jp","hatoyama.saitama.jp","hidaka.saitama.jp","higashichichibu.saitama.jp","higashimatsuyama.saitama.jp","honjo.saitama.jp","ina.saitama.jp","iruma.saitama.jp","iwatsuki.saitama.jp","kamiizumi.saitama.jp","kamikawa.saitama.jp","kamisato.saitama.jp","kasukabe.saitama.jp","kawagoe.saitama.jp","kawaguchi.saitama.jp","kawajima.saitama.jp","kazo.saitama.jp","kitamoto.saitama.jp","koshigaya.saitama.jp","kounosu.saitama.jp","kuki.saitama.jp","kumagaya.saitama.jp","matsubushi.saitama.jp","minano.saitama.jp","misato.saitama.jp","miyashiro.saitama.jp","miyoshi.saitama.jp","moroyama.saitama.jp","nagatoro.saitama.jp","namegawa.saitama.jp","niiza.saitama.jp","ogano.saitama.jp","ogawa.saitama.jp","ogose.saitama.jp","okegawa.saitama.jp","omiya.saitama.jp","otaki.saitama.jp","ranzan.saitama.jp","ryokami.saitama.jp","saitama.saitama.jp","sakado.saitama.jp","satte.saitama.jp","sayama.saitama.jp","shiki.saitama.jp","shiraoka.saitama.jp","soka.saitama.jp","sugito.saitama.jp","toda.saitama.jp","tokigawa.saitama.jp","tokorozawa.saitama.jp","tsurugashima.saitama.jp","urawa.saitama.jp","warabi.saitama.jp","yashio.saitama.jp","yokoze.saitama.jp","yono.saitama.jp","yorii.saitama.jp","yoshida.saitama.jp","yoshikawa.saitama.jp","yoshimi.saitama.jp","aisho.shiga.jp","gamo.shiga.jp","higashiomi.shiga.jp","hikone.shiga.jp","koka.shiga.jp","konan.shiga.jp","kosei.shiga.jp","koto.shiga.jp","kusatsu.shiga.jp","maibara.shiga.jp","moriyama.shiga.jp","nagahama.shiga.jp","nishiazai.shiga.jp","notogawa.shiga.jp","omihachiman.shiga.jp","otsu.shiga.jp","ritto.shiga.jp","ryuoh.shiga.jp","takashima.shiga.jp","takatsuki.shiga.jp","torahime.shiga.jp","toyosato.shiga.jp","yasu.shiga.jp","akagi.shimane.jp","ama.shimane.jp","gotsu.shimane.jp","hamada.shimane.jp","higashiizumo.shimane.jp","hikawa.shimane.jp","hikimi.shimane.jp","izumo.shimane.jp","kakinoki.shimane.jp","masuda.shimane.jp","matsue.shimane.jp","misato.shimane.jp","nishinoshima.shimane.jp","ohda.shimane.jp","okinoshima.shimane.jp","okuizumo.shimane.jp","shimane.shimane.jp","tamayu.shimane.jp","tsuwano.shimane.jp","unnan.shimane.jp","yakumo.shimane.jp","yasugi.shimane.jp","yatsuka.shimane.jp","arai.shizuoka.jp","atami.shizuoka.jp","fuji.shizuoka.jp","fujieda.shizuoka.jp","fujikawa.shizuoka.jp","fujinomiya.shizuoka.jp","fukuroi.shizuoka.jp","gotemba.shizuoka.jp","haibara.shizuoka.jp","hamamatsu.shizuoka.jp","higashiizu.shizuoka.jp","ito.shizuoka.jp","iwata.shizuoka.jp","izu.shizuoka.jp","izunokuni.shizuoka.jp","kakegawa.shizuoka.jp","kannami.shizuoka.jp","kawanehon.shizuoka.jp","kawazu.shizuoka.jp","kikugawa.shizuoka.jp","kosai.shizuoka.jp","makinohara.shizuoka.jp","matsuzaki.shizuoka.jp","minamiizu.shizuoka.jp","mishima.shizuoka.jp","morimachi.shizuoka.jp","nishiizu.shizuoka.jp","numazu.shizuoka.jp","omaezaki.shizuoka.jp","shimada.shizuoka.jp","shimizu.shizuoka.jp","shimoda.shizuoka.jp","shizuoka.shizuoka.jp","susono.shizuoka.jp","yaizu.shizuoka.jp","yoshida.shizuoka.jp","ashikaga.tochigi.jp","bato.tochigi.jp","haga.tochigi.jp","ichikai.tochigi.jp","iwafune.tochigi.jp","kaminokawa.tochigi.jp","kanuma.tochigi.jp","karasuyama.tochigi.jp","kuroiso.tochigi.jp","mashiko.tochigi.jp","mibu.tochigi.jp","moka.tochigi.jp","motegi.tochigi.jp","nasu.tochigi.jp","nasushiobara.tochigi.jp","nikko.tochigi.jp","nishikata.tochigi.jp","nogi.tochigi.jp","ohira.tochigi.jp","ohtawara.tochigi.jp","oyama.tochigi.jp","sakura.tochigi.jp","sano.tochigi.jp","shimotsuke.tochigi.jp","shioya.tochigi.jp","takanezawa.tochigi.jp","tochigi.tochigi.jp","tsuga.tochigi.jp","ujiie.tochigi.jp","utsunomiya.tochigi.jp","yaita.tochigi.jp","aizumi.tokushima.jp","anan.tokushima.jp","ichiba.tokushima.jp","itano.tokushima.jp","kainan.tokushima.jp","komatsushima.tokushima.jp","matsushige.tokushima.jp","mima.tokushima.jp","minami.tokushima.jp","miyoshi.tokushima.jp","mugi.tokushima.jp","nakagawa.tokushima.jp","naruto.tokushima.jp","sanagochi.tokushima.jp","shishikui.tokushima.jp","tokushima.tokushima.jp","wajiki.tokushima.jp","adachi.tokyo.jp","akiruno.tokyo.jp","akishima.tokyo.jp","aogashima.tokyo.jp","arakawa.tokyo.jp","bunkyo.tokyo.jp","chiyoda.tokyo.jp","chofu.tokyo.jp","chuo.tokyo.jp","edogawa.tokyo.jp","fuchu.tokyo.jp","fussa.tokyo.jp","hachijo.tokyo.jp","hachioji.tokyo.jp","hamura.tokyo.jp","higashikurume.tokyo.jp","higashimurayama.tokyo.jp","higashiyamato.tokyo.jp","hino.tokyo.jp","hinode.tokyo.jp","hinohara.tokyo.jp","inagi.tokyo.jp","itabashi.tokyo.jp","katsushika.tokyo.jp","kita.tokyo.jp","kiyose.tokyo.jp","kodaira.tokyo.jp","koganei.tokyo.jp","kokubunji.tokyo.jp","komae.tokyo.jp","koto.tokyo.jp","kouzushima.tokyo.jp","kunitachi.tokyo.jp","machida.tokyo.jp","meguro.tokyo.jp","minato.tokyo.jp","mitaka.tokyo.jp","mizuho.tokyo.jp","musashimurayama.tokyo.jp","musashino.tokyo.jp","nakano.tokyo.jp","nerima.tokyo.jp","ogasawara.tokyo.jp","okutama.tokyo.jp","ome.tokyo.jp","oshima.tokyo.jp","ota.tokyo.jp","setagaya.tokyo.jp","shibuya.tokyo.jp","shinagawa.tokyo.jp","shinjuku.tokyo.jp","suginami.tokyo.jp","sumida.tokyo.jp","tachikawa.tokyo.jp","taito.tokyo.jp","tama.tokyo.jp","toshima.tokyo.jp","chizu.tottori.jp","hino.tottori.jp","kawahara.tottori.jp","koge.tottori.jp","kotoura.tottori.jp","misasa.tottori.jp","nanbu.tottori.jp","nichinan.tottori.jp","sakaiminato.tottori.jp","tottori.tottori.jp","wakasa.tottori.jp","yazu.tottori.jp","yonago.tottori.jp","asahi.toyama.jp","fuchu.toyama.jp","fukumitsu.toyama.jp","funahashi.toyama.jp","himi.toyama.jp","imizu.toyama.jp","inami.toyama.jp","johana.toyama.jp","kamiichi.toyama.jp","kurobe.toyama.jp","nakaniikawa.toyama.jp","namerikawa.toyama.jp","nanto.toyama.jp","nyuzen.toyama.jp","oyabe.toyama.jp","taira.toyama.jp","takaoka.toyama.jp","tateyama.toyama.jp","toga.toyama.jp","tonami.toyama.jp","toyama.toyama.jp","unazuki.toyama.jp","uozu.toyama.jp","yamada.toyama.jp","arida.wakayama.jp","aridagawa.wakayama.jp","gobo.wakayama.jp","hashimoto.wakayama.jp","hidaka.wakayama.jp","hirogawa.wakayama.jp","inami.wakayama.jp","iwade.wakayama.jp","kainan.wakayama.jp","kamitonda.wakayama.jp","katsuragi.wakayama.jp","kimino.wakayama.jp","kinokawa.wakayama.jp","kitayama.wakayama.jp","koya.wakayama.jp","koza.wakayama.jp","kozagawa.wakayama.jp","kudoyama.wakayama.jp","kushimoto.wakayama.jp","mihama.wakayama.jp","misato.wakayama.jp","nachikatsuura.wakayama.jp","shingu.wakayama.jp","shirahama.wakayama.jp","taiji.wakayama.jp","tanabe.wakayama.jp","wakayama.wakayama.jp","yuasa.wakayama.jp","yura.wakayama.jp","asahi.yamagata.jp","funagata.yamagata.jp","higashine.yamagata.jp","iide.yamagata.jp","kahoku.yamagata.jp","kaminoyama.yamagata.jp","kaneyama.yamagata.jp","kawanishi.yamagata.jp","mamurogawa.yamagata.jp","mikawa.yamagata.jp","murayama.yamagata.jp","nagai.yamagata.jp","nakayama.yamagata.jp","nanyo.yamagata.jp","nishikawa.yamagata.jp","obanazawa.yamagata.jp","oe.yamagata.jp","oguni.yamagata.jp","ohkura.yamagata.jp","oishida.yamagata.jp","sagae.yamagata.jp","sakata.yamagata.jp","sakegawa.yamagata.jp","shinjo.yamagata.jp","shirataka.yamagata.jp","shonai.yamagata.jp","takahata.yamagata.jp","tendo.yamagata.jp","tozawa.yamagata.jp","tsuruoka.yamagata.jp","yamagata.yamagata.jp","yamanobe.yamagata.jp","yonezawa.yamagata.jp","yuza.yamagata.jp","abu.yamaguchi.jp","hagi.yamaguchi.jp","hikari.yamaguchi.jp","hofu.yamaguchi.jp","iwakuni.yamaguchi.jp","kudamatsu.yamaguchi.jp","mitou.yamaguchi.jp","nagato.yamaguchi.jp","oshima.yamaguchi.jp","shimonoseki.yamaguchi.jp","shunan.yamaguchi.jp","tabuse.yamaguchi.jp","tokuyama.yamaguchi.jp","toyota.yamaguchi.jp","ube.yamaguchi.jp","yuu.yamaguchi.jp","chuo.yamanashi.jp","doshi.yamanashi.jp","fuefuki.yamanashi.jp","fujikawa.yamanashi.jp","fujikawaguchiko.yamanashi.jp","fujiyoshida.yamanashi.jp","hayakawa.yamanashi.jp","hokuto.yamanashi.jp","ichikawamisato.yamanashi.jp","kai.yamanashi.jp","kofu.yamanashi.jp","koshu.yamanashi.jp","kosuge.yamanashi.jp","minami-alps.yamanashi.jp","minobu.yamanashi.jp","nakamichi.yamanashi.jp","nanbu.yamanashi.jp","narusawa.yamanashi.jp","nirasaki.yamanashi.jp","nishikatsura.yamanashi.jp","oshino.yamanashi.jp","otsuki.yamanashi.jp","showa.yamanashi.jp","tabayama.yamanashi.jp","tsuru.yamanashi.jp","uenohara.yamanashi.jp","yamanakako.yamanashi.jp","yamanashi.yamanashi.jp","ke","ac.ke","co.ke","go.ke","info.ke","me.ke","mobi.ke","ne.ke","or.ke","sc.ke","kg","com.kg","edu.kg","gov.kg","mil.kg","net.kg","org.kg","*.kh","ki","biz.ki","com.ki","edu.ki","gov.ki","info.ki","net.ki","org.ki","km","ass.km","com.km","edu.km","gov.km","mil.km","nom.km","org.km","prd.km","tm.km","asso.km","coop.km","gouv.km","medecin.km","notaires.km","pharmaciens.km","presse.km","veterinaire.km","kn","edu.kn","gov.kn","net.kn","org.kn","kp","com.kp","edu.kp","gov.kp","org.kp","rep.kp","tra.kp","kr","ac.kr","co.kr","es.kr","go.kr","hs.kr","kg.kr","mil.kr","ms.kr","ne.kr","or.kr","pe.kr","re.kr","sc.kr","busan.kr","chungbuk.kr","chungnam.kr","daegu.kr","daejeon.kr","gangwon.kr","gwangju.kr","gyeongbuk.kr","gyeonggi.kr","gyeongnam.kr","incheon.kr","jeju.kr","jeonbuk.kr","jeonnam.kr","seoul.kr","ulsan.kr","kw","com.kw","edu.kw","emb.kw","gov.kw","ind.kw","net.kw","org.kw","ky","com.ky","edu.ky","net.ky","org.ky","kz","com.kz","edu.kz","gov.kz","mil.kz","net.kz","org.kz","la","com.la","edu.la","gov.la","info.la","int.la","net.la","org.la","per.la","lb","com.lb","edu.lb","gov.lb","net.lb","org.lb","lc","co.lc","com.lc","edu.lc","gov.lc","net.lc","org.lc","li","lk","ac.lk","assn.lk","com.lk","edu.lk","gov.lk","grp.lk","hotel.lk","int.lk","ltd.lk","net.lk","ngo.lk","org.lk","sch.lk","soc.lk","web.lk","lr","com.lr","edu.lr","gov.lr","net.lr","org.lr","ls","ac.ls","biz.ls","co.ls","edu.ls","gov.ls","info.ls","net.ls","org.ls","sc.ls","lt","gov.lt","lu","lv","asn.lv","com.lv","conf.lv","edu.lv","gov.lv","id.lv","mil.lv","net.lv","org.lv","ly","com.ly","edu.ly","gov.ly","id.ly","med.ly","net.ly","org.ly","plc.ly","sch.ly","ma","ac.ma","co.ma","gov.ma","net.ma","org.ma","press.ma","mc","asso.mc","tm.mc","md","me","ac.me","co.me","edu.me","gov.me","its.me","net.me","org.me","priv.me","mg","co.mg","com.mg","edu.mg","gov.mg","mil.mg","nom.mg","org.mg","prd.mg","mh","mil","mk","com.mk","edu.mk","gov.mk","inf.mk","name.mk","net.mk","org.mk","ml","com.ml","edu.ml","gouv.ml","gov.ml","net.ml","org.ml","presse.ml","*.mm","mn","edu.mn","gov.mn","org.mn","mo","com.mo","edu.mo","gov.mo","net.mo","org.mo","mobi","mp","mq","mr","gov.mr","ms","com.ms","edu.ms","gov.ms","net.ms","org.ms","mt","com.mt","edu.mt","net.mt","org.mt","mu","ac.mu","co.mu","com.mu","gov.mu","net.mu","or.mu","org.mu","museum","mv","aero.mv","biz.mv","com.mv","coop.mv","edu.mv","gov.mv","info.mv","int.mv","mil.mv","museum.mv","name.mv","net.mv","org.mv","pro.mv","mw","ac.mw","biz.mw","co.mw","com.mw","coop.mw","edu.mw","gov.mw","int.mw","net.mw","org.mw","mx","com.mx","edu.mx","gob.mx","net.mx","org.mx","my","biz.my","com.my","edu.my","gov.my","mil.my","name.my","net.my","org.my","mz","ac.mz","adv.mz","co.mz","edu.mz","gov.mz","mil.mz","net.mz","org.mz","na","alt.na","co.na","com.na","gov.na","net.na","org.na","name","nc","asso.nc","nom.nc","ne","net","nf","arts.nf","com.nf","firm.nf","info.nf","net.nf","other.nf","per.nf","rec.nf","store.nf","web.nf","ng","com.ng","edu.ng","gov.ng","i.ng","mil.ng","mobi.ng","name.ng","net.ng","org.ng","sch.ng","ni","ac.ni","biz.ni","co.ni","com.ni","edu.ni","gob.ni","in.ni","info.ni","int.ni","mil.ni","net.ni","nom.ni","org.ni","web.ni","nl","no","fhs.no","folkebibl.no","fylkesbibl.no","idrett.no","museum.no","priv.no","vgs.no","dep.no","herad.no","kommune.no","mil.no","stat.no","aa.no","ah.no","bu.no","fm.no","hl.no","hm.no","jan-mayen.no","mr.no","nl.no","nt.no","of.no","ol.no","oslo.no","rl.no","sf.no","st.no","svalbard.no","tm.no","tr.no","va.no","vf.no","gs.aa.no","gs.ah.no","gs.bu.no","gs.fm.no","gs.hl.no","gs.hm.no","gs.jan-mayen.no","gs.mr.no","gs.nl.no","gs.nt.no","gs.of.no","gs.ol.no","gs.oslo.no","gs.rl.no","gs.sf.no","gs.st.no","gs.svalbard.no","gs.tm.no","gs.tr.no","gs.va.no","gs.vf.no","akrehamn.no","\xe5krehamn.no","algard.no","\xe5lg\xe5rd.no","arna.no","bronnoysund.no","br\xf8nn\xf8ysund.no","brumunddal.no","bryne.no","drobak.no","dr\xf8bak.no","egersund.no","fetsund.no","floro.no","flor\xf8.no","fredrikstad.no","hokksund.no","honefoss.no","h\xf8nefoss.no","jessheim.no","jorpeland.no","j\xf8rpeland.no","kirkenes.no","kopervik.no","krokstadelva.no","langevag.no","langev\xe5g.no","leirvik.no","mjondalen.no","mj\xf8ndalen.no","mo-i-rana.no","mosjoen.no","mosj\xf8en.no","nesoddtangen.no","orkanger.no","osoyro.no","os\xf8yro.no","raholt.no","r\xe5holt.no","sandnessjoen.no","sandnessj\xf8en.no","skedsmokorset.no","slattum.no","spjelkavik.no","stathelle.no","stavern.no","stjordalshalsen.no","stj\xf8rdalshalsen.no","tananger.no","tranby.no","vossevangen.no","aarborte.no","aejrie.no","afjord.no","\xe5fjord.no","agdenes.no","nes.akershus.no","aknoluokta.no","\xe1kŋoluokta.no","al.no","\xe5l.no","alaheadju.no","\xe1laheadju.no","alesund.no","\xe5lesund.no","alstahaug.no","alta.no","\xe1lt\xe1.no","alvdal.no","amli.no","\xe5mli.no","amot.no","\xe5mot.no","andasuolo.no","andebu.no","andoy.no","and\xf8y.no","ardal.no","\xe5rdal.no","aremark.no","arendal.no","\xe5s.no","aseral.no","\xe5seral.no","asker.no","askim.no","askoy.no","ask\xf8y.no","askvoll.no","asnes.no","\xe5snes.no","audnedaln.no","aukra.no","aure.no","aurland.no","aurskog-holand.no","aurskog-h\xf8land.no","austevoll.no","austrheim.no","averoy.no","aver\xf8y.no","badaddja.no","b\xe5d\xe5ddj\xe5.no","b\xe6rum.no","bahcavuotna.no","b\xe1hcavuotna.no","bahccavuotna.no","b\xe1hccavuotna.no","baidar.no","b\xe1id\xe1r.no","bajddar.no","b\xe1jddar.no","balat.no","b\xe1l\xe1t.no","balestrand.no","ballangen.no","balsfjord.no","bamble.no","bardu.no","barum.no","batsfjord.no","b\xe5tsfjord.no","bearalvahki.no","bearalv\xe1hki.no","beardu.no","beiarn.no","berg.no","bergen.no","berlevag.no","berlev\xe5g.no","bievat.no","biev\xe1t.no","bindal.no","birkenes.no","bjarkoy.no","bjark\xf8y.no","bjerkreim.no","bjugn.no","bodo.no","bod\xf8.no","bokn.no","bomlo.no","b\xf8mlo.no","bremanger.no","bronnoy.no","br\xf8nn\xf8y.no","budejju.no","nes.buskerud.no","bygland.no","bykle.no","cahcesuolo.no","č\xe1hcesuolo.no","davvenjarga.no","davvenj\xe1rga.no","davvesiida.no","deatnu.no","dielddanuorri.no","divtasvuodna.no","divttasvuotna.no","donna.no","d\xf8nna.no","dovre.no","drammen.no","drangedal.no","dyroy.no","dyr\xf8y.no","eid.no","eidfjord.no","eidsberg.no","eidskog.no","eidsvoll.no","eigersund.no","elverum.no","enebakk.no","engerdal.no","etne.no","etnedal.no","evenassi.no","even\xe1šši.no","evenes.no","evje-og-hornnes.no","farsund.no","fauske.no","fedje.no","fet.no","finnoy.no","finn\xf8y.no","fitjar.no","fjaler.no","fjell.no","fla.no","fl\xe5.no","flakstad.no","flatanger.no","flekkefjord.no","flesberg.no","flora.no","folldal.no","forde.no","f\xf8rde.no","forsand.no","fosnes.no","fr\xe6na.no","frana.no","frei.no","frogn.no","froland.no","frosta.no","froya.no","fr\xf8ya.no","fuoisku.no","fuossko.no","fusa.no","fyresdal.no","gaivuotna.no","g\xe1ivuotna.no","galsa.no","g\xe1ls\xe1.no","gamvik.no","gangaviika.no","g\xe1ŋgaviika.no","gaular.no","gausdal.no","giehtavuoatna.no","gildeskal.no","gildesk\xe5l.no","giske.no","gjemnes.no","gjerdrum.no","gjerstad.no","gjesdal.no","gjovik.no","gj\xf8vik.no","gloppen.no","gol.no","gran.no","grane.no","granvin.no","gratangen.no","grimstad.no","grong.no","grue.no","gulen.no","guovdageaidnu.no","ha.no","h\xe5.no","habmer.no","h\xe1bmer.no","hadsel.no","h\xe6gebostad.no","hagebostad.no","halden.no","halsa.no","hamar.no","hamaroy.no","hammarfeasta.no","h\xe1mm\xe1rfeasta.no","hammerfest.no","hapmir.no","h\xe1pmir.no","haram.no","hareid.no","harstad.no","hasvik.no","hattfjelldal.no","haugesund.no","os.hedmark.no","valer.hedmark.no","v\xe5ler.hedmark.no","hemne.no","hemnes.no","hemsedal.no","hitra.no","hjartdal.no","hjelmeland.no","hobol.no","hob\xf8l.no","hof.no","hol.no","hole.no","holmestrand.no","holtalen.no","holt\xe5len.no","os.hordaland.no","hornindal.no","horten.no","hoyanger.no","h\xf8yanger.no","hoylandet.no","h\xf8ylandet.no","hurdal.no","hurum.no","hvaler.no","hyllestad.no","ibestad.no","inderoy.no","inder\xf8y.no","iveland.no","ivgu.no","jevnaker.no","jolster.no","j\xf8lster.no","jondal.no","kafjord.no","k\xe5fjord.no","karasjohka.no","k\xe1r\xe1šjohka.no","karasjok.no","karlsoy.no","karmoy.no","karm\xf8y.no","kautokeino.no","klabu.no","kl\xe6bu.no","klepp.no","kongsberg.no","kongsvinger.no","kraanghke.no","kr\xe5anghke.no","kragero.no","krager\xf8.no","kristiansand.no","kristiansund.no","krodsherad.no","kr\xf8dsherad.no","kv\xe6fjord.no","kv\xe6nangen.no","kvafjord.no","kvalsund.no","kvam.no","kvanangen.no","kvinesdal.no","kvinnherad.no","kviteseid.no","kvitsoy.no","kvits\xf8y.no","laakesvuemie.no","l\xe6rdal.no","lahppi.no","l\xe1hppi.no","lardal.no","larvik.no","lavagis.no","lavangen.no","leangaviika.no","leaŋgaviika.no","lebesby.no","leikanger.no","leirfjord.no","leka.no","leksvik.no","lenvik.no","lerdal.no","lesja.no","levanger.no","lier.no","lierne.no","lillehammer.no","lillesand.no","lindas.no","lind\xe5s.no","lindesnes.no","loabat.no","loab\xe1t.no","lodingen.no","l\xf8dingen.no","lom.no","loppa.no","lorenskog.no","l\xf8renskog.no","loten.no","l\xf8ten.no","lund.no","lunner.no","luroy.no","lur\xf8y.no","luster.no","lyngdal.no","lyngen.no","malatvuopmi.no","m\xe1latvuopmi.no","malselv.no","m\xe5lselv.no","malvik.no","mandal.no","marker.no","marnardal.no","masfjorden.no","masoy.no","m\xe5s\xf8y.no","matta-varjjat.no","m\xe1tta-v\xe1rjjat.no","meland.no","meldal.no","melhus.no","meloy.no","mel\xf8y.no","meraker.no","mer\xe5ker.no","midsund.no","midtre-gauldal.no","moareke.no","mo\xe5reke.no","modalen.no","modum.no","molde.no","heroy.more-og-romsdal.no","sande.more-og-romsdal.no","her\xf8y.m\xf8re-og-romsdal.no","sande.m\xf8re-og-romsdal.no","moskenes.no","moss.no","mosvik.no","muosat.no","muos\xe1t.no","naamesjevuemie.no","n\xe5\xe5mesjevuemie.no","n\xe6r\xf8y.no","namdalseid.no","namsos.no","namsskogan.no","nannestad.no","naroy.no","narviika.no","narvik.no","naustdal.no","navuotna.no","n\xe1vuotna.no","nedre-eiker.no","nesna.no","nesodden.no","nesseby.no","nesset.no","nissedal.no","nittedal.no","nord-aurdal.no","nord-fron.no","nord-odal.no","norddal.no","nordkapp.no","bo.nordland.no","b\xf8.nordland.no","heroy.nordland.no","her\xf8y.nordland.no","nordre-land.no","nordreisa.no","nore-og-uvdal.no","notodden.no","notteroy.no","n\xf8tter\xf8y.no","odda.no","oksnes.no","\xf8ksnes.no","omasvuotna.no","oppdal.no","oppegard.no","oppeg\xe5rd.no","orkdal.no","orland.no","\xf8rland.no","orskog.no","\xf8rskog.no","orsta.no","\xf8rsta.no","osen.no","osteroy.no","oster\xf8y.no","valer.ostfold.no","v\xe5ler.\xf8stfold.no","ostre-toten.no","\xf8stre-toten.no","overhalla.no","ovre-eiker.no","\xf8vre-eiker.no","oyer.no","\xf8yer.no","oygarden.no","\xf8ygarden.no","oystre-slidre.no","\xf8ystre-slidre.no","porsanger.no","porsangu.no","pors\xe1ŋgu.no","porsgrunn.no","rade.no","r\xe5de.no","radoy.no","rad\xf8y.no","r\xe6lingen.no","rahkkeravju.no","r\xe1hkker\xe1vju.no","raisa.no","r\xe1isa.no","rakkestad.no","ralingen.no","rana.no","randaberg.no","rauma.no","rendalen.no","rennebu.no","rennesoy.no","rennes\xf8y.no","rindal.no","ringebu.no","ringerike.no","ringsaker.no","risor.no","ris\xf8r.no","rissa.no","roan.no","rodoy.no","r\xf8d\xf8y.no","rollag.no","romsa.no","romskog.no","r\xf8mskog.no","roros.no","r\xf8ros.no","rost.no","r\xf8st.no","royken.no","r\xf8yken.no","royrvik.no","r\xf8yrvik.no","ruovat.no","rygge.no","salangen.no","salat.no","s\xe1lat.no","s\xe1l\xe1t.no","saltdal.no","samnanger.no","sandefjord.no","sandnes.no","sandoy.no","sand\xf8y.no","sarpsborg.no","sauda.no","sauherad.no","sel.no","selbu.no","selje.no","seljord.no","siellak.no","sigdal.no","siljan.no","sirdal.no","skanit.no","sk\xe1nit.no","skanland.no","sk\xe5nland.no","skaun.no","skedsmo.no","ski.no","skien.no","skierva.no","skierv\xe1.no","skiptvet.no","skjak.no","skj\xe5k.no","skjervoy.no","skjerv\xf8y.no","skodje.no","smola.no","sm\xf8la.no","snaase.no","sn\xe5ase.no","snasa.no","sn\xe5sa.no","snillfjord.no","snoasa.no","sogndal.no","sogne.no","s\xf8gne.no","sokndal.no","sola.no","solund.no","somna.no","s\xf8mna.no","sondre-land.no","s\xf8ndre-land.no","songdalen.no","sor-aurdal.no","s\xf8r-aurdal.no","sor-fron.no","s\xf8r-fron.no","sor-odal.no","s\xf8r-odal.no","sor-varanger.no","s\xf8r-varanger.no","sorfold.no","s\xf8rfold.no","sorreisa.no","s\xf8rreisa.no","sortland.no","sorum.no","s\xf8rum.no","spydeberg.no","stange.no","stavanger.no","steigen.no","steinkjer.no","stjordal.no","stj\xf8rdal.no","stokke.no","stor-elvdal.no","stord.no","stordal.no","storfjord.no","strand.no","stranda.no","stryn.no","sula.no","suldal.no","sund.no","sunndal.no","surnadal.no","sveio.no","svelvik.no","sykkylven.no","tana.no","bo.telemark.no","b\xf8.telemark.no","time.no","tingvoll.no","tinn.no","tjeldsund.no","tjome.no","tj\xf8me.no","tokke.no","tolga.no","tonsberg.no","t\xf8nsberg.no","torsken.no","tr\xe6na.no","trana.no","tranoy.no","tran\xf8y.no","troandin.no","trogstad.no","tr\xf8gstad.no","tromsa.no","tromso.no","troms\xf8.no","trondheim.no","trysil.no","tvedestrand.no","tydal.no","tynset.no","tysfjord.no","tysnes.no","tysv\xe6r.no","tysvar.no","ullensaker.no","ullensvang.no","ulvik.no","unjarga.no","unj\xe1rga.no","utsira.no","vaapste.no","vadso.no","vads\xf8.no","v\xe6r\xf8y.no","vaga.no","v\xe5g\xe5.no","vagan.no","v\xe5gan.no","vagsoy.no","v\xe5gs\xf8y.no","vaksdal.no","valle.no","vang.no","vanylven.no","vardo.no","vard\xf8.no","varggat.no","v\xe1rgg\xe1t.no","varoy.no","vefsn.no","vega.no","vegarshei.no","veg\xe5rshei.no","vennesla.no","verdal.no","verran.no","vestby.no","sande.vestfold.no","vestnes.no","vestre-slidre.no","vestre-toten.no","vestvagoy.no","vestv\xe5g\xf8y.no","vevelstad.no","vik.no","vikna.no","vindafjord.no","voagat.no","volda.no","voss.no","*.np","nr","biz.nr","com.nr","edu.nr","gov.nr","info.nr","net.nr","org.nr","nu","nz","ac.nz","co.nz","cri.nz","geek.nz","gen.nz","govt.nz","health.nz","iwi.nz","kiwi.nz","maori.nz","māori.nz","mil.nz","net.nz","org.nz","parliament.nz","school.nz","om","co.om","com.om","edu.om","gov.om","med.om","museum.om","net.om","org.om","pro.om","onion","org","pa","abo.pa","ac.pa","com.pa","edu.pa","gob.pa","ing.pa","med.pa","net.pa","nom.pa","org.pa","sld.pa","pe","com.pe","edu.pe","gob.pe","mil.pe","net.pe","nom.pe","org.pe","pf","com.pf","edu.pf","org.pf","*.pg","ph","com.ph","edu.ph","gov.ph","i.ph","mil.ph","net.ph","ngo.ph","org.ph","pk","ac.pk","biz.pk","com.pk","edu.pk","fam.pk","gkp.pk","gob.pk","gog.pk","gok.pk","gon.pk","gop.pk","gos.pk","gov.pk","net.pk","org.pk","web.pk","pl","com.pl","net.pl","org.pl","agro.pl","aid.pl","atm.pl","auto.pl","biz.pl","edu.pl","gmina.pl","gsm.pl","info.pl","mail.pl","media.pl","miasta.pl","mil.pl","nieruchomosci.pl","nom.pl","pc.pl","powiat.pl","priv.pl","realestate.pl","rel.pl","sex.pl","shop.pl","sklep.pl","sos.pl","szkola.pl","targi.pl","tm.pl","tourism.pl","travel.pl","turystyka.pl","gov.pl","ap.gov.pl","griw.gov.pl","ic.gov.pl","is.gov.pl","kmpsp.gov.pl","konsulat.gov.pl","kppsp.gov.pl","kwp.gov.pl","kwpsp.gov.pl","mup.gov.pl","mw.gov.pl","oia.gov.pl","oirm.gov.pl","oke.gov.pl","oow.gov.pl","oschr.gov.pl","oum.gov.pl","pa.gov.pl","pinb.gov.pl","piw.gov.pl","po.gov.pl","pr.gov.pl","psp.gov.pl","psse.gov.pl","pup.gov.pl","rzgw.gov.pl","sa.gov.pl","sdn.gov.pl","sko.gov.pl","so.gov.pl","sr.gov.pl","starostwo.gov.pl","ug.gov.pl","ugim.gov.pl","um.gov.pl","umig.gov.pl","upow.gov.pl","uppo.gov.pl","us.gov.pl","uw.gov.pl","uzs.gov.pl","wif.gov.pl","wiih.gov.pl","winb.gov.pl","wios.gov.pl","witd.gov.pl","wiw.gov.pl","wkz.gov.pl","wsa.gov.pl","wskr.gov.pl","wsse.gov.pl","wuoz.gov.pl","wzmiuw.gov.pl","zp.gov.pl","zpisdn.gov.pl","augustow.pl","babia-gora.pl","bedzin.pl","beskidy.pl","bialowieza.pl","bialystok.pl","bielawa.pl","bieszczady.pl","boleslawiec.pl","bydgoszcz.pl","bytom.pl","cieszyn.pl","czeladz.pl","czest.pl","dlugoleka.pl","elblag.pl","elk.pl","glogow.pl","gniezno.pl","gorlice.pl","grajewo.pl","ilawa.pl","jaworzno.pl","jelenia-gora.pl","jgora.pl","kalisz.pl","karpacz.pl","kartuzy.pl","kaszuby.pl","katowice.pl","kazimierz-dolny.pl","kepno.pl","ketrzyn.pl","klodzko.pl","kobierzyce.pl","kolobrzeg.pl","konin.pl","konskowola.pl","kutno.pl","lapy.pl","lebork.pl","legnica.pl","lezajsk.pl","limanowa.pl","lomza.pl","lowicz.pl","lubin.pl","lukow.pl","malbork.pl","malopolska.pl","mazowsze.pl","mazury.pl","mielec.pl","mielno.pl","mragowo.pl","naklo.pl","nowaruda.pl","nysa.pl","olawa.pl","olecko.pl","olkusz.pl","olsztyn.pl","opoczno.pl","opole.pl","ostroda.pl","ostroleka.pl","ostrowiec.pl","ostrowwlkp.pl","pila.pl","pisz.pl","podhale.pl","podlasie.pl","polkowice.pl","pomorskie.pl","pomorze.pl","prochowice.pl","pruszkow.pl","przeworsk.pl","pulawy.pl","radom.pl","rawa-maz.pl","rybnik.pl","rzeszow.pl","sanok.pl","sejny.pl","skoczow.pl","slask.pl","slupsk.pl","sosnowiec.pl","stalowa-wola.pl","starachowice.pl","stargard.pl","suwalki.pl","swidnica.pl","swiebodzin.pl","swinoujscie.pl","szczecin.pl","szczytno.pl","tarnobrzeg.pl","tgory.pl","turek.pl","tychy.pl","ustka.pl","walbrzych.pl","warmia.pl","warszawa.pl","waw.pl","wegrow.pl","wielun.pl","wlocl.pl","wloclawek.pl","wodzislaw.pl","wolomin.pl","wroclaw.pl","zachpomor.pl","zagan.pl","zarow.pl","zgora.pl","zgorzelec.pl","pm","pn","co.pn","edu.pn","gov.pn","net.pn","org.pn","post","pr","biz.pr","com.pr","edu.pr","gov.pr","info.pr","isla.pr","name.pr","net.pr","org.pr","pro.pr","ac.pr","est.pr","prof.pr","pro","aaa.pro","aca.pro","acct.pro","avocat.pro","bar.pro","cpa.pro","eng.pro","jur.pro","law.pro","med.pro","recht.pro","ps","com.ps","edu.ps","gov.ps","net.ps","org.ps","plo.ps","sec.ps","pt","com.pt","edu.pt","gov.pt","int.pt","net.pt","nome.pt","org.pt","publ.pt","pw","belau.pw","co.pw","ed.pw","go.pw","or.pw","py","com.py","coop.py","edu.py","gov.py","mil.py","net.py","org.py","qa","com.qa","edu.qa","gov.qa","mil.qa","name.qa","net.qa","org.qa","sch.qa","re","asso.re","com.re","ro","arts.ro","com.ro","firm.ro","info.ro","nom.ro","nt.ro","org.ro","rec.ro","store.ro","tm.ro","www.ro","rs","ac.rs","co.rs","edu.rs","gov.rs","in.rs","org.rs","ru","rw","ac.rw","co.rw","coop.rw","gov.rw","mil.rw","net.rw","org.rw","sa","com.sa","edu.sa","gov.sa","med.sa","net.sa","org.sa","pub.sa","sch.sa","sb","com.sb","edu.sb","gov.sb","net.sb","org.sb","sc","com.sc","edu.sc","gov.sc","net.sc","org.sc","sd","com.sd","edu.sd","gov.sd","info.sd","med.sd","net.sd","org.sd","tv.sd","se","a.se","ac.se","b.se","bd.se","brand.se","c.se","d.se","e.se","f.se","fh.se","fhsk.se","fhv.se","g.se","h.se","i.se","k.se","komforb.se","kommunalforbund.se","komvux.se","l.se","lanbib.se","m.se","n.se","naturbruksgymn.se","o.se","org.se","p.se","parti.se","pp.se","press.se","r.se","s.se","t.se","tm.se","u.se","w.se","x.se","y.se","z.se","sg","com.sg","edu.sg","gov.sg","net.sg","org.sg","sh","com.sh","gov.sh","mil.sh","net.sh","org.sh","si","sj","sk","sl","com.sl","edu.sl","gov.sl","net.sl","org.sl","sm","sn","art.sn","com.sn","edu.sn","gouv.sn","org.sn","perso.sn","univ.sn","so","com.so","edu.so","gov.so","me.so","net.so","org.so","sr","ss","biz.ss","co.ss","com.ss","edu.ss","gov.ss","me.ss","net.ss","org.ss","sch.ss","st","co.st","com.st","consulado.st","edu.st","embaixada.st","mil.st","net.st","org.st","principe.st","saotome.st","store.st","su","sv","com.sv","edu.sv","gob.sv","org.sv","red.sv","sx","gov.sx","sy","com.sy","edu.sy","gov.sy","mil.sy","net.sy","org.sy","sz","ac.sz","co.sz","org.sz","tc","td","tel","tf","tg","th","ac.th","co.th","go.th","in.th","mi.th","net.th","or.th","tj","ac.tj","biz.tj","co.tj","com.tj","edu.tj","go.tj","gov.tj","int.tj","mil.tj","name.tj","net.tj","nic.tj","org.tj","test.tj","web.tj","tk","tl","gov.tl","tm","co.tm","com.tm","edu.tm","gov.tm","mil.tm","net.tm","nom.tm","org.tm","tn","com.tn","ens.tn","fin.tn","gov.tn","ind.tn","info.tn","intl.tn","mincom.tn","nat.tn","net.tn","org.tn","perso.tn","tourism.tn","to","com.to","edu.to","gov.to","mil.to","net.to","org.to","tr","av.tr","bbs.tr","bel.tr","biz.tr","com.tr","dr.tr","edu.tr","gen.tr","gov.tr","info.tr","k12.tr","kep.tr","mil.tr","name.tr","net.tr","org.tr","pol.tr","tel.tr","tsk.tr","tv.tr","web.tr","nc.tr","gov.nc.tr","tt","biz.tt","co.tt","com.tt","edu.tt","gov.tt","info.tt","mil.tt","name.tt","net.tt","org.tt","pro.tt","tv","tw","club.tw","com.tw","ebiz.tw","edu.tw","game.tw","gov.tw","idv.tw","mil.tw","net.tw","org.tw","tz","ac.tz","co.tz","go.tz","hotel.tz","info.tz","me.tz","mil.tz","mobi.tz","ne.tz","or.tz","sc.tz","tv.tz","ua","com.ua","edu.ua","gov.ua","in.ua","net.ua","org.ua","cherkassy.ua","cherkasy.ua","chernigov.ua","chernihiv.ua","chernivtsi.ua","chernovtsy.ua","ck.ua","cn.ua","cr.ua","crimea.ua","cv.ua","dn.ua","dnepropetrovsk.ua","dnipropetrovsk.ua","donetsk.ua","dp.ua","if.ua","ivano-frankivsk.ua","kh.ua","kharkiv.ua","kharkov.ua","kherson.ua","khmelnitskiy.ua","khmelnytskyi.ua","kiev.ua","kirovograd.ua","km.ua","kr.ua","kropyvnytskyi.ua","krym.ua","ks.ua","kv.ua","kyiv.ua","lg.ua","lt.ua","lugansk.ua","luhansk.ua","lutsk.ua","lv.ua","lviv.ua","mk.ua","mykolaiv.ua","nikolaev.ua","od.ua","odesa.ua","odessa.ua","pl.ua","poltava.ua","rivne.ua","rovno.ua","rv.ua","sb.ua","sebastopol.ua","sevastopol.ua","sm.ua","sumy.ua","te.ua","ternopil.ua","uz.ua","uzhgorod.ua","uzhhorod.ua","vinnica.ua","vinnytsia.ua","vn.ua","volyn.ua","yalta.ua","zakarpattia.ua","zaporizhzhe.ua","zaporizhzhia.ua","zhitomir.ua","zhytomyr.ua","zp.ua","zt.ua","ug","ac.ug","co.ug","com.ug","go.ug","ne.ug","or.ug","org.ug","sc.ug","uk","ac.uk","co.uk","gov.uk","ltd.uk","me.uk","net.uk","nhs.uk","org.uk","plc.uk","police.uk","*.sch.uk","us","dni.us","fed.us","isa.us","kids.us","nsn.us","ak.us","al.us","ar.us","as.us","az.us","ca.us","co.us","ct.us","dc.us","de.us","fl.us","ga.us","gu.us","hi.us","ia.us","id.us","il.us","in.us","ks.us","ky.us","la.us","ma.us","md.us","me.us","mi.us","mn.us","mo.us","ms.us","mt.us","nc.us","nd.us","ne.us","nh.us","nj.us","nm.us","nv.us","ny.us","oh.us","ok.us","or.us","pa.us","pr.us","ri.us","sc.us","sd.us","tn.us","tx.us","ut.us","va.us","vi.us","vt.us","wa.us","wi.us","wv.us","wy.us","k12.ak.us","k12.al.us","k12.ar.us","k12.as.us","k12.az.us","k12.ca.us","k12.co.us","k12.ct.us","k12.dc.us","k12.fl.us","k12.ga.us","k12.gu.us","k12.ia.us","k12.id.us","k12.il.us","k12.in.us","k12.ks.us","k12.ky.us","k12.la.us","k12.ma.us","k12.md.us","k12.me.us","k12.mi.us","k12.mn.us","k12.mo.us","k12.ms.us","k12.mt.us","k12.nc.us","k12.ne.us","k12.nh.us","k12.nj.us","k12.nm.us","k12.nv.us","k12.ny.us","k12.oh.us","k12.ok.us","k12.or.us","k12.pa.us","k12.pr.us","k12.sc.us","k12.tn.us","k12.tx.us","k12.ut.us","k12.va.us","k12.vi.us","k12.vt.us","k12.wa.us","k12.wi.us","cc.ak.us","lib.ak.us","cc.al.us","lib.al.us","cc.ar.us","lib.ar.us","cc.as.us","lib.as.us","cc.az.us","lib.az.us","cc.ca.us","lib.ca.us","cc.co.us","lib.co.us","cc.ct.us","lib.ct.us","cc.dc.us","lib.dc.us","cc.de.us","cc.fl.us","cc.ga.us","cc.gu.us","cc.hi.us","cc.ia.us","cc.id.us","cc.il.us","cc.in.us","cc.ks.us","cc.ky.us","cc.la.us","cc.ma.us","cc.md.us","cc.me.us","cc.mi.us","cc.mn.us","cc.mo.us","cc.ms.us","cc.mt.us","cc.nc.us","cc.nd.us","cc.ne.us","cc.nh.us","cc.nj.us","cc.nm.us","cc.nv.us","cc.ny.us","cc.oh.us","cc.ok.us","cc.or.us","cc.pa.us","cc.pr.us","cc.ri.us","cc.sc.us","cc.sd.us","cc.tn.us","cc.tx.us","cc.ut.us","cc.va.us","cc.vi.us","cc.vt.us","cc.wa.us","cc.wi.us","cc.wv.us","cc.wy.us","k12.wy.us","lib.fl.us","lib.ga.us","lib.gu.us","lib.hi.us","lib.ia.us","lib.id.us","lib.il.us","lib.in.us","lib.ks.us","lib.ky.us","lib.la.us","lib.ma.us","lib.md.us","lib.me.us","lib.mi.us","lib.mn.us","lib.mo.us","lib.ms.us","lib.mt.us","lib.nc.us","lib.nd.us","lib.ne.us","lib.nh.us","lib.nj.us","lib.nm.us","lib.nv.us","lib.ny.us","lib.oh.us","lib.ok.us","lib.or.us","lib.pa.us","lib.pr.us","lib.ri.us","lib.sc.us","lib.sd.us","lib.tn.us","lib.tx.us","lib.ut.us","lib.va.us","lib.vi.us","lib.vt.us","lib.wa.us","lib.wi.us","lib.wy.us","chtr.k12.ma.us","paroch.k12.ma.us","pvt.k12.ma.us","ann-arbor.mi.us","cog.mi.us","dst.mi.us","eaton.mi.us","gen.mi.us","mus.mi.us","tec.mi.us","washtenaw.mi.us","uy","com.uy","edu.uy","gub.uy","mil.uy","net.uy","org.uy","uz","co.uz","com.uz","net.uz","org.uz","va","vc","com.vc","edu.vc","gov.vc","mil.vc","net.vc","org.vc","ve","arts.ve","bib.ve","co.ve","com.ve","e12.ve","edu.ve","firm.ve","gob.ve","gov.ve","info.ve","int.ve","mil.ve","net.ve","nom.ve","org.ve","rar.ve","rec.ve","store.ve","tec.ve","web.ve","vg","vi","co.vi","com.vi","k12.vi","net.vi","org.vi","vn","ac.vn","ai.vn","biz.vn","com.vn","edu.vn","gov.vn","health.vn","id.vn","info.vn","int.vn","io.vn","name.vn","net.vn","org.vn","pro.vn","angiang.vn","bacgiang.vn","backan.vn","baclieu.vn","bacninh.vn","baria-vungtau.vn","bentre.vn","binhdinh.vn","binhduong.vn","binhphuoc.vn","binhthuan.vn","camau.vn","cantho.vn","caobang.vn","daklak.vn","daknong.vn","danang.vn","dienbien.vn","dongnai.vn","dongthap.vn","gialai.vn","hagiang.vn","haiduong.vn","haiphong.vn","hanam.vn","hanoi.vn","hatinh.vn","haugiang.vn","hoabinh.vn","hungyen.vn","khanhhoa.vn","kiengiang.vn","kontum.vn","laichau.vn","lamdong.vn","langson.vn","laocai.vn","longan.vn","namdinh.vn","nghean.vn","ninhbinh.vn","ninhthuan.vn","phutho.vn","phuyen.vn","quangbinh.vn","quangnam.vn","quangngai.vn","quangninh.vn","quangtri.vn","soctrang.vn","sonla.vn","tayninh.vn","thaibinh.vn","thainguyen.vn","thanhhoa.vn","thanhphohochiminh.vn","thuathienhue.vn","tiengiang.vn","travinh.vn","tuyenquang.vn","vinhlong.vn","vinhphuc.vn","yenbai.vn","vu","com.vu","edu.vu","net.vu","org.vu","wf","ws","com.ws","edu.ws","gov.ws","net.ws","org.ws","yt","امارات","հայ","বাংলা","бг","البحرين","бел","中国","中國","الجزائر","مصر","ею","ευ","موريتانيا","გე","ελ","香港","個人.香港","公司.香港","政府.香港","教育.香港","組織.香港","網絡.香港","ಭಾರತ","ଭାରତ","ভাৰত","भारतम्","भारोत","ڀارت","ഭാരതം","भारत","بارت","بھارت","భారత్","ભારત","ਭਾਰਤ","ভারত","இந்தியா","ایران","ايران","عراق","الاردن","한국","қаз","ລາວ","ලංකා","இலங்கை","المغرب","мкд","мон","澳門","澳门","مليسيا","عمان","پاکستان","پاكستان","فلسطين","срб","ак.срб","обр.срб","од.срб","орг.срб","пр.срб","упр.срб","рф","قطر","السعودية","السعودیة","السعودیۃ","السعوديه","سودان","新加坡","சிங்கப்பூர்","سورية","سوريا","ไทย","ทหาร.ไทย","ธุรกิจ.ไทย","เน็ต.ไทย","รัฐบาล.ไทย","ศึกษา.ไทย","องค์กร.ไทย","تونس","台灣","台湾","臺灣","укр","اليمن","xxx","ye","com.ye","edu.ye","gov.ye","mil.ye","net.ye","org.ye","ac.za","agric.za","alt.za","co.za","edu.za","gov.za","grondar.za","law.za","mil.za","net.za","ngo.za","nic.za","nis.za","nom.za","org.za","school.za","tm.za","web.za","zm","ac.zm","biz.zm","co.zm","com.zm","edu.zm","gov.zm","info.zm","mil.zm","net.zm","org.zm","sch.zm","zw","ac.zw","co.zw","gov.zw","mil.zw","org.zw","aaa","aarp","abb","abbott","abbvie","abc","able","abogado","abudhabi","academy","accenture","accountant","accountants","aco","actor","ads","adult","aeg","aetna","afl","africa","agakhan","agency","aig","airbus","airforce","airtel","akdn","alibaba","alipay","allfinanz","allstate","ally","alsace","alstom","amazon","americanexpress","americanfamily","amex","amfam","amica","amsterdam","analytics","android","anquan","anz","aol","apartments","app","apple","aquarelle","arab","aramco","archi","army","art","arte","asda","associates","athleta","attorney","auction","audi","audible","audio","auspost","author","auto","autos","aws","axa","azure","baby","baidu","banamex","band","bank","bar","barcelona","barclaycard","barclays","barefoot","bargains","baseball","basketball","bauhaus","bayern","bbc","bbt","bbva","bcg","bcn","beats","beauty","beer","bentley","berlin","best","bestbuy","bet","bharti","bible","bid","bike","bing","bingo","bio","black","blackfriday","blockbuster","blog","bloomberg","blue","bms","bmw","bnpparibas","boats","boehringer","bofa","bom","bond","boo","book","booking","bosch","bostik","boston","bot","boutique","box","bradesco","bridgestone","broadway","broker","brother","brussels","build","builders","business","buy","buzz","bzh","cab","cafe","cal","call","calvinklein","cam","camera","camp","canon","capetown","capital","capitalone","car","caravan","cards","care","career","careers","cars","casa","case","cash","casino","catering","catholic","cba","cbn","cbre","center","ceo","cern","cfa","cfd","chanel","channel","charity","chase","chat","cheap","chintai","christmas","chrome","church","cipriani","circle","cisco","citadel","citi","citic","city","claims","cleaning","click","clinic","clinique","clothing","cloud","club","clubmed","coach","codes","coffee","college","cologne","commbank","community","company","compare","computer","comsec","condos","construction","consulting","contact","contractors","cooking","cool","corsica","country","coupon","coupons","courses","cpa","credit","creditcard","creditunion","cricket","crown","crs","cruise","cruises","cuisinella","cymru","cyou","dad","dance","data","date","dating","datsun","day","dclk","dds","deal","dealer","deals","degree","delivery","dell","deloitte","delta","democrat","dental","dentist","desi","design","dev","dhl","diamonds","diet","digital","direct","directory","discount","discover","dish","diy","dnp","docs","doctor","dog","domains","dot","download","drive","dtv","dubai","dunlop","dupont","durban","dvag","dvr","earth","eat","eco","edeka","education","email","emerck","energy","engineer","engineering","enterprises","epson","equipment","ericsson","erni","esq","estate","eurovision","eus","events","exchange","expert","exposed","express","extraspace","fage","fail","fairwinds","faith","family","fan","fans","farm","farmers","fashion","fast","fedex","feedback","ferrari","ferrero","fidelity","fido","film","final","finance","financial","fire","firestone","firmdale","fish","fishing","fit","fitness","flickr","flights","flir","florist","flowers","fly","foo","food","football","ford","forex","forsale","forum","foundation","fox","free","fresenius","frl","frogans","frontier","ftr","fujitsu","fun","fund","furniture","futbol","fyi","gal","gallery","gallo","gallup","game","games","gap","garden","gay","gbiz","gdn","gea","gent","genting","george","ggee","gift","gifts","gives","giving","glass","gle","global","globo","gmail","gmbh","gmo","gmx","godaddy","gold","goldpoint","golf","goo","goodyear","goog","google","gop","got","grainger","graphics","gratis","green","gripe","grocery","group","gucci","guge","guide","guitars","guru","hair","hamburg","hangout","haus","hbo","hdfc","hdfcbank","health","healthcare","help","helsinki","here","hermes","hiphop","hisamitsu","hitachi","hiv","hkt","hockey","holdings","holiday","homedepot","homegoods","homes","homesense","honda","horse","hospital","host","hosting","hot","hotels","hotmail","house","how","hsbc","hughes","hyatt","hyundai","ibm","icbc","ice","icu","ieee","ifm","ikano","imamat","imdb","immo","immobilien","inc","industries","infiniti","ing","ink","institute","insurance","insure","international","intuit","investments","ipiranga","irish","ismaili","ist","istanbul","itau","itv","jaguar","java","jcb","jeep","jetzt","jewelry","jio","jll","jmp","jnj","joburg","jot","joy","jpmorgan","jprs","juegos","juniper","kaufen","kddi","kerryhotels","kerrylogistics","kerryproperties","kfh","kia","kids","kim","kindle","kitchen","kiwi","koeln","komatsu","kosher","kpmg","kpn","krd","kred","kuokgroup","kyoto","lacaixa","lamborghini","lamer","lancaster","land","landrover","lanxess","lasalle","lat","latino","latrobe","law","lawyer","lds","lease","leclerc","lefrak","legal","lego","lexus","lgbt","lidl","life","lifeinsurance","lifestyle","lighting","like","lilly","limited","limo","lincoln","link","lipsy","live","living","llc","llp","loan","loans","locker","locus","lol","london","lotte","lotto","love","lpl","lplfinancial","ltd","ltda","lundbeck","luxe","luxury","madrid","maif","maison","makeup","man","management","mango","map","market","marketing","markets","marriott","marshalls","mattel","mba","mckinsey","med","media","meet","melbourne","meme","memorial","men","menu","merck","merckmsd","miami","microsoft","mini","mint","mit","mitsubishi","mlb","mls","mma","mobile","moda","moe","moi","mom","monash","money","monster","mormon","mortgage","moscow","moto","motorcycles","mov","movie","msd","mtn","mtr","music","nab","nagoya","navy","nba","nec","netbank","netflix","network","neustar","new","news","next","nextdirect","nexus","nfl","ngo","nhk","nico","nike","nikon","ninja","nissan","nissay","nokia","norton","now","nowruz","nowtv","nra","nrw","ntt","nyc","obi","observer","office","okinawa","olayan","olayangroup","ollo","omega","one","ong","onl","online","ooo","open","oracle","orange","organic","origins","osaka","otsuka","ott","ovh","page","panasonic","paris","pars","partners","parts","party","pay","pccw","pet","pfizer","pharmacy","phd","philips","phone","photo","photography","photos","physio","pics","pictet","pictures","pid","pin","ping","pink","pioneer","pizza","place","play","playstation","plumbing","plus","pnc","pohl","poker","politie","porn","pramerica","praxi","press","prime","prod","productions","prof","progressive","promo","properties","property","protection","pru","prudential","pub","pwc","qpon","quebec","quest","racing","radio","read","realestate","realtor","realty","recipes","red","redstone","redumbrella","rehab","reise","reisen","reit","reliance","ren","rent","rentals","repair","report","republican","rest","restaurant","review","reviews","rexroth","rich","richardli","ricoh","ril","rio","rip","rocks","rodeo","rogers","room","rsvp","rugby","ruhr","run","rwe","ryukyu","saarland","safe","safety","sakura","sale","salon","samsclub","samsung","sandvik","sandvikcoromant","sanofi","sap","sarl","sas","save","saxo","sbi","sbs","scb","schaeffler","schmidt","scholarships","school","schule","schwarz","science","scot","search","seat","secure","security","seek","select","sener","services","seven","sew","sex","sexy","sfr","shangrila","sharp","shell","shia","shiksha","shoes","shop","shopping","shouji","show","silk","sina","singles","site","ski","skin","sky","skype","sling","smart","smile","sncf","soccer","social","softbank","software","sohu","solar","solutions","song","sony","soy","spa","space","sport","spot","srl","stada","staples","star","statebank","statefarm","stc","stcgroup","stockholm","storage","store","stream","studio","study","style","sucks","supplies","supply","support","surf","surgery","suzuki","swatch","swiss","sydney","systems","tab","taipei","talk","taobao","target","tatamotors","tatar","tattoo","tax","taxi","tci","tdk","team","tech","technology","temasek","tennis","teva","thd","theater","theatre","tiaa","tickets","tienda","tips","tires","tirol","tjmaxx","tjx","tkmaxx","tmall","today","tokyo","tools","top","toray","toshiba","total","tours","town","toyota","toys","trade","trading","training","travel","travelers","travelersinsurance","trust","trv","tube","tui","tunes","tushu","tvs","ubank","ubs","unicom","university","uno","uol","ups","vacations","vana","vanguard","vegas","ventures","verisign","versicherung","vet","viajes","video","vig","viking","villas","vin","vip","virgin","visa","vision","viva","vivo","vlaanderen","vodka","volvo","vote","voting","voto","voyage","wales","walmart","walter","wang","wanggou","watch","watches","weather","weatherchannel","webcam","weber","website","wed","wedding","weibo","weir","whoswho","wien","wiki","williamhill","win","windows","wine","winners","wme","wolterskluwer","woodside","work","works","world","wow","wtc","wtf","xbox","xerox","xihuan","xin","कॉम","セール","佛山","慈善","集团","在线","点看","คอม","八卦","موقع","公益","公司","香格里拉","网站","移动","我爱你","москва","католик","онлайн","сайт","联通","קום","时尚","微博","淡马锡","ファッション","орг","नेट","ストア","アマゾン","삼성","商标","商店","商城","дети","ポイント","新闻","家電","كوم","中文网","中信","娱乐","谷歌","電訊盈科","购物","クラウド","通販","网店","संगठन","餐厅","网络","ком","亚马逊","食品","飞利浦","手机","ارامكو","العليان","بازار","ابوظبي","كاثوليك","همراه","닷컴","政府","شبكة","بيتك","عرب","机构","组织机构","健康","招聘","рус","大拿","みんな","グーグル","世界","書籍","网址","닷넷","コム","天主教","游戏","verm\xf6gensberater","verm\xf6gensberatung","企业","信息","嘉里大酒店","嘉里","广东","政务","xyz","yachts","yahoo","yamaxun","yandex","yodobashi","yoga","yokohama","you","youtube","yun","zappos","zara","zero","zip","zone","zuerich","co.krd","edu.krd","art.pl","gliwice.pl","krakow.pl","poznan.pl","wroc.pl","zakopane.pl","lib.de.us","12chars.dev","12chars.it","12chars.pro","cc.ua","inf.ua","ltd.ua","611.to","a2hosted.com","cpserver.com","aaa.vodka","*.on-acorn.io","activetrail.biz","adaptable.app","adobeaemcloud.com","*.dev.adobeaemcloud.com","aem.live","hlx.live","adobeaemcloud.net","aem.page","hlx.page","hlx3.page","adobeio-static.net","adobeioruntime.net","africa.com","beep.pl","airkitapps.com","airkitapps-au.com","airkitapps.eu","aivencloud.com","akadns.net","akamai.net","akamai-staging.net","akamaiedge.net","akamaiedge-staging.net","akamaihd.net","akamaihd-staging.net","akamaiorigin.net","akamaiorigin-staging.net","akamaized.net","akamaized-staging.net","edgekey.net","edgekey-staging.net","edgesuite.net","edgesuite-staging.net","barsy.ca","*.compute.estate","*.alces.network","kasserver.com","altervista.org","alwaysdata.net","myamaze.net","execute-api.cn-north-1.amazonaws.com.cn","execute-api.cn-northwest-1.amazonaws.com.cn","execute-api.af-south-1.amazonaws.com","execute-api.ap-east-1.amazonaws.com","execute-api.ap-northeast-1.amazonaws.com","execute-api.ap-northeast-2.amazonaws.com","execute-api.ap-northeast-3.amazonaws.com","execute-api.ap-south-1.amazonaws.com","execute-api.ap-south-2.amazonaws.com","execute-api.ap-southeast-1.amazonaws.com","execute-api.ap-southeast-2.amazonaws.com","execute-api.ap-southeast-3.amazonaws.com","execute-api.ap-southeast-4.amazonaws.com","execute-api.ap-southeast-5.amazonaws.com","execute-api.ca-central-1.amazonaws.com","execute-api.ca-west-1.amazonaws.com","execute-api.eu-central-1.amazonaws.com","execute-api.eu-central-2.amazonaws.com","execute-api.eu-north-1.amazonaws.com","execute-api.eu-south-1.amazonaws.com","execute-api.eu-south-2.amazonaws.com","execute-api.eu-west-1.amazonaws.com","execute-api.eu-west-2.amazonaws.com","execute-api.eu-west-3.amazonaws.com","execute-api.il-central-1.amazonaws.com","execute-api.me-central-1.amazonaws.com","execute-api.me-south-1.amazonaws.com","execute-api.sa-east-1.amazonaws.com","execute-api.us-east-1.amazonaws.com","execute-api.us-east-2.amazonaws.com","execute-api.us-gov-east-1.amazonaws.com","execute-api.us-gov-west-1.amazonaws.com","execute-api.us-west-1.amazonaws.com","execute-api.us-west-2.amazonaws.com","cloudfront.net","auth.af-south-1.amazoncognito.com","auth.ap-east-1.amazoncognito.com","auth.ap-northeast-1.amazoncognito.com","auth.ap-northeast-2.amazoncognito.com","auth.ap-northeast-3.amazoncognito.com","auth.ap-south-1.amazoncognito.com","auth.ap-south-2.amazoncognito.com","auth.ap-southeast-1.amazoncognito.com","auth.ap-southeast-2.amazoncognito.com","auth.ap-southeast-3.amazoncognito.com","auth.ap-southeast-4.amazoncognito.com","auth.ca-central-1.amazoncognito.com","auth.ca-west-1.amazoncognito.com","auth.eu-central-1.amazoncognito.com","auth.eu-central-2.amazoncognito.com","auth.eu-north-1.amazoncognito.com","auth.eu-south-1.amazoncognito.com","auth.eu-south-2.amazoncognito.com","auth.eu-west-1.amazoncognito.com","auth.eu-west-2.amazoncognito.com","auth.eu-west-3.amazoncognito.com","auth.il-central-1.amazoncognito.com","auth.me-central-1.amazoncognito.com","auth.me-south-1.amazoncognito.com","auth.sa-east-1.amazoncognito.com","auth.us-east-1.amazoncognito.com","auth-fips.us-east-1.amazoncognito.com","auth.us-east-2.amazoncognito.com","auth-fips.us-east-2.amazoncognito.com","auth-fips.us-gov-west-1.amazoncognito.com","auth.us-west-1.amazoncognito.com","auth-fips.us-west-1.amazoncognito.com","auth.us-west-2.amazoncognito.com","auth-fips.us-west-2.amazoncognito.com","*.compute.amazonaws.com.cn","*.compute.amazonaws.com","*.compute-1.amazonaws.com","us-east-1.amazonaws.com","emrappui-prod.cn-north-1.amazonaws.com.cn","emrnotebooks-prod.cn-north-1.amazonaws.com.cn","emrstudio-prod.cn-north-1.amazonaws.com.cn","emrappui-prod.cn-northwest-1.amazonaws.com.cn","emrnotebooks-prod.cn-northwest-1.amazonaws.com.cn","emrstudio-prod.cn-northwest-1.amazonaws.com.cn","emrappui-prod.af-south-1.amazonaws.com","emrnotebooks-prod.af-south-1.amazonaws.com","emrstudio-prod.af-south-1.amazonaws.com","emrappui-prod.ap-east-1.amazonaws.com","emrnotebooks-prod.ap-east-1.amazonaws.com","emrstudio-prod.ap-east-1.amazonaws.com","emrappui-prod.ap-northeast-1.amazonaws.com","emrnotebooks-prod.ap-northeast-1.amazonaws.com","emrstudio-prod.ap-northeast-1.amazonaws.com","emrappui-prod.ap-northeast-2.amazonaws.com","emrnotebooks-prod.ap-northeast-2.amazonaws.com","emrstudio-prod.ap-northeast-2.amazonaws.com","emrappui-prod.ap-northeast-3.amazonaws.com","emrnotebooks-prod.ap-northeast-3.amazonaws.com","emrstudio-prod.ap-northeast-3.amazonaws.com","emrappui-prod.ap-south-1.amazonaws.com","emrnotebooks-prod.ap-south-1.amazonaws.com","emrstudio-prod.ap-south-1.amazonaws.com","emrappui-prod.ap-south-2.amazonaws.com","emrnotebooks-prod.ap-south-2.amazonaws.com","emrstudio-prod.ap-south-2.amazonaws.com","emrappui-prod.ap-southeast-1.amazonaws.com","emrnotebooks-prod.ap-southeast-1.amazonaws.com","emrstudio-prod.ap-southeast-1.amazonaws.com","emrappui-prod.ap-southeast-2.amazonaws.com","emrnotebooks-prod.ap-southeast-2.amazonaws.com","emrstudio-prod.ap-southeast-2.amazonaws.com","emrappui-prod.ap-southeast-3.amazonaws.com","emrnotebooks-prod.ap-southeast-3.amazonaws.com","emrstudio-prod.ap-southeast-3.amazonaws.com","emrappui-prod.ap-southeast-4.amazonaws.com","emrnotebooks-prod.ap-southeast-4.amazonaws.com","emrstudio-prod.ap-southeast-4.amazonaws.com","emrappui-prod.ca-central-1.amazonaws.com","emrnotebooks-prod.ca-central-1.amazonaws.com","emrstudio-prod.ca-central-1.amazonaws.com","emrappui-prod.ca-west-1.amazonaws.com","emrnotebooks-prod.ca-west-1.amazonaws.com","emrstudio-prod.ca-west-1.amazonaws.com","emrappui-prod.eu-central-1.amazonaws.com","emrnotebooks-prod.eu-central-1.amazonaws.com","emrstudio-prod.eu-central-1.amazonaws.com","emrappui-prod.eu-central-2.amazonaws.com","emrnotebooks-prod.eu-central-2.amazonaws.com","emrstudio-prod.eu-central-2.amazonaws.com","emrappui-prod.eu-north-1.amazonaws.com","emrnotebooks-prod.eu-north-1.amazonaws.com","emrstudio-prod.eu-north-1.amazonaws.com","emrappui-prod.eu-south-1.amazonaws.com","emrnotebooks-prod.eu-south-1.amazonaws.com","emrstudio-prod.eu-south-1.amazonaws.com","emrappui-prod.eu-south-2.amazonaws.com","emrnotebooks-prod.eu-south-2.amazonaws.com","emrstudio-prod.eu-south-2.amazonaws.com","emrappui-prod.eu-west-1.amazonaws.com","emrnotebooks-prod.eu-west-1.amazonaws.com","emrstudio-prod.eu-west-1.amazonaws.com","emrappui-prod.eu-west-2.amazonaws.com","emrnotebooks-prod.eu-west-2.amazonaws.com","emrstudio-prod.eu-west-2.amazonaws.com","emrappui-prod.eu-west-3.amazonaws.com","emrnotebooks-prod.eu-west-3.amazonaws.com","emrstudio-prod.eu-west-3.amazonaws.com","emrappui-prod.il-central-1.amazonaws.com","emrnotebooks-prod.il-central-1.amazonaws.com","emrstudio-prod.il-central-1.amazonaws.com","emrappui-prod.me-central-1.amazonaws.com","emrnotebooks-prod.me-central-1.amazonaws.com","emrstudio-prod.me-central-1.amazonaws.com","emrappui-prod.me-south-1.amazonaws.com","emrnotebooks-prod.me-south-1.amazonaws.com","emrstudio-prod.me-south-1.amazonaws.com","emrappui-prod.sa-east-1.amazonaws.com","emrnotebooks-prod.sa-east-1.amazonaws.com","emrstudio-prod.sa-east-1.amazonaws.com","emrappui-prod.us-east-1.amazonaws.com","emrnotebooks-prod.us-east-1.amazonaws.com","emrstudio-prod.us-east-1.amazonaws.com","emrappui-prod.us-east-2.amazonaws.com","emrnotebooks-prod.us-east-2.amazonaws.com","emrstudio-prod.us-east-2.amazonaws.com","emrappui-prod.us-gov-east-1.amazonaws.com","emrnotebooks-prod.us-gov-east-1.amazonaws.com","emrstudio-prod.us-gov-east-1.amazonaws.com","emrappui-prod.us-gov-west-1.amazonaws.com","emrnotebooks-prod.us-gov-west-1.amazonaws.com","emrstudio-prod.us-gov-west-1.amazonaws.com","emrappui-prod.us-west-1.amazonaws.com","emrnotebooks-prod.us-west-1.amazonaws.com","emrstudio-prod.us-west-1.amazonaws.com","emrappui-prod.us-west-2.amazonaws.com","emrnotebooks-prod.us-west-2.amazonaws.com","emrstudio-prod.us-west-2.amazonaws.com","*.cn-north-1.airflow.amazonaws.com.cn","*.cn-northwest-1.airflow.amazonaws.com.cn","*.af-south-1.airflow.amazonaws.com","*.ap-east-1.airflow.amazonaws.com","*.ap-northeast-1.airflow.amazonaws.com","*.ap-northeast-2.airflow.amazonaws.com","*.ap-northeast-3.airflow.amazonaws.com","*.ap-south-1.airflow.amazonaws.com","*.ap-south-2.airflow.amazonaws.com","*.ap-southeast-1.airflow.amazonaws.com","*.ap-southeast-2.airflow.amazonaws.com","*.ap-southeast-3.airflow.amazonaws.com","*.ap-southeast-4.airflow.amazonaws.com","*.ca-central-1.airflow.amazonaws.com","*.ca-west-1.airflow.amazonaws.com","*.eu-central-1.airflow.amazonaws.com","*.eu-central-2.airflow.amazonaws.com","*.eu-north-1.airflow.amazonaws.com","*.eu-south-1.airflow.amazonaws.com","*.eu-south-2.airflow.amazonaws.com","*.eu-west-1.airflow.amazonaws.com","*.eu-west-2.airflow.amazonaws.com","*.eu-west-3.airflow.amazonaws.com","*.il-central-1.airflow.amazonaws.com","*.me-central-1.airflow.amazonaws.com","*.me-south-1.airflow.amazonaws.com","*.sa-east-1.airflow.amazonaws.com","*.us-east-1.airflow.amazonaws.com","*.us-east-2.airflow.amazonaws.com","*.us-west-1.airflow.amazonaws.com","*.us-west-2.airflow.amazonaws.com","s3.dualstack.cn-north-1.amazonaws.com.cn","s3-accesspoint.dualstack.cn-north-1.amazonaws.com.cn","s3-website.dualstack.cn-north-1.amazonaws.com.cn","s3.cn-north-1.amazonaws.com.cn","s3-accesspoint.cn-north-1.amazonaws.com.cn","s3-deprecated.cn-north-1.amazonaws.com.cn","s3-object-lambda.cn-north-1.amazonaws.com.cn","s3-website.cn-north-1.amazonaws.com.cn","s3.dualstack.cn-northwest-1.amazonaws.com.cn","s3-accesspoint.dualstack.cn-northwest-1.amazonaws.com.cn","s3.cn-northwest-1.amazonaws.com.cn","s3-accesspoint.cn-northwest-1.amazonaws.com.cn","s3-object-lambda.cn-northwest-1.amazonaws.com.cn","s3-website.cn-northwest-1.amazonaws.com.cn","s3.dualstack.af-south-1.amazonaws.com","s3-accesspoint.dualstack.af-south-1.amazonaws.com","s3-website.dualstack.af-south-1.amazonaws.com","s3.af-south-1.amazonaws.com","s3-accesspoint.af-south-1.amazonaws.com","s3-object-lambda.af-south-1.amazonaws.com","s3-website.af-south-1.amazonaws.com","s3.dualstack.ap-east-1.amazonaws.com","s3-accesspoint.dualstack.ap-east-1.amazonaws.com","s3.ap-east-1.amazonaws.com","s3-accesspoint.ap-east-1.amazonaws.com","s3-object-lambda.ap-east-1.amazonaws.com","s3-website.ap-east-1.amazonaws.com","s3.dualstack.ap-northeast-1.amazonaws.com","s3-accesspoint.dualstack.ap-northeast-1.amazonaws.com","s3-website.dualstack.ap-northeast-1.amazonaws.com","s3.ap-northeast-1.amazonaws.com","s3-accesspoint.ap-northeast-1.amazonaws.com","s3-object-lambda.ap-northeast-1.amazonaws.com","s3-website.ap-northeast-1.amazonaws.com","s3.dualstack.ap-northeast-2.amazonaws.com","s3-accesspoint.dualstack.ap-northeast-2.amazonaws.com","s3-website.dualstack.ap-northeast-2.amazonaws.com","s3.ap-northeast-2.amazonaws.com","s3-accesspoint.ap-northeast-2.amazonaws.com","s3-object-lambda.ap-northeast-2.amazonaws.com","s3-website.ap-northeast-2.amazonaws.com","s3.dualstack.ap-northeast-3.amazonaws.com","s3-accesspoint.dualstack.ap-northeast-3.amazonaws.com","s3-website.dualstack.ap-northeast-3.amazonaws.com","s3.ap-northeast-3.amazonaws.com","s3-accesspoint.ap-northeast-3.amazonaws.com","s3-object-lambda.ap-northeast-3.amazonaws.com","s3-website.ap-northeast-3.amazonaws.com","s3.dualstack.ap-south-1.amazonaws.com","s3-accesspoint.dualstack.ap-south-1.amazonaws.com","s3-website.dualstack.ap-south-1.amazonaws.com","s3.ap-south-1.amazonaws.com","s3-accesspoint.ap-south-1.amazonaws.com","s3-object-lambda.ap-south-1.amazonaws.com","s3-website.ap-south-1.amazonaws.com","s3.dualstack.ap-south-2.amazonaws.com","s3-accesspoint.dualstack.ap-south-2.amazonaws.com","s3-website.dualstack.ap-south-2.amazonaws.com","s3.ap-south-2.amazonaws.com","s3-accesspoint.ap-south-2.amazonaws.com","s3-object-lambda.ap-south-2.amazonaws.com","s3-website.ap-south-2.amazonaws.com","s3.dualstack.ap-southeast-1.amazonaws.com","s3-accesspoint.dualstack.ap-southeast-1.amazonaws.com","s3-website.dualstack.ap-southeast-1.amazonaws.com","s3.ap-southeast-1.amazonaws.com","s3-accesspoint.ap-southeast-1.amazonaws.com","s3-object-lambda.ap-southeast-1.amazonaws.com","s3-website.ap-southeast-1.amazonaws.com","s3.dualstack.ap-southeast-2.amazonaws.com","s3-accesspoint.dualstack.ap-southeast-2.amazonaws.com","s3-website.dualstack.ap-southeast-2.amazonaws.com","s3.ap-southeast-2.amazonaws.com","s3-accesspoint.ap-southeast-2.amazonaws.com","s3-object-lambda.ap-southeast-2.amazonaws.com","s3-website.ap-southeast-2.amazonaws.com","s3.dualstack.ap-southeast-3.amazonaws.com","s3-accesspoint.dualstack.ap-southeast-3.amazonaws.com","s3-website.dualstack.ap-southeast-3.amazonaws.com","s3.ap-southeast-3.amazonaws.com","s3-accesspoint.ap-southeast-3.amazonaws.com","s3-object-lambda.ap-southeast-3.amazonaws.com","s3-website.ap-southeast-3.amazonaws.com","s3.dualstack.ap-southeast-4.amazonaws.com","s3-accesspoint.dualstack.ap-southeast-4.amazonaws.com","s3-website.dualstack.ap-southeast-4.amazonaws.com","s3.ap-southeast-4.amazonaws.com","s3-accesspoint.ap-southeast-4.amazonaws.com","s3-object-lambda.ap-southeast-4.amazonaws.com","s3-website.ap-southeast-4.amazonaws.com","s3.dualstack.ap-southeast-5.amazonaws.com","s3-accesspoint.dualstack.ap-southeast-5.amazonaws.com","s3-website.dualstack.ap-southeast-5.amazonaws.com","s3.ap-southeast-5.amazonaws.com","s3-accesspoint.ap-southeast-5.amazonaws.com","s3-deprecated.ap-southeast-5.amazonaws.com","s3-object-lambda.ap-southeast-5.amazonaws.com","s3-website.ap-southeast-5.amazonaws.com","s3.dualstack.ca-central-1.amazonaws.com","s3-accesspoint.dualstack.ca-central-1.amazonaws.com","s3-accesspoint-fips.dualstack.ca-central-1.amazonaws.com","s3-fips.dualstack.ca-central-1.amazonaws.com","s3-website.dualstack.ca-central-1.amazonaws.com","s3.ca-central-1.amazonaws.com","s3-accesspoint.ca-central-1.amazonaws.com","s3-accesspoint-fips.ca-central-1.amazonaws.com","s3-fips.ca-central-1.amazonaws.com","s3-object-lambda.ca-central-1.amazonaws.com","s3-website.ca-central-1.amazonaws.com","s3.dualstack.ca-west-1.amazonaws.com","s3-accesspoint.dualstack.ca-west-1.amazonaws.com","s3-accesspoint-fips.dualstack.ca-west-1.amazonaws.com","s3-fips.dualstack.ca-west-1.amazonaws.com","s3-website.dualstack.ca-west-1.amazonaws.com","s3.ca-west-1.amazonaws.com","s3-accesspoint.ca-west-1.amazonaws.com","s3-accesspoint-fips.ca-west-1.amazonaws.com","s3-fips.ca-west-1.amazonaws.com","s3-object-lambda.ca-west-1.amazonaws.com","s3-website.ca-west-1.amazonaws.com","s3.dualstack.eu-central-1.amazonaws.com","s3-accesspoint.dualstack.eu-central-1.amazonaws.com","s3-website.dualstack.eu-central-1.amazonaws.com","s3.eu-central-1.amazonaws.com","s3-accesspoint.eu-central-1.amazonaws.com","s3-object-lambda.eu-central-1.amazonaws.com","s3-website.eu-central-1.amazonaws.com","s3.dualstack.eu-central-2.amazonaws.com","s3-accesspoint.dualstack.eu-central-2.amazonaws.com","s3-website.dualstack.eu-central-2.amazonaws.com","s3.eu-central-2.amazonaws.com","s3-accesspoint.eu-central-2.amazonaws.com","s3-object-lambda.eu-central-2.amazonaws.com","s3-website.eu-central-2.amazonaws.com","s3.dualstack.eu-north-1.amazonaws.com","s3-accesspoint.dualstack.eu-north-1.amazonaws.com","s3.eu-north-1.amazonaws.com","s3-accesspoint.eu-north-1.amazonaws.com","s3-object-lambda.eu-north-1.amazonaws.com","s3-website.eu-north-1.amazonaws.com","s3.dualstack.eu-south-1.amazonaws.com","s3-accesspoint.dualstack.eu-south-1.amazonaws.com","s3-website.dualstack.eu-south-1.amazonaws.com","s3.eu-south-1.amazonaws.com","s3-accesspoint.eu-south-1.amazonaws.com","s3-object-lambda.eu-south-1.amazonaws.com","s3-website.eu-south-1.amazonaws.com","s3.dualstack.eu-south-2.amazonaws.com","s3-accesspoint.dualstack.eu-south-2.amazonaws.com","s3-website.dualstack.eu-south-2.amazonaws.com","s3.eu-south-2.amazonaws.com","s3-accesspoint.eu-south-2.amazonaws.com","s3-object-lambda.eu-south-2.amazonaws.com","s3-website.eu-south-2.amazonaws.com","s3.dualstack.eu-west-1.amazonaws.com","s3-accesspoint.dualstack.eu-west-1.amazonaws.com","s3-website.dualstack.eu-west-1.amazonaws.com","s3.eu-west-1.amazonaws.com","s3-accesspoint.eu-west-1.amazonaws.com","s3-deprecated.eu-west-1.amazonaws.com","s3-object-lambda.eu-west-1.amazonaws.com","s3-website.eu-west-1.amazonaws.com","s3.dualstack.eu-west-2.amazonaws.com","s3-accesspoint.dualstack.eu-west-2.amazonaws.com","s3.eu-west-2.amazonaws.com","s3-accesspoint.eu-west-2.amazonaws.com","s3-object-lambda.eu-west-2.amazonaws.com","s3-website.eu-west-2.amazonaws.com","s3.dualstack.eu-west-3.amazonaws.com","s3-accesspoint.dualstack.eu-west-3.amazonaws.com","s3-website.dualstack.eu-west-3.amazonaws.com","s3.eu-west-3.amazonaws.com","s3-accesspoint.eu-west-3.amazonaws.com","s3-object-lambda.eu-west-3.amazonaws.com","s3-website.eu-west-3.amazonaws.com","s3.dualstack.il-central-1.amazonaws.com","s3-accesspoint.dualstack.il-central-1.amazonaws.com","s3-website.dualstack.il-central-1.amazonaws.com","s3.il-central-1.amazonaws.com","s3-accesspoint.il-central-1.amazonaws.com","s3-object-lambda.il-central-1.amazonaws.com","s3-website.il-central-1.amazonaws.com","s3.dualstack.me-central-1.amazonaws.com","s3-accesspoint.dualstack.me-central-1.amazonaws.com","s3-website.dualstack.me-central-1.amazonaws.com","s3.me-central-1.amazonaws.com","s3-accesspoint.me-central-1.amazonaws.com","s3-object-lambda.me-central-1.amazonaws.com","s3-website.me-central-1.amazonaws.com","s3.dualstack.me-south-1.amazonaws.com","s3-accesspoint.dualstack.me-south-1.amazonaws.com","s3.me-south-1.amazonaws.com","s3-accesspoint.me-south-1.amazonaws.com","s3-object-lambda.me-south-1.amazonaws.com","s3-website.me-south-1.amazonaws.com","s3.amazonaws.com","s3-1.amazonaws.com","s3-ap-east-1.amazonaws.com","s3-ap-northeast-1.amazonaws.com","s3-ap-northeast-2.amazonaws.com","s3-ap-northeast-3.amazonaws.com","s3-ap-south-1.amazonaws.com","s3-ap-southeast-1.amazonaws.com","s3-ap-southeast-2.amazonaws.com","s3-ca-central-1.amazonaws.com","s3-eu-central-1.amazonaws.com","s3-eu-north-1.amazonaws.com","s3-eu-west-1.amazonaws.com","s3-eu-west-2.amazonaws.com","s3-eu-west-3.amazonaws.com","s3-external-1.amazonaws.com","s3-fips-us-gov-east-1.amazonaws.com","s3-fips-us-gov-west-1.amazonaws.com","mrap.accesspoint.s3-global.amazonaws.com","s3-me-south-1.amazonaws.com","s3-sa-east-1.amazonaws.com","s3-us-east-2.amazonaws.com","s3-us-gov-east-1.amazonaws.com","s3-us-gov-west-1.amazonaws.com","s3-us-west-1.amazonaws.com","s3-us-west-2.amazonaws.com","s3-website-ap-northeast-1.amazonaws.com","s3-website-ap-southeast-1.amazonaws.com","s3-website-ap-southeast-2.amazonaws.com","s3-website-eu-west-1.amazonaws.com","s3-website-sa-east-1.amazonaws.com","s3-website-us-east-1.amazonaws.com","s3-website-us-gov-west-1.amazonaws.com","s3-website-us-west-1.amazonaws.com","s3-website-us-west-2.amazonaws.com","s3.dualstack.sa-east-1.amazonaws.com","s3-accesspoint.dualstack.sa-east-1.amazonaws.com","s3-website.dualstack.sa-east-1.amazonaws.com","s3.sa-east-1.amazonaws.com","s3-accesspoint.sa-east-1.amazonaws.com","s3-object-lambda.sa-east-1.amazonaws.com","s3-website.sa-east-1.amazonaws.com","s3.dualstack.us-east-1.amazonaws.com","s3-accesspoint.dualstack.us-east-1.amazonaws.com","s3-accesspoint-fips.dualstack.us-east-1.amazonaws.com","s3-fips.dualstack.us-east-1.amazonaws.com","s3-website.dualstack.us-east-1.amazonaws.com","s3.us-east-1.amazonaws.com","s3-accesspoint.us-east-1.amazonaws.com","s3-accesspoint-fips.us-east-1.amazonaws.com","s3-deprecated.us-east-1.amazonaws.com","s3-fips.us-east-1.amazonaws.com","s3-object-lambda.us-east-1.amazonaws.com","s3-website.us-east-1.amazonaws.com","s3.dualstack.us-east-2.amazonaws.com","s3-accesspoint.dualstack.us-east-2.amazonaws.com","s3-accesspoint-fips.dualstack.us-east-2.amazonaws.com","s3-fips.dualstack.us-east-2.amazonaws.com","s3-website.dualstack.us-east-2.amazonaws.com","s3.us-east-2.amazonaws.com","s3-accesspoint.us-east-2.amazonaws.com","s3-accesspoint-fips.us-east-2.amazonaws.com","s3-deprecated.us-east-2.amazonaws.com","s3-fips.us-east-2.amazonaws.com","s3-object-lambda.us-east-2.amazonaws.com","s3-website.us-east-2.amazonaws.com","s3.dualstack.us-gov-east-1.amazonaws.com","s3-accesspoint.dualstack.us-gov-east-1.amazonaws.com","s3-accesspoint-fips.dualstack.us-gov-east-1.amazonaws.com","s3-fips.dualstack.us-gov-east-1.amazonaws.com","s3.us-gov-east-1.amazonaws.com","s3-accesspoint.us-gov-east-1.amazonaws.com","s3-accesspoint-fips.us-gov-east-1.amazonaws.com","s3-fips.us-gov-east-1.amazonaws.com","s3-object-lambda.us-gov-east-1.amazonaws.com","s3-website.us-gov-east-1.amazonaws.com","s3.dualstack.us-gov-west-1.amazonaws.com","s3-accesspoint.dualstack.us-gov-west-1.amazonaws.com","s3-accesspoint-fips.dualstack.us-gov-west-1.amazonaws.com","s3-fips.dualstack.us-gov-west-1.amazonaws.com","s3.us-gov-west-1.amazonaws.com","s3-accesspoint.us-gov-west-1.amazonaws.com","s3-accesspoint-fips.us-gov-west-1.amazonaws.com","s3-fips.us-gov-west-1.amazonaws.com","s3-object-lambda.us-gov-west-1.amazonaws.com","s3-website.us-gov-west-1.amazonaws.com","s3.dualstack.us-west-1.amazonaws.com","s3-accesspoint.dualstack.us-west-1.amazonaws.com","s3-accesspoint-fips.dualstack.us-west-1.amazonaws.com","s3-fips.dualstack.us-west-1.amazonaws.com","s3-website.dualstack.us-west-1.amazonaws.com","s3.us-west-1.amazonaws.com","s3-accesspoint.us-west-1.amazonaws.com","s3-accesspoint-fips.us-west-1.amazonaws.com","s3-fips.us-west-1.amazonaws.com","s3-object-lambda.us-west-1.amazonaws.com","s3-website.us-west-1.amazonaws.com","s3.dualstack.us-west-2.amazonaws.com","s3-accesspoint.dualstack.us-west-2.amazonaws.com","s3-accesspoint-fips.dualstack.us-west-2.amazonaws.com","s3-fips.dualstack.us-west-2.amazonaws.com","s3-website.dualstack.us-west-2.amazonaws.com","s3.us-west-2.amazonaws.com","s3-accesspoint.us-west-2.amazonaws.com","s3-accesspoint-fips.us-west-2.amazonaws.com","s3-deprecated.us-west-2.amazonaws.com","s3-fips.us-west-2.amazonaws.com","s3-object-lambda.us-west-2.amazonaws.com","s3-website.us-west-2.amazonaws.com","labeling.ap-northeast-1.sagemaker.aws","labeling.ap-northeast-2.sagemaker.aws","labeling.ap-south-1.sagemaker.aws","labeling.ap-southeast-1.sagemaker.aws","labeling.ap-southeast-2.sagemaker.aws","labeling.ca-central-1.sagemaker.aws","labeling.eu-central-1.sagemaker.aws","labeling.eu-west-1.sagemaker.aws","labeling.eu-west-2.sagemaker.aws","labeling.us-east-1.sagemaker.aws","labeling.us-east-2.sagemaker.aws","labeling.us-west-2.sagemaker.aws","notebook.af-south-1.sagemaker.aws","notebook.ap-east-1.sagemaker.aws","notebook.ap-northeast-1.sagemaker.aws","notebook.ap-northeast-2.sagemaker.aws","notebook.ap-northeast-3.sagemaker.aws","notebook.ap-south-1.sagemaker.aws","notebook.ap-south-2.sagemaker.aws","notebook.ap-southeast-1.sagemaker.aws","notebook.ap-southeast-2.sagemaker.aws","notebook.ap-southeast-3.sagemaker.aws","notebook.ap-southeast-4.sagemaker.aws","notebook.ca-central-1.sagemaker.aws","notebook-fips.ca-central-1.sagemaker.aws","notebook.ca-west-1.sagemaker.aws","notebook-fips.ca-west-1.sagemaker.aws","notebook.eu-central-1.sagemaker.aws","notebook.eu-central-2.sagemaker.aws","notebook.eu-north-1.sagemaker.aws","notebook.eu-south-1.sagemaker.aws","notebook.eu-south-2.sagemaker.aws","notebook.eu-west-1.sagemaker.aws","notebook.eu-west-2.sagemaker.aws","notebook.eu-west-3.sagemaker.aws","notebook.il-central-1.sagemaker.aws","notebook.me-central-1.sagemaker.aws","notebook.me-south-1.sagemaker.aws","notebook.sa-east-1.sagemaker.aws","notebook.us-east-1.sagemaker.aws","notebook-fips.us-east-1.sagemaker.aws","notebook.us-east-2.sagemaker.aws","notebook-fips.us-east-2.sagemaker.aws","notebook.us-gov-east-1.sagemaker.aws","notebook-fips.us-gov-east-1.sagemaker.aws","notebook.us-gov-west-1.sagemaker.aws","notebook-fips.us-gov-west-1.sagemaker.aws","notebook.us-west-1.sagemaker.aws","notebook-fips.us-west-1.sagemaker.aws","notebook.us-west-2.sagemaker.aws","notebook-fips.us-west-2.sagemaker.aws","notebook.cn-north-1.sagemaker.com.cn","notebook.cn-northwest-1.sagemaker.com.cn","studio.af-south-1.sagemaker.aws","studio.ap-east-1.sagemaker.aws","studio.ap-northeast-1.sagemaker.aws","studio.ap-northeast-2.sagemaker.aws","studio.ap-northeast-3.sagemaker.aws","studio.ap-south-1.sagemaker.aws","studio.ap-southeast-1.sagemaker.aws","studio.ap-southeast-2.sagemaker.aws","studio.ap-southeast-3.sagemaker.aws","studio.ca-central-1.sagemaker.aws","studio.eu-central-1.sagemaker.aws","studio.eu-north-1.sagemaker.aws","studio.eu-south-1.sagemaker.aws","studio.eu-south-2.sagemaker.aws","studio.eu-west-1.sagemaker.aws","studio.eu-west-2.sagemaker.aws","studio.eu-west-3.sagemaker.aws","studio.il-central-1.sagemaker.aws","studio.me-central-1.sagemaker.aws","studio.me-south-1.sagemaker.aws","studio.sa-east-1.sagemaker.aws","studio.us-east-1.sagemaker.aws","studio.us-east-2.sagemaker.aws","studio.us-gov-east-1.sagemaker.aws","studio-fips.us-gov-east-1.sagemaker.aws","studio.us-gov-west-1.sagemaker.aws","studio-fips.us-gov-west-1.sagemaker.aws","studio.us-west-1.sagemaker.aws","studio.us-west-2.sagemaker.aws","studio.cn-north-1.sagemaker.com.cn","studio.cn-northwest-1.sagemaker.com.cn","*.experiments.sagemaker.aws","analytics-gateway.ap-northeast-1.amazonaws.com","analytics-gateway.ap-northeast-2.amazonaws.com","analytics-gateway.ap-south-1.amazonaws.com","analytics-gateway.ap-southeast-1.amazonaws.com","analytics-gateway.ap-southeast-2.amazonaws.com","analytics-gateway.eu-central-1.amazonaws.com","analytics-gateway.eu-west-1.amazonaws.com","analytics-gateway.us-east-1.amazonaws.com","analytics-gateway.us-east-2.amazonaws.com","analytics-gateway.us-west-2.amazonaws.com","amplifyapp.com","*.awsapprunner.com","webview-assets.aws-cloud9.af-south-1.amazonaws.com","vfs.cloud9.af-south-1.amazonaws.com","webview-assets.cloud9.af-south-1.amazonaws.com","webview-assets.aws-cloud9.ap-east-1.amazonaws.com","vfs.cloud9.ap-east-1.amazonaws.com","webview-assets.cloud9.ap-east-1.amazonaws.com","webview-assets.aws-cloud9.ap-northeast-1.amazonaws.com","vfs.cloud9.ap-northeast-1.amazonaws.com","webview-assets.cloud9.ap-northeast-1.amazonaws.com","webview-assets.aws-cloud9.ap-northeast-2.amazonaws.com","vfs.cloud9.ap-northeast-2.amazonaws.com","webview-assets.cloud9.ap-northeast-2.amazonaws.com","webview-assets.aws-cloud9.ap-northeast-3.amazonaws.com","vfs.cloud9.ap-northeast-3.amazonaws.com","webview-assets.cloud9.ap-northeast-3.amazonaws.com","webview-assets.aws-cloud9.ap-south-1.amazonaws.com","vfs.cloud9.ap-south-1.amazonaws.com","webview-assets.cloud9.ap-south-1.amazonaws.com","webview-assets.aws-cloud9.ap-southeast-1.amazonaws.com","vfs.cloud9.ap-southeast-1.amazonaws.com","webview-assets.cloud9.ap-southeast-1.amazonaws.com","webview-assets.aws-cloud9.ap-southeast-2.amazonaws.com","vfs.cloud9.ap-southeast-2.amazonaws.com","webview-assets.cloud9.ap-southeast-2.amazonaws.com","webview-assets.aws-cloud9.ca-central-1.amazonaws.com","vfs.cloud9.ca-central-1.amazonaws.com","webview-assets.cloud9.ca-central-1.amazonaws.com","webview-assets.aws-cloud9.eu-central-1.amazonaws.com","vfs.cloud9.eu-central-1.amazonaws.com","webview-assets.cloud9.eu-central-1.amazonaws.com","webview-assets.aws-cloud9.eu-north-1.amazonaws.com","vfs.cloud9.eu-north-1.amazonaws.com","webview-assets.cloud9.eu-north-1.amazonaws.com","webview-assets.aws-cloud9.eu-south-1.amazonaws.com","vfs.cloud9.eu-south-1.amazonaws.com","webview-assets.cloud9.eu-south-1.amazonaws.com","webview-assets.aws-cloud9.eu-west-1.amazonaws.com","vfs.cloud9.eu-west-1.amazonaws.com","webview-assets.cloud9.eu-west-1.amazonaws.com","webview-assets.aws-cloud9.eu-west-2.amazonaws.com","vfs.cloud9.eu-west-2.amazonaws.com","webview-assets.cloud9.eu-west-2.amazonaws.com","webview-assets.aws-cloud9.eu-west-3.amazonaws.com","vfs.cloud9.eu-west-3.amazonaws.com","webview-assets.cloud9.eu-west-3.amazonaws.com","webview-assets.aws-cloud9.il-central-1.amazonaws.com","vfs.cloud9.il-central-1.amazonaws.com","webview-assets.aws-cloud9.me-south-1.amazonaws.com","vfs.cloud9.me-south-1.amazonaws.com","webview-assets.cloud9.me-south-1.amazonaws.com","webview-assets.aws-cloud9.sa-east-1.amazonaws.com","vfs.cloud9.sa-east-1.amazonaws.com","webview-assets.cloud9.sa-east-1.amazonaws.com","webview-assets.aws-cloud9.us-east-1.amazonaws.com","vfs.cloud9.us-east-1.amazonaws.com","webview-assets.cloud9.us-east-1.amazonaws.com","webview-assets.aws-cloud9.us-east-2.amazonaws.com","vfs.cloud9.us-east-2.amazonaws.com","webview-assets.cloud9.us-east-2.amazonaws.com","webview-assets.aws-cloud9.us-west-1.amazonaws.com","vfs.cloud9.us-west-1.amazonaws.com","webview-assets.cloud9.us-west-1.amazonaws.com","webview-assets.aws-cloud9.us-west-2.amazonaws.com","vfs.cloud9.us-west-2.amazonaws.com","webview-assets.cloud9.us-west-2.amazonaws.com","awsapps.com","cn-north-1.eb.amazonaws.com.cn","cn-northwest-1.eb.amazonaws.com.cn","elasticbeanstalk.com","af-south-1.elasticbeanstalk.com","ap-east-1.elasticbeanstalk.com","ap-northeast-1.elasticbeanstalk.com","ap-northeast-2.elasticbeanstalk.com","ap-northeast-3.elasticbeanstalk.com","ap-south-1.elasticbeanstalk.com","ap-southeast-1.elasticbeanstalk.com","ap-southeast-2.elasticbeanstalk.com","ap-southeast-3.elasticbeanstalk.com","ca-central-1.elasticbeanstalk.com","eu-central-1.elasticbeanstalk.com","eu-north-1.elasticbeanstalk.com","eu-south-1.elasticbeanstalk.com","eu-west-1.elasticbeanstalk.com","eu-west-2.elasticbeanstalk.com","eu-west-3.elasticbeanstalk.com","il-central-1.elasticbeanstalk.com","me-south-1.elasticbeanstalk.com","sa-east-1.elasticbeanstalk.com","us-east-1.elasticbeanstalk.com","us-east-2.elasticbeanstalk.com","us-gov-east-1.elasticbeanstalk.com","us-gov-west-1.elasticbeanstalk.com","us-west-1.elasticbeanstalk.com","us-west-2.elasticbeanstalk.com","*.elb.amazonaws.com.cn","*.elb.amazonaws.com","awsglobalaccelerator.com","*.private.repost.aws","eero.online","eero-stage.online","apigee.io","panel.dev","siiites.com","appspacehosted.com","appspaceusercontent.com","appudo.net","on-aptible.com","f5.si","arvanedge.ir","user.aseinet.ne.jp","gv.vc","d.gv.vc","user.party.eus","pimienta.org","poivron.org","potager.org","sweetpepper.org","myasustor.com","cdn.prod.atlassian-dev.net","translated.page","myfritz.link","myfritz.net","onavstack.net","*.awdev.ca","*.advisor.ws","ecommerce-shop.pl","b-data.io","balena-devices.com","base.ec","official.ec","buyshop.jp","fashionstore.jp","handcrafted.jp","kawaiishop.jp","supersale.jp","theshop.jp","shopselect.net","base.shop","beagleboard.io","*.beget.app","pages.gay","bnr.la","bitbucket.io","blackbaudcdn.net","of.je","bluebite.io","boomla.net","boutir.com","boxfuse.io","square7.ch","bplaced.com","bplaced.de","square7.de","bplaced.net","square7.net","*.s.brave.io","shop.brendly.hr","shop.brendly.rs","browsersafetymark.io","radio.am","radio.fm","uk0.bigv.io","dh.bytemark.co.uk","vm.bytemark.co.uk","cafjs.com","canva-apps.cn","*.my.canvasite.cn","canva-apps.com","*.my.canva.site","drr.ac","uwu.ai","carrd.co","crd.co","ju.mp","api.gov.uk","cdn77-storage.com","rsc.contentproxy9.cz","r.cdn77.net","cdn77-ssl.net","c.cdn77.org","rsc.cdn77.org","ssl.origin.cdn77-secure.org","za.bz","br.com","cn.com","de.com","eu.com","jpn.com","mex.com","ru.com","sa.com","uk.com","us.com","za.com","com.de","gb.net","hu.net","jp.net","se.net","uk.net","ae.org","com.se","cx.ua","discourse.group","discourse.team","clerk.app","clerkstage.app","*.lcl.dev","*.lclstage.dev","*.stg.dev","*.stgstage.dev","cleverapps.cc","*.services.clever-cloud.com","cleverapps.io","cleverapps.tech","clickrising.net","cloudns.asia","cloudns.be","cloud-ip.biz","cloudns.biz","cloudns.cc","cloudns.ch","cloudns.cl","cloudns.club","dnsabr.com","ip-ddns.com","cloudns.cx","cloudns.eu","cloudns.in","cloudns.info","ddns-ip.net","dns-cloud.net","dns-dynamic.net","cloudns.nz","cloudns.org","ip-dynamic.org","cloudns.ph","cloudns.pro","cloudns.pw","cloudns.us","c66.me","cloud66.ws","cloud66.zone","jdevcloud.com","wpdevcloud.com","cloudaccess.host","freesite.host","cloudaccess.net","*.cloudera.site","cf-ipfs.com","cloudflare-ipfs.com","trycloudflare.com","pages.dev","r2.dev","workers.dev","cloudflare.net","cdn.cloudflare.net","cdn.cloudflareanycast.net","cdn.cloudflarecn.net","cdn.cloudflareglobal.net","cust.cloudscale.ch","objects.lpg.cloudscale.ch","objects.rma.cloudscale.ch","wnext.app","cnpy.gdn","*.otap.co","co.ca","co.com","codeberg.page","csb.app","preview.csb.app","co.nl","co.no","webhosting.be","hosting-cluster.nl","ctfcloud.net","convex.site","ac.ru","edu.ru","gov.ru","int.ru","mil.ru","test.ru","dyn.cosidns.de","dnsupdater.de","dynamisches-dns.de","internet-dns.de","l-o-g-i-n.de","dynamic-dns.info","feste-ip.net","knx-server.net","static-access.net","craft.me","realm.cz","on.crisp.email","*.cryptonomic.net","curv.dev","cfolks.pl","cyon.link","cyon.site","platform0.app","fnwk.site","folionetwork.site","biz.dk","co.dk","firm.dk","reg.dk","store.dk","dyndns.dappnode.io","builtwithdark.com","darklang.io","demo.datadetect.com","instance.datadetect.com","edgestack.me","dattolocal.com","dattorelay.com","dattoweb.com","mydatto.com","dattolocal.net","mydatto.net","ddnss.de","dyn.ddnss.de","dyndns.ddnss.de","dyn-ip24.de","dyndns1.de","home-webserver.de","dyn.home-webserver.de","myhome-server.de","ddnss.org","debian.net","definima.io","definima.net","deno.dev","deno-staging.dev","dedyn.io","deta.app","deta.dev","dfirma.pl","dkonto.pl","you2.pl","ondigitalocean.app","*.digitaloceanspaces.com","us.kg","rss.my.id","diher.solutions","discordsays.com","discordsez.com","jozi.biz","dnshome.de","online.th","shop.th","drayddns.com","shoparena.pl","dreamhosters.com","durumis.com","mydrobo.com","drud.io","drud.us","duckdns.org","dy.fi","tunk.org","dyndns.biz","for-better.biz","for-more.biz","for-some.biz","for-the.biz","selfip.biz","webhop.biz","ftpaccess.cc","game-server.cc","myphotos.cc","scrapping.cc","blogdns.com","cechire.com","dnsalias.com","dnsdojo.com","doesntexist.com","dontexist.com","doomdns.com","dyn-o-saur.com","dynalias.com","dyndns-at-home.com","dyndns-at-work.com","dyndns-blog.com","dyndns-free.com","dyndns-home.com","dyndns-ip.com","dyndns-mail.com","dyndns-office.com","dyndns-pics.com","dyndns-remote.com","dyndns-server.com","dyndns-web.com","dyndns-wiki.com","dyndns-work.com","est-a-la-maison.com","est-a-la-masion.com","est-le-patron.com","est-mon-blogueur.com","from-ak.com","from-al.com","from-ar.com","from-ca.com","from-ct.com","from-dc.com","from-de.com","from-fl.com","from-ga.com","from-hi.com","from-ia.com","from-id.com","from-il.com","from-in.com","from-ks.com","from-ky.com","from-ma.com","from-md.com","from-mi.com","from-mn.com","from-mo.com","from-ms.com","from-mt.com","from-nc.com","from-nd.com","from-ne.com","from-nh.com","from-nj.com","from-nm.com","from-nv.com","from-oh.com","from-ok.com","from-or.com","from-pa.com","from-pr.com","from-ri.com","from-sc.com","from-sd.com","from-tn.com","from-tx.com","from-ut.com","from-va.com","from-vt.com","from-wa.com","from-wi.com","from-wv.com","from-wy.com","getmyip.com","gotdns.com","hobby-site.com","homelinux.com","homeunix.com","iamallama.com","is-a-anarchist.com","is-a-blogger.com","is-a-bookkeeper.com","is-a-bulls-fan.com","is-a-caterer.com","is-a-chef.com","is-a-conservative.com","is-a-cpa.com","is-a-cubicle-slave.com","is-a-democrat.com","is-a-designer.com","is-a-doctor.com","is-a-financialadvisor.com","is-a-geek.com","is-a-green.com","is-a-guru.com","is-a-hard-worker.com","is-a-hunter.com","is-a-landscaper.com","is-a-lawyer.com","is-a-liberal.com","is-a-libertarian.com","is-a-llama.com","is-a-musician.com","is-a-nascarfan.com","is-a-nurse.com","is-a-painter.com","is-a-personaltrainer.com","is-a-photographer.com","is-a-player.com","is-a-republican.com","is-a-rockstar.com","is-a-socialist.com","is-a-student.com","is-a-teacher.com","is-a-techie.com","is-a-therapist.com","is-an-accountant.com","is-an-actor.com","is-an-actress.com","is-an-anarchist.com","is-an-artist.com","is-an-engineer.com","is-an-entertainer.com","is-certified.com","is-gone.com","is-into-anime.com","is-into-cars.com","is-into-cartoons.com","is-into-games.com","is-leet.com","is-not-certified.com","is-slick.com","is-uberleet.com","is-with-theband.com","isa-geek.com","isa-hockeynut.com","issmarterthanyou.com","likes-pie.com","likescandy.com","neat-url.com","saves-the-whales.com","selfip.com","sells-for-less.com","sells-for-u.com","servebbs.com","simple-url.com","space-to-rent.com","teaches-yoga.com","writesthisblog.com","ath.cx","fuettertdasnetz.de","isteingeek.de","istmein.de","lebtimnetz.de","leitungsen.de","traeumtgerade.de","barrel-of-knowledge.info","barrell-of-knowledge.info","dyndns.info","for-our.info","groks-the.info","groks-this.info","here-for-more.info","knowsitall.info","selfip.info","webhop.info","forgot.her.name","forgot.his.name","at-band-camp.net","blogdns.net","broke-it.net","buyshouses.net","dnsalias.net","dnsdojo.net","does-it.net","dontexist.net","dynalias.net","dynathome.net","endofinternet.net","from-az.net","from-co.net","from-la.net","from-ny.net","gets-it.net","ham-radio-op.net","homeftp.net","homeip.net","homelinux.net","homeunix.net","in-the-band.net","is-a-chef.net","is-a-geek.net","isa-geek.net","kicks-ass.net","office-on-the.net","podzone.net","scrapper-site.net","selfip.net","sells-it.net","servebbs.net","serveftp.net","thruhere.net","webhop.net","merseine.nu","mine.nu","shacknet.nu","blogdns.org","blogsite.org","boldlygoingnowhere.org","dnsalias.org","dnsdojo.org","doesntexist.org","dontexist.org","doomdns.org","dvrdns.org","dynalias.org","dyndns.org","go.dyndns.org","home.dyndns.org","endofinternet.org","endoftheinternet.org","from-me.org","game-host.org","gotdns.org","hobby-site.org","homedns.org","homeftp.org","homelinux.org","homeunix.org","is-a-bruinsfan.org","is-a-candidate.org","is-a-celticsfan.org","is-a-chef.org","is-a-geek.org","is-a-knight.org","is-a-linux-user.org","is-a-patsfan.org","is-a-soxfan.org","is-found.org","is-lost.org","is-saved.org","is-very-bad.org","is-very-evil.org","is-very-good.org","is-very-nice.org","is-very-sweet.org","isa-geek.org","kicks-ass.org","misconfused.org","podzone.org","readmyblog.org","selfip.org","sellsyourhome.org","servebbs.org","serveftp.org","servegame.org","stuff-4-sale.org","webhop.org","better-than.tv","dyndns.tv","on-the-web.tv","worse-than.tv","is-by.us","land-4-sale.us","stuff-4-sale.us","dyndns.ws","mypets.ws","ddnsfree.com","ddnsgeek.com","giize.com","gleeze.com","kozow.com","loseyourip.com","ooguy.com","theworkpc.com","casacam.net","dynu.net","accesscam.org","camdvr.org","freeddns.org","mywire.org","webredirect.org","myddns.rocks","dynv6.net","e4.cz","easypanel.app","easypanel.host","*.ewp.live","twmail.cc","twmail.net","twmail.org","mymailer.com.tw","url.tw","at.emf.camp","rt.ht","elementor.cloud","elementor.cool","en-root.fr","mytuleap.com","tuleap-partners.com","encr.app","encoreapi.com","eu.encoway.cloud","eu.org","al.eu.org","asso.eu.org","at.eu.org","au.eu.org","be.eu.org","bg.eu.org","ca.eu.org","cd.eu.org","ch.eu.org","cn.eu.org","cy.eu.org","cz.eu.org","de.eu.org","dk.eu.org","edu.eu.org","ee.eu.org","es.eu.org","fi.eu.org","fr.eu.org","gr.eu.org","hr.eu.org","hu.eu.org","ie.eu.org","il.eu.org","in.eu.org","int.eu.org","is.eu.org","it.eu.org","jp.eu.org","kr.eu.org","lt.eu.org","lu.eu.org","lv.eu.org","me.eu.org","mk.eu.org","mt.eu.org","my.eu.org","net.eu.org","ng.eu.org","nl.eu.org","no.eu.org","nz.eu.org","pl.eu.org","pt.eu.org","ro.eu.org","ru.eu.org","se.eu.org","si.eu.org","sk.eu.org","tr.eu.org","uk.eu.org","us.eu.org","eurodir.ru","eu-1.evennode.com","eu-2.evennode.com","eu-3.evennode.com","eu-4.evennode.com","us-1.evennode.com","us-2.evennode.com","us-3.evennode.com","us-4.evennode.com","relay.evervault.app","relay.evervault.dev","expo.app","staging.expo.app","onfabrica.com","ru.net","adygeya.ru","bashkiria.ru","bir.ru","cbg.ru","com.ru","dagestan.ru","grozny.ru","kalmykia.ru","kustanai.ru","marine.ru","mordovia.ru","msk.ru","mytis.ru","nalchik.ru","nov.ru","pyatigorsk.ru","spb.ru","vladikavkaz.ru","vladimir.ru","abkhazia.su","adygeya.su","aktyubinsk.su","arkhangelsk.su","armenia.su","ashgabad.su","azerbaijan.su","balashov.su","bashkiria.su","bryansk.su","bukhara.su","chimkent.su","dagestan.su","east-kazakhstan.su","exnet.su","georgia.su","grozny.su","ivanovo.su","jambyl.su","kalmykia.su","kaluga.su","karacol.su","karaganda.su","karelia.su","khakassia.su","krasnodar.su","kurgan.su","kustanai.su","lenug.su","mangyshlak.su","mordovia.su","msk.su","murmansk.su","nalchik.su","navoi.su","north-kazakhstan.su","nov.su","obninsk.su","penza.su","pokrovsk.su","sochi.su","spb.su","tashkent.su","termez.su","togliatti.su","troitsk.su","tselinograd.su","tula.su","tuva.su","vladikavkaz.su","vladimir.su","vologda.su","channelsdvr.net","u.channelsdvr.net","edgecompute.app","fastly-edge.com","fastly-terrarium.com","freetls.fastly.net","map.fastly.net","a.prod.fastly.net","global.prod.fastly.net","a.ssl.fastly.net","b.ssl.fastly.net","global.ssl.fastly.net","fastlylb.net","map.fastlylb.net","*.user.fm","fastvps-server.com","fastvps.host","myfast.host","fastvps.site","myfast.space","conn.uk","copro.uk","hosp.uk","fedorainfracloud.org","fedorapeople.org","cloud.fedoraproject.org","app.os.fedoraproject.org","app.os.stg.fedoraproject.org","mydobiss.com","fh-muenster.io","filegear.me","firebaseapp.com","fldrv.com","flutterflow.app","fly.dev","shw.io","edgeapp.net","forgeblocks.com","id.forgerock.io","framer.ai","framer.app","framercanvas.com","framer.media","framer.photos","framer.website","framer.wiki","0e.vc","freebox-os.com","freeboxos.com","fbx-os.fr","fbxos.fr","freebox-os.fr","freeboxos.fr","freedesktop.org","freemyip.com","*.frusky.de","wien.funkfeuer.at","daemon.asia","dix.asia","mydns.bz","0am.jp","0g0.jp","0j0.jp","0t0.jp","mydns.jp","pgw.jp","wjg.jp","keyword-on.net","live-on.net","server-on.net","mydns.tw","mydns.vc","*.futurecms.at","*.ex.futurecms.at","*.in.futurecms.at","futurehosting.at","futuremailing.at","*.ex.ortsinfo.at","*.kunden.ortsinfo.at","*.statics.cloud","aliases121.com","campaign.gov.uk","service.gov.uk","independent-commission.uk","independent-inquest.uk","independent-inquiry.uk","independent-panel.uk","independent-review.uk","public-inquiry.uk","royal-commission.uk","gehirn.ne.jp","usercontent.jp","gentapps.com","gentlentapis.com","lab.ms","cdn-edges.net","localcert.net","localhostcert.net","gsj.bz","githubusercontent.com","githubpreview.dev","github.io","gitlab.io","gitapp.si","gitpage.si","glitch.me","nog.community","co.ro","shop.ro","lolipop.io","angry.jp","babyblue.jp","babymilk.jp","backdrop.jp","bambina.jp","bitter.jp","blush.jp","boo.jp","boy.jp","boyfriend.jp","but.jp","candypop.jp","capoo.jp","catfood.jp","cheap.jp","chicappa.jp","chillout.jp","chips.jp","chowder.jp","chu.jp","ciao.jp","cocotte.jp","coolblog.jp","cranky.jp","cutegirl.jp","daa.jp","deca.jp","deci.jp","digick.jp","egoism.jp","fakefur.jp","fem.jp","flier.jp","floppy.jp","fool.jp","frenchkiss.jp","girlfriend.jp","girly.jp","gloomy.jp","gonna.jp","greater.jp","hacca.jp","heavy.jp","her.jp","hiho.jp","hippy.jp","holy.jp","hungry.jp","icurus.jp","itigo.jp","jellybean.jp","kikirara.jp","kill.jp","kilo.jp","kuron.jp","littlestar.jp","lolipopmc.jp","lolitapunk.jp","lomo.jp","lovepop.jp","lovesick.jp","main.jp","mods.jp","mond.jp","mongolian.jp","moo.jp","namaste.jp","nikita.jp","nobushi.jp","noor.jp","oops.jp","parallel.jp","parasite.jp","pecori.jp","peewee.jp","penne.jp","pepper.jp","perma.jp","pigboat.jp","pinoko.jp","punyu.jp","pupu.jp","pussycat.jp","pya.jp","raindrop.jp","readymade.jp","sadist.jp","schoolbus.jp","secret.jp","staba.jp","stripper.jp","sub.jp","sunnyday.jp","thick.jp","tonkotsu.jp","under.jp","upper.jp","velvet.jp","verse.jp","versus.jp","vivian.jp","watson.jp","weblike.jp","whitesnow.jp","zombie.jp","heteml.net","graphic.design","goip.de","blogspot.ae","blogspot.al","blogspot.am","*.hosted.app","*.run.app","web.app","blogspot.com.ar","blogspot.co.at","blogspot.com.au","blogspot.ba","blogspot.be","blogspot.bg","blogspot.bj","blogspot.com.br","blogspot.com.by","blogspot.ca","blogspot.cf","blogspot.ch","blogspot.cl","blogspot.com.co","*.0emm.com","appspot.com","*.r.appspot.com","blogspot.com","codespot.com","googleapis.com","googlecode.com","pagespeedmobilizer.com","withgoogle.com","withyoutube.com","blogspot.cv","blogspot.com.cy","blogspot.cz","blogspot.de","*.gateway.dev","blogspot.dk","blogspot.com.ee","blogspot.com.eg","blogspot.com.es","blogspot.fi","blogspot.fr","cloud.goog","translate.goog","*.usercontent.goog","blogspot.gr","blogspot.hk","blogspot.hr","blogspot.hu","blogspot.co.id","blogspot.ie","blogspot.co.il","blogspot.in","blogspot.is","blogspot.it","blogspot.jp","blogspot.co.ke","blogspot.kr","blogspot.li","blogspot.lt","blogspot.lu","blogspot.md","blogspot.mk","blogspot.com.mt","blogspot.mx","blogspot.my","cloudfunctions.net","blogspot.com.ng","blogspot.nl","blogspot.no","blogspot.co.nz","blogspot.pe","blogspot.pt","blogspot.qa","blogspot.re","blogspot.ro","blogspot.rs","blogspot.ru","blogspot.se","blogspot.sg","blogspot.si","blogspot.sk","blogspot.sn","blogspot.td","blogspot.com.tr","blogspot.tw","blogspot.ug","blogspot.co.uk","blogspot.com.uy","blogspot.vn","blogspot.co.za","goupile.fr","pymnt.uk","cloudapps.digital","london.cloudapps.digital","gov.nl","grafana-dev.net","grayjayleagues.com","g\xfcnstigbestellen.de","g\xfcnstigliefern.de","fin.ci","free.hr","caa.li","ua.rs","conf.se","h\xe4kkinen.fi","hrsn.dev","hashbang.sh","hasura.app","hasura-app.io","hatenablog.com","hatenadiary.com","hateblo.jp","hatenablog.jp","hatenadiary.jp","hatenadiary.org","pages.it.hs-heilbronn.de","pages-research.it.hs-heilbronn.de","heiyu.space","helioho.st","heliohost.us","hepforge.org","herokuapp.com","herokussl.com","heyflow.page","heyflow.site","ravendb.cloud","ravendb.community","development.run","ravendb.run","homesklep.pl","*.kin.one","*.id.pub","*.kin.pub","secaas.hk","hoplix.shop","orx.biz","biz.gl","biz.ng","co.biz.ng","dl.biz.ng","go.biz.ng","lg.biz.ng","on.biz.ng","col.ng","firm.ng","gen.ng","ltd.ng","ngo.ng","plc.ng","ie.ua","hostyhosting.io","hf.space","static.hf.space","hypernode.io","iobb.net","co.cz","*.moonscale.io","moonscale.net","gr.com","iki.fi","ibxos.it","iliadboxos.it","smushcdn.com","wphostedmail.com","wpmucdn.com","tempurl.host","wpmudev.host","dyn-berlin.de","in-berlin.de","in-brb.de","in-butter.de","in-dsl.de","in-vpn.de","in-dsl.net","in-vpn.net","in-dsl.org","in-vpn.org","biz.at","info.at","info.cx","ac.leg.br","al.leg.br","am.leg.br","ap.leg.br","ba.leg.br","ce.leg.br","df.leg.br","es.leg.br","go.leg.br","ma.leg.br","mg.leg.br","ms.leg.br","mt.leg.br","pa.leg.br","pb.leg.br","pe.leg.br","pi.leg.br","pr.leg.br","rj.leg.br","rn.leg.br","ro.leg.br","rr.leg.br","rs.leg.br","sc.leg.br","se.leg.br","sp.leg.br","to.leg.br","pixolino.com","na4u.ru","apps-1and1.com","live-website.com","apps-1and1.net","websitebuilder.online","app-ionos.space","iopsys.se","*.dweb.link","ipifony.net","ir.md","is-a-good.dev","is-a.dev","iservschule.de","mein-iserv.de","schulplattform.de","schulserver.de","test-iserv.de","iserv.dev","mel.cloudlets.com.au","cloud.interhostsolutions.be","alp1.ae.flow.ch","appengine.flow.ch","es-1.axarnet.cloud","diadem.cloud","vip.jelastic.cloud","jele.cloud","it1.eur.aruba.jenv-aruba.cloud","it1.jenv-aruba.cloud","keliweb.cloud","cs.keliweb.cloud","oxa.cloud","tn.oxa.cloud","uk.oxa.cloud","primetel.cloud","uk.primetel.cloud","ca.reclaim.cloud","uk.reclaim.cloud","us.reclaim.cloud","ch.trendhosting.cloud","de.trendhosting.cloud","jele.club","dopaas.com","paas.hosted-by-previder.com","rag-cloud.hosteur.com","rag-cloud-ch.hosteur.com","jcloud.ik-server.com","jcloud-ver-jpc.ik-server.com","demo.jelastic.com","paas.massivegrid.com","jed.wafaicloud.com","ryd.wafaicloud.com","j.scaleforce.com.cy","jelastic.dogado.eu","fi.cloudplatform.fi","demo.datacenter.fi","paas.datacenter.fi","jele.host","mircloud.host","paas.beebyte.io","sekd1.beebyteapp.io","jele.io","jc.neen.it","jcloud.kz","cloudjiffy.net","fra1-de.cloudjiffy.net","west1-us.cloudjiffy.net","jls-sto1.elastx.net","jls-sto2.elastx.net","jls-sto3.elastx.net","fr-1.paas.massivegrid.net","lon-1.paas.massivegrid.net","lon-2.paas.massivegrid.net","ny-1.paas.massivegrid.net","ny-2.paas.massivegrid.net","sg-1.paas.massivegrid.net","jelastic.saveincloud.net","nordeste-idc.saveincloud.net","j.scaleforce.net","sdscloud.pl","unicloud.pl","mircloud.ru","enscaled.sg","jele.site","jelastic.team","orangecloud.tn","j.layershift.co.uk","phx.enscaled.us","mircloud.us","myjino.ru","*.hosting.myjino.ru","*.landing.myjino.ru","*.spectrum.myjino.ru","*.vps.myjino.ru","jotelulu.cloud","webadorsite.com","jouwweb.site","*.cns.joyent.com","*.triton.zone","js.org","kaas.gg","khplay.nl","kapsi.fi","ezproxy.kuleuven.be","kuleuven.cloud","keymachine.de","kinghost.net","uni5.net","knightpoint.systems","koobin.events","webthings.io","krellian.net","oya.to","git-repos.de","lcube-server.de","svn-repos.de","leadpages.co","lpages.co","lpusercontent.com","lelux.site","libp2p.direct","runcontainers.dev","co.business","co.education","co.events","co.financial","co.network","co.place","co.technology","linkyard-cloud.ch","linkyard.cloud","members.linode.com","*.nodebalancer.linode.com","*.linodeobjects.com","ip.linodeusercontent.com","we.bs","filegear-sg.me","ggff.net","*.user.localcert.dev","lodz.pl","pabianice.pl","plock.pl","sieradz.pl","skierniewice.pl","zgierz.pl","loginline.app","loginline.dev","loginline.io","loginline.services","loginline.site","lohmus.me","servers.run","krasnik.pl","leczna.pl","lubartow.pl","lublin.pl","poniatowa.pl","swidnik.pl","glug.org.uk","lug.org.uk","lugs.org.uk","barsy.bg","barsy.club","barsycenter.com","barsyonline.com","barsy.de","barsy.dev","barsy.eu","barsy.gr","barsy.in","barsy.info","barsy.io","barsy.me","barsy.menu","barsyonline.menu","barsy.mobi","barsy.net","barsy.online","barsy.org","barsy.pro","barsy.pub","barsy.ro","barsy.rs","barsy.shop","barsyonline.shop","barsy.site","barsy.store","barsy.support","barsy.uk","barsy.co.uk","barsyonline.co.uk","*.magentosite.cloud","hb.cldmail.ru","matlab.cloud","modelscape.com","mwcloudnonprod.com","polyspace.com","mayfirst.info","mayfirst.org","mazeplay.com","mcdir.me","mcdir.ru","vps.mcdir.ru","mcpre.ru","mediatech.by","mediatech.dev","hra.health","medusajs.app","miniserver.com","memset.net","messerli.app","atmeta.com","apps.fbsbx.com","*.cloud.metacentrum.cz","custom.metacentrum.cz","flt.cloud.muni.cz","usr.cloud.muni.cz","meteorapp.com","eu.meteorapp.com","co.pl","*.azurecontainer.io","azure-api.net","azure-mobile.net","azureedge.net","azurefd.net","azurestaticapps.net","1.azurestaticapps.net","2.azurestaticapps.net","3.azurestaticapps.net","4.azurestaticapps.net","5.azurestaticapps.net","6.azurestaticapps.net","7.azurestaticapps.net","centralus.azurestaticapps.net","eastasia.azurestaticapps.net","eastus2.azurestaticapps.net","westeurope.azurestaticapps.net","westus2.azurestaticapps.net","azurewebsites.net","cloudapp.net","trafficmanager.net","blob.core.windows.net","servicebus.windows.net","routingthecloud.com","sn.mynetname.net","routingthecloud.net","routingthecloud.org","csx.cc","mydbserver.com","webspaceconfig.de","mittwald.info","mittwaldserver.info","typo3server.info","project.space","modx.dev","bmoattachments.org","net.ru","org.ru","pp.ru","hostedpi.com","caracal.mythic-beasts.com","customer.mythic-beasts.com","fentiger.mythic-beasts.com","lynx.mythic-beasts.com","ocelot.mythic-beasts.com","oncilla.mythic-beasts.com","onza.mythic-beasts.com","sphinx.mythic-beasts.com","vs.mythic-beasts.com","x.mythic-beasts.com","yali.mythic-beasts.com","cust.retrosnub.co.uk","ui.nabu.casa","cloud.nospamproxy.com","netfy.app","netlify.app","4u.com","nfshost.com","ipfs.nftstorage.link","ngo.us","ngrok.app","ngrok-free.app","ngrok.dev","ngrok-free.dev","ngrok.io","ap.ngrok.io","au.ngrok.io","eu.ngrok.io","in.ngrok.io","jp.ngrok.io","sa.ngrok.io","us.ngrok.io","ngrok.pizza","ngrok.pro","torun.pl","nh-serv.co.uk","nimsite.uk","mmafan.biz","myftp.biz","no-ip.biz","no-ip.ca","fantasyleague.cc","gotdns.ch","3utilities.com","blogsyte.com","ciscofreak.com","damnserver.com","ddnsking.com","ditchyourip.com","dnsiskinky.com","dynns.com","geekgalaxy.com","health-carereform.com","homesecuritymac.com","homesecuritypc.com","myactivedirectory.com","mysecuritycamera.com","myvnc.com","net-freaks.com","onthewifi.com","point2this.com","quicksytes.com","securitytactics.com","servebeer.com","servecounterstrike.com","serveexchange.com","serveftp.com","servegame.com","servehalflife.com","servehttp.com","servehumour.com","serveirc.com","servemp3.com","servep2p.com","servepics.com","servequake.com","servesarcasm.com","stufftoread.com","unusualperson.com","workisboring.com","dvrcam.info","ilovecollege.info","no-ip.info","brasilia.me","ddns.me","dnsfor.me","hopto.me","loginto.me","noip.me","webhop.me","bounceme.net","ddns.net","eating-organic.net","mydissent.net","myeffect.net","mymediapc.net","mypsx.net","mysecuritycamera.net","nhlfan.net","no-ip.net","pgafan.net","privatizehealthinsurance.net","redirectme.net","serveblog.net","serveminecraft.net","sytes.net","cable-modem.org","collegefan.org","couchpotatofries.org","hopto.org","mlbfan.org","myftp.org","mysecuritycamera.org","nflfan.org","no-ip.org","read-books.org","ufcfan.org","zapto.org","no-ip.co.uk","golffan.us","noip.us","pointto.us","stage.nodeart.io","*.developer.app","noop.app","*.northflank.app","*.build.run","*.code.run","*.database.run","*.migration.run","noticeable.news","notion.site","dnsking.ch","mypi.co","n4t.co","001www.com","myiphost.com","forumz.info","soundcast.me","tcp4.me","dnsup.net","hicam.net","now-dns.net","ownip.net","vpndns.net","dynserv.org","now-dns.org","x443.pw","now-dns.top","ntdll.top","freeddns.us","nsupdate.info","nerdpol.ovh","nyc.mn","prvcy.page","obl.ong","observablehq.cloud","static.observableusercontent.com","omg.lol","cloudycluster.net","omniwe.site","123webseite.at","123website.be","simplesite.com.br","123website.ch","simplesite.com","123webseite.de","123hjemmeside.dk","123miweb.es","123kotisivu.fi","123siteweb.fr","simplesite.gr","123homepage.it","123website.lu","123website.nl","123hjemmeside.no","service.one","simplesite.pl","123paginaweb.pt","123minsida.se","is-a-fullstack.dev","is-cool.dev","is-not-a.dev","localplayer.dev","is-local.org","opensocial.site","opencraft.hosting","16-b.it","32-b.it","64-b.it","orsites.com","operaunite.com","*.customer-oci.com","*.oci.customer-oci.com","*.ocp.customer-oci.com","*.ocs.customer-oci.com","*.oraclecloudapps.com","*.oraclegovcloudapps.com","*.oraclegovcloudapps.uk","tech.orange","can.re","authgear-staging.com","authgearapps.com","skygearapp.com","outsystemscloud.com","*.hosting.ovh.net","*.webpaas.ovh.net","ownprovider.com","own.pm","*.owo.codes","ox.rs","oy.lc","pgfog.com","pagexl.com","gotpantheon.com","pantheonsite.io","*.paywhirl.com","*.xmit.co","xmit.dev","madethis.site","srv.us","gh.srv.us","gl.srv.us","lk3.ru","mypep.link","perspecta.cloud","on-web.fr","*.upsun.app","upsunapp.com","ent.platform.sh","eu.platform.sh","us.platform.sh","*.platformsh.site","*.tst.site","platter-app.com","platter-app.dev","platterp.us","pley.games","onporter.run","co.bn","postman-echo.com","pstmn.io","mock.pstmn.io","httpbin.org","prequalifyme.today","xen.prgmr.com","priv.at","protonet.io","chirurgiens-dentistes-en-france.fr","byen.site","pubtls.org","pythonanywhere.com","eu.pythonanywhere.com","qa2.com","qcx.io","*.sys.qcx.io","myqnapcloud.cn","alpha-myqnapcloud.com","dev-myqnapcloud.com","mycloudnas.com","mynascloud.com","myqnapcloud.com","qoto.io","qualifioapp.com","ladesk.com","qbuser.com","*.quipelements.com","vapor.cloud","vaporcloud.io","rackmaze.com","rackmaze.net","cloudsite.builders","myradweb.net","servername.us","web.in","in.net","myrdbx.io","site.rb-hosting.io","*.on-rancher.cloud","*.on-k3s.io","*.on-rio.io","ravpage.co.il","readthedocs-hosted.com","readthedocs.io","rhcloud.com","instances.spawn.cc","onrender.com","app.render.com","replit.app","id.replit.app","firewalledreplit.co","id.firewalledreplit.co","repl.co","id.repl.co","replit.dev","archer.replit.dev","bones.replit.dev","canary.replit.dev","global.replit.dev","hacker.replit.dev","id.replit.dev","janeway.replit.dev","kim.replit.dev","kira.replit.dev","kirk.replit.dev","odo.replit.dev","paris.replit.dev","picard.replit.dev","pike.replit.dev","prerelease.replit.dev","reed.replit.dev","riker.replit.dev","sisko.replit.dev","spock.replit.dev","staging.replit.dev","sulu.replit.dev","tarpit.replit.dev","teams.replit.dev","tucker.replit.dev","wesley.replit.dev","worf.replit.dev","repl.run","resindevice.io","devices.resinstaging.io","hzc.io","adimo.co.uk","itcouldbewor.se","aus.basketball","nz.basketball","git-pages.rit.edu","rocky.page","rub.de","ruhr-uni-bochum.de","io.noc.ruhr-uni-bochum.de","биз.рус","ком.рус","крым.рус","мир.рус","мск.рус","орг.рус","самара.рус","сочи.рус","спб.рус","я.рус","ras.ru","nyat.app","180r.com","dojin.com","sakuratan.com","sakuraweb.com","x0.com","2-d.jp","bona.jp","crap.jp","daynight.jp","eek.jp","flop.jp","halfmoon.jp","jeez.jp","matrix.jp","mimoza.jp","ivory.ne.jp","mail-box.ne.jp","mints.ne.jp","mokuren.ne.jp","opal.ne.jp","sakura.ne.jp","sumomo.ne.jp","topaz.ne.jp","netgamers.jp","nyanta.jp","o0o0.jp","rdy.jp","rgr.jp","rulez.jp","s3.isk01.sakurastorage.jp","s3.isk02.sakurastorage.jp","saloon.jp","sblo.jp","skr.jp","tank.jp","uh-oh.jp","undo.jp","rs.webaccel.jp","user.webaccel.jp","websozai.jp","xii.jp","squares.net","jpn.org","kirara.st","x0.to","from.tv","sakura.tv","*.builder.code.com","*.dev-builder.code.com","*.stg-builder.code.com","*.001.test.code-builder-stg.platform.salesforce.com","*.d.crm.dev","*.w.crm.dev","*.wa.crm.dev","*.wb.crm.dev","*.wc.crm.dev","*.wd.crm.dev","*.we.crm.dev","*.wf.crm.dev","sandcats.io","logoip.com","logoip.de","fr-par-1.baremetal.scw.cloud","fr-par-2.baremetal.scw.cloud","nl-ams-1.baremetal.scw.cloud","cockpit.fr-par.scw.cloud","fnc.fr-par.scw.cloud","functions.fnc.fr-par.scw.cloud","k8s.fr-par.scw.cloud","nodes.k8s.fr-par.scw.cloud","s3.fr-par.scw.cloud","s3-website.fr-par.scw.cloud","whm.fr-par.scw.cloud","priv.instances.scw.cloud","pub.instances.scw.cloud","k8s.scw.cloud","cockpit.nl-ams.scw.cloud","k8s.nl-ams.scw.cloud","nodes.k8s.nl-ams.scw.cloud","s3.nl-ams.scw.cloud","s3-website.nl-ams.scw.cloud","whm.nl-ams.scw.cloud","cockpit.pl-waw.scw.cloud","k8s.pl-waw.scw.cloud","nodes.k8s.pl-waw.scw.cloud","s3.pl-waw.scw.cloud","s3-website.pl-waw.scw.cloud","scalebook.scw.cloud","smartlabeling.scw.cloud","dedibox.fr","schokokeks.net","gov.scot","service.gov.scot","scrysec.com","client.scrypted.io","firewall-gateway.com","firewall-gateway.de","my-gateway.de","my-router.de","spdns.de","spdns.eu","firewall-gateway.net","my-firewall.org","myfirewall.org","spdns.org","seidat.net","sellfy.store","minisite.ms","senseering.net","servebolt.cloud","biz.ua","co.ua","pp.ua","as.sh.cn","sheezy.games","shiftedit.io","myshopblocks.com","myshopify.com","shopitsite.com","shopware.shop","shopware.store","mo-siemens.io","1kapp.com","appchizi.com","applinzi.com","sinaapp.com","vipsinaapp.com","siteleaf.net","small-web.org","aeroport.fr","avocat.fr","chambagri.fr","chirurgiens-dentistes.fr","experts-comptables.fr","medecin.fr","notaires.fr","pharmacien.fr","port.fr","veterinaire.fr","vp4.me","*.snowflake.app","*.privatelink.snowflake.app","streamlit.app","streamlitapp.com","try-snowplow.com","mafelo.net","playstation-cloud.com","srht.site","apps.lair.io","*.stolos.io","spacekit.io","ind.mom","customer.speedpartner.de","myspreadshop.at","myspreadshop.com.au","myspreadshop.be","myspreadshop.ca","myspreadshop.ch","myspreadshop.com","myspreadshop.de","myspreadshop.dk","myspreadshop.es","myspreadshop.fi","myspreadshop.fr","myspreadshop.ie","myspreadshop.it","myspreadshop.net","myspreadshop.nl","myspreadshop.no","myspreadshop.pl","myspreadshop.se","myspreadshop.co.uk","w-corp-staticblitz.com","w-credentialless-staticblitz.com","w-staticblitz.com","stackhero-network.com","runs.onstackit.cloud","stackit.gg","stackit.rocks","stackit.run","stackit.zone","musician.io","novecore.site","api.stdlib.com","feedback.ac","forms.ac","assessments.cx","calculators.cx","funnels.cx","paynow.cx","quizzes.cx","researched.cx","tests.cx","surveys.so","storebase.store","storipress.app","storj.farm","strapiapp.com","media.strapiapp.com","vps-host.net","atl.jelastic.vps-host.net","njs.jelastic.vps-host.net","ric.jelastic.vps-host.net","streak-link.com","streaklinks.com","streakusercontent.com","soc.srcf.net","user.srcf.net","utwente.io","temp-dns.com","supabase.co","supabase.in","supabase.net","syncloud.it","dscloud.biz","direct.quickconnect.cn","dsmynas.com","familyds.com","diskstation.me","dscloud.me","i234.me","myds.me","synology.me","dscloud.mobi","dsmynas.net","familyds.net","dsmynas.org","familyds.org","direct.quickconnect.to","vpnplus.to","mytabit.com","mytabit.co.il","tabitorder.co.il","taifun-dns.de","ts.net","*.c.ts.net","gda.pl","gdansk.pl","gdynia.pl","med.pl","sopot.pl","taveusercontent.com","p.tawk.email","p.tawkto.email","site.tb-hosting.com","edugit.io","s3.teckids.org","telebit.app","telebit.io","*.telebit.xyz","*.firenet.ch","*.svc.firenet.ch","reservd.com","thingdustdata.com","cust.dev.thingdust.io","reservd.dev.thingdust.io","cust.disrec.thingdust.io","reservd.disrec.thingdust.io","cust.prod.thingdust.io","cust.testing.thingdust.io","reservd.testing.thingdust.io","tickets.io","arvo.network","azimuth.network","tlon.network","torproject.net","pages.torproject.net","townnews-staging.com","12hp.at","2ix.at","4lima.at","lima-city.at","12hp.ch","2ix.ch","4lima.ch","lima-city.ch","trafficplex.cloud","de.cool","12hp.de","2ix.de","4lima.de","lima-city.de","1337.pictures","clan.rip","lima-city.rocks","webspace.rocks","lima.zone","*.transurl.be","*.transurl.eu","site.transip.me","*.transurl.nl","tuxfamily.org","dd-dns.de","dray-dns.de","draydns.de","dyn-vpn.de","dynvpn.de","mein-vigor.de","my-vigor.de","my-wan.de","syno-ds.de","synology-diskstation.de","synology-ds.de","diskstation.eu","diskstation.org","typedream.app","pro.typeform.com","*.uberspace.de","uber.space","hk.com","inc.hk","ltd.hk","hk.org","it.com","unison-services.cloud","virtual-user.de","virtualuser.de","name.pm","sch.tf","biz.wf","sch.wf","org.yt","rs.ba","bielsko.pl","upli.io","urown.cloud","dnsupdate.info","us.org","v.ua","express.val.run","web.val.run","vercel.app","v0.build","vercel.dev","vusercontent.net","now.sh","2038.io","router.management","v-info.info","voorloper.cloud","*.vultrobjects.com","wafflecell.com","webflow.io","webflowtest.io","*.webhare.dev","bookonline.app","hotelwithflight.com","reserve-online.com","reserve-online.net","cprapid.com","pleskns.com","wp2.host","pdns.page","plesk.page","wpsquared.site","*.wadl.top","remotewd.com","box.ca","pages.wiardweb.com","toolforge.org","wmcloud.org","wmflabs.org","wdh.app","panel.gg","daemon.panel.gg","wixsite.com","wixstudio.com","editorx.io","wixstudio.io","wix.run","messwithdns.com","woltlab-demo.com","myforum.community","community-pro.de","diskussionsbereich.de","community-pro.net","meinforum.net","affinitylottery.org.uk","raffleentry.org.uk","weeklylottery.org.uk","wpenginepowered.com","js.wpenginepowered.com","half.host","xnbay.com","u2.xnbay.com","u2-local.xnbay.com","cistron.nl","demon.nl","xs4all.space","yandexcloud.net","storage.yandexcloud.net","website.yandexcloud.net","official.academy","yolasite.com","yombo.me","ynh.fr","nohost.me","noho.st","za.net","za.org","zap.cloud","zeabur.app","bss.design","basicserver.io","virtualserver.io","enterprisecloud.nu"].reduce((e,a)=>{let t=a.replace(/^(\*\.|\!)/,""),o=i.toASCII(t),n=a.charAt(0);if(e.has(o))throw Error(`Multiple rules found for ${a} (${o})`);return e.set(o,{rule:a,suffix:t,punySuffix:o,wildcard:"*"===n,exception:"!"===n}),e},new Map),r=e=>{let a=i.toASCII(e).split(".");for(let e=0;e<a.length;e++){let t=a.slice(e).join("."),o=n.get(t);if(o)return o}return null},s={DOMAIN_TOO_SHORT:"Domain name too short.",DOMAIN_TOO_LONG:"Domain name too long. It should be no more than 255 chars.",LABEL_STARTS_WITH_DASH:"Domain name label can not start with a dash.",LABEL_ENDS_WITH_DASH:"Domain name label can not end with a dash.",LABEL_TOO_LONG:"Domain name label should be at most 63 chars long.",LABEL_TOO_SHORT:"Domain name label should be at least 1 character long.",LABEL_INVALID_CHARS:"Domain name label can only contain alphanumeric characters or dashes."},l=e=>{let a,t=i.toASCII(e);if(t.length<1)return"DOMAIN_TOO_SHORT";if(t.length>255)return"DOMAIN_TOO_LONG";let o=t.split(".");for(let e=0;e<o.length;++e){if(!(a=o[e]).length)return"LABEL_TOO_SHORT";if(a.length>63)return"LABEL_TOO_LONG";if("-"===a.charAt(0))return"LABEL_STARTS_WITH_DASH";if("-"===a.charAt(a.length-1))return"LABEL_ENDS_WITH_DASH";if(!/^[a-z0-9\-_]+$/.test(a))return"LABEL_INVALID_CHARS"}},u=e=>{if("string"!=typeof e)throw TypeError("Domain name must be a string.");let a=e.slice(0).toLowerCase();"."===a.charAt(a.length-1)&&(a=a.slice(0,a.length-1));let t=l(a);if(t)return{input:e,error:{message:s[t],code:t}};let o={input:e,tld:null,sld:null,domain:null,subdomain:null,listed:!1},n=a.split(".");if("local"===n[n.length-1])return o;let u=()=>(/xn--/.test(a)&&(o.domain&&(o.domain=i.toASCII(o.domain)),o.subdomain&&(o.subdomain=i.toASCII(o.subdomain))),o),c=r(a);if(!c)return n.length<2?o:(o.tld=n.pop(),o.sld=n.pop(),o.domain=[o.sld,o.tld].join("."),n.length&&(o.subdomain=n.pop()),u());o.listed=!0;let m=c.suffix.split("."),p=n.slice(0,n.length-m.length);return c.exception&&p.push(m.shift()),o.tld=m.join("."),p.length&&(c.wildcard&&(m.unshift(p.pop()),o.tld=m.join(".")),p.length)&&(o.sld=p.pop(),o.domain=[o.sld,o.tld].join("."),p.length&&(o.subdomain=p.join("."))),u()},c=e=>e&&u(e).domain||null,m=e=>{let a=u(e);return!!(a.domain&&a.listed)};a.default={parse:u,get:c,isValid:m},a.errorCodes=s,a.get=c,a.isValid=m,a.parse=u},40376:e=>{e.exports="4.1.4"},41394:(e,a,t)=>{function o(){try{return t(28354)}catch(e){return null}}a.getUtilInspect=function(e,a={}){let t=(a.requireUtil||o)();return function(a,o,i){return t?t.inspect(a,o,i):e(a)}},a.getCustomInspectSymbol=function(e={}){return(e.lookupCustomInspectSymbol||function(){return Symbol.for("nodejs.util.inspect.custom")})()||function(e){let a=(e.requireUtil||o)();return a?a.inspect.custom:null}(e)}},43229:(e,a)=>{"use strict";class t{constructor(){this.synchronous=!1}findCookie(e,a,t,o){throw Error("findCookie is not implemented")}findCookies(e,a,t,o){throw Error("findCookies is not implemented")}putCookie(e,a){throw Error("putCookie is not implemented")}updateCookie(e,a,t){throw Error("updateCookie is not implemented")}removeCookie(e,a,t,o){throw Error("removeCookie is not implemented")}removeCookies(e,a,t){throw Error("removeCookies is not implemented")}removeAllCookies(e){throw Error("removeAllCookies is not implemented")}getAllCookies(e){throw Error("getAllCookies is not implemented (therefore jar cannot be serialized)")}}a.i=t},47524:(e,a,t)=>{"use strict";t.d(a,{A:()=>s7});var o,i,n,r,s,l,u,c,m,p={};t.r(p),t.d(p,{Any:()=>eW,Array:()=>eK,AsyncIterator:()=>eZ,Awaited:()=>aB,BigInt:()=>am,Boolean:()=>ac,Capitalize:()=>tO,Composite:()=>aW,Const:()=>a3,Constructor:()=>e3,ConstructorParameters:()=>a2,Date:()=>aG,Deref:()=>a5,Enum:()=>a9,Exclude:()=>function e(a,t,i={}){if(G(a))return eJ(e(aj(a),t),i);if($(a))return eJ(e0(function(a,t){var o=a.properties;let i={};for(let a of globalThis.Object.getOwnPropertyNames(o))i[a]=e(o[a],t);return i}(a,t)),i);return eJ(Z(a)?function(e,a){let t=e.filter(e=>tf(e,a)===o.False);return 1===t.length?t[0]:e6(t)}(a.anyOf,t):tf(a,t)!==o.False?e1():a,i)},Extends:()=>function e(a,t,i,n,r={}){return $(a)?e0(function(a,t,o,i,n){var r=a.properties;let s={};for(let a of globalThis.Object.getOwnPropertyNames(r))s[a]=e(r[a],t,o,i,n);return s}(a,t,i,n,r)):q(a)?eJ(function(a,t,o,i,n){var r;return e0((r=a.keys,r.reduce((a,r)=>({...a,...{[r]:e(au(r),t,o,i,n)}}),{})))}(a,t,i,n,r)):eJ(function(e,a,t,i){let n=tf(e,a);return n===o.Union?e6([t,i]):n===o.True?t:i}(a,t,i,n),r)},Extract:()=>function e(a,t,i={}){if(G(a))return eJ(e(aj(a),t),i);if($(a))return eJ(e0(function(a,t){var o=a.properties;let i={};for(let a of globalThis.Object.getOwnPropertyNames(o))i[a]=e(o[a],t);return i}(a,t)),i);return eJ(Z(a)?function(e,a){let t=e.filter(e=>tf(e,a)!==o.False);return 1===t.length?t[0]:e6(t)}(a.anyOf,t):tf(a,t)!==o.False?a:e1(),i)},Function:()=>e2,Index:()=>function e(a,t,o={}){return $(t)?eJ(e0(function(a,t,o){var i=t.properties;let n={};for(let t of Object.getOwnPropertyNames(i))n[t]=e(a,av(i[t]),o);return n}(a,t,o))):q(t)?eJ(function(a,t,o){var i;return e0((i=t.keys,i.reduce((t,i)=>({...t,...{[i]:e(a,[i],o)}}),{})))}(a,t,o)):X(t)?eJ(e9(aS(a,av(t))),o):eJ(e9(aS(a,t)),o)},InstanceType:()=>tV,Integer:()=>tb,Intersect:()=>aU,Iterator:()=>ax,KeyOf:()=>function e(a,t={}){if($(a))return e0(function(a,t){var o=a.properties;let i={};for(let a of globalThis.Object.getOwnPropertyNames(o))i[a]=e(o[a],t);return i}(a,t));return eJ(e9(a_(a).map(e=>"[number]"===e?ap():au(e))),t)},Literal:()=>au,Lowercase:()=>tz,Mapped:()=>aN,Never:()=>e1,Not:()=>tA,Null:()=>aJ,Number:()=>ap,Object:()=>aA,Omit:()=>function e(a,t,o={}){if(q(t))return function(a,t,o){var i;return e0((i=t.keys,i.reduce((t,i)=>({...t,...{[i]:e(a,[i],o)}}),{})))}(a,t,o);if($(a))return e0(function(a,t,o){var i=a.properties;let n={};for(let a of globalThis.Object.getOwnPropertyNames(i))n[a]=e(i[a],t,o);return n}(a,t,o));let i=X(t)?av(t):t,n=eX(a,[O,"$id","required"]),r=eJ(function e(a,t){var o,i,n;return R(a)?aU((o=a.allOf,o.map(a=>e(a,t)))):Z(a)?e6((i=a.anyOf,i.map(a=>e(a,t)))):_(a)?aA((n=a.properties,t.reduce((e,a)=>(function(e,a){let{[a]:t,...o}=e;return o})(e,a),n))):aA({})}(a,i),o);return{...n,...r}},Optional:()=>aL,Parameters:()=>tC,Partial:()=>function e(a,t={}){if($(a))return e0(function(a,t){var o=a.properties;let i={};for(let a of globalThis.Object.getOwnPropertyNames(o))i[a]=e(o[a],t);return i}(a,t));let o=eX(a,[O,"$id","required"]),i=eJ(tT(a),t);return{...o,...i}},Pick:()=>function e(a,t,o={}){if(q(t))return function(a,t,o){var i;return e0((i=t.keys,i.reduce((t,i)=>({...t,...{[i]:e(a,[i],o)}}),{})))}(a,t,o);if($(a))return e0(function(a,t,o){var i=a.properties;let n={};for(let a of globalThis.Object.getOwnPropertyNames(i))n[a]=e(i[a],t,o);return n}(a,t,o));let i=X(t)?av(t):t,n=eX(a,[O,"$id","required"]),r=eJ(function e(a,t){var o,i;return R(a)?aU((o=a.allOf,o.map(a=>e(a,t)))):Z(a)?e6((i=a.anyOf,i.map(a=>e(a,t)))):_(a)?aA(function(e,a){let t={};for(let o of a)o in e&&(t[o]=e[o]);return t}(a.properties,t)):aA({})}(a,i),o);return{...n,...r}},Promise:()=>aC,Readonly:()=>aP,ReadonlyOptional:()=>tI,Record:()=>tN,Recursive:()=>tR,Ref:()=>tM,RegExp:()=>tF,Required:()=>function e(a,t={}){if($(a))return e0(function(a,t){var o=a.properties;let i={};for(let a of globalThis.Object.getOwnPropertyNames(o))i[a]=e(o[a],t);return i}(a,t));{let e=eX(a,[O,"$id","required"]),o=eJ(tq(a),t);return{...e,...o}}},Rest:()=>t$,ReturnType:()=>tW,Strict:()=>tG,String:()=>ad,Symbol:()=>aK,TemplateLiteral:()=>aw,Transform:()=>t_,Tuple:()=>aT,Uint8Array:()=>aX,Uncapitalize:()=>tS,Undefined:()=>aZ,Union:()=>e6,Unknown:()=>a1,Unsafe:()=>tQ,Uppercase:()=>tx,Void:()=>tY});class d extends Error{constructor(e){super(e)}}function g(e){return Array.isArray(e)}function h(e){return"bigint"==typeof e}function k(e){return"boolean"==typeof e}function f(e){return e instanceof globalThis.Date}function y(e){return"number"==typeof e}function b(e){return"object"==typeof e&&null!==e}function j(e){return"string"==typeof e}function w(e){return e instanceof globalThis.Uint8Array}function v(e){return void 0===e}let O=Symbol.for("TypeBox.Transform"),z=Symbol.for("TypeBox.Readonly"),S=Symbol.for("TypeBox.Optional"),x=Symbol.for("TypeBox.Hint"),A=Symbol.for("TypeBox.Kind");function C(e){return b(e)&&"Optional"===e[S]}function P(e){return F(e,"Array")}function T(e){return F(e,"AsyncIterator")}function I(e){return F(e,"BigInt")}function E(e){return F(e,"Boolean")}function D(e){return F(e,"Constructor")}function N(e){return F(e,"Function")}function L(e){return F(e,"Integer")}function R(e){return F(e,"Intersect")}function M(e){return F(e,"Iterator")}function F(e,a){return b(e)&&A in e&&e[A]===a}function U(e){return F(e,"Literal")}function q(e){return F(e,"MappedKey")}function $(e){return F(e,"MappedResult")}function B(e){return F(e,"Never")}function H(e){return F(e,"Number")}function _(e){return F(e,"Object")}function Q(e){return F(e,"Promise")}function Y(e){return F(e,"Record")}function V(e){return F(e,"RegExp")}function W(e){return F(e,"String")}function G(e){return F(e,"TemplateLiteral")}function J(e){return b(e)&&O in e}function K(e){return F(e,"Tuple")}function Z(e){return F(e,"Union")}function X(e){return F(e,"Any")||P(e)||E(e)||I(e)||T(e)||D(e)||F(e,"Date")||N(e)||L(e)||R(e)||M(e)||U(e)||q(e)||$(e)||B(e)||F(e,"Not")||F(e,"Null")||H(e)||_(e)||Q(e)||Y(e)||F(e,"Ref")||V(e)||W(e)||F(e,"Symbol")||G(e)||F(e,"This")||K(e)||F(e,"Undefined")||Z(e)||F(e,"Uint8Array")||F(e,"Unknown")||F(e,"Unsafe")||F(e,"Void")||b(e)&&A in e&&j(e[A])}let ee=["Any","Array","AsyncIterator","BigInt","Boolean","Constructor","Date","Enum","Function","Integer","Intersect","Iterator","Literal","MappedKey","MappedResult","Not","Null","Number","Object","Promise","Record","Ref","RegExp","String","Symbol","TemplateLiteral","This","Tuple","Undefined","Union","Uint8Array","Unknown","Void"];function ea(e){try{return new RegExp(e),!0}catch{return!1}}function et(e){if(!j(e))return!1;for(let a=0;a<e.length;a++){let t=e.charCodeAt(a);if(t>=7&&t<=13||27===t||127===t)return!1}return!0}function eo(e){return v(e)||h(e)}function ei(e){return v(e)||y(e)}function en(e){return v(e)||k(e)}function er(e){return v(e)||j(e)}function es(e){return b(e)&&"Optional"===e[S]}function el(e){return ej(e,"Any")&&er(e.$id)}function eu(e){var a;return ej(e,"Array")&&"array"===e.type&&er(e.$id)&&eH(e.items)&&ei(e.minItems)&&ei(e.maxItems)&&en(e.uniqueItems)&&(v(a=e.contains)||eH(a))&&ei(e.minContains)&&ei(e.maxContains)}function ec(e){return ej(e,"AsyncIterator")&&"AsyncIterator"===e.type&&er(e.$id)&&eH(e.items)}function em(e){return ej(e,"BigInt")&&"bigint"===e.type&&er(e.$id)&&eo(e.exclusiveMaximum)&&eo(e.exclusiveMinimum)&&eo(e.maximum)&&eo(e.minimum)&&eo(e.multipleOf)}function ep(e){return ej(e,"Boolean")&&"boolean"===e.type&&er(e.$id)}function ed(e){return ej(e,"Constructor")&&"Constructor"===e.type&&er(e.$id)&&g(e.parameters)&&e.parameters.every(e=>eH(e))&&eH(e.returns)}function eg(e){return ej(e,"Date")&&"Date"===e.type&&er(e.$id)&&ei(e.exclusiveMaximumTimestamp)&&ei(e.exclusiveMinimumTimestamp)&&ei(e.maximumTimestamp)&&ei(e.minimumTimestamp)&&ei(e.multipleOfTimestamp)}function eh(e){return ej(e,"Function")&&"Function"===e.type&&er(e.$id)&&g(e.parameters)&&e.parameters.every(e=>eH(e))&&eH(e.returns)}function ek(e){return ej(e,"Integer")&&"integer"===e.type&&er(e.$id)&&ei(e.exclusiveMaximum)&&ei(e.exclusiveMinimum)&&ei(e.maximum)&&ei(e.minimum)&&ei(e.multipleOf)}function ef(e){return b(e)&&Object.entries(e).every(([e,a])=>et(e)&&eH(a))}function ey(e){var a;return ej(e,"Intersect")&&(!j(e.type)||"object"===e.type)&&g(e.allOf)&&e.allOf.every(e=>eH(e)&&!eR(e))&&er(e.type)&&(en(e.unevaluatedProperties)||v(a=e.unevaluatedProperties)||eH(a))&&er(e.$id)}function eb(e){return ej(e,"Iterator")&&"Iterator"===e.type&&er(e.$id)&&eH(e.items)}function ej(e,a){return b(e)&&A in e&&e[A]===a}function ew(e){return ez(e)&&j(e.const)}function ev(e){return ez(e)&&y(e.const)}function eO(e){return ez(e)&&k(e.const)}function ez(e){var a;return ej(e,"Literal")&&er(e.$id)&&(k(a=e.const)||y(a)||j(a))}function eS(e){return ej(e,"Never")&&b(e.not)&&0===Object.getOwnPropertyNames(e.not).length}function ex(e){return ej(e,"Not")&&eH(e.not)}function eA(e){return ej(e,"Null")&&"null"===e.type&&er(e.$id)}function eC(e){return ej(e,"Number")&&"number"===e.type&&er(e.$id)&&ei(e.exclusiveMaximum)&&ei(e.exclusiveMinimum)&&ei(e.maximum)&&ei(e.minimum)&&ei(e.multipleOf)}function eP(e){var a;return ej(e,"Object")&&"object"===e.type&&er(e.$id)&&ef(e.properties)&&(en(a=e.additionalProperties)||eH(a))&&ei(e.minProperties)&&ei(e.maxProperties)}function eT(e){return ej(e,"Promise")&&"Promise"===e.type&&er(e.$id)&&eH(e.item)}function eI(e){var a;return ej(e,"Record")&&"object"===e.type&&er(e.$id)&&(en(a=e.additionalProperties)||eH(a))&&b(e.patternProperties)&&(e=>{let a=Object.getOwnPropertyNames(e.patternProperties);return 1===a.length&&ea(a[0])&&b(e.patternProperties)&&eH(e.patternProperties[a[0]])})(e)}function eE(e){return ej(e,"RegExp")&&er(e.$id)&&j(e.source)&&j(e.flags)&&ei(e.maxLength)&&ei(e.minLength)}function eD(e){var a,t;return ej(e,"String")&&"string"===e.type&&er(e.$id)&&ei(e.minLength)&&ei(e.maxLength)&&(v(a=e.pattern)||j(a)&&et(a)&&ea(a))&&(v(t=e.format)||j(t)&&et(t))}function eN(e){return ej(e,"Symbol")&&"symbol"===e.type&&er(e.$id)}function eL(e){return ej(e,"TemplateLiteral")&&"string"===e.type&&j(e.pattern)&&"^"===e.pattern[0]&&"$"===e.pattern[e.pattern.length-1]}function eR(e){return b(e)&&O in e}function eM(e){return ej(e,"Tuple")&&"array"===e.type&&er(e.$id)&&y(e.minItems)&&y(e.maxItems)&&e.minItems===e.maxItems&&(v(e.items)&&v(e.additionalItems)&&0===e.minItems||g(e.items)&&e.items.every(e=>eH(e)))}function eF(e){return ej(e,"Undefined")&&"undefined"===e.type&&er(e.$id)}function eU(e){return ej(e,"Union")&&er(e.$id)&&b(e)&&g(e.anyOf)&&e.anyOf.every(e=>eH(e))}function eq(e){return ej(e,"Uint8Array")&&"Uint8Array"===e.type&&er(e.$id)&&ei(e.minByteLength)&&ei(e.maxByteLength)}function e$(e){return ej(e,"Unknown")&&er(e.$id)}function eB(e){return ej(e,"Void")&&"void"===e.type&&er(e.$id)}function eH(e){return b(e)&&(el(e)||eu(e)||ep(e)||em(e)||ec(e)||ed(e)||eg(e)||eh(e)||ek(e)||ey(e)||eb(e)||ez(e)||ej(e,"MappedKey")&&g(e.keys)&&e.keys.every(e=>y(e)||j(e))||ej(e,"MappedResult")&&ef(e.properties)||eS(e)||ex(e)||eA(e)||eC(e)||eP(e)||eT(e)||eI(e)||ej(e,"Ref")&&er(e.$id)&&j(e.$ref)||eE(e)||eD(e)||eN(e)||eL(e)||ej(e,"This")&&er(e.$id)&&j(e.$ref)||eM(e)||eF(e)||eU(e)||eq(e)||e$(e)||ej(e,"Unsafe")||eB(e)||b(e)&&A in e&&j(e[A])&&!ee.includes(e[A]))}let e_=new Map;function eQ(e,a){e_.set(e,a)}let eY=new Map;function eV(e){return eY.has(e)}function eW(e={}){return{...e,[A]:"Any"}}function eG(e){return e.map(e=>eJ(e))}function eJ(e,a={}){return{...function e(a){return g(a)?a.map(a=>e(a)):f(a)?new Date(a.getTime()):w(a)?new Uint8Array(a):a instanceof globalThis.RegExp?new RegExp(a.source,a.flags):b(a)?function(a){let t={};for(let o of Object.getOwnPropertyNames(a))t[o]=e(a[o]);for(let o of Object.getOwnPropertySymbols(a))t[o]=e(a[o]);return t}(a):a}(e),...a}}function eK(e,a={}){return{...a,[A]:"Array",type:"array",items:eJ(e)}}function eZ(e,a={}){return{...a,[A]:"AsyncIterator",type:"AsyncIterator",items:eJ(e)}}function eX(e,a){return a.reduce((e,a)=>(function(e,a){let{[a]:t,...o}=e;return o})(e,a),e)}function e1(e={}){return{...e,[A]:"Never",not:{}}}function e0(e){return{[A]:"MappedResult",properties:e}}function e3(e,a,t){return{...t,[A]:"Constructor",type:"Constructor",parameters:eG(e),returns:eJ(a)}}function e2(e,a,t){return{...t,[A]:"Function",type:"Function",parameters:eG(e),returns:eJ(a)}}function e4(e,a){return{...a,[A]:"Union",anyOf:eG(e)}}function e5(e){return e.map(e=>C(e)?eX(e,[S]):e)}function e9(e,a={}){var t,o;return 0===e.length?e1(a):1===e.length?eJ(e[0],a):(t=e,o=a,t.some(e=>C(e))?aL(e4(e5(t),o)):e4(e5(t),o))}function e6(e,a={}){return 0===e.length?e1(a):1===e.length?eJ(e[0],a):e4(e,a)}class e7 extends d{}function e8(e,a,t){return e[a]===t&&92!==e.charCodeAt(a-1)}function ae(e,a){return e8(e,a,"(")}function aa(e,a){return e8(e,a,")")}function at(e){return function e(a){return!function(e){if(!(ae(e,0)&&aa(e,e.length-1)))return!1;let a=0;for(let t=0;t<e.length;t++)if(ae(e,t)&&(a+=1),aa(e,t)&&(a-=1),0===a&&t!==e.length-1)return!1;return!0}(a)?!function(e){let a=0;for(let t=0;t<e.length;t++)if(ae(e,t)&&(a+=1),aa(e,t)&&(a-=1),e8(e,t,"|")&&0===a)return!0;return!1}(a)?!function(e){for(let a=0;a<e.length;a++)if(ae(e,a))return!0;return!1}(a)?{type:"const",const:a.replace(/\\\$/g,"$").replace(/\\\*/g,"*").replace(/\\\^/g,"^").replace(/\\\|/g,"|").replace(/\\\(/g,"(").replace(/\\\)/g,")")}:function(a){let t=[];for(let o=0;o<a.length;o++)if(ae(a,o)){let[i,n]=function(e,a){if(!ae(e,a))throw new e7("TemplateLiteralParser: Index must point to open parens");let t=0;for(let o=a;o<e.length;o++)if(ae(e,o)&&(t+=1),aa(e,o)&&(t-=1),0===t)return[a,o];throw new e7("TemplateLiteralParser: Unclosed group parens in expression")}(a,o),r=a.slice(i,n+1);t.push(e(r)),o=n}else{let[i,n]=function(e,a){for(let t=a;t<e.length;t++)if(ae(e,t))return[a,t];return[a,e.length]}(a,o),r=a.slice(i,n);r.length>0&&t.push(e(r)),o=n-1}return 0===t.length?{type:"const",const:""}:1===t.length?t[0]:{type:"and",expr:t}}(a):function(a){let[t,o]=[0,0],i=[];for(let n=0;n<a.length;n++)if(ae(a,n)&&(t+=1),aa(a,n)&&(t-=1),e8(a,n,"|")&&0===t){let t=a.slice(o,n);t.length>0&&i.push(e(t)),o=n+1}let n=a.slice(o);return(n.length>0&&i.push(e(n)),0===i.length)?{type:"const",const:""}:1===i.length?i[0]:{type:"or",expr:i}}(a):e(a.slice(1,a.length-1))}(e.slice(1,e.length-1))}class ao extends d{}function ai(e){return("or"!==e.type||2!==e.expr.length||"const"!==e.expr[0].type||"0"!==e.expr[0].const||"const"!==e.expr[1].type||"[1-9][0-9]*"!==e.expr[1].const)&&("const"!==e.type||".*"!==e.const)&&("or"===e.type&&2===e.expr.length&&"const"===e.expr[0].type&&"true"===e.expr[0].const&&"const"===e.expr[1].type&&"false"===e.expr[1].const||("and"===e.type?e.expr.every(e=>ai(e)):"or"===e.type?e.expr.every(e=>ai(e)):"const"===e.type||(()=>{throw new ao("Unknown expression type")})()))}function an(e){return ai(at(e.pattern))}class ar extends d{}function*as(e){return"and"===e.type?yield*function*(e){return yield*function* e(a){if(1===a.length)return yield*a[0];for(let t of a[0])for(let o of e(a.slice(1)))yield`${t}${o}`}(e.expr.map(e=>[...as(e)]))}(e):"or"===e.type?yield*function*(e){for(let a of e.expr)yield*as(a)}(e):"const"===e.type?yield*function*(e){return yield e.const}(e):(()=>{throw new ar("Unknown expression")})()}function al(e){let a=at(e.pattern);return ai(a)?[...as(a)]:[]}function au(e,a={}){return{...a,[A]:"Literal",const:e,type:typeof e}}function ac(e={}){return{...e,[A]:"Boolean",type:"boolean"}}function am(e={}){return{...e,[A]:"BigInt",type:"bigint"}}function ap(e={}){return{...e,[A]:"Number",type:"number"}}function ad(e={}){return{...e,[A]:"String",type:"string"}}let ag="(0|[1-9][0-9]*)",ah="(.*)",ak=`^${ag}$`,af=`^${ah}$`;class ay extends d{}function ab(e){return`^${e.map(e=>(function e(a,t){return G(a)?a.pattern.slice(1,a.pattern.length-1):Z(a)?`(${a.anyOf.map(a=>e(a,t)).join("|")})`:H(a)||L(a)||I(a)?`${t}${ag}`:W(a)?`${t}${ah}`:U(a)?`${t}${a.const.toString().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}`:E(a)?`${t}(true|false)`:(()=>{throw new ay(`Unexpected Kind '${a[A]}'`)})()})(e,"")).join("")}$`}function aj(e){return e9(al(e).map(e=>au(e)))}function aw(e,a={}){let t=j(e)?ab([...function* e(a){for(let t=0;t<a.length;t++)if("$"===a[t]){let o=au(a.slice(0,t)),i=function*(a){if("{"!==a[1]){let t=au("$"),o=e(a.slice(1));return yield*[t,...o]}for(let t=2;t<a.length;t++)if("}"===a[t]){let o=function*(e){let a=e.trim().replace(/"|'/g,"");return"boolean"===a?yield ac():"number"===a?yield ap():"bigint"===a?yield am():"string"===a?yield ad():yield(()=>{let e=a.split("|").map(e=>au(e.trim()));return 0===e.length?e1():1===e.length?e[0]:e9(e)})()}(a.slice(2,t)),i=e(a.slice(t+1));return yield*[...o,...i]}yield au(a)}(a.slice(t));return yield*[o,...i]}yield au(a)}(e)]):ab(e);return{...a,[A]:"TemplateLiteral",type:"string",pattern:t}}function av(e){return[...new Set(G(e)?al(e).map(e=>e.toString()):Z(e)?function(e){let a=[];for(let t of e)a.push(...av(t));return a}(e.anyOf):U(e)?[e.const.toString()]:H(e)||L(e)?["[number]"]:[])]}function aO(e,a){return e.map(e=>az(e,a))}function az(e,a){var t,o,i,n,r;return R(e)?(r=e.allOf,aF(aO(r,a).filter(e=>!B(e)))):Z(e)?e9((t=aO(e.anyOf,a)).some(e=>B(e))?[]:t):K(e)?a in(o=e.items??[])?o[a]:"[number]"===a?e9(o):e1():P(e)?(i=e.items,"[number]"===a?i:e1()):_(e)&&a in(n=e.properties)?n[a]:e1()}function aS(e,a){return a.map(a=>az(e,a))}function ax(e,a={}){return{...a,[A]:"Iterator",type:"Iterator",items:eJ(e)}}let aA=function(e,a={}){let t=globalThis.Object.getOwnPropertyNames(e),o=t.filter(a=>C(e[a])),i=t.filter(e=>!o.includes(e)),n=X(a.additionalProperties)?{additionalProperties:eJ(a.additionalProperties)}:{},r={};for(let a of t)r[a]=eJ(e[a]);return i.length>0?{...a,...n,[A]:"Object",type:"object",properties:r,required:i}:{...a,...n,[A]:"Object",type:"object",properties:r}};function aC(e,a={}){return{...a,[A]:"Promise",type:"Promise",item:eJ(e)}}function aP(e,a){var t;let o=a??!0;return $(e)?e0(function(e,a){var t=e.properties;let o={};for(let e of globalThis.Object.getOwnPropertyNames(t))o[e]=aP(t[e],a);return o}(e,o)):(t=e,!1===o?eX(eJ(t),[z]):{...eJ(t),[z]:"Readonly"})}function aT(e,a={}){let[t,o,i]=[!1,e.length,e.length];return e.length>0?{...a,[A]:"Tuple",type:"array",items:eG(e),additionalItems:t,minItems:o,maxItems:i}:{...a,[A]:"Tuple",type:"array",minItems:o,maxItems:i}}function aI(e,a){return e in a?aD(e,a[e]):e0(a)}function aE(e,a){return a.map(a=>aD(e,a))}function aD(e,a){return C(a)?aL(aD(e,eX(a,[S]))):b(a)&&"Readonly"===a[z]?aP(aD(e,eX(a,[z]))):$(a)?aI(e,a.properties):q(a)?function(e,a){let t=a.includes(e)?{[e]:au(e)}:function(e){let a={};for(let t of e)a[t]=au(t);return a}(a);return aI(e,t)}(e,a.keys):D(a)?e3(aE(e,a.parameters),aD(e,a.returns)):N(a)?e2(aE(e,a.parameters),aD(e,a.returns)):T(a)?eZ(aD(e,a.items)):M(a)?ax(aD(e,a.items)):R(a)?aU(aE(e,a.allOf)):Z(a)?e6(aE(e,a.anyOf)):K(a)?aT(aE(e,a.items??[])):_(a)?aA(function(e,a){let t={};for(let o of globalThis.Object.getOwnPropertyNames(a))t[o]=aD(e,a[o]);return t}(e,a.properties)):P(a)?eK(aD(e,a.items)):Q(a)?aC(aD(e,a.item)):a}function aN(e,a,t={}){let o=X(e)?av(e):e,i=a({[A]:"MappedKey",keys:o});return eJ(aA(function(e,a){let t={};for(let o of e)t[o]=aD(o,a);return t}(o,i)),t)}function aL(e,a){var t;let o=a??!0;return $(e)?e0(function(e,a){var t=e.properties;let o={};for(let e of globalThis.Object.getOwnPropertyNames(t))o[e]=aL(t[e],a);return o}(e,o)):(t=e,!1===o?eX(eJ(t),[S]):{...eJ(t),[S]:"Optional"})}function aR(e,a){let t=e.every(e=>_(e)),o=X(a.unevaluatedProperties)?{unevaluatedProperties:eJ(a.unevaluatedProperties)}:{};return!1===a.unevaluatedProperties||X(a.unevaluatedProperties)||t?{...a,...o,[A]:"Intersect",type:"object",allOf:eG(e)}:{...a,...o,[A]:"Intersect",allOf:eG(e)}}function aM(e){return e.map(e=>C(e)?eX(e,[S]):e)}function aF(e,a={}){if(0===e.length)return e1(a);if(1===e.length)return eJ(e[0],a);if(e.some(e=>J(e)))throw Error("Cannot intersect transform types");return e.every(e=>C(e))?aL(aR(aM(e),a)):aR(aM(e),a)}function aU(e,a={}){if(0===e.length)return e1(a);if(1===e.length)return eJ(e[0],a);if(e.some(e=>J(e)))throw Error("Cannot intersect transform types");return aR(e,a)}function aq(e){return e.map(e=>a$(e))}function a$(e){return R(e)?aU(aq(e.allOf)):Z(e)?e6(aq(e.anyOf)):Q(e)?a$(e.item):e}function aB(e,a={}){return eJ(a$(e),a)}function aH(e){let a=[];for(let t of e)a.push(a_(t));return a}function a_(e){var a,t,o,i,n,r;return R(e)?function(e){let a=[];for(let t of e)a.push(...t);return a}(aH(e.allOf)):Z(e)?1===(a=aH(e.anyOf)).length?a[0]:a.length>1?(t=a.slice(1),o=a[0],t.reduce((e,a)=>e.filter(e=>a.includes(e)),o)):[]:K(e)?(e.items??[]).map((e,a)=>a.toString()):P(e)?(e.items,["[number]"]):_(e)?(n=e.properties,globalThis.Object.getOwnPropertyNames(n)):Y(e)?(r=e.patternProperties,aQ?globalThis.Object.getOwnPropertyNames(r).map(e=>"^"===e[0]&&"$"===e[e.length-1]?e.slice(1,e.length-1):e):[]):[]}let aQ=!1;function aY(e){aQ=!0;let a=a_(e);aQ=!1;let t=a.map(e=>`(${e})`);return`^(${t.join("|")})$`}function aV(e){let a=a_(e),t=aS(e,a);return a.map((e,o)=>[a[o],t[o]])}function aW(e,a={}){let t=function(e){let a=[];for(let t of e)a.push(...a_(t));return[...new Set(a)]}(e);return aA(function(e,a){let t={};for(let o of a)t[o]=aF(function(e,a){let t=[];for(let o of e)t.push(...aS(o,[a]));return t.filter(e=>!B(e))}(e,o));return t}(e,t),a)}function aG(e={}){return{...e,[A]:"Date",type:"Date"}}function aJ(e={}){return{...e,[A]:"Null",type:"null"}}function aK(e){return{...e,[A]:"Symbol",type:"symbol"}}function aZ(e={}){return{...e,[A]:"Undefined",type:"undefined"}}function aX(e={}){return{...e,[A]:"Uint8Array",type:"Uint8Array"}}function a1(e={}){return{...e,[A]:"Unknown"}}function a0(e,a){return!0===a?e:aP(e)}function a3(e,a={}){return eJ(function e(a,t){return b(a)&&!g(a)&&!w(a)&&Symbol.asyncIterator in a||b(a)&&!g(a)&&!w(a)&&Symbol.iterator in a?a0(eW(),t):g(a)?aP(aT(a.map(a=>e(a,!1)))):w(a)?aX():f(a)?aG():b(a)?a0(aA(function(a){let t={};for(let o of globalThis.Object.getOwnPropertyNames(a))t[o]=aP(e(a[o],!1));return t}(a)),t):"function"==typeof a?a0(e2([],a1()),t):v(a)?aZ():null===a?aJ():"symbol"==typeof a?aK():h(a)?am():y(a)||k(a)||j(a)?au(a):aA({})}(e,!0),a)}function a2(e,a={}){return aT(eG(e.parameters),{...a})}function a4(e,a){return e.map(e=>a5(e,a))}function a5(e,a){return function(e,a){return D(e)||N(e)?(e.parameters=a4(e.parameters,a),e.returns=a5(e.returns,a),e):R(e)?(e.allOf=a4(e.allOf,a),e):Z(e)?(e.anyOf=a4(e.anyOf,a),e):K(e)?(v(e.items)||(e.items=a4(e.items,a)),e):P(e)?(e.items=a5(e.items,a),e):_(e)?(e.properties=function(e,a){let t={};for(let o of globalThis.Object.getOwnPropertyNames(e))t[o]=a5(e[o],a);return t}(e.properties,a),e):Q(e)?(e.item=a5(e.item,a),e):T(e)||M(e)?(e.items=a5(e.items,a),e):F(e,"Ref")?function(e,a){let t=a.find(a=>a.$id===e.$ref);if(void 0===t)throw Error(`Unable to dereference schema with $id ${e.$ref}`);return a5(eX(t,["$id"]),a)}(e,a):e}(eJ(e),eG(a))}function a9(e,a={}){if(v(e))throw Error("Enum undefined or empty");return e6([...new Set(globalThis.Object.getOwnPropertyNames(e).filter(e=>isNaN(e)).map(a=>e[a]))].map(e=>au(e)),{...a,[x]:"Enum"})}class a6 extends d{}function a7(e){return e===o.False?e:o.True}function a8(e){throw new a6(e)}function te(e){return eS(e)||ey(e)||eU(e)||e$(e)||el(e)}function ta(e,a){var t,i,n,r,s,l;return eS(a)?(t=0,i=0,o.False):ey(a)?ti(e,a):eU(a)?tk(e,a):e$(a)?(n=0,r=0,o.True):el(a)?(s=0,l=0,o.True):a8("StructuralRight")}function tt(e,a){return eO(e)||ep(e)?o.True:o.False}function to(e,a){return ez(e)&&y(e.const)||eC(e)||ek(e)?o.True:o.False}function ti(e,a){return a.allOf.every(a=>tf(e,a)===o.True)?o.True:o.False}!function(e){e[e.Union=0]="Union",e[e.True=1]="True",e[e.False=2]="False"}(o||(o={}));function tn(e){let[a,t]=[e,0];for(;ex(a);)a=a.not,t+=1;return t%2==0?a:a1()}function tr(e,a){return ev(e)||eC(e)||ek(e)?o.True:o.False}function ts(e,a){return Object.getOwnPropertyNames(e.properties).length===a}function tl(e){return ts(e,0)||ts(e,1)&&"description"in e.properties&&eU(e.properties.description)&&2===e.properties.description.anyOf.length&&(eD(e.properties.description.anyOf[0])&&eF(e.properties.description.anyOf[1])||eD(e.properties.description.anyOf[1])&&eF(e.properties.description.anyOf[0]))}function tu(e){let a=ap();return ts(e,0)||ts(e,1)&&"length"in e.properties&&a7(tf(e.properties.length,a))===o.True}function tc(e,a){return tf(e,a)===o.False||es(e)&&!es(a)?o.False:o.True}function tm(e,a){return e$(e)?o.False:el(e)?o.Union:eS(e)||ew(e)&&tu(a)||ev(e)&&ts(a,0)||eO(e)&&ts(a,0)||eN(e)&&tl(a)||em(e)&&ts(a,0)||eD(e)&&tu(a)||eN(e)&&tl(a)||eC(e)&&ts(a,0)||ek(e)&&ts(a,0)||ep(e)&&ts(a,0)||eq(e)&&tu(a)||eg(e)&&ts(a,0)||ed(e)&&ts(a,0)||eh(e)&&function(e){let a=ap();return ts(e,0)||ts(e,1)&&"length"in e.properties&&a7(tf(e.properties.length,a))===o.True}(a)?o.True:eI(e)&&eD(tp(e))?"Record"===a[x]?o.True:o.False:eI(e)&&eC(tp(e))&&ts(a,0)?o.True:o.False}function tp(e){return ak in e.patternProperties?ap():af in e.patternProperties?ad():a8("Unknown record key pattern")}function td(e){return ak in e.patternProperties?e.patternProperties[ak]:af in e.patternProperties?e.patternProperties[af]:a8("Unable to get record value schema")}function tg(e,a){let[t,i]=[tp(a),td(a)];return ew(e)&&eC(t)&&a7(tf(e,i))===o.True?o.True:eq(e)&&eC(t)||eD(e)&&eC(t)||eu(e)&&eC(t)?tf(e,i):eP(e)?(()=>{for(let a of Object.getOwnPropertyNames(e.properties))if(tc(i,e.properties[a])===o.False)return o.False;return o.True})():o.False}function th(e,a){return ez(e)&&j(e.const)||eD(e)?o.True:o.False}function tk(e,a){return a.anyOf.some(a=>tf(e,a)===o.True)?o.True:o.False}function tf(e,a){var t,i,n,r;return eL(e)||eL(a)?eL(e)?tf(aj(e),a):eL(a)?tf(e,aj(a)):a8("Invalid fallthrough for TemplateLiteral"):eE(e)||eE(a)?(t=e,i=a,tf(eE(t)?ad():t,eE(i)?ad():i)):ex(e)||ex(a)?ex(e)?tf(tn(e),a):ex(a)?tf(e,tn(a)):a8("Invalid fallthrough for Not"):el(e)?ey(a)?ti(e,a):eU(a)&&a.anyOf.some(e=>el(e)||e$(e))?o.True:eU(a)?o.Union:e$(a)||el(a)?o.True:o.Union:eu(e)?eP(a)&&tu(a)?o.True:te(a)?ta(e,a):eu(a)?a7(tf(e.items,a.items)):o.False:em(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):em(a)?o.True:o.False:ep(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):ep(a)?o.True:o.False:ec(e)?te(a)?ta(e,a):ec(a)?a7(tf(e.items,a.items)):o.False:ed(e)?te(a)?ta(e,a):eP(a)?tm(e,a):ed(a)?e.parameters.length>a.parameters.length?o.False:e.parameters.every((e,t)=>a7(tf(a.parameters[t],e))===o.True)?a7(tf(e.returns,a.returns)):o.False:o.False:eg(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eg(a)?o.True:o.False:eh(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eh(a)?e.parameters.length>a.parameters.length?o.False:e.parameters.every((e,t)=>a7(tf(a.parameters[t],e))===o.True)?a7(tf(e.returns,a.returns)):o.False:o.False:ek(e)?ek(a)||eC(a)?o.True:te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):o.False:ey(e)?e.allOf.some(e=>tf(e,a)===o.True)?o.True:o.False:eb(e)?te(a)?ta(e,a):eb(a)?a7(tf(e.items,a.items)):o.False:ez(e)?ez(a)&&a.const===e.const?o.True:te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eD(a)?th(e,a):eC(a)?tr(e,a):ek(a)?to(e,a):ep(a)?tt(e,a):o.False:eS(e)?o.True:eA(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eA(a)?o.True:o.False:eC(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):ek(a)||eC(a)?o.True:o.False:eP(e)?te(a)?ta(e,a):eI(a)?tg(e,a):eP(a)?(()=>{for(let t of Object.getOwnPropertyNames(a.properties)){if(!(t in e.properties)&&!es(a.properties[t]))return o.False;if(es(a.properties[t]))break;if(tc(e.properties[t],a.properties[t])===o.False)return o.False}return o.True})():o.False:eI(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tf(td(e),td(a)):o.False:eD(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eD(a)?o.True:o.False:eN(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eN(a)?o.True:o.False:eM(e)?te(a)?ta(e,a):eP(a)&&tu(a)||eu(a)&&eu(a)&&void 0!==e.items&&e.items.every(e=>tf(e,a.items)===o.True)?o.True:eM(a)?v(e.items)&&!v(a.items)||!v(e.items)&&v(a.items)?o.False:v(e.items)&&!v(a.items)||e.items.every((e,t)=>tf(e,a.items[t])===o.True)?o.True:o.False:o.False:eT(e)?te(a)?ta(e,a):eP(a)&&function(e){let a=e2([eW()],eW());return ts(e,0)||ts(e,1)&&"then"in e.properties&&a7(tf(e.properties.then,a))===o.True}(a)?o.True:eT(a)?a7(tf(e.item,a.item)):o.False:eq(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eq(a)?o.True:o.False:eF(e)?te(a)?ta(e,a):eP(a)?tm(e,a):eI(a)?tg(e,a):eB(a)?(r=0,eF(n=e)||eF(n)?o.True:o.False):eF(a)?o.True:o.False:eU(e)?e.anyOf.every(e=>tf(e,a)===o.True)?o.True:o.False:e$(e)?eS(a)?o.False:ey(a)?ti(e,a):eU(a)?tk(e,a):el(a)?o.True:eD(a)?th(e,a):eC(a)?tr(e,a):ek(a)?to(e,a):ep(a)?tt(e,a):eu(a)?e$(e)?o.False:el(e)?o.Union:eS(e)?o.True:o.False:eM(a)?eS(e)?o.True:e$(e)?o.False:el(e)?o.Union:o.False:eP(a)?tm(e,a):e$(a)?o.True:o.False:eB(e)?ey(a)?ti(e,a):eU(a)?tk(e,a):e$(a)||el(a)?o.True:eP(a)?tm(e,a):eB(a)?o.True:o.False:a8(`Unknown left type operand '${e[A]}'`)}function ty(e){return"Intersect"===e[A]?e.allOf.every(e=>ty(e)):"Union"===e[A]?e.anyOf.some(e=>ty(e)):"Not"===e[A]?!ty(e.not):"Undefined"===e[A]}function tb(e={}){return{...e,[A]:"Integer",type:"integer"}}function tj(e,a){return"string"==typeof e?"Uncapitalize"===a?function(e){let[a,t]=[e.slice(0,1),e.slice(1)];return[a.toLowerCase(),t].join("")}(e):"Capitalize"===a?function(e){let[a,t]=[e.slice(0,1),e.slice(1)];return[a.toUpperCase(),t].join("")}(e):"Uppercase"===a?e.toUpperCase():"Lowercase"===a?e.toLowerCase():e:e.toString()}function tw(e,a){return e.map(e=>tv(e,a))}function tv(e,a,t={}){var o;return q(e)?e0((o=e.keys,o.reduce((e,o)=>({...e,...{[o]:tv(au(o),a,t)}}),{}))):G(e)?function(e,a,t){let o=at(e.pattern);return ai(o)?aw([e6(tw([...as(o)].map(e=>au(e)),a))],t):{...e,pattern:tj(e.pattern,a)}}(e,a,e):Z(e)?e6(tw(e.anyOf,a),t):U(e)?au(tj(e.const,a),t):e}function tO(e,a={}){return tv(e,"Capitalize",a)}function tz(e,a={}){return tv(e,"Lowercase",a)}function tS(e,a={}){return tv(e,"Uncapitalize",a)}function tx(e,a={}){return tv(e,"Uppercase",a)}function tA(e,a){return{...a,[A]:"Not",not:eJ(e)}}function tC(e,a={}){return aT(eG(e.parameters),{...a})}function tP(e){return e.map(e=>tT(e))}function tT(e){return R(e)?aU(tP(e.allOf)):Z(e)?e6(tP(e.anyOf)):_(e)?aA(function(e){let a={};for(let t of globalThis.Object.getOwnPropertyNames(e))a[t]=aL(e[t]);return a}(e.properties)):aA({})}function tI(e){return aP(aL(e))}function tE(e,a,t){return{...t,[A]:"Record",type:"object",patternProperties:{[e]:eJ(a)}}}function tD(e,a,t){let o={};for(let t of e)o[t]=eJ(a);return aA(o,{...t,[x]:"Record"})}function tN(e,a,t={}){var o,i;return Z(e)?(o=e.anyOf,tD(av(e6(o)),a,t)):G(e)?an(e)?tD(av(e),a,t):tE(e.pattern,a,t):U(e)?(i=e.const,tD([i.toString()],a,t)):L(e)||H(e)?tE(ak,a,t):V(e)?tE(e.source,a,t):W(e)?tE(v(e.pattern)?af:e.pattern,a,t):F(e,"Any")?tE(af,a,t):B(e)?tE("^(?!.*)$",a,t):e1(t)}let tL=0;function tR(e,a={}){v(a.$id)&&(a.$id=`T${tL++}`);let t=e({[A]:"This",$ref:`${a.$id}`});return t.$id=a.$id,eJ({...a,[x]:"Recursive",...t})}function tM(e,a={}){if(j(e))return{...a,[A]:"Ref",$ref:e};if(v(e.$id))throw Error("Reference target type must specify an $id");return{...a,[A]:"Ref",$ref:e.$id}}function tF(e,a={}){let t=j(e)?new globalThis.RegExp(e):e;return{...a,[A]:"RegExp",type:"RegExp",source:t.source,flags:t.flags}}function tU(e){return e.map(e=>tq(e))}function tq(e){return R(e)?aU(tU(e.allOf)):Z(e)?e6(tU(e.anyOf)):_(e)?aA(function(e){let a={};for(let t of globalThis.Object.getOwnPropertyNames(e))a[t]=eX(e[t],[S]);return a}(e.properties)):aA({})}function t$(e){return eG(R(e)?eG(e.allOf):Z(e)?eG(e.anyOf):K(e)?eG(e.items??[]):[])}class tB{constructor(e){this.schema=e}Decode(e){return new tH(this.schema,e)}}class tH{constructor(e,a){this.schema=e,this.decode=a}EncodeTransform(e,a){return{...a,[O]:{Encode:t=>a[O].Encode(e(t)),Decode:e=>this.decode(a[O].Decode(e))}}}EncodeSchema(e,a){let t={Decode:this.decode,Encode:e};return{...a,[O]:t}}Encode(e){let a=eJ(this.schema);return J(a)?this.EncodeTransform(e,a):this.EncodeSchema(e,a)}}function t_(e){return new tB(e)}function tQ(e={}){return{...e,[A]:e[A]??"Unsafe"}}function tY(e={}){return{...e,[A]:"Void",type:"void"}}function tV(e,a={}){return eJ(e.returns,a)}function tW(e,a={}){return eJ(e.returns,a)}function tG(e){return JSON.parse(JSON.stringify(e))}let tJ=p.Object({concurrency:p.Optional(p.Number()),timeout:p.Optional(p.Number())},{additionalProperties:!1,title:"QueueOptions"});class tK{constructor(e={}){this.concurrency=1,this._running=0,this._queue=[],e.concurrency&&(this.concurrency=e.concurrency)}runNext(){let e=this._queue.shift();e&&(this._running++,e.func().then(a=>e.resolve(a)).catch(a=>e.reject(a)).finally(()=>{this._running--,this.checkQueue()}))}checkQueue(){this._running<this.concurrency&&this.runNext()}add(e){return new Promise((a,t)=>{this._queue.push({func:e,resolve:a,reject:t}),this.checkQueue()})}}class tZ extends Error{constructor(){super(...arguments),this.name="BadRequestError"}}class tX extends Error{constructor(){super(...arguments),this.name="HTTPError"}}class t1 extends Error{constructor(){super(...arguments),this.name="InvalidOptionsError"}}class t0 extends Error{constructor(){super(...arguments),this.name="NoEnvironmentError"}}class t3 extends Error{constructor(e,{result:a,errors:t}){super(e),this.name="FailedYahooValidationError",this.result=a,this.errors=t}}let t2={BadRequestError:tZ,HTTPError:tX,InvalidOptionsError:t1,NoEnvironmentError:t0,FailedYahooValidationError:t3},t4=JSON.parse('{"UU":"yahoo-finance2","rE":"2.13.3","Jk":"https://github.com/gadicc/node-yahoo-finance2"}');var t5=t(93520);class t9 extends t5.cP{async setFromSetCookieHeaders(e,a){let t;if(void 0===e||(e instanceof Array?t=e.map(e=>t5.Hk.parse(e)):"string"==typeof e&&(t=[t5.Hk.parse(e)])),t)for(let e of t)e instanceof t5.Hk&&await this.setCookie(e,a)}}let t6=p.Object({info:p.Function([],p.Void()),warn:p.Function([],p.Void()),error:p.Function([],p.Void()),debug:p.Function([],p.Void())}),t7=p.Object({logErrors:p.Optional(p.Boolean()),logOptionsErrors:p.Optional(p.Boolean()),_internalThrowOnAdditionalProperties:p.Optional(p.Boolean({default:!1,description:"Use this property to throw when properties beyond what is explicitly specified in the schema are provided. It is an internal option and subject to change, use at your own risk"}))}),t8=p.Object({YF_QUERY_HOST:p.Optional(p.String()),cookieJar:p.Optional(p.Any()),queue:p.Optional(tJ),validation:p.Optional(t7),logger:p.Optional(t6)},{title:"YahooFinanceOptions"}),oe={YF_QUERY_HOST:process.env.YF_QUERY_HOST||"query2.finance.yahoo.com",cookieJar:new t9,queue:{concurrency:4,timeout:60},validation:{logErrors:!0,logOptionsErrors:!0},logger:{info:(...e)=>console.log(...e),warn:(...e)=>console.warn(...e),error:(...e)=>console.error(...e),debug:(...e)=>console.log(...e)}},oa=oe.logger||console,ot={yahooSurvey:{id:"yahooSurvey",text:"Please consider completing the survey at https://bit.ly/yahoo-finance-api-feedback if you haven't already; for more info see https://github.com/gadicc/node-yahoo-finance2/issues/764#issuecomment-2056623851.",onceOnly:!0},ripHistorical:{id:"ripHistorical",text:"[Deprecated] historical() relies on an API that Yahoo have removed.  We'll map this request to chart() for convenience, but, please consider using chart() directly instead; for more info see https://github.com/gadicc/node-yahoo-finance2/issues/795.",level:"warn",onceOnly:!0}};function oo(e){let a=ot[e];if(!a)throw Error(`Unknown notice id: ${e}`);if(a.suppress)return;a.onceOnly&&(a.suppress=!0);let t=a.text+(a.onceOnly?"  This will only be shown once, but you":"You")+" can suppress this message in future with `yahooFinance.suppressNotices(['"+e+"'])`.";oa[a.level||"info"](t)}let oi="http://config.yf2/",on=null,or=e=>e.replace(/&#x([0-9A-Fa-f]{1,3});/gi,(e,a)=>String.fromCharCode(parseInt(a,16)));async function os(e,a,t,o,i="https://finance.yahoo.com/quote/AAPL",n="getCrumb-quote-AAPL.json",r=!1){if(!on){for(let a of(await e.getCookies(oi)))if("crumb"===a.key){on=a.value,o.debug("Retrieved crumb from cookie store: "+on);break}}if(on&&!r&&(await e.getCookies(i,{expire:!0})).length)return on;async function s(a,t){return!!a&&(await e.setFromSetCookieHeaders(a,t),!0)}o.debug("Fetching crumb and cookies from "+i+"...");let l={...t,headers:{...t.headers,accept:"text/html,application/xhtml+xml,application/xml"},redirect:"manual",devel:t.devel&&n},u=await a(i,l);await s(u.headers.getSetCookie(),i);let c=u.headers.get("location");if(c)if(c.match(/guce.yahoo/)){let t={...l,headers:{...l.headers,cookie:await e.getCookieString(c)},devel:"getCrumb-quote-AAPL-consent.html"};o.debug("fetch",c);let i=(await a(c,t)).headers.get("location");if(i){if(!i.match(/collectConsent/))throw Error("Unexpected redirect to "+i);let n={...t,headers:{...l.headers,cookie:await e.getCookieString(i)},devel:"getCrumb-quote-AAPL-collectConsent.html"};o.debug("fetch",i);let u=await a(i,n),c=[...(await u.text()).matchAll(/<input type="hidden" name="([^"]+)" value="([^"]+)">/g)].map(([,e,a])=>`${e}=${encodeURIComponent(or(a))}&`).join("")+"agree=agree&agree=agree",m={...t,headers:{...l.headers,cookie:await e.getCookieString(i),"content-type":"application/x-www-form-urlencoded"},method:"POST",body:c,devel:"getCrumb-quote-AAPL-collectConsentSubmit"};o.debug("fetch",i);let p=await a(i,m);if(!await s(p.headers.getSetCookie(),i))throw Error("No set-cookie header on collectConsentSubmitResponse, please report.");let d=p.headers.get("location");if(!d)throw Error("collectConsentSubmitResponse unexpectedly did not return a Location header, please report.");let g={...t,headers:{...l.headers,cookie:await e.getCookieString(d)},devel:"getCrumb-quote-AAPL-copyConsent"};o.debug("fetch",d);let h=await a(d,g);if(!await s(h.headers.getSetCookie(),d))throw Error("No set-cookie header on copyConsentResponse, please report.");let k=h.headers.get("location");if(!k)throw Error("collectConsentSubmitResponse unexpectedly did not return a Location header, please report.");let f={...l,headers:{...l.headers,cookie:await e.getCookieString(d)},devel:"getCrumb-quote-AAPL-consent-final-redirect.html"};return await os(e,a,f,o,k,"getCrumb-quote-AAPL-consent-final-redirect.html",r)}}else console.error("We expected a redirect to guce.yahoo.com, but got "+c),console.error("We'll try to continue anyway - you can safely ignore this if the request succeeds");let m=(await e.getCookies(i,{expire:!0}))[0];if(m)o.debug("Success. Cookie expires on "+m.expires);else throw Error("No set-cookie header present in Yahoo's response.  Something must have changed, please report.");let p="https://query1.finance.yahoo.com/v1/test/getcrumb",d={...l,headers:{...l.headers,"User-Agent":`Mozilla/5.0 (compatible; ${t4.UU}/${t4.rE})`,cookie:await e.getCookieString(p),origin:"https://finance.yahoo.com",referer:i,accept:"*/*","accept-encoding":"gzip, deflate, br","accept-language":"en-US,en;q=0.9","content-type":"text/plain"},devel:"getCrumb-getcrumb"};o.debug("fetch",p);let g=await a(p,d);if(200!==g.status)throw Error("Failed to get crumb, status "+g.status+", statusText: "+g.statusText);if(!(on=await g.text()))throw Error("Could not find crumb.  Yahoo's API may have changed; please report.");return o.debug("New crumb: "+on),await e.setCookie(new t5.Hk({key:"crumb",value:on}),oi),ol=null,on}let ol=null,ou=`${t4.UU}/${t4.rE} (+${t4.Jk})`,oc=new tK;function om(e){return e.replace(/\$\{([^}]+)\}/g,(e,a)=>"YF_QUERY_HOST"===a?this._opts.YF_QUERY_HOST||"query2.finance.yahoo.com":e)}async function op(e,a={},t={},o="json",i=!1){var n,r;if(!(this&&this._env))throw new t2.NoEnvironmentError("yahooFinanceFetch called without this._env set");"number"==typeof(r={...this._opts.queue,...t.queue}).concurrency&&oc.concurrency!==r.concurrency&&(oc.concurrency=r.concurrency),"number"==typeof r.timeout&&oc.timeout!==r.timeout&&(oc.timeout=r.timeout);let{URLSearchParams:s,fetch:l,fetchDevel:u}=this._env,c=t.devel?await u():l,m={...t.fetchOptions,devel:t.devel,headers:{"User-Agent":ou,...null==(n=t.fetchOptions)?void 0:n.headers}};if(i){if(!this._opts.cookieJar)throw Error("No cookieJar set");if(!this._opts.logger)throw Error("Logger was unset.");let e=await function(e,a,t,o,i="https://finance.yahoo.com/quote/AAPL",n=os){return oo("yahooSurvey"),ol||(ol=n(e,a,t,o,i)),ol}(this._opts.cookieJar,c,m,this._opts.logger);e&&(a.crumb=e)}let p=new s(a),d=om.call(this,e)+"?"+p.toString();if(!this._opts.cookieJar)throw Error("No cookieJar set");let g={...m,headers:{...m.headers,cookie:await this._opts.cookieJar.getCookieString(d,{allPaths:!0})}};"csv"===o&&(o="text");let h=await oc.add(()=>c(d,g)),k=h.headers.getSetCookie();if(k){if(!this._opts.cookieJar)throw Error("No cookieJar set");this._opts.cookieJar.setFromSetCookieHeaders(k,d)}let f=await h[o]();if("json"===o){let e=Object.keys(f);if(1===e.length){let a=f[e[0]].error;if(a)throw new(t2[a.code.replace(/ /g,"")+"Error"]||Error)(a.description)}}if(!h.ok){console.error(d);let e=new t2.HTTPError(h.statusText);throw e.code=h.status,e}return f}function od(e){return oj(e)&&Symbol.asyncIterator in e}function og(e){return oj(e)&&Symbol.iterator in e}function oh(e){return oj(e)&&(Object.getPrototypeOf(e)===Object.prototype||null===Object.getPrototypeOf(e))}function ok(e){return e instanceof Promise}function of(e){return e instanceof Date&&Number.isFinite(e.getTime())}function oy(e){return ArrayBuffer.isView(e)}function ob(e){return e instanceof globalThis.Uint8Array}function oj(e){return null!==e&&"object"==typeof e}function ow(e){return Array.isArray(e)&&!ArrayBuffer.isView(e)}function ov(e){return void 0===e}function oO(e){return null===e}function oz(e){return"boolean"==typeof e}function oS(e){return"number"==typeof e}function ox(e){return Number.isInteger(e)}function oA(e){return"bigint"==typeof e}function oC(e){return"string"==typeof e}function oP(e){return"function"==typeof e}function oT(e){return"symbol"==typeof e}function oI(e){return oA(e)||oz(e)||oO(e)||oS(e)||oC(e)||oT(e)||ov(e)}!function(e){function a(a){let t=oj(a);return e.AllowArrayObject?t:t&&!ow(a)}e.ExactOptionalPropertyTypes=!1,e.AllowArrayObject=!1,e.AllowNaN=!1,e.AllowNullVoid=!1,e.IsExactOptionalProperty=function(a,t){return e.ExactOptionalPropertyTypes?t in a:void 0!==a[t]},e.IsObjectLike=a,e.IsRecordLike=function(e){return a(e)&&!(e instanceof Date)&&!(e instanceof Uint8Array)},e.IsNumberLike=function(a){return e.AllowNaN?oS(a):Number.isFinite(a)},e.IsVoidLike=function(a){let t=ov(a);return e.AllowNullVoid?t||null===a:t}}(i||(i={}));class oE extends d{constructor(e){super(`Duplicate type kind '${e}' detected`)}}class oD extends d{constructor(e){super(`Duplicate string format '${e}' detected`)}}!function(e){e.Type=function(e,a){if(eV(e))throw new oE(e);return eY.set(e,a),(a={})=>tQ({...a,[A]:e})},e.Format=function(e,a){if(e_.has(e))throw new oD(e);return eQ(e,a),e}}(n||(n={}));let oN=function(e){switch(e.errorType){case s.ArrayContains:return"Expected array to contain at least one matching value";case s.ArrayMaxContains:return`Expected array to contain no more than ${e.schema.maxContains} matching values`;case s.ArrayMinContains:return`Expected array to contain at least ${e.schema.minContains} matching values`;case s.ArrayMaxItems:return`Expected array length to be less or equal to ${e.schema.maxItems}`;case s.ArrayMinItems:return`Expected array length to be greater or equal to ${e.schema.minItems}`;case s.ArrayUniqueItems:return"Expected array elements to be unique";case s.Array:return"Expected array";case s.AsyncIterator:return"Expected AsyncIterator";case s.BigIntExclusiveMaximum:return`Expected bigint to be less than ${e.schema.exclusiveMaximum}`;case s.BigIntExclusiveMinimum:return`Expected bigint to be greater than ${e.schema.exclusiveMinimum}`;case s.BigIntMaximum:return`Expected bigint to be less or equal to ${e.schema.maximum}`;case s.BigIntMinimum:return`Expected bigint to be greater or equal to ${e.schema.minimum}`;case s.BigIntMultipleOf:return`Expected bigint to be a multiple of ${e.schema.multipleOf}`;case s.BigInt:return"Expected bigint";case s.Boolean:return"Expected boolean";case s.DateExclusiveMinimumTimestamp:return`Expected Date timestamp to be greater than ${e.schema.exclusiveMinimumTimestamp}`;case s.DateExclusiveMaximumTimestamp:return`Expected Date timestamp to be less than ${e.schema.exclusiveMaximumTimestamp}`;case s.DateMinimumTimestamp:return`Expected Date timestamp to be greater or equal to ${e.schema.minimumTimestamp}`;case s.DateMaximumTimestamp:return`Expected Date timestamp to be less or equal to ${e.schema.maximumTimestamp}`;case s.DateMultipleOfTimestamp:return`Expected Date timestamp to be a multiple of ${e.schema.multipleOfTimestamp}`;case s.Date:return"Expected Date";case s.Function:return"Expected function";case s.IntegerExclusiveMaximum:return`Expected integer to be less than ${e.schema.exclusiveMaximum}`;case s.IntegerExclusiveMinimum:return`Expected integer to be greater than ${e.schema.exclusiveMinimum}`;case s.IntegerMaximum:return`Expected integer to be less or equal to ${e.schema.maximum}`;case s.IntegerMinimum:return`Expected integer to be greater or equal to ${e.schema.minimum}`;case s.IntegerMultipleOf:return`Expected integer to be a multiple of ${e.schema.multipleOf}`;case s.Integer:return"Expected integer";case s.IntersectUnevaluatedProperties:return"Unexpected property";case s.Intersect:return"Expected all values to match";case s.Iterator:return"Expected Iterator";case s.Literal:return`Expected ${"string"==typeof e.schema.const?`'${e.schema.const}'`:e.schema.const}`;case s.Never:return"Never";case s.Not:return"Value should not match";case s.Null:return"Expected null";case s.NumberExclusiveMaximum:return`Expected number to be less than ${e.schema.exclusiveMaximum}`;case s.NumberExclusiveMinimum:return`Expected number to be greater than ${e.schema.exclusiveMinimum}`;case s.NumberMaximum:return`Expected number to be less or equal to ${e.schema.maximum}`;case s.NumberMinimum:return`Expected number to be greater or equal to ${e.schema.minimum}`;case s.NumberMultipleOf:return`Expected number to be a multiple of ${e.schema.multipleOf}`;case s.Number:return"Expected number";case s.Object:return"Expected object";case s.ObjectAdditionalProperties:return"Unexpected property";case s.ObjectMaxProperties:return`Expected object to have no more than ${e.schema.maxProperties} properties`;case s.ObjectMinProperties:return`Expected object to have at least ${e.schema.minProperties} properties`;case s.ObjectRequiredProperty:return"Expected required property";case s.Promise:return"Expected Promise";case s.RegExp:return"Expected string to match regular expression";case s.StringFormatUnknown:return`Unknown format '${e.schema.format}'`;case s.StringFormat:return`Expected string to match '${e.schema.format}' format`;case s.StringMaxLength:return`Expected string length less or equal to ${e.schema.maxLength}`;case s.StringMinLength:return`Expected string length greater or equal to ${e.schema.minLength}`;case s.StringPattern:return`Expected string to match '${e.schema.pattern}'`;case s.String:return"Expected string";case s.Symbol:return"Expected symbol";case s.TupleLength:return`Expected tuple to have ${e.schema.maxItems||0} elements`;case s.Tuple:return"Expected tuple";case s.Uint8ArrayMaxByteLength:return`Expected byte length less or equal to ${e.schema.maxByteLength}`;case s.Uint8ArrayMinByteLength:return`Expected byte length greater or equal to ${e.schema.minByteLength}`;case s.Uint8Array:return"Expected Uint8Array";case s.Undefined:return"Expected undefined";case s.Union:return"Expected union value";case s.Void:return"Expected void";case s.Kind:return`Expected kind '${e.schema[A]}'`;default:return"Unknown error type"}};class oL extends d{constructor(e){super(`Unable to dereference schema with $id '${e.$id}'`),this.schema=e}}function oR(e,a){return"This"===e[A]||"Ref"===e[A]?function(e,a){let t=a.find(a=>a.$id===e.$ref);if(void 0===t)throw new oL(e);return oR(t,a)}(e,a):e}class oM extends d{constructor(e){super("Unable to hash value"),this.value=e}}!function(e){e[e.Undefined=0]="Undefined",e[e.Null=1]="Null",e[e.Boolean=2]="Boolean",e[e.Number=3]="Number",e[e.String=4]="String",e[e.Object=5]="Object",e[e.Array=6]="Array",e[e.Date=7]="Date",e[e.Uint8Array=8]="Uint8Array",e[e.Symbol=9]="Symbol",e[e.BigInt=10]="BigInt"}(r||(r={}));let oF=BigInt("14695981039346656037"),[oU,oq]=[BigInt("1099511628211"),BigInt("2")**BigInt("64")],o$=Array.from({length:256}).map((e,a)=>BigInt(a)),oB=new Float64Array(1),oH=new DataView(oB.buffer),o_=new Uint8Array(oB.buffer);function oQ(e){oF^=o$[e],oF=oF*oU%oq}function oY(e){return oF=BigInt("14695981039346656037"),function e(a){if(ow(a)){for(let t of(oQ(r.Array),a))e(t);return}if(oz(a))return void(oQ(r.Boolean),oQ(+!!a));if(oA(a)){for(let e of(oQ(r.BigInt),oH.setBigInt64(0,a),o_))oQ(e);return}if(of(a))return void(oQ(r.Date),e(a.getTime()));if(oO(a))return void oQ(r.Null);if(oS(a)){for(let e of(oQ(r.Number),oH.setFloat64(0,a),o_))oQ(e);return}if(oh(a)){for(let t of(oQ(r.Object),globalThis.Object.getOwnPropertyNames(a).sort()))e(t),e(a[t]);return}if(oC(a)){oQ(r.String);for(let e=0;e<a.length;e++)for(let t of function*(e){let a=0===e?1:Math.ceil(Math.floor(Math.log2(e)+1)/8);for(let t=0;t<a;t++)yield e>>8*(a-1-t)&255}(a.charCodeAt(e)))oQ(t);return}if(oT(a))return void(oQ(r.Symbol),e(a.description));if(ob(a)){oQ(r.Uint8Array);for(let e=0;e<a.length;e++)oQ(a[e]);return}if(ov(a))return oQ(r.Undefined);throw new oM(a)}(e),oF}!function(e){e[e.ArrayContains=0]="ArrayContains",e[e.ArrayMaxContains=1]="ArrayMaxContains",e[e.ArrayMaxItems=2]="ArrayMaxItems",e[e.ArrayMinContains=3]="ArrayMinContains",e[e.ArrayMinItems=4]="ArrayMinItems",e[e.ArrayUniqueItems=5]="ArrayUniqueItems",e[e.Array=6]="Array",e[e.AsyncIterator=7]="AsyncIterator",e[e.BigIntExclusiveMaximum=8]="BigIntExclusiveMaximum",e[e.BigIntExclusiveMinimum=9]="BigIntExclusiveMinimum",e[e.BigIntMaximum=10]="BigIntMaximum",e[e.BigIntMinimum=11]="BigIntMinimum",e[e.BigIntMultipleOf=12]="BigIntMultipleOf",e[e.BigInt=13]="BigInt",e[e.Boolean=14]="Boolean",e[e.DateExclusiveMaximumTimestamp=15]="DateExclusiveMaximumTimestamp",e[e.DateExclusiveMinimumTimestamp=16]="DateExclusiveMinimumTimestamp",e[e.DateMaximumTimestamp=17]="DateMaximumTimestamp",e[e.DateMinimumTimestamp=18]="DateMinimumTimestamp",e[e.DateMultipleOfTimestamp=19]="DateMultipleOfTimestamp",e[e.Date=20]="Date",e[e.Function=21]="Function",e[e.IntegerExclusiveMaximum=22]="IntegerExclusiveMaximum",e[e.IntegerExclusiveMinimum=23]="IntegerExclusiveMinimum",e[e.IntegerMaximum=24]="IntegerMaximum",e[e.IntegerMinimum=25]="IntegerMinimum",e[e.IntegerMultipleOf=26]="IntegerMultipleOf",e[e.Integer=27]="Integer",e[e.IntersectUnevaluatedProperties=28]="IntersectUnevaluatedProperties",e[e.Intersect=29]="Intersect",e[e.Iterator=30]="Iterator",e[e.Kind=31]="Kind",e[e.Literal=32]="Literal",e[e.Never=33]="Never",e[e.Not=34]="Not",e[e.Null=35]="Null",e[e.NumberExclusiveMaximum=36]="NumberExclusiveMaximum",e[e.NumberExclusiveMinimum=37]="NumberExclusiveMinimum",e[e.NumberMaximum=38]="NumberMaximum",e[e.NumberMinimum=39]="NumberMinimum",e[e.NumberMultipleOf=40]="NumberMultipleOf",e[e.Number=41]="Number",e[e.ObjectAdditionalProperties=42]="ObjectAdditionalProperties",e[e.ObjectMaxProperties=43]="ObjectMaxProperties",e[e.ObjectMinProperties=44]="ObjectMinProperties",e[e.ObjectRequiredProperty=45]="ObjectRequiredProperty",e[e.Object=46]="Object",e[e.Promise=47]="Promise",e[e.RegExp=48]="RegExp",e[e.StringFormatUnknown=49]="StringFormatUnknown",e[e.StringFormat=50]="StringFormat",e[e.StringMaxLength=51]="StringMaxLength",e[e.StringMinLength=52]="StringMinLength",e[e.StringPattern=53]="StringPattern",e[e.String=54]="String",e[e.Symbol=55]="Symbol",e[e.TupleLength=56]="TupleLength",e[e.Tuple=57]="Tuple",e[e.Uint8ArrayMaxByteLength=58]="Uint8ArrayMaxByteLength",e[e.Uint8ArrayMinByteLength=59]="Uint8ArrayMinByteLength",e[e.Uint8Array=60]="Uint8Array",e[e.Undefined=61]="Undefined",e[e.Union=62]="Union",e[e.Void=63]="Void"}(s||(s={}));class oV extends d{constructor(e){super("Unknown type"),this.schema=e}}function oW(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}function oG(e){return void 0!==e}class oJ{constructor(e){this.iterator=e}[Symbol.iterator](){return this.iterator}First(){let e=this.iterator.next();return e.done?void 0:e.value}}function oK(e,a,t,o){return{type:e,schema:a,path:t,value:o,message:oN({errorType:e,path:t,schema:a,value:o})}}function*oZ(e,a,t,o){let n=oG(e.$id)?[...a,e]:a;switch(e[A]){case"Any":return yield*function*(e,a,t,o){}(0,0,0,0);case"Array":return yield*function*(e,a,t,o){if(!ow(o))return yield oK(s.Array,e,t,o);oG(e.minItems)&&!(o.length>=e.minItems)&&(yield oK(s.ArrayMinItems,e,t,o)),oG(e.maxItems)&&!(o.length<=e.maxItems)&&(yield oK(s.ArrayMaxItems,e,t,o));for(let i=0;i<o.length;i++)yield*oZ(e.items,a,`${t}/${i}`,o[i]);if(!0===e.uniqueItems&&!function(){let e=new Set;for(let a of o){let t=oY(a);if(e.has(t))return!1;e.add(t)}return!0}()&&(yield oK(s.ArrayUniqueItems,e,t,o)),!(oG(e.contains)||oG(e.minContains)||oG(e.maxContains)))return;let i=oG(e.contains)?e.contains:e1(),n=o.reduce((e,o,n)=>!0===oZ(i,a,`${t}${n}`,o).next().done?e+1:e,0);0===n&&(yield oK(s.ArrayContains,e,t,o)),oS(e.minContains)&&n<e.minContains&&(yield oK(s.ArrayMinContains,e,t,o)),oS(e.maxContains)&&n>e.maxContains&&(yield oK(s.ArrayMaxContains,e,t,o))}(e,n,t,o);case"AsyncIterator":return yield*function*(e,a,t,o){od(o)||(yield oK(s.AsyncIterator,e,t,o))}(e,0,t,o);case"BigInt":return yield*function*(e,a,t,o){if(!oA(o))return yield oK(s.BigInt,e,t,o);oG(e.exclusiveMaximum)&&!(o<e.exclusiveMaximum)&&(yield oK(s.BigIntExclusiveMaximum,e,t,o)),oG(e.exclusiveMinimum)&&!(o>e.exclusiveMinimum)&&(yield oK(s.BigIntExclusiveMinimum,e,t,o)),oG(e.maximum)&&!(o<=e.maximum)&&(yield oK(s.BigIntMaximum,e,t,o)),oG(e.minimum)&&!(o>=e.minimum)&&(yield oK(s.BigIntMinimum,e,t,o)),oG(e.multipleOf)&&o%e.multipleOf!==BigInt(0)&&(yield oK(s.BigIntMultipleOf,e,t,o))}(e,0,t,o);case"Boolean":return yield*function*(e,a,t,o){oz(o)||(yield oK(s.Boolean,e,t,o))}(e,0,t,o);case"Constructor":return yield*function*(e,a,t,o){yield*oZ(e.returns,a,t,o.prototype)}(e,n,t,o);case"Date":return yield*function*(e,a,t,o){if(!of(o))return yield oK(s.Date,e,t,o);oG(e.exclusiveMaximumTimestamp)&&!(o.getTime()<e.exclusiveMaximumTimestamp)&&(yield oK(s.DateExclusiveMaximumTimestamp,e,t,o)),oG(e.exclusiveMinimumTimestamp)&&!(o.getTime()>e.exclusiveMinimumTimestamp)&&(yield oK(s.DateExclusiveMinimumTimestamp,e,t,o)),oG(e.maximumTimestamp)&&!(o.getTime()<=e.maximumTimestamp)&&(yield oK(s.DateMaximumTimestamp,e,t,o)),oG(e.minimumTimestamp)&&!(o.getTime()>=e.minimumTimestamp)&&(yield oK(s.DateMinimumTimestamp,e,t,o)),oG(e.multipleOfTimestamp)&&o.getTime()%e.multipleOfTimestamp!=0&&(yield oK(s.DateMultipleOfTimestamp,e,t,o))}(e,0,t,o);case"Function":return yield*function*(e,a,t,o){oP(o)||(yield oK(s.Function,e,t,o))}(e,0,t,o);case"Integer":return yield*function*(e,a,t,o){if(!ox(o))return yield oK(s.Integer,e,t,o);oG(e.exclusiveMaximum)&&!(o<e.exclusiveMaximum)&&(yield oK(s.IntegerExclusiveMaximum,e,t,o)),oG(e.exclusiveMinimum)&&!(o>e.exclusiveMinimum)&&(yield oK(s.IntegerExclusiveMinimum,e,t,o)),oG(e.maximum)&&!(o<=e.maximum)&&(yield oK(s.IntegerMaximum,e,t,o)),oG(e.minimum)&&!(o>=e.minimum)&&(yield oK(s.IntegerMinimum,e,t,o)),oG(e.multipleOf)&&o%e.multipleOf!=0&&(yield oK(s.IntegerMultipleOf,e,t,o))}(e,0,t,o);case"Intersect":return yield*function*(e,a,t,o){for(let i of e.allOf){let n=oZ(i,a,t,o).next();n.done||(yield oK(s.Intersect,e,t,o),yield n.value)}if(!1===e.unevaluatedProperties){let a=new RegExp(aY(e));for(let i of Object.getOwnPropertyNames(o))a.test(i)||(yield oK(s.IntersectUnevaluatedProperties,e,`${t}/${i}`,o))}if("object"==typeof e.unevaluatedProperties){let i=new RegExp(aY(e));for(let n of Object.getOwnPropertyNames(o))if(!i.test(n)){let i=oZ(e.unevaluatedProperties,a,`${t}/${n}`,o[n]).next();i.done||(yield i.value)}}}(e,n,t,o);case"Iterator":return yield*function*(e,a,t,o){og(o)||(yield oK(s.Iterator,e,t,o))}(e,0,t,o);case"Literal":return yield*function*(e,a,t,o){o!==e.const&&(yield oK(s.Literal,e,t,o))}(e,0,t,o);case"Never":return yield*function*(e,a,t,o){yield oK(s.Never,e,t,o)}(e,0,t,o);case"Not":return yield*function*(e,a,t,o){!0===oZ(e.not,a,t,o).next().done&&(yield oK(s.Not,e,t,o))}(e,n,t,o);case"Null":return yield*function*(e,a,t,o){oO(o)||(yield oK(s.Null,e,t,o))}(e,0,t,o);case"Number":return yield*function*(e,a,t,o){if(!i.IsNumberLike(o))return yield oK(s.Number,e,t,o);oG(e.exclusiveMaximum)&&!(o<e.exclusiveMaximum)&&(yield oK(s.NumberExclusiveMaximum,e,t,o)),oG(e.exclusiveMinimum)&&!(o>e.exclusiveMinimum)&&(yield oK(s.NumberExclusiveMinimum,e,t,o)),oG(e.maximum)&&!(o<=e.maximum)&&(yield oK(s.NumberMaximum,e,t,o)),oG(e.minimum)&&!(o>=e.minimum)&&(yield oK(s.NumberMinimum,e,t,o)),oG(e.multipleOf)&&o%e.multipleOf!=0&&(yield oK(s.NumberMultipleOf,e,t,o))}(e,0,t,o);case"Object":return yield*function*(e,a,t,o){if(!i.IsObjectLike(o))return yield oK(s.Object,e,t,o);oG(e.minProperties)&&!(Object.getOwnPropertyNames(o).length>=e.minProperties)&&(yield oK(s.ObjectMinProperties,e,t,o)),oG(e.maxProperties)&&!(Object.getOwnPropertyNames(o).length<=e.maxProperties)&&(yield oK(s.ObjectMaxProperties,e,t,o));let n=Array.isArray(e.required)?e.required:[],r=Object.getOwnPropertyNames(e.properties),l=Object.getOwnPropertyNames(o);for(let a of n)l.includes(a)||(yield oK(s.ObjectRequiredProperty,e.properties[a],`${t}/${oW(a)}`,void 0));if(!1===e.additionalProperties)for(let a of l)r.includes(a)||(yield oK(s.ObjectAdditionalProperties,e,`${t}/${oW(a)}`,o[a]));if("object"==typeof e.additionalProperties)for(let i of l)r.includes(i)||(yield*oZ(e.additionalProperties,a,`${t}/${oW(i)}`,o[i]));for(let n of r){let r=e.properties[n];e.required&&e.required.includes(n)?(yield*oZ(r,a,`${t}/${oW(n)}`,o[n]),!ty(e)||n in o||(yield oK(s.ObjectRequiredProperty,r,`${t}/${oW(n)}`,void 0))):i.IsExactOptionalProperty(o,n)&&(yield*oZ(r,a,`${t}/${oW(n)}`,o[n]))}}(e,n,t,o);case"Promise":return yield*function*(e,a,t,o){ok(o)||(yield oK(s.Promise,e,t,o))}(e,0,t,o);case"Record":return yield*function*(e,a,t,o){if(!i.IsRecordLike(o))return yield oK(s.Object,e,t,o);oG(e.minProperties)&&!(Object.getOwnPropertyNames(o).length>=e.minProperties)&&(yield oK(s.ObjectMinProperties,e,t,o)),oG(e.maxProperties)&&!(Object.getOwnPropertyNames(o).length<=e.maxProperties)&&(yield oK(s.ObjectMaxProperties,e,t,o));let[n,r]=Object.entries(e.patternProperties)[0],l=new RegExp(n);for(let[e,i]of Object.entries(o))l.test(e)&&(yield*oZ(r,a,`${t}/${oW(e)}`,i));if("object"==typeof e.additionalProperties)for(let[i,n]of Object.entries(o))l.test(i)||(yield*oZ(e.additionalProperties,a,`${t}/${oW(i)}`,n));if(!1===e.additionalProperties){for(let[a,i]of Object.entries(o))if(!l.test(a))return yield oK(s.ObjectAdditionalProperties,e,`${t}/${oW(a)}`,i)}}(e,n,t,o);case"Ref":return yield*function*(e,a,t,o){yield*oZ(oR(e,a),a,t,o)}(e,n,t,o);case"RegExp":return yield*function*(e,a,t,o){return oC(o)?(oG(e.minLength)&&!(o.length>=e.minLength)&&(yield oK(s.StringMinLength,e,t,o)),oG(e.maxLength)&&!(o.length<=e.maxLength)&&(yield oK(s.StringMaxLength,e,t,o)),new RegExp(e.source,e.flags).test(o))?void 0:yield oK(s.RegExp,e,t,o):yield oK(s.String,e,t,o)}(e,0,t,o);case"String":return yield*function*(e,a,t,o){if(!oC(o))return yield oK(s.String,e,t,o);if(oG(e.minLength)&&!(o.length>=e.minLength)&&(yield oK(s.StringMinLength,e,t,o)),oG(e.maxLength)&&!(o.length<=e.maxLength)&&(yield oK(s.StringMaxLength,e,t,o)),oC(e.pattern)&&(new RegExp(e.pattern).test(o)||(yield oK(s.StringPattern,e,t,o))),oC(e.format)){var i,n;(i=e.format,e_.has(i))?(n=e.format,e_.get(n))(o)||(yield oK(s.StringFormat,e,t,o)):yield oK(s.StringFormatUnknown,e,t,o)}}(e,0,t,o);case"Symbol":return yield*function*(e,a,t,o){oT(o)||(yield oK(s.Symbol,e,t,o))}(e,0,t,o);case"TemplateLiteral":return yield*function*(e,a,t,o){if(!oC(o))return yield oK(s.String,e,t,o);new RegExp(e.pattern).test(o)||(yield oK(s.StringPattern,e,t,o))}(e,0,t,o);case"This":return yield*function*(e,a,t,o){yield*oZ(oR(e,a),a,t,o)}(e,n,t,o);case"Tuple":return yield*function*(e,a,t,o){if(!ow(o))return yield oK(s.Tuple,e,t,o);if(void 0===e.items&&0!==o.length||o.length!==e.maxItems)return yield oK(s.TupleLength,e,t,o);if(e.items)for(let i=0;i<e.items.length;i++)yield*oZ(e.items[i],a,`${t}/${i}`,o[i])}(e,n,t,o);case"Undefined":return yield*function*(e,a,t,o){ov(o)||(yield oK(s.Undefined,e,t,o))}(e,0,t,o);case"Union":return yield*function*(e,a,t,o){let i=0;for(let n of e.anyOf){let e=[...oZ(n,a,t,o)];if(0===e.length)return;i+=e.length}i>0&&(yield oK(s.Union,e,t,o))}(e,n,t,o);case"Uint8Array":return yield*function*(e,a,t,o){if(!ob(o))return yield oK(s.Uint8Array,e,t,o);oG(e.maxByteLength)&&!(o.length<=e.maxByteLength)&&(yield oK(s.Uint8ArrayMaxByteLength,e,t,o)),oG(e.minByteLength)&&!(o.length>=e.minByteLength)&&(yield oK(s.Uint8ArrayMinByteLength,e,t,o))}(e,0,t,o);case"Unknown":return yield*function*(e,a,t,o){}(0,0,0,0);case"Void":return yield*function*(e,a,t,o){i.IsVoidLike(o)||(yield oK(s.Void,e,t,o))}(e,0,t,o);default:if(!eV(e[A]))throw new oV(e);return yield*function*(e,a,t,o){var i;(i=e[A],eY.get(i))(e,o)||(yield oK(s.Kind,e,t,o))}(e,0,t,o)}}function oX(...e){return new oJ(3===e.length?oZ(e[0],e[1],"",e[2]):oZ(e[0],[],"",e[1]))}class o1 extends d{constructor(e){super("Unknown type"),this.schema=e}}function o0(e){return void 0!==e}function o3(e,a,t){var o,n,r,s;let l=o0(e.$id)?[...a,e]:a;switch(e[A]){case"Any":case"Unknown":return!0;case"Array":if(!ow(t)||o0(e.minItems)&&!(t.length>=e.minItems)||o0(e.maxItems)&&!(t.length<=e.maxItems)||!t.every(a=>o3(e.items,l,a))||!0===e.uniqueItems&&!function(){let e=new Set;for(let a of t){let t=oY(a);if(e.has(t))return!1;e.add(t)}return!0}())return!1;if(!(o0(e.contains)||oS(e.minContains)||oS(e.maxContains)))return!0;let u=o0(e.contains)?e.contains:e1(),c=t.reduce((e,a)=>o3(u,l,a)?e+1:e,0);return!(0===c||oS(e.minContains)&&c<e.minContains||oS(e.maxContains)&&c>e.maxContains);case"AsyncIterator":return od(t);case"BigInt":return!(!oA(t)||o0(e.exclusiveMaximum)&&!(t<e.exclusiveMaximum)||o0(e.exclusiveMinimum)&&!(t>e.exclusiveMinimum)||o0(e.maximum)&&!(t<=e.maximum)||o0(e.minimum)&&!(t>=e.minimum)||o0(e.multipleOf)&&t%e.multipleOf!==BigInt(0));case"Boolean":return oz(t);case"Constructor":return o3(e.returns,l,t.prototype);case"Date":return!(!of(t)||o0(e.exclusiveMaximumTimestamp)&&!(t.getTime()<e.exclusiveMaximumTimestamp)||o0(e.exclusiveMinimumTimestamp)&&!(t.getTime()>e.exclusiveMinimumTimestamp)||o0(e.maximumTimestamp)&&!(t.getTime()<=e.maximumTimestamp)||o0(e.minimumTimestamp)&&!(t.getTime()>=e.minimumTimestamp)||o0(e.multipleOfTimestamp)&&t.getTime()%e.multipleOfTimestamp!=0);case"Function":return oP(t);case"Integer":return!(!ox(t)||o0(e.exclusiveMaximum)&&!(t<e.exclusiveMaximum)||o0(e.exclusiveMinimum)&&!(t>e.exclusiveMinimum)||o0(e.maximum)&&!(t<=e.maximum)||o0(e.minimum)&&!(t>=e.minimum)||o0(e.multipleOf)&&t%e.multipleOf!=0);case"Intersect":return function(e,a,t){let o=e.allOf.every(e=>o3(e,a,t));if(!1===e.unevaluatedProperties){let a=new RegExp(aY(e)),i=Object.getOwnPropertyNames(t).every(e=>a.test(e));return o&&i}if(!eH(e.unevaluatedProperties))return o;{let i=new RegExp(aY(e)),n=Object.getOwnPropertyNames(t).every(o=>i.test(o)||o3(e.unevaluatedProperties,a,t[o]));return o&&n}}(e,l,t);case"Iterator":return og(t);case"Literal":return t===e.const;case"Never":return!1;case"Not":return!o3(e.not,l,t);case"Null":return oO(t);case"Number":return!(!i.IsNumberLike(t)||o0(e.exclusiveMaximum)&&!(t<e.exclusiveMaximum)||o0(e.exclusiveMinimum)&&!(t>e.exclusiveMinimum)||o0(e.minimum)&&!(t>=e.minimum)||o0(e.maximum)&&!(t<=e.maximum)||o0(e.multipleOf)&&t%e.multipleOf!=0);case"Object":if(!i.IsObjectLike(t)||o0(e.minProperties)&&!(Object.getOwnPropertyNames(t).length>=e.minProperties)||o0(e.maxProperties)&&!(Object.getOwnPropertyNames(t).length<=e.maxProperties))return!1;let m=Object.getOwnPropertyNames(e.properties);for(let a of m){let n=e.properties[a];if(e.required&&e.required.includes(a)){if(!o3(n,l,t[a])||(ty(n)||"Any"===(o=n)[A]||"Unknown"===o[A])&&!(a in t))return!1}else if(i.IsExactOptionalProperty(t,a)&&!o3(n,l,t[a]))return!1}if(!1===e.additionalProperties){let a=Object.getOwnPropertyNames(t);return!!e.required&&e.required.length===m.length&&a.length===m.length||a.every(e=>m.includes(e))}if("object"==typeof e.additionalProperties)return Object.getOwnPropertyNames(t).every(a=>m.includes(a)||o3(e.additionalProperties,l,t[a]));return!0;case"Promise":return ok(t);case"Record":return function(e,a,t){if(!i.IsRecordLike(t)||o0(e.minProperties)&&!(Object.getOwnPropertyNames(t).length>=e.minProperties)||o0(e.maxProperties)&&!(Object.getOwnPropertyNames(t).length<=e.maxProperties))return!1;let[o,n]=Object.entries(e.patternProperties)[0],r=new RegExp(o),s=Object.entries(t).every(([e,t])=>!r.test(e)||o3(n,a,t)),l="object"!=typeof e.additionalProperties||Object.entries(t).every(([t,o])=>!!r.test(t)||o3(e.additionalProperties,a,o)),u=!1!==e.additionalProperties||Object.getOwnPropertyNames(t).every(e=>r.test(e));return s&&l&&u}(e,l,t);case"Ref":case"This":return o3(oR(e,l),l,t);case"RegExp":let p=new RegExp(e.source,e.flags);return!(o0(e.minLength)&&!(t.length>=e.minLength)||o0(e.maxLength)&&!(t.length<=e.maxLength))&&p.test(t);case"String":if(!oC(t)||o0(e.minLength)&&!(t.length>=e.minLength)||o0(e.maxLength)&&!(t.length<=e.maxLength)||o0(e.pattern)&&!new RegExp(e.pattern).test(t))return!1;if(o0(e.format)){if(n=e.format,!e_.has(n))return!1;return(r=e.format,e_.get(r))(t)}return!0;case"Symbol":return oT(t);case"TemplateLiteral":return oC(t)&&new RegExp(e.pattern).test(t);case"Tuple":if(!ow(t)||void 0===e.items&&0!==t.length||t.length!==e.maxItems)return!1;if(!e.items)return!0;for(let a=0;a<e.items.length;a++)if(!o3(e.items[a],l,t[a]))return!1;return!0;case"Undefined":return ov(t);case"Union":return e.anyOf.some(e=>o3(e,l,t));case"Uint8Array":return!(!ob(t)||o0(e.maxByteLength)&&!(t.length<=e.maxByteLength)||o0(e.minByteLength)&&!(t.length>=e.minByteLength));case"Void":return i.IsVoidLike(t);default:if(!eV(e[A]))throw new o1(e);if(!eV(e[A]))return!1;return(s=e[A],eY.get(s))(e,t)}}function o2(...e){return 3===e.length?o3(e[0],e[1],e[2]):o3(e[0],[],e[1])}function o4(e){if(ow(e))return e.map(e=>o4(e));if(of(e))return new Date(e.toISOString());if(oh(e)){let a={};for(let t of Object.getOwnPropertyNames(e))a[t]=o4(e[t]);for(let t of Object.getOwnPropertySymbols(e))a[t]=o4(e[t]);return a}if(oy(e))return e.slice();if(oI(e))return e;throw Error("ValueClone: Unable to clone value")}class o5 extends d{constructor(e,a){super(a),this.schema=e}}function o9(e){return"function"==typeof e?e:o4(e)}function o6(e,a){let t=oC(e.$id)?[...a,e]:a;switch(e[A]){case"Any":case"Unknown":return"default"in e?o9(e.default):{};case"Array":if(!0!==e.uniqueItems||"default"in e)if("contains"in e&&!("default"in e))throw new o5(e,"Array with the contains constraint requires a default value");else if("default"in e)return o9(e.default);else if(void 0!==e.minItems)return Array.from({length:e.minItems}).map(a=>o6(e.items,t));else return[];throw new o5(e,"Array with the uniqueItems constraint requires a default value");case"AsyncIterator":return"default"in e?o9(e.default):async function*(){}();case"BigInt":return"default"in e?o9(e.default):BigInt(0);case"Boolean":return"default"in e&&o9(e.default);case"Constructor":if("default"in e)return o9(e.default);{let a=o6(e.returns,t);return"object"!=typeof a||Array.isArray(a)?class{}:class{constructor(){for(let[e,t]of Object.entries(a))this[e]=t}}}case"Date":return"default"in e?o9(e.default):void 0!==e.minimumTimestamp?new Date(e.minimumTimestamp):new Date;case"Function":return"default"in e?o9(e.default):()=>o6(e.returns,t);case"Integer":case"Number":return"default"in e?o9(e.default):void 0!==e.minimum?e.minimum:0;case"Intersect":if("default"in e)return o9(e.default);{let a=e.allOf.reduce((e,a)=>{let o=o6(a,t);return"object"==typeof o?{...e,...o}:o},{});if(!o2(e,t,a))throw new o5(e,"Intersect produced invalid value. Consider using a default value.");return a}case"Iterator":return"default"in e?o9(e.default):function*(){}();case"Literal":return"default"in e?o9(e.default):e.const;case"Never":if("default"in e)return o9(e.default);throw new o5(e,"Never types cannot be created. Consider using a default value.");case"Not":if("default"in e)return o9(e.default);throw new o5(e,"Not types must have a default value");case"Null":return"default"in e?o9(e.default):null;case"Object":if("default"in e)return o9(e.default);{let a=new Set(e.required),o={};for(let[i,n]of Object.entries(e.properties))a.has(i)&&(o[i]=o6(n,t));return o}case"Promise":return"default"in e?o9(e.default):Promise.resolve(o6(e.item,t));case"Record":let[o,i]=Object.entries(e.patternProperties)[0];if("default"in e)return o9(e.default);if(o===af||o===ak)return{};{let e=o.slice(1,o.length-1).split("|"),a={};for(let o of e)a[o]=o6(i,t);return a}case"Ref":return"default"in e?o9(e.default):o6(oR(e,t),t);case"RegExp":if("default"in e)return o9(e.default);throw new o5(e,"RegExp types cannot be created. Consider using a default value.");case"String":if(void 0!==e.pattern)if("default"in e)return o9(e.default);else throw new o5(e,"String types with patterns must specify a default value");if(void 0!==e.format)if("default"in e)return o9(e.default);else throw new o5(e,"String types with formats must specify a default value");if("default"in e)return o9(e.default);if(void 0!==e.minLength)return Array.from({length:e.minLength}).map(()=>" ").join("");else return"";case"Symbol":return"default"in e?o9(e.default):"value"in e?Symbol.for(e.value):Symbol();case"TemplateLiteral":if("default"in e)return o9(e.default);if(!an(e))throw new o5(e,"Can only create template literals that produce a finite variants. Consider using a default value.");return al(e)[0];case"This":if(o8++>o7)throw new o5(e,"Cannot create recursive type as it appears possibly infinite. Consider using a default.");return"default"in e?o9(e.default):o6(oR(e,t),t);case"Tuple":return"default"in e?o9(e.default):void 0===e.items?[]:Array.from({length:e.minItems}).map((a,o)=>o6(e.items[o],t));case"Undefined":case"Void":return"default"in e?o9(e.default):void 0;case"Union":if("default"in e)return o9(e.default);if(0!==e.anyOf.length)return o6(e.anyOf[0],t);throw Error("ValueCreate.Union: Cannot create Union with zero variants");case"Uint8Array":return"default"in e?o9(e.default):new Uint8Array(void 0!==e.minByteLength?e.minByteLength:0);default:if(!eV(e[A]))throw new o5(e,"Unknown type");if("default"in e)return o9(e.default);throw Error("User defined types must specify a default value")}}let o7=512,o8=0;function ie(...e){return o8=0,2===e.length?o6(e[0],e[1]):o6(e[0],[])}class ia extends d{constructor(e,a){super(a),this.schema=e}}function it(e,a,t){let o=oC(e.$id)?[...a,e]:a;switch(e[A]){case"Array":if(o2(e,o,t))return o4(t);let i=ow(t)?o4(t):ie(e,o),n=oS(e.minItems)&&i.length<e.minItems?[...i,...Array.from({length:e.minItems-i.length},()=>null)]:i,r=(oS(e.maxItems)&&n.length>e.maxItems?n.slice(0,e.maxItems):n).map(a=>it(e.items,o,a));if(!0!==e.uniqueItems)return r;let s=[...new Set(r)];if(!o2(e,o,s))throw new ia(e,"Array cast produced invalid data due to uniqueItems constraint");return s;case"Constructor":if(o2(e,o,t))return ie(e,o);let l=new Set(e.returns.required||[]),u=function(){};for(let[a,i]of Object.entries(e.returns.properties))(l.has(a)||void 0!==t.prototype[a])&&(u.prototype[a]=it(i,o,t.prototype[a]));return u;case"Intersect":let c=ie(e,o),m=oh(c)&&oh(t)?{...c,...t}:t;return o2(e,o,m)?m:ie(e,o);case"Never":throw new ia(e,"Never types cannot be cast");case"Object":if(o2(e,o,t))return t;if(null===t||"object"!=typeof t)return ie(e,o);let p=new Set(e.required||[]),d={};for(let[a,i]of Object.entries(e.properties))(p.has(a)||void 0!==t[a])&&(d[a]=it(i,o,t[a]));if("object"==typeof e.additionalProperties){let a=Object.getOwnPropertyNames(e.properties);for(let i of Object.getOwnPropertyNames(t))a.includes(i)||(d[i]=it(e.additionalProperties,o,t[i]))}return d;case"Record":if(o2(e,o,t))return o4(t);if(null===t||"object"!=typeof t||Array.isArray(t)||t instanceof Date)return ie(e,o);let g=Object.getOwnPropertyNames(e.patternProperties)[0],h=e.patternProperties[g],k={};for(let[e,a]of Object.entries(t))k[e]=it(h,o,a);return k;case"Ref":case"This":return it(oR(e,o),o,t);case"Tuple":return o2(e,o,t)?o4(t):ow(t)?void 0===e.items?[]:e.items.map((e,a)=>it(e,o,t[a])):ie(e,o);case"Union":return o2(e,o,t)?o4(t):"default"in e?"function"==typeof t?e.default:o4(e.default):function(...e){return 3===e.length?it(e[0],e[1],e[2]):it(e[0],[],e[1])}(function(e,a,t){let o=e.anyOf.map(e=>oR(e,a)),[i,n]=[o[0],0];for(let e of o){let o=function(e,a,t){if("Object"!==e[A]||"object"!=typeof t||oO(t))return+!!o2(e,a,t);{let o=Object.getOwnPropertyNames(t),i=Object.entries(e.properties),[n,r]=[1/i.length,i.length];return i.reduce((e,[i,s])=>{let l="Literal"===s[A]&&s.const===t[i]?r:0;return e+(l+(o2(s,a,t[i])?n:0)+(o.includes(i)?n:0))},0)}}(e,a,t);o>n&&(i=e,n=o)}return i}(e,o,t),o,t);case"Date":case"Symbol":case"Uint8Array":return o2(e,a,t)?o4(t):ie(e,a);default:return o2(e,o,t)?t:ie(e,o)}}function io(e,a,t){let o=oC(e.$id)?[...a,e]:a;switch(e[A]){case"Array":return ow(t)?t.map(a=>io(e.items,o,a)):t;case"Intersect":let i=e.unevaluatedProperties,n=e.allOf.map(e=>io(e,o,o4(t))).reduce((e,a)=>oj(a)?{...e,...a}:a,{});if(!oj(t)||!oj(n)||!eH(i))return n;let r=a_(e);for(let e of Object.getOwnPropertyNames(t))!r.includes(e)&&o2(i,o,t[e])&&(n[e]=io(i,o,t[e]));return n;case"Object":if(!oj(t)||ow(t))return t;let s=e.additionalProperties;for(let a of Object.getOwnPropertyNames(t)){if(a in e.properties){t[a]=io(e.properties[a],o,t[a]);continue}if(eH(s)&&o2(s,o,t[a])){t[a]=io(s,o,t[a]);continue}delete t[a]}return t;case"Record":if(!oj(t))return t;let l=e.additionalProperties,u=Object.getOwnPropertyNames(t),[c,m]=Object.entries(e.patternProperties)[0],p=new RegExp(c);for(let e of u){if(p.test(e)){t[e]=io(m,o,t[e]);continue}if(eH(l)&&o2(l,o,t[e])){t[e]=io(l,o,t[e]);continue}delete t[e]}return t;case"Ref":case"This":return io(oR(e,o),o,t);case"Tuple":if(!ow(t))return t;if(ov(e.items))return[];let d=Math.min(t.length,e.items.length);for(let a=0;a<d;a++)t[a]=io(e.items[a],o,t[a]);return t.length>d?t.slice(0,d):t;case"Union":for(let a of e.anyOf)if(eH(a)&&"Unsafe"!==a[A]&&o2(a,o,t))return io(a,o,t);return t;default:return t}}function ii(e){return oC(e)&&!isNaN(e)&&!isNaN(parseFloat(e))}function ir(e){return!0===e||oS(e)&&1===e||oA(e)&&e===BigInt("1")||oC(e)&&("true"===e.toLowerCase()||"1"===e)}function is(e){return!1===e||oS(e)&&(0===e||Object.is(e,-0))||oA(e)&&e===BigInt("0")||oC(e)&&("false"===e.toLowerCase()||"0"===e||"-0"===e)}function il(e){return!!ir(e)||!is(e)&&e}function iu(e){return oA(e)||oz(e)||oS(e)?e.toString():oT(e)&&void 0!==e.description?e.description.toString():e}function ic(e){return ii(e)?parseFloat(e):ir(e)?1:is(e)?0:e}function im(e,a,t){let o=oC(e.$id)?[...a,e]:a;switch(e[A]){case"Array":return(ow(t)?t:[t]).map(a=>im(e.items,o,a));case"BigInt":return ii(t)?BigInt(parseInt(t)):oS(t)?BigInt(0|t):is(t)?BigInt(0):ir(t)?BigInt(1):t;case"Boolean":return il(t);case"Date":return of(t)?t:oS(t)?new Date(t):ir(t)?new Date(1):is(t)?new Date(0):ii(t)?new Date(parseInt(t)):oC(t)&&/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)?$/i.test(t)?new Date(`1970-01-01T${t}.000Z`):oC(t)&&/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i.test(t)?new Date(`1970-01-01T${t}`):oC(t)&&/^\d\d\d\d-[0-1]\d-[0-3]\dt(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)?$/i.test(t)?new Date(`${t}.000Z`):oC(t)&&/^\d\d\d\d-[0-1]\d-[0-3]\dt(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i.test(t)?new Date(t):oC(t)&&/^\d\d\d\d-[0-1]\d-[0-3]\d$/i.test(t)?new Date(`${t}T00:00:00.000Z`):t;case"Integer":return ii(t)?parseInt(t):oS(t)?0|t:ir(t)?1:is(t)?0:t;case"Intersect":return e.allOf.reduce((e,a)=>im(a,o,e),t);case"Literal":return oC(e.const)?function(e,a){let t=iu(e);return t===a?t:e}(t,e.const):oS(e.const)?function(e,a){let t=ic(e);return t===a?t:e}(t,e.const):oz(e.const)?function(e,a){let t=il(e);return t===a?t:e}(t,e.const):o4(t);case"Null":return oC(t)&&"null"===t.toLowerCase()?null:t;case"Number":return ic(t);case"Object":var i;if(!oj(t))return t;let n={};for(let a of Object.keys(t))i=e.properties,n[a]=a in i?im(e.properties[a],o,t[a]):t[a];return n;case"Record":if(!oj(t))return t;let r=Object.getOwnPropertyNames(e.patternProperties)[0],s=e.patternProperties[r],l={};for(let[e,a]of Object.entries(t))l[e]=im(s,o,a);return l;case"Ref":case"This":return im(oR(e,o),o,t);case"String":return iu(t);case"Symbol":return oC(t)||oS(t)?Symbol(t):t;case"Tuple":return ow(t)&&!ov(e.items)?t.map((a,t)=>t<e.items.length?im(e.items[t],o,a):a):t;case"Undefined":return oC(t)&&"undefined"===t?void 0:t;case"Union":for(let a of e.anyOf){let e=im(a,o,t);if(o2(a,o,e))return e}return t;default:return t}}function ip(e,a){return void 0===a&&"default"in e?o4(e.default):a}function id(e){return eH(e)&&"default"in e}function ig(e,a,t){let o=oC(e.$id)?[...a,e]:a;switch(e[A]){case"Array":let i=ip(e,t);if(!ow(i))return i;for(let a=0;a<i.length;a++)i[a]=ig(e.items,o,i[a]);return i;case"Intersect":let n=ip(e,t);return e.allOf.reduce((e,a)=>{let t=ig(a,o,n);return oj(t)?{...e,...t}:t},{});case"Object":let r=ip(e,t);if(!oj(r))return r;let s=e.additionalProperties,l=Object.getOwnPropertyNames(e.properties);for(let a of l)id(e.properties[a])&&(r[a]=ig(e.properties[a],o,r[a]));if(!id(s))return r;for(let e of Object.getOwnPropertyNames(r))l.includes(e)||(r[e]=ig(s,o,r[e]));return r;case"Record":let u=ip(e,t);if(!oj(u))return u;let c=e.additionalProperties,[m,p]=Object.entries(e.patternProperties)[0],d=new RegExp(m);for(let e of Object.getOwnPropertyNames(u))d.test(e)&&id(p)&&(u[e]=ig(p,o,u[e]));if(!id(c))return u;for(let e of Object.getOwnPropertyNames(u))d.test(e)||(u[e]=ig(c,o,u[e]));return u;case"Ref":return ig(oR(e,o),o,ip(e,t));case"This":return ig(oR(e,o),o,t);case"Tuple":let g=ip(e,t);if(!ow(g)||ov(e.items))return g;let[h,k]=[e.items,Math.max(e.items.length,g.length)];for(let e=0;e<k;e++)e<h.length&&(g[e]=ig(h[e],o,g[e]));return g;case"Union":let f=ip(e,t);for(let a of e.anyOf){let e=ig(a,o,f);if(eH(a)&&"Unsafe"!==a[A]&&o2(a,e))return e}return f;default:return ip(e,t)}}class ih extends d{constructor(e,a,t){super("Cannot set root value"),this.value=e,this.path=a,this.update=t}}function ik(e){return -1===e.indexOf("~")?e:e.replace(/~1/g,"/").replace(/~0/g,"~")}function iy(e,a,t){if(""===a)throw new ih(e,a,t);let[o,i,n]=[null,e,""];for(let e of function*(e){if(""===e)return;let[a,t]=[0,0];for(let o=0;o<e.length;o++)"/"===e.charAt(o)?(0===o||(t=o,yield ik(e.slice(a,t))),a=o+1):t=o;yield ik(e.slice(a))}(a))void 0===i[e]&&(i[e]={}),o=i,i=i[e],n=e;o[n]=t}let ib=aA({type:au("insert"),path:ad(),value:a1()});e6([ib,aA({type:au("update"),path:ad(),value:a1()}),aA({type:au("delete"),path:ad()})]);class ij extends d{constructor(e,a){super(a),this.value=e}}class iw extends ij{constructor(e){super(e,"Cannot diff objects with symbol keys"),this.value=e}}function iv(e,a){return{type:"update",path:e,value:a}}function iO(e,a){return{type:"insert",path:e,value:a}}function iz(e){return{type:"delete",path:e}}class iS extends d{constructor(e,a,t){super("Unable to decode value as it does not match the expected schema"),this.schema=e,this.value=a,this.error=t}}class ix extends d{constructor(e,a,t,o){super(o instanceof Error?o.message:"Unknown error"),this.schema=e,this.path=a,this.value=t,this.error=o}}function iA(e,a,t){try{return eR(e)?e[O].Decode(t):t}catch(o){throw new ix(e,a,t,o)}}class iC extends d{constructor(e,a,t){super("The encoded value does not match the expected schema"),this.schema=e,this.value=a,this.error=t}}class iP extends d{constructor(e,a,t,o){super(`${o instanceof Error?o.message:"Unknown error"}`),this.schema=e,this.path=a,this.value=t,this.error=o}}function iT(e,a,t){try{return eR(e)?e[O].Encode(t):t}catch(o){throw new iP(e,a,t,o)}}let iI=new Set;function iE(e,a){return iI.clear(),function e(a,t){let o=oC(a.$id)?[...t,a]:t;if(a.$id&&iI.has(a.$id))return!1;switch(a.$id&&iI.add(a.$id),a[A]){case"Array":case"AsyncIterator":case"Iterator":return eR(a)||e(a.items,o);case"Constructor":return eR(a)||e(a.returns,o)||a.parameters.some(a=>e(a,o));case"Function":return eR(a)||e(a.returns,o)||a.parameters.some(a=>e(a,o));case"Intersect":return eR(a)||eR(a.unevaluatedProperties)||a.allOf.some(a=>e(a,o));case"Not":return eR(a)||e(a.not,o);case"Object":return eR(a)||Object.values(a.properties).some(a=>e(a,o))||eH(a.additionalProperties)&&e(a.additionalProperties,o);case"Promise":return eR(a)||e(a.item,o);case"Record":let i=Object.getOwnPropertyNames(a.patternProperties)[0],n=a.patternProperties[i];return eR(a)||e(n,o)||eH(a.additionalProperties)&&eR(a.additionalProperties);case"Ref":case"This":return!!eR(a)||e(oR(a,o),o);case"Tuple":return eR(a)||!ov(a.items)&&a.items.some(a=>e(a,o));case"Union":return eR(a)||a.anyOf.some(a=>e(a,o));default:return eR(a)}}(e,a)}function iD(...e){return o2.apply(o2,e)}function iN(...e){let[a,t,o]=3===e.length?[e[0],e[1],e[2]]:[e[0],[],e[1]];if(!iD(a,t,o))throw new iS(a,o,iL(a,t,o).First());return iE(a,t)?function e(a,t,o,i){let n="string"==typeof a.$id?[...t,a]:t;switch(a[A]){case"Array":return ow(i)?iA(a,o,i.map((t,i)=>e(a.items,n,`${o}/${i}`,t))):iA(a,o,i);case"Intersect":return function(a,t,o,i){if(!oh(i)||oI(i))return iA(a,o,i);let n=aV(a),r=n.map(e=>e[0]),s={...i};for(let[a,i]of n)a in s&&(s[a]=e(i,t,`${o}/${a}`,s[a]));if(!eR(a.unevaluatedProperties))return iA(a,o,s);let l=Object.getOwnPropertyNames(s),u=a.unevaluatedProperties,c={...s};for(let e of l)r.includes(e)||(c[e]=iA(u,`${o}/${e}`,c[e]));return iA(a,o,c)}(a,n,o,i);case"Not":return iA(a,o,e(a.not,n,o,i));case"Object":return function(a,t,o,i){if(!oh(i))return iA(a,o,i);let n=a_(a),r={...i};for(let i of n)i in r&&(r[i]=e(a.properties[i],t,`${o}/${i}`,r[i]));if(!eH(a.additionalProperties))return iA(a,o,r);let s=Object.getOwnPropertyNames(r),l=a.additionalProperties,u={...r};for(let e of s)n.includes(e)||(u[e]=iA(l,`${o}/${e}`,u[e]));return iA(a,o,u)}(a,n,o,i);case"Record":return function(a,t,o,i){if(!oh(i))return iA(a,o,i);let n=Object.getOwnPropertyNames(a.patternProperties)[0],r=new RegExp(n),s={...i};for(let l of Object.getOwnPropertyNames(i))r.test(l)&&(s[l]=e(a.patternProperties[n],t,`${o}/${l}`,s[l]));if(!eH(a.additionalProperties))return iA(a,o,s);let l=Object.getOwnPropertyNames(s),u=a.additionalProperties,c={...s};for(let e of l)r.test(e)||(c[e]=iA(u,`${o}/${e}`,c[e]));return iA(a,o,c)}(a,n,o,i);case"Ref":let r=oR(a,n);return iA(a,o,e(r,n,o,i));case"Symbol":default:return iA(a,o,i);case"This":let s=oR(a,n);return iA(a,o,e(s,n,o,i));case"Tuple":return ow(i)&&ow(a.items)?iA(a,o,a.items.map((a,t)=>e(a,n,`${o}/${t}`,i[t]))):iA(a,o,i);case"Union":for(let t of a.anyOf){if(!o2(t,n,i))continue;let r=e(t,n,o,i);return iA(a,o,r)}return iA(a,o,i)}}(a,t,"",o):o}function iL(...e){return oX.apply(oX,e)}let iR=(e,a)=>{let t=e.schema.title;throw a.logErrors&&(!function(e){let{error:a}=e;console.log(JSON.stringify(a,null,2))}(e),console.log(`
    This may happen intermittently and you should catch errors appropriately.
    However:  1) if this recently started happening on every request for a symbol
    that used to work, Yahoo may have changed their API.  2) If this happens on
    every request for a symbol you've never used before, but not for other
    symbols, you've found an edge-case (OR, we may just be protecting you from
    "bad" data sometimes stored for e.g. misspelt symbols on Yahoo's side).
    Please see if anyone has reported this previously:
    
      ${t4.Jk}/issues?q=is%3Aissue+${t}
    
    or open a new issue (and mention the symbol):  ${t4.UU} v${t4.rE}
    
      ${t4.Jk}/issues/new?labels=bug%2C+validation&template=validation.md&title=${t}
    
    For information on how to turn off the above logging or skip these errors,
    see https://github.com/gadicc/node-yahoo-finance2/tree/devel/docs/validation.md.
    
    At the end of the doc, there's also a section on how to
    [Help Fix Validation Errors](https://github.com/gadicc/node-yahoo-finance2/blob/devel/docs/validation.md#help-fix)
    in case you'd like to contribute to the project.  Most of the time, these
    fixes are very quick and easy; it's just hard for our small core team to keep up,
    so help is always appreciated!
    `)),new t3("Failed Yahoo Schema validation",{result:e.value,errors:[e]})},iM=(e,{logOptionsErrors:a})=>{throw a&&console.error(`[yahooFinance] Invalid options ("${JSON.stringify(e.error,null,2)}")`),new t1("Validation called with invalid options")},iF=({type:e,data:a,schema:t,options:o})=>{try{let e=o._internalThrowOnAdditionalProperties?{...t,additionalProperties:!1}:t;return iN(e,a)}catch(a){throw(a instanceof ix||a instanceof iS)&&("result"===e?iR(a,o):iM(a,o)),a}};function iU(e){return e.split(" ").map((e,a)=>0===a?e.toLowerCase():e[0].toUpperCase()+e.substr(1).toLowerCase()).join("")}async function iq(e){var a,t;let o=e.query,i=e.moduleOptions,n=e.moduleName,r=e.result;if(o.assertSymbol){let e=o.assertSymbol;if("string"!=typeof e)throw Error(`yahooFinance.${n}() expects a single string symbol as its query, not a(n) ${typeof e}: ${JSON.stringify(e)}`)}iF({type:"options",data:null!=(a=o.overrides)?a:{},schema:o.schema,options:this._opts.validation});let s={...o.defaults,...o.runtime,...o.overrides};o.transformWith&&(s=o.transformWith(s));let l=await this._fetch(o.url,s,i,o.fetchType,null!=(t=o.needsCrumb)&&t);"csv"===o.fetchType&&(l=function(e){let a=e.split("\n"),t=a.shift().split(",").map(iU),o=Array(a.length);for(let e=0;e<a.length;e++){let n=a[e].split(","),r=o[e]={};for(let e=0;e<n.length;e++){var i;r[t[e]]=(i=n[e]).match(/\d{4,4}-\d{2,2}-\d{2,2}/)?new Date(i):i.match(/^[0-9\.]+$/)?parseFloat(i):"null"===i?null:i}}return o}(l)),r.transformWith&&(l=r.transformWith(l));let u=!i||void 0===i.validateResult||!0===i.validateResult,c={...this._opts.validation,logErrors:!!u&&this._opts.validation.logErrors};try{return iF({type:"result",data:l,schema:r.schema,options:c})}catch(e){if(u)throw e}return l}async function i$(){throw Error("Yahoo decomissioned their autoc server sometime before 20 Nov 2021 (see https://github.com/gadicc/node-yahoo-finance2/issues/337])). Use `search` instead (just like they do).")}let iB=[0,31,28,31,30,31,30,31,31,30,31,30,31],iH=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,i_=/^(\d\d\d\d)$/,iQ=/^(\d\d):(\d\d):(\d\d(?:\.\d+)?)(z|([+-])(\d\d)(?::?(\d\d))?)?$/i,iY=/t|\s/i,iV=e=>{let a=iH.exec(e);if(!a)return!1;let t=+a[1],o=+a[2],i=+a[3];return o>=1&&o<=12&&i>=1&&i<=(2===o&&function(e){return e%4==0&&(e%100!=0||e%400==0)}(t)?29:iB[o])},iW=(e,a)=>{let t=iQ.exec(e);if(!t)return!1;let o=+t[1],i=+t[2],n=+t[3],r=t[4],s="-"===t[5]?-1:1,l=+(t[6]||0),u=+(t[7]||0);if(l>23||u>59||a&&!r)return!1;if(o<=23&&i<=59&&n<60)return!0;let c=i-u*s,m=o-l*s-(c<0);return(23===m||-1===m)&&(59===c||-1===c)&&n<61};eQ("date",iV),eQ("date-time",(e,a)=>{let t=e.split(iY);return 2===t.length&&iV(t[0])&&iW(t[1],a)}),eQ("year",e=>!!i_.exec(e));let iG=p.Transform(p.Object({},{maxProperties:0,title:"EmptyObjectCoerceToNull"})).Decode(()=>null).Encode(()=>({})),iJ=p.Transform(p.Object({raw:p.Number()},{title:"RawNumber"})).Decode(e=>e.raw).Encode(e=>({raw:e})),iK=p.Transform(p.RegExp(/^(-?\d+(?:\.\d+)?) - (-?\d+(?:\.\d+)?)$/g,{title:"TwoNumberRangeString"})).Decode(e=>{let a=e.match(/-?\d+(?:\.\d+)?/g);if(!a)throw Error(`Unable to decode number range from: ${e}`);let[t,o]=a.map(e=>parseFloat(e));if(isNaN(t)||isNaN(o))throw Error(`Unable to decode number range from: ${e}. Decoded value for low is: ${t}, decoded value for high is: ${o}`);return{low:t,high:o}}).Encode(({low:e,high:a})=>`${e} - ${a}`),iZ=p.Object({low:p.Number(),high:p.Number()},{title:"TwoNumberRange"}),iX=p.Transform(p.Number()).Decode(e=>new Date(1e3*e)).Encode(e=>e/1e3),i1=p.Transform(p.Object({raw:iX},{title:"RawDateObject"})).Decode(e=>e.raw).Encode(e=>({raw:function(...e){let[a,t,o]=3===e.length?[e[0],e[1],e[2]]:[e[0],[],e[1]],i=iE(a,t)?function e(a,t,o,i){let n="string"==typeof a.$id?[...t,a]:t;switch(a[A]){case"Array":let r=iT(a,o,i);return ow(r)?r.map((t,i)=>e(a.items,n,`${o}/${i}`,t)):r;case"Intersect":return function(a,t,o,i){let n=iT(a,o,i);if(!oh(i)||oI(i))return n;let r=aV(a),s=r.map(e=>e[0]),l={...n};for(let[a,i]of r)a in l&&(l[a]=e(i,t,`${o}/${a}`,l[a]));if(!eR(a.unevaluatedProperties))return iT(a,o,l);let u=Object.getOwnPropertyNames(l),c=a.unevaluatedProperties,m={...l};for(let e of u)s.includes(e)||(m[e]=iT(c,`${o}/${e}`,m[e]));return m}(a,n,o,i);case"Not":return iT(a.not,o,iT(a,o,i));case"Object":return function(a,t,o,i){let n=iT(a,o,i);if(!oh(n))return n;let r=a_(a),s={...n};for(let i of r)i in s&&(s[i]=e(a.properties[i],t,`${o}/${i}`,s[i]));if(!eH(a.additionalProperties))return s;let l=Object.getOwnPropertyNames(s),u=a.additionalProperties,c={...s};for(let e of l)r.includes(e)||(c[e]=iT(u,`${o}/${e}`,c[e]));return c}(a,n,o,i);case"Record":return function(a,t,o,i){let n=iT(a,o,i);if(!oh(i))return n;let r=Object.getOwnPropertyNames(a.patternProperties)[0],s=new RegExp(r),l={...n};for(let n of Object.getOwnPropertyNames(i))s.test(n)&&(l[n]=e(a.patternProperties[r],t,`${o}/${n}`,l[n]));if(!eH(a.additionalProperties))return iT(a,o,l);let u=Object.getOwnPropertyNames(l),c=a.additionalProperties,m={...l};for(let e of u)s.test(e)||(m[e]=iT(c,`${o}/${e}`,m[e]));return m}(a,n,o,i);case"Ref":let s=e(oR(a,n),n,o,i);return iT(a,o,s);case"This":let l=e(oR(a,n),n,o,i);return iT(a,o,l);case"Tuple":let u=iT(a,o,i);return ow(a.items)?a.items.map((a,t)=>e(a,n,`${o}/${t}`,u[t])):[];case"Union":for(let t of a.anyOf){if(!o2(t,n,i))continue;let r=e(t,n,o,i);return iT(a,o,r)}for(let t of a.anyOf){let r=e(t,n,o,i);if(o2(a,n,r))return iT(a,o,r)}return iT(a,o,i);default:return iT(a,o,i)}}(a,t,"",o):o;if(!iD(a,t,i))throw new iC(a,i,iL(a,t,i).First());return i}(iX,e)})),i0=p.Transform(p.Union([p.String({format:"date"}),p.String({format:"year"}),p.String({format:"date-time"})],{title:"ISOStringDate"})).Decode(e=>new Date(e)).Encode(e=>e.toISOString()),i3=p.Union([p.Date(),iX,i1,i0],{title:"YahooFinanceDate"}),i2=p.Union([i3,p.Null(),iG],{title:"NullableYahooFinanceDate"}),i4=p.Union([iJ,p.Number()],{title:"YahooNumber"}),i5=p.Transform(p.Number({title:"YahooDateInMs"})).Decode(e=>new Date(e)).Encode(e=>+e),i9=p.Union([iJ,iG,p.Number(),p.Null()],{title:"NullableYahooNumber"}),i6=p.Union([iZ,iK],{title:"YahooTwoNumberRange"}),i7=p.Object({timezone:p.String(),start:i3,end:i3,gmtoffset:i4},{additionalProperties:p.Any(),title:"ChartMetaTradingPeriod"}),i8=p.Object({pre:p.Optional(p.Array(p.Array(i7))),post:p.Optional(p.Array(p.Array(i7))),regular:p.Optional(p.Array(p.Array(i7)))},{additionalProperties:p.Any(),title:"ChartMetaTradingPeriods"}),ne=p.Object({date:i3,high:p.Union([i4,p.Null()]),low:p.Union([i4,p.Null()]),open:p.Union([i4,p.Null()]),close:p.Union([i4,p.Null()]),volume:p.Union([i4,p.Null()]),adjclose:p.Optional(p.Union([i4,p.Null()]))},{additionalProperties:p.Any(),title:"ChartResultArrayQuote"}),na=p.Object({amount:i4,date:i3},{additionalProperties:p.Any(),title:"ChartEventDividend"}),nt=p.Object({},{additionalProperties:na,title:"ChartEventDividends"}),no=p.Object({date:i3,numerator:i4,denominator:i4,splitRatio:p.String()},{additionalProperties:p.Any()}),ni=p.Object({dividends:p.Optional(p.Array(na)),splits:p.Optional(p.Array(no))},{additionalProperties:p.Any(),title:"ChartEventsArray"}),nn=p.Object({currency:p.String(),symbol:p.String(),exchangeName:p.String(),instrumentType:p.String(),firstTradeDate:p.Union([i3,p.Null()]),regularMarketTime:i3,gmtoffset:i4,timezone:p.String(),exchangeTimezoneName:p.String(),regularMarketPrice:i4,chartPreviousClose:p.Optional(i4),previousClose:p.Optional(i4),scale:p.Optional(i4),priceHint:i4,currentTradingPeriod:p.Object({pre:i7,regular:i7,post:i7},{additionalProperties:p.Any()}),tradingPeriods:p.Optional(p.Union([i8,p.Array(p.Array(i7))])),dataGranularity:p.String(),range:p.String(),validRanges:p.Array(p.String())},{additionalProperties:p.Any(),title:"ChartMeta"});p.Object({meta:nn,events:p.Optional(ni),quotes:p.Array(ne)},{title:"ChartResultArray"});let nr=p.Object({},{additionalProperties:no,title:"ChartEventSplits"}),ns=p.Object({high:p.Array(p.Union([i4,p.Null()])),low:p.Array(p.Union([i4,p.Null()])),open:p.Array(p.Union([i4,p.Null()])),close:p.Array(p.Union([i4,p.Null()])),volume:p.Array(p.Union([i4,p.Null()]))},{additionalProperties:p.Any(),title:"ChartIndicatorQuote"}),nl=p.Object({adjclose:p.Optional(p.Array(p.Union([i4,p.Null()])))},{additionalProperties:p.Any(),title:"ChartIndicatorAdjClose"}),nu=p.Object({dividends:p.Optional(nt),splits:p.Optional(nr)},{additionalProperties:p.Any()}),nc=p.Object({quote:p.Array(ns),adjclose:p.Optional(p.Array(nl))},{additionalProperties:p.Any(),title:"ChartIndicatorObject"}),nm=p.Object({meta:nn,timestamp:p.Optional(p.Array(i4)),events:p.Optional(nu),indicators:nc},{additionalProperties:p.Any(),title:"ChartResultObject"}),np=p.Object({period1:p.Union([p.Date(),p.String(),i4]),period2:p.Optional(p.Union([p.Date(),p.String(),i4])),useYfid:p.Optional(p.Boolean()),interval:p.Optional(p.Union([p.Literal("1m"),p.Literal("2m"),p.Literal("5m"),p.Literal("15m"),p.Literal("30m"),p.Literal("60m"),p.Literal("90m"),p.Literal("1h"),p.Literal("1d"),p.Literal("5d"),p.Literal("1wk"),p.Literal("1mo"),p.Literal("3mo")])),includePrePost:p.Optional(p.Boolean()),events:p.Optional(p.String()),lang:p.Optional(p.String()),return:p.Optional(p.Union([p.Literal("array"),p.Literal("object")]))},{title:"ChartOptions"});p.Composite([np,p.Object({return:p.Optional(p.Literal("array"))})],{title:"ChartOptionsWithReturnArray"}),p.Composite([np,p.Object({return:p.Literal("object")})],{title:"ChartOptionsWithReturnObject"});let nd={useYfid:!0,interval:"1d",includePrePost:!0,events:"div|split|earn",lang:"en-US",return:"array"};async function ng(e,a,t){var o,i,n;let r=(null==a?void 0:a.return)||"array",s=await this._moduleExec({moduleName:"chart",query:{assertSymbol:e,url:"https://${YF_QUERY_HOST}/v8/finance/chart/"+e,schema:np,defaults:nd,overrides:a,transformWith(e){for(let a of(e.period2||(e.period2=new Date),["period1","period2"])){let t=e[a];if(t instanceof Date)e[a]=Math.floor(t.getTime()/1e3);else if("string"==typeof t){let o=new Date(t).getTime();if(isNaN(o))throw Error("yahooFinance.chart() option '"+a+"' invalid date provided: '"+t+"'");e[a]=Math.floor(o/1e3)}}if(e.period1===e.period2)throw Error("yahooFinance.chart() options `period1` and `period2` cannot share the same value.");return delete e.return,e}},result:{schema:nm,transformWith(e){if(!e.chart)throw Error("Unexpected result: "+JSON.stringify(e));let a=e.chart.result[0];if(!a.timestamp){if(1!==a.indicators.quote.length)throw Error("No timestamp with quotes.length !== 1, please report with your query");if(0!==Object.keys(a.indicators.quote[0]).length)throw Error("No timestamp with unexpected quote, please report with your query"+JSON.stringify(a.indicators.quote[0]));a.indicators.quote.pop()}return a}},moduleOptions:t});if("object"===r)return s;if("array"===r){let e=s.timestamp;if(e&&(null==(o=null==s?void 0:s.indicators)?void 0:o.quote)&&s.indicators.quote[0].high.length!==e.length)throw console.log({origTimestampSize:s.timestamp&&s.timestamp.length,filteredSize:e.length,quoteSize:s.indicators.quote[0].high.length}),Error("Timestamp count mismatch, please report this with the query you used");let a={meta:s.meta,quotes:e?Array(e.length):[]},t=null==(n=null==(i=null==s?void 0:s.indicators)?void 0:i.adjclose)?void 0:n[0].adjclose;if(e)for(let o=0;o<e.length;o++)a.quotes[o]={date:new Date(1e3*e[o]),high:s.indicators.quote[0].high[o],volume:s.indicators.quote[0].volume[o],open:s.indicators.quote[0].open[o],low:s.indicators.quote[0].low[o],close:s.indicators.quote[0].close[o]},t&&(a.quotes[o].adjclose=t[o]);if(s.events)for(let e of(a.events={},["dividends","splits"]))s.events[e]&&(a.events[e]=Object.values(s.events[e]));return a}}let nh=p.Object({date:i3,open:i4,high:i4,low:i4,close:i4,adjClose:p.Optional(i4),volume:i4},{additionalProperties:p.Any(),title:"HistoricalRowHistory"}),nk=p.Object({date:i3,dividends:i4},{title:"HistoricalRowDividend"}),nf=p.Object({date:i3,stockSplits:p.String()},{title:"HistoricalRowStockSplit"}),ny=p.Object({period1:p.Union([p.Date(),p.String(),p.Number()]),period2:p.Optional(p.Union([p.Date(),p.String(),p.Number()])),interval:p.Optional(p.Union([p.Literal("1d"),p.Literal("1wk"),p.Literal("1mo")])),events:p.Optional(p.Union([p.Literal("history"),p.Literal("dividends"),p.Literal("split")])),includeAdjustedClose:p.Optional(p.Boolean())},{title:"HistoricalOptions"});p.Composite([ny,p.Object({events:p.Optional(p.Literal("history"))})],{title:"HistoricalOptionsEventsHistory"}),p.Composite([ny,p.Object({events:p.Literal("dividends")})],{title:"HistoricalOptionsEventsDividends"}),p.Composite([ny,p.Object({events:p.Literal("split")})],{title:"HistoricalOptionsEventsSplit"});let nb=p.Array(nh,{title:"HistoricalHistoryResult"}),nj=p.Array(nk,{title:"HistoricalDividendsResult"}),nw=p.Array(nf,{title:"HistoricalRowStockSplit"}),nv={interval:"1d",events:"history",includeAdjustedClose:!0};async function nO(e,a,t){var o,i,n,r,s;let l,u;if(oo("ripHistorical"),iF({type:"options",data:null!=a?a:{},schema:ny,options:this._opts.validation}),a.events&&"history"!==a.events)if("dividends"===a.events)l=nj;else if("split"===a.events)l=nw;else throw Error("No such event type:"+a.events);else l=nb;let c={...nv,...a};if(!iD(ny,c))throw Error("Internal error, please report.  Overrides validated but not defaults?");let m={period1:c.period1,period2:c.period2,interval:c.interval,events:{history:"",dividends:"div",split:"split"}[c.events||"history"]};if(!iD(np,m))throw Error("Internal error, please report.  historical() provided invalid chart() query options.");c.includeAdjustedClose;let p=await this.chart(e,m,{...t,validateResult:!0});u="dividends"===c.events?(null!=(i=null==(o=p.events)?void 0:o.dividends)?i:[]).map(e=>({date:e.date,dividends:e.amount})):"split"===c.events?(null!=(r=null==(n=p.events)?void 0:n.splits)?r:[]).map(e=>({date:e.date,stockSplits:e.splitRatio})):(null!=(s=p.quotes)?s:[]).filter(e=>{let a=Object.keys(e).length,t=function(e){if(null==e)return;let a=0;for(let t of Object.values(e))null===t&&a++;return a}(e);if(0===t)return!0;if(t===a-1)return!1;throw console.error(t,e),Error("Historical returned a result with SOME (but not all) null values.  Please report this, and provide the query that caused it.")}).map(e=>{if(!e.adjclose)return e;let{adjclose:a,...t}=e;return{...t,adjClose:a}});let d=!t||void 0===t.validateResult||!0===t.validateResult,g={...this._opts.validation,logErrors:!!d&&this._opts.validation.logErrors};try{return iF({type:"result",data:u,schema:l,options:g})}catch(e){if(d)throw e}return u}let nz=p.Union([p.Literal("Bearish"),p.Literal("Bullish"),p.Literal("Neutral")],{title:"InsightsDirection"}),nS=p.Object({stateDescription:p.String(),direction:nz,score:i4,scoreDescription:p.String(),sectorDirection:p.Optional(nz),sectorScore:p.Optional(i4),sectorScoreDescription:p.Optional(p.String()),indexDirection:nz,indexScore:i4,indexScoreDescription:p.String()},{additionalProperties:p.Any(),title:"InsightsOutlook"}),nx=p.Object({keyTechnicals:p.Object({provider:p.String(),support:p.Optional(i4),resistance:p.Optional(i4),stopLoss:p.Optional(i4)},{additionalProperties:p.Any()}),technicalEvents:p.Object({provider:p.String(),sector:p.Optional(p.String()),shortTermOutlook:nS,intermediateTermOutlook:nS,longTermOutlook:nS},{additionalProperties:p.Any()}),valuation:p.Object({color:p.Optional(i4),description:p.Optional(p.String()),discount:p.Optional(p.String()),provider:p.String(),relativeValue:p.Optional(p.String())},{additionalProperties:p.Any()})},{additionalProperties:p.Any(),title:"InsightsInstrumentInfo"}),nA=p.Object({sectorInfo:p.Optional(p.String()),company:p.Object({innovativeness:p.Optional(i4),hiring:p.Optional(i4),sustainability:p.Optional(i4),insiderSentiments:p.Optional(i4),earningsReports:p.Optional(i4),dividends:p.Optional(i4)},{additionalProperties:p.Any()}),sector:p.Object({innovativeness:i4,hiring:i4,sustainability:p.Optional(i4),insiderSentiments:i4,earningsReports:p.Optional(i4),dividends:i4},{additionalProperties:p.Any()})},{title:"InsightsCompanySnapshot",additionalProperties:p.Any()}),nC=p.Object({eventType:p.String(),pricePeriod:p.String(),tradingHorizon:p.String(),tradeType:p.String(),imageUrl:p.String(),startDate:i3,endDate:i3},{title:"InsightsEvent",additionalProperties:p.Any()}),nP=p.Object({id:p.String(),headHtml:p.String(),provider:p.String(),reportDate:i3,reportTitle:p.String(),reportType:p.String(),targetPrice:p.Optional(i4),targetPriceStatus:p.Optional(p.Union([p.Literal("Increased"),p.Literal("Maintained"),p.Literal("Decreased"),p.Literal("-")])),investmentRating:p.Optional(p.Union([p.Literal("Bullish"),p.Literal("Neutral"),p.Literal("Bearish")])),tickers:p.Optional(p.Array(p.String()))},{title:"InsightsReport",additionalProperties:p.Any()}),nT=p.Object({headline:p.String(),date:i3},{title:"InsightsSigDev",additionalProperties:p.Any()}),nI=p.Object({msBullishSummary:p.Optional(p.Array(p.String())),msBearishSummary:p.Optional(p.Array(p.String())),msBullishBearishSummariesPublishDate:p.Optional(i5),companyName:p.Optional(p.String()),upsellReportType:p.Optional(p.String())},{title:"InsightsUpsell",additionalProperties:p.Any()}),nE=p.Object({reportId:p.String(),provider:p.String(),title:p.String(),reportDate:i3,summary:p.String(),investmentRating:p.Optional(p.Union([p.Literal("Bullish"),p.Literal("Neutral"),p.Literal("Bearish")]))},{title:"InsightsResearchReport"}),nD=p.Object({id:p.String(),type:p.String(),title:p.String(),description:p.String(),filingDate:i5,snapshotUrl:p.String(),formType:p.String()},{title:"InsightsSecReport",additionalProperties:p.Any()}),nN=p.Object({symbol:p.String(),instrumentInfo:p.Optional(nx),companySnapshot:p.Optional(nA),recommendation:p.Optional(p.Object({targetPrice:p.Optional(i4),provider:p.String(),rating:p.Union([p.Literal("BUY"),p.Literal("SELL"),p.Literal("HOLD")])})),events:p.Optional(p.Array(nC)),reports:p.Optional(p.Array(nP)),sigDevs:p.Optional(p.Array(nT)),upsell:p.Optional(nI),upsellSearchDD:p.Optional(p.Object({researchReports:nE})),secReports:p.Optional(p.Array(nD))},{additionalProperties:p.Any(),title:"InsightsResult"}),nL=p.Object({lang:p.Optional(p.String()),region:p.Optional(p.String()),reportsCount:p.Optional(i4)},{title:"InsightsOptions"}),nR={lang:"en-US",region:"US",getAllResearchReports:!0,reportsCount:2},nM=p.Object({language:p.String(),region:p.String(),quoteType:p.String(),typeDisp:p.Optional(p.String()),quoteSourceName:p.Optional(p.String()),triggerable:p.Boolean(),currency:p.Optional(p.String()),customPriceAlertConfidence:p.Optional(p.String()),marketState:p.Union([p.Literal("REGULAR"),p.Literal("CLOSED"),p.Literal("PRE"),p.Literal("PREPRE"),p.Literal("POST"),p.Literal("POSTPOST")]),tradeable:p.Boolean(),cryptoTradeable:p.Optional(p.Boolean()),exchange:p.String(),shortName:p.Optional(p.String()),longName:p.Optional(p.String()),messageBoardId:p.Optional(p.String()),exchangeTimezoneName:p.String(),exchangeTimezoneShortName:p.String(),gmtOffSetMilliseconds:i4,market:p.String(),esgPopulated:p.Boolean(),fiftyTwoWeekLowChange:p.Optional(i4),fiftyTwoWeekLowChangePercent:p.Optional(i4),fiftyTwoWeekRange:p.Optional(i6),fiftyTwoWeekHighChange:p.Optional(i4),fiftyTwoWeekHighChangePercent:p.Optional(i4),fiftyTwoWeekLow:p.Optional(i4),fiftyTwoWeekHigh:p.Optional(i4),fiftyTwoWeekChangePercent:p.Optional(i4),dividendDate:p.Optional(i3),earningsTimestamp:p.Optional(i3),earningsTimestampStart:p.Optional(i3),earningsTimestampEnd:p.Optional(i3),trailingAnnualDividendRate:p.Optional(i4),trailingPE:p.Optional(i4),trailingAnnualDividendYield:p.Optional(i4),epsTrailingTwelveMonths:p.Optional(i4),epsForward:p.Optional(i4),epsCurrentYear:p.Optional(i4),priceEpsCurrentYear:p.Optional(i4),sharesOutstanding:p.Optional(i4),bookValue:p.Optional(i4),fiftyDayAverage:p.Optional(i4),fiftyDayAverageChange:p.Optional(i4),fiftyDayAverageChangePercent:p.Optional(i4),twoHundredDayAverage:p.Optional(i4),twoHundredDayAverageChange:p.Optional(i4),twoHundredDayAverageChangePercent:p.Optional(i4),marketCap:p.Optional(i4),forwardPE:p.Optional(i4),priceToBook:p.Optional(i4),sourceInterval:i4,exchangeDataDelayedBy:i4,firstTradeDateMilliseconds:p.Optional(i5),priceHint:i4,postMarketChangePercent:p.Optional(i4),postMarketTime:p.Optional(i3),postMarketPrice:p.Optional(i4),postMarketChange:p.Optional(i4),regularMarketChange:p.Optional(i4),regularMarketChangePercent:p.Optional(i4),regularMarketTime:p.Optional(i3),regularMarketPrice:p.Optional(i4),regularMarketDayHigh:p.Optional(i4),regularMarketDayRange:p.Optional(i6),regularMarketDayLow:p.Optional(i4),regularMarketVolume:p.Optional(i4),regularMarketPreviousClose:p.Optional(i4),preMarketChange:p.Optional(i4),preMarketChangePercent:p.Optional(i4),preMarketTime:p.Optional(i3),preMarketPrice:p.Optional(i4),bid:p.Optional(i4),ask:p.Optional(i4),bidSize:p.Optional(i4),askSize:p.Optional(i4),fullExchangeName:p.String(),financialCurrency:p.Optional(p.String()),regularMarketOpen:p.Optional(i4),averageDailyVolume3Month:p.Optional(i4),averageDailyVolume10Day:p.Optional(i4),displayName:p.Optional(p.String()),symbol:p.String(),underlyingSymbol:p.Optional(p.String()),ytdReturn:p.Optional(i4),trailingThreeMonthReturns:p.Optional(i4),trailingThreeMonthNavReturns:p.Optional(i4),ipoExpectedDate:p.Optional(i3),newListingDate:p.Optional(i3),nameChangeDate:p.Optional(i3),prevName:p.Optional(p.String()),averageAnalystRating:p.Optional(p.String()),pageViewGrowthWeekly:p.Optional(i4),openInterest:p.Optional(i4),beta:p.Optional(i4)},{additionalProperties:p.Any()}),nF=p.Composite([nM,p.Object({quoteType:p.Literal("CRYPTOCURRENCY"),circulatingSupply:i4,fromCurrency:p.String(),toCurrency:p.String(),lastMarket:p.String(),coinImageUrl:p.Optional(p.String()),volume24Hr:p.Optional(i4),volumeAllCurrencies:p.Optional(i4),startDate:p.Optional(i3)})]),nU=p.Composite([nM,p.Object({quoteType:p.Literal("CURRENCY")})]),nq=p.Composite([nM,p.Object({quoteType:p.Literal("ETF")})]),n$=p.Composite([nM,p.Object({quoteType:p.Literal("EQUITY"),dividendRate:p.Optional(p.Number()),dividendYield:p.Optional(p.Number())})]),nB=p.Composite([nM,p.Object({quoteType:p.Literal("FUTURE"),headSymbolAsString:p.String(),contractSymbol:p.Boolean(),underlyingExchangeSymbol:p.String(),expireDate:i3,expireIsoDate:i3})]),nH=p.Composite([nM,p.Object({quoteType:p.Literal("INDEX")})]),n_=p.Composite([nM,p.Object({quoteType:p.Literal("OPTION"),strike:i4,openInterest:i4,expireDate:i4,expireIsoDate:i4,underlyingSymbol:p.String()})]),nQ=p.Composite([nM,p.Object({quoteType:p.Literal("MUTUALFUND")})]),nY=p.Union([nF,nU,nq,n$,nB,nH,nQ,n_]),nV=p.KeyOf(nY),nW=p.Union([p.Literal("array"),p.Literal("object"),p.Literal("map")]),nG=p.Array(nY),nJ=p.Object({fields:p.Optional(p.Array(nV)),return:p.Optional(nW)});p.Composite([nJ,p.Object({return:p.Optional(p.Literal("array"))})]),p.Composite([nJ,p.Object({return:p.Literal("map")})]),p.Composite([nJ,p.Object({return:p.Literal("object")})]);let nK={};async function nZ(e,a,t){let o="string"==typeof e?e:e.join(","),i=a&&a.return,n=await this._moduleExec({moduleName:"quote",query:{url:"https://${YF_QUERY_HOST}/v7/finance/quote",needsCrumb:!0,schema:nJ,defaults:nK,runtime:{symbols:o},overrides:a,transformWith:e=>(e.fields&&e.fields.join(","),delete e.return,e)},result:{schema:nG,transformWith(e){var a;let t=null==(a=null==e?void 0:e.quoteResponse)?void 0:a.result;if(!t||!Array.isArray(t))throw Error("Unexpected result: "+JSON.stringify(e));return t.filter(e=>(null==e?void 0:e.quoteType)!=="NONE")}},moduleOptions:t});if(!i)return"string"==typeof e?n[0]:n;switch(i){case"array":return n;case"object":{let e={};for(let a of n)e[a.symbol]=a;return e}case"map":{let e=new Map;for(let a of n)e.set(a.symbol,a);return e}}}let nX=p.Composite([nM,p.Object({quoteType:p.Literal("CRYPTOCURRENCY"),circulatingSupply:i4,fromCurrency:p.String(),toCurrency:p.String(),lastMarket:p.String(),coinImageUrl:p.Optional(p.String()),volume24Hr:p.Optional(i4),volumeAllCurrencies:p.Optional(i4),startDate:p.Optional(i3)})],{title:"QuoteCryptoCurrency"}),n1=p.Composite([nM,p.Object({quoteType:p.Literal("CURRENCY")})],{title:"QuoteCurrency"}),n0=p.Composite([nM,p.Object({quoteType:p.Literal("ETF")})]),n3=p.Composite([nM,p.Object({quoteType:p.Literal("EQUITY"),dividendRate:p.Optional(p.Number()),dividendYield:p.Optional(p.Number())})],{title:"QuoteEquity"}),n2=p.Composite([nM,p.Object({quoteType:p.Literal("FUTURE"),headSymbolAsString:p.String(),contractSymbol:p.Boolean(),underlyingExchangeSymbol:p.String(),expireDate:i3,expireIsoDate:i3})],{title:"QuoteFuture"}),n4=p.Composite([nM,p.Object({quoteType:p.Literal("INDEX")})],{title:"QuoteIndex"}),n5=p.Composite([nM,p.Object({quoteType:p.Literal("OPTION"),strike:i4,openInterest:i4,expireDate:i4,expireIsoDate:i4,underlyingSymbol:p.String()})],{title:"QuoteOption"}),n9=p.Composite([nM,p.Object({quoteType:p.Literal("MUTUALFUND")})],{title:"QuoteMutualFund"}),n6=p.Union([nX,n1,n0,n3,n2,n4,n9,n5],{title:"Quote"}),n7=p.Object({contractSymbol:p.String(),strike:i4,currency:p.Optional(p.String()),lastPrice:i4,change:i4,percentChange:p.Optional(i4),volume:p.Optional(i4),openInterest:p.Optional(i4),bid:p.Optional(i4),ask:p.Optional(i4),contractSize:p.Literal("REGULAR"),expiration:i3,lastTradeDate:i3,impliedVolatility:i4,inTheMoney:p.Boolean()},{additionalProperties:p.Any(),title:"CallOrPut"}),n8=p.Object({expirationDate:i3,hasMiniOptions:p.Boolean(),calls:p.Array(n7),puts:p.Array(n7)},{additionalProperties:p.Any(),title:"Option"}),re=p.Object({underlyingSymbol:p.String(),expirationDates:p.Array(i3),strikes:p.Array(i4),hasMiniOptions:p.Boolean(),quote:n6,options:p.Array(n8)},{additionalProperties:p.Any(),title:"OptionsResult"}),ra=p.Object({formatted:p.Optional(p.Boolean()),lang:p.Optional(p.String()),region:p.Optional(p.String()),date:p.Optional(i3)},{title:"OptionsOptions"}),rt={formatted:!1,lang:"en-US",region:"US"};!function(e){e.Accumulate="Accumulate",e.Add="Add",e.Average="Average",e.BelowAverage="Below Average",e.Buy="Buy",e.ConvictionBuy="Conviction Buy",e.Empty="",e.EqualWeight="Equal-Weight",e.FairValue="Fair Value",e.GradeEqualWeight="Equal-weight",e.GradeLongTermBuy="Long-term Buy",e.Hold="Hold",e.LongTermBuy="Long-Term Buy",e.MarketOutperform="Market Outperform",e.MarketPerform="Market Perform",e.Mixed="Mixed",e.Negative="Negative",e.Neutral="Neutral",e.InLine="In-Line",e.Outperform="Outperform",e.Overweight="Overweight",e.PeerPerform="Peer Perform",e.Perform="Perform",e.Positive="Positive",e.Reduce="Reduce",e.SectorOutperform="Sector Outperform",e.SectorPerform="Sector Perform",e.SectorWeight="Sector Weight",e.Sell="Sell",e.StrongBuy="Strong Buy",e.TopPick="Top Pick",e.Underperform="Underperform",e.Underperformer="Underperformer",e.Underweight="Underweight",e.Trim="Trim",e.AboveAverage="Above Average",e.Inline="In-line",e.Outperformer="Outperformer",e.OVerweight="OVerweight",e.Cautious="Cautious",e.MarketWeight="Market Weight",e.SectorUnderperform="Sector Underperform",e.MarketUnderperform="Market Underperform",e.Peerperform="Peer perform",e.GraduallyAccumulate="Gradually Accumulate",e.ActionListBuy="Action List Buy",e.Performer="Performer",e.SectorPerformer="Sector Performer",e.SpeculativeBuy="Speculative Buy",e.StrongSell="Strong Sell",e.SpeculativeHold="Speculative Hold",e.NotRated="Not Rated",e.HoldNeutral="Hold Neutral",e.Developing="Developing",e.buy="buy",e.HOld="HOld",e.TradingSell="Trading Sell",e.Tender="Tender",e.marketperform="market perform",e.BUy="BUy"}(l||(l={})),function(e){e.Down="down",e.Init="init",e.Main="main",e.Reit="reit",e.Up="up"}(u||(u={}));let ro=p.Enum(l,{title:"QuoteSummaryEnumGrade"}),ri=p.Enum(u,{title:"QuoteSummaryAction"}),rn=p.Object({epochGradeDate:i3,firm:p.String(),toGrade:ro,fromGrade:p.Optional(ro),action:ri},{additionalProperties:p.Any(),title:"QuoteSummaryUpgradeDowngradeHistoryHistory"}),rr=p.Object({history:p.Array(rn),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryUpgradeDowngradeHistory"}),rs=p.Object({realestate:p.Optional(i4),consumer_cyclical:p.Optional(i4),basic_materials:p.Optional(i4),consumer_defensive:p.Optional(i4),technology:p.Optional(i4),communication_services:p.Optional(i4),financial_services:p.Optional(i4),utilities:p.Optional(i4),industrials:p.Optional(i4),energy:p.Optional(i4),healthcare:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryTopHoldingsSectorWeighting"}),rl=p.Object({a:p.Optional(i4),aa:p.Optional(i4),aaa:p.Optional(i4),other:p.Optional(i4),b:p.Optional(i4),bb:p.Optional(i4),bbb:p.Optional(i4),below_b:p.Optional(i4),us_government:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryTopHoldingsBondRating"}),ru=p.Object({medianMarketCap:p.Optional(i4),medianMarketCapCat:p.Optional(i4),priceToBook:i4,priceToBookCat:p.Optional(i4),priceToCashflow:i4,priceToCashflowCat:p.Optional(i4),priceToEarnings:i4,priceToEarningsCat:p.Optional(i4),priceToSales:i4,priceToSalesCat:p.Optional(i4),threeYearEarningsGrowth:p.Optional(i4),threeYearEarningsGrowthCat:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryTopHoldingsEquityHoldings"}),rc=p.Object({symbol:p.String(),holdingName:p.String(),holdingPercent:i4},{additionalProperties:p.Any(),title:"QuoteSummaryTopHoldingsHolding"}),rm=p.Object({maxAge:i4,stockPosition:p.Optional(i4),bondPosition:p.Optional(i4),holdings:p.Array(rc),equityHoldings:ru,bondHoldings:p.Object({}),bondRatings:p.Array(rl),sectorWeightings:p.Array(rs),cashPosition:p.Optional(i4),otherPosition:p.Optional(i4),preferredPosition:p.Optional(i4),convertiblePosition:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryTopHoldings"}),rp=p.Object({address1:p.Optional(p.String()),address2:p.Optional(p.String()),address3:p.Optional(p.String()),city:p.Optional(p.String()),state:p.Optional(p.String()),zip:p.Optional(p.String()),country:p.Optional(p.String()),phone:p.Optional(p.String()),fax:p.Optional(p.String()),website:p.Optional(p.String()),industry:p.Optional(p.String()),industryDisp:p.Optional(p.String()),sector:p.Optional(p.String()),sectorDisp:p.Optional(p.String()),longBusinessSummary:p.Optional(p.String()),fullTimeEmployees:p.Optional(i4),companyOfficers:p.Array(p.Any()),maxAge:i4,twitter:p.Optional(p.String()),name:p.Optional(p.String()),startDate:p.Optional(i3),description:p.Optional(p.String())},{additionalProperties:p.Any(),title:"QuoteSummarySummaryProfile"}),rd=p.Object({maxAge:i4,priceHint:i4,previousClose:p.Optional(i4),open:p.Optional(i4),dayLow:p.Optional(i4),dayHigh:p.Optional(i4),regularMarketPreviousClose:p.Optional(i4),regularMarketOpen:p.Optional(i4),regularMarketDayLow:p.Optional(i4),regularMarketDayHigh:p.Optional(i4),regularMarketVolume:p.Optional(i4),dividendRate:p.Optional(i4),dividendYield:p.Optional(i4),exDividendDate:p.Optional(i3),payoutRatio:p.Optional(i4),fiveYearAvgDividendYield:p.Optional(i4),beta:p.Optional(i4),trailingPE:p.Optional(i4),forwardPE:p.Optional(i4),volume:p.Optional(i4),averageVolume:p.Optional(i4),averageVolume10days:p.Optional(i4),averageDailyVolume10Day:p.Optional(i4),bid:p.Optional(i4),ask:p.Optional(i4),bidSize:p.Optional(i4),askSize:p.Optional(i4),marketCap:p.Optional(i4),fiftyDayAverage:p.Optional(i4),fiftyTwoWeekLow:p.Optional(i4),fiftyTwoWeekHigh:p.Optional(i4),twoHundredDayAverage:p.Optional(i4),priceToSalesTrailing12Months:p.Optional(i4),trailingAnnualDividendRate:p.Optional(i4),trailingAnnualDividendYield:p.Optional(i4),currency:p.String(),algorithm:p.Null(),tradeable:p.Boolean(),yield:p.Optional(i4),totalAssets:p.Optional(i4),navPrice:p.Optional(i4),ytdReturn:p.Optional(i4),fromCurrency:p.Union([p.String(),p.Null()]),toCurrency:p.Optional(p.Union([p.String(),p.Null()])),lastMarket:p.Union([p.String(),p.Null()]),volume24Hr:p.Optional(i4),volumeAllCurrencies:p.Optional(i4),circulatingSupply:p.Optional(i4),startDate:p.Optional(i3),coinMarketCapLink:p.Optional(p.Union([p.String(),p.Null()])),expireDate:p.Optional(i3),openInterest:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummarySummaryDetail"}),rg=p.Union([p.Literal("10-K"),p.Literal("10-Q"),p.Literal("8-K"),p.Literal("8-K/A"),p.Literal("10-K/A"),p.Literal("10-Q/A"),p.Literal("SD"),p.Literal("PX14A6G"),p.Literal("SC 13G/A"),p.Literal("DEFA14A"),p.Literal("25-NSE"),p.Literal("S-8 POS"),p.Literal("6-K"),p.Literal("F-3ASR"),p.Literal("SC 13D/A"),p.Literal("20-F"),p.Literal("425"),p.Literal("SC14D9C"),p.Literal("SC 13G"),p.Literal("S-8"),p.Literal("DEF 14A"),p.Literal("F-10")],{title:"QuoteSummaryFilingType"}),rh=p.Object({date:p.String(),epochDate:i3,type:rg,title:p.String(),edgarUrl:p.String(),maxAge:i4,url:p.Optional(p.String()),exhibits:p.Optional(p.Array(p.Object({type:p.String(),url:p.String(),downloadUrl:p.Optional(p.String())})))},{additionalProperties:p.Any(),title:"QuoteSummaryFiling"}),rk=p.Object({filings:p.Array(rh),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummarySECFilings"}),rf=p.Object({period:p.String(),strongBuy:i4,buy:i4,hold:i4,sell:i4,strongSell:i4},{additionalProperties:p.Any(),title:"QuoteSummaryRecommendationTrendTrend"}),ry=p.Object({trend:p.Array(rf),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryRecommendationTrend"}),rb=p.Object({exchange:p.String(),quoteType:p.String(),symbol:p.String(),underlyingSymbol:p.String(),shortName:p.Union([p.Null(),p.String()]),longName:p.Union([p.Null(),p.String()]),firstTradeDateEpochUtc:i2,timeZoneFullName:p.String(),timeZoneShortName:p.String(),uuid:p.String(),messageBoardId:p.Optional(p.Union([p.Null(),p.String()])),gmtOffSetMilliseconds:i4,maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryQuoteType"}),rj=p.Object({averageDailyVolume10Day:p.Optional(i4),averageDailyVolume3Month:p.Optional(i4),exchange:p.Optional(p.String()),exchangeName:p.Optional(p.String()),exchangeDataDelayedBy:p.Optional(i4),maxAge:i4,postMarketChangePercent:p.Optional(i4),postMarketChange:p.Optional(i4),postMarketTime:p.Optional(i3),postMarketPrice:p.Optional(i4),postMarketSource:p.Optional(p.String()),preMarketChangePercent:p.Optional(i4),preMarketChange:p.Optional(i4),preMarketTime:p.Optional(i3),preMarketPrice:p.Optional(i4),preMarketSource:p.Optional(p.String()),priceHint:i4,regularMarketChangePercent:p.Optional(i4),regularMarketChange:p.Optional(i4),regularMarketTime:p.Optional(i3),regularMarketPrice:p.Optional(i4),regularMarketDayHigh:p.Optional(i4),regularMarketDayLow:p.Optional(i4),regularMarketVolume:p.Optional(i4),regularMarketPreviousClose:p.Optional(i4),regularMarketSource:p.Optional(p.String()),regularMarketOpen:p.Optional(i4),quoteSourceName:p.Optional(p.String()),quoteType:p.String(),symbol:p.String(),underlyingSymbol:p.Union([p.Null(),p.String()]),shortName:p.Union([p.Null(),p.String()]),longName:p.Union([p.Null(),p.String()]),lastMarket:p.Union([p.Null(),p.String()]),marketState:p.Optional(p.String()),marketCap:p.Optional(i4),currency:p.Optional(p.String()),currencySymbol:p.Optional(p.String()),fromCurrency:p.Union([p.String(),p.Null()]),toCurrency:p.Optional(p.Union([p.String(),p.Null()])),volume24Hr:p.Optional(i4),volumeAllCurrencies:p.Optional(i4),circulatingSupply:p.Optional(i4),expireDate:p.Optional(i3),openInterest:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryPrice"}),rw=p.Object({maxAge:i4,period:p.String(),buyInfoCount:i4,buyInfoShares:i4,buyPercentInsiderShares:p.Optional(i4),sellInfoCount:i4,sellInfoShares:p.Optional(i4),sellPercentInsiderShares:p.Optional(i4),netInfoCount:i4,netInfoShares:i4,netPercentInsiderShares:p.Optional(i4),totalInsiderShares:i4},{additionalProperties:p.Any(),title:"QuoteSummaryNetSharePurchaseActivity"}),rv=p.Object({maxAge:i4,insidersPercentHeld:p.Optional(i4),institutionsPercentHeld:p.Optional(i4),institutionsFloatPercentHeld:p.Optional(i4),institutionsCount:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryMajorHoldersBreakdown"});!function(e){e.D="D",e.I="I"}(c||(c={})),function(e){e.ChairmanOfTheBoard="Chairman of the Board",e.ChiefExecutiveOfficer="Chief Executive Officer",e.ChiefFinancialOfficer="Chief Financial Officer",e.ChiefOperatingOfficer="Chief Operating Officer",e.ChiefTechnologyOfficer="Chief Technology Officer",e.Director="Director",e.DirectorIndependent="Director (Independent)",e.Empty="",e.GeneralCounsel="General Counsel",e.IndependentNonExecutiveDirector="Independent Non-Executive Director",e.Officer="Officer",e.President="President"}(m||(m={}));let rO=p.Enum(m,{title:"QuoteSummaryRelation"}),rz=p.Enum(c,{title:"QuoteSummaryOwnershipEnum"}),rS=p.Object({maxAge:i4,shares:i4,filerUrl:p.String(),transactionText:p.String(),filerName:p.String(),filerRelation:p.Union([rO,p.String()]),moneyText:p.String(),startDate:i3,ownership:p.Union([rz,p.String()]),value:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryTransaction"});p.Object({transactions:p.Array(rS),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryInsiderTransactions"});let rx=p.Object({maxAge:i4,name:p.String(),relation:p.Union([rO,p.String()]),url:p.String(),transactionDescription:p.String(),latestTransDate:i3,positionDirect:p.Optional(i4),positionDirectDate:p.Optional(i3),positionIndirect:p.Optional(i4),positionIndirectDate:p.Optional(i3),positionSummaryDate:p.Optional(i3)},{additionalProperties:p.Any(),title:"QuoteSummaryHolder"}),rA=p.Object({holders:p.Array(rx),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryHolders"}),rC=p.Object({maxAge:i4,symbol:p.Null(),estimates:p.Array(p.Any())},{additionalProperties:p.Any(),title:"QuoteSummaryTrend"}),rP=p.Object({period:p.String(),growth:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryEstimate"}),rT=p.Object({maxAge:i4,symbol:p.String(),peRatio:i4,pegRatio:i4,estimates:p.Array(rP)},{additionalProperties:p.Any(),title:"QuoteSummaryIndexTrend"}),rI=p.Object({maxAge:i9,endDate:i3,totalRevenue:i9,costOfRevenue:i9,grossProfit:i9,researchDevelopment:i9,sellingGeneralAdministrative:i9,nonRecurring:i9,otherOperatingExpenses:i9,totalOperatingExpenses:i9,operatingIncome:i9,totalOtherIncomeExpenseNet:i9,ebit:i9,interestExpense:i9,incomeBeforeTax:i9,incomeTaxExpense:i9,minorityInterest:i9,netIncomeFromContinuingOps:i9,discontinuedOperations:i9,extraordinaryItems:i9,effectOfAccountingCharges:i9,otherItems:i9,netIncome:i9,netIncomeApplicableToCommonShares:i9},{additionalProperties:p.Any(),title:"QuoteSummaryIncomeStatementHistoryElement"}),rE=p.Object({incomeStatementHistory:p.Array(rI),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryIncomeStatementHistory"}),rD=p.Object({},{title:"QuoteSummaryFundProfileBrokerage"}),rN=p.Object({annualHoldingsTurnover:p.Optional(i4),annualReportExpenseRatio:p.Optional(i4),grossExpRatio:p.Optional(i4),netExpRatio:p.Optional(i4),projectionValues:p.Object({}),totalNetAssets:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryFundProfileFeesExpensesInvestment"}),rL=p.Composite([p.Omit(rN,["projectionValues"]),p.Object({projectionValuesCat:p.Object({})})],{title:"QuoteSummaryFundProfileFeesExpensesInvestmentCat",additionalProperties:p.Any()}),rR=p.Object({managerName:p.Union([p.Null(),p.String()]),managerBio:p.Union([p.Null(),p.String()]),startdate:p.Optional(i3)},{additionalProperties:p.Any(),title:"QuoteSummaryFundProfileManagementInfo"}),rM=p.Object({maxAge:i4,styleBoxUrl:p.Optional(p.Union([p.Null(),p.String()])),family:p.Union([p.Null(),p.String()]),categoryName:p.Union([p.Null(),p.String()]),legalType:p.Union([p.Null(),p.String()]),managementInfo:p.Optional(rR),feesExpensesInvestment:p.Optional(rN),feesExpensesInvestmentCat:p.Optional(rL),brokerages:p.Optional(p.Array(rD)),initInvestment:p.Optional(i4),initIraInvestment:p.Optional(i4),initAipInvestment:p.Optional(i4),subseqInvestment:p.Optional(i4),subseqIraInvestment:p.Optional(i4),subseqAipInvestment:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryFundProfile"}),rF=p.Object({year:p.String(),alpha:i4,beta:i4,meanAnnualReturn:i4,rSquared:i4,stdDev:p.Optional(i4),sharpeRatio:i4,treynorRatio:i4},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformanceRiskOverviewStatsRow"}),rU=p.Object({riskStatisticsCat:p.Array(rF)},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformanceRiskOverviewStatsCat"}),rq=p.Object({riskStatistics:p.Array(rF),riskRating:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformanceRiskOverviewStats"}),r$=p.Object({year:i3,annualValue:p.Optional(i4),q1:p.Optional(i4),q2:p.Optional(i4),q3:p.Optional(i4),q4:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformanceReturnsRow"}),rB=p.Object({returns:p.Array(r$),returnsCat:p.Optional(p.Array(r$))},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformanceReturns"}),rH=p.Object({ytdReturnPct:p.Optional(i4),fiveYrAvgReturnPct:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformancePerformanceOverviewCat"}),r_=p.Object({asOfDate:p.Optional(i3),ytdReturnPct:p.Optional(i4),oneYearTotalReturn:p.Optional(i4),threeYearTotalReturn:p.Optional(i4),fiveYrAvgReturnPct:p.Optional(i4),morningStarReturnRating:p.Optional(i4),numYearsUp:p.Optional(i4),numYearsDown:p.Optional(i4),bestOneYrTotalReturn:p.Optional(i4),worstOneYrTotalReturn:p.Optional(i4),bestThreeYrTotalReturn:p.Optional(i4),worstThreeYrTotalReturn:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformancePerformanceOverview"}),rQ=p.Object({asOfDate:p.Optional(i3),ytd:p.Optional(i4),oneMonth:p.Optional(i4),threeMonth:p.Optional(i4),oneYear:p.Optional(i4),threeYear:p.Optional(i4),fiveYear:p.Optional(i4),tenYear:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryPeriodRange"}),rY=p.Composite([rQ,p.Object({lastBullMkt:p.Optional(i4),lastBearMkt:p.Optional(i4)},{additionalProperties:p.Any()})],{title:"QuoteSummaryFundPerformanceTrailingReturns"}),rV=p.Object({maxAge:i4,loadAdjustedReturns:p.Optional(rQ),rankInCategory:p.Optional(rQ),performanceOverview:r_,performanceOverviewCat:rH,trailingReturns:rY,trailingReturnsNav:rY,trailingReturnsCat:rY,annualTotalReturns:rB,pastQuarterlyReturns:rB,riskOverviewStatistics:rq,riskOverviewStatisticsCat:rU},{additionalProperties:p.Any(),title:"QuoteSummaryFundPerformance"}),rW=p.Object({maxAge:i4,reportDate:i3,organization:p.String(),pctHeld:i4,position:i4,value:i4,pctChange:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryOwnershipList"}),rG=p.Object({maxAge:i4,ownershipList:p.Array(rW)},{additionalProperties:p.Any(),title:"QuoteSummaryOwnership"}),rJ=p.Object({maxAge:i4,currentPrice:p.Optional(i4),targetHighPrice:p.Optional(i4),targetLowPrice:p.Optional(i4),targetMeanPrice:p.Optional(i4),targetMedianPrice:p.Optional(i4),recommendationMean:p.Optional(i4),recommendationKey:p.String(),numberOfAnalystOpinions:p.Optional(i4),totalCash:p.Optional(i4),totalCashPerShare:p.Optional(i4),ebitda:p.Optional(i4),totalDebt:p.Optional(i4),quickRatio:p.Optional(i4),currentRatio:p.Optional(i4),totalRevenue:p.Optional(i4),debtToEquity:p.Optional(i4),revenuePerShare:p.Optional(i4),returnOnAssets:p.Optional(i4),returnOnEquity:p.Optional(i4),grossProfits:p.Optional(i4),freeCashflow:p.Optional(i4),operatingCashflow:p.Optional(i4),earningsGrowth:p.Optional(i4),revenueGrowth:p.Optional(i4),grossMargins:p.Optional(i4),ebitdaMargins:p.Optional(i4),operatingMargins:p.Optional(i4),profitMargins:p.Optional(i4),financialCurrency:p.Union([p.String(),p.Null()])},{additionalProperties:p.Any(),title:"QuoteSummaryFinancialData"}),rK=p.Object({avg:i9,low:i9,high:i9,numberOfAnalysts:i9,yearAgoRevenue:i9,growth:i9},{additionalProperties:p.Any(),title:"QuoteSummaryRevenueEstimate"}),rZ=p.Object({current:i9,"7daysAgo":i9,"30daysAgo":i9,"60daysAgo":i9,"90daysAgo":i9},{additionalProperties:p.Any(),title:"QuoteSummaryEpsTrend"}),rX=p.Object({upLast7days:i9,upLast30days:i9,downLast30days:i9,downLast90days:i9},{additionalProperties:p.Any(),title:"QuoteSummaryEpsRevisions"}),r1=p.Object({avg:i9,low:i9,high:i9,yearAgoEps:i9,numberOfAnalysts:i9,growth:i9},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsEstimate"}),r0=p.Object({maxAge:i4,period:p.String(),endDate:i2,growth:i9,earningsEstimate:r1,revenueEstimate:rK,epsTrend:rZ,epsRevisions:rX},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsTrendTrend"}),r3=p.Object({trend:p.Array(r0),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsTrend"}),r2=p.Object({maxAge:i4,epsActual:i9,epsEstimate:i9,epsDifference:i9,surprisePercent:i9,quarter:i2,period:p.String()},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsHistoryHistory"}),r4=p.Object({history:p.Array(r2),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsHistory"}),r5=p.Object({date:i4,revenue:i4,earnings:i4},{additionalProperties:p.Any(),title:"QuoteSummaryYearly"}),r9=p.Object({date:p.String(),revenue:i4,earnings:i4},{additionalProperties:p.Any(),title:"QuoteSummaryFinancialsChartQuarterly"}),r6=p.Object({yearly:p.Array(r5),quarterly:p.Array(r9)},{additionalProperties:p.Any(),title:"QuoteSummaryFinancialsChart"}),r7=p.Object({date:p.String(),actual:i4,estimate:i4},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsChartQuarterly"}),r8=p.Object({quarterly:p.Array(r7),currentQuarterEstimate:p.Optional(i4),currentQuarterEstimateDate:p.Optional(p.String()),currentQuarterEstimateYear:p.Optional(i4),earningsDate:p.Array(i3)},{additionalProperties:p.Any(),title:"QuoteSummaryEarningsChart"}),se=p.Object({maxAge:i4,earningsChart:r8,financialsChart:r6,financialCurrency:p.Optional(p.String())},{additionalProperties:p.Any(),title:"QuoteSummaryEarnings"}),sa=p.Object({maxAge:i4,priceHint:i4,enterpriseValue:p.Optional(i4),forwardPE:p.Optional(i4),profitMargins:p.Optional(i4),floatShares:p.Optional(i4),sharesOutstanding:p.Optional(i4),sharesShort:p.Optional(i4),sharesShortPriorMonth:p.Optional(i3),sharesShortPreviousMonthDate:p.Optional(i3),dateShortInterest:p.Optional(i4),sharesPercentSharesOut:p.Optional(i4),heldPercentInsiders:p.Optional(i4),heldPercentInstitutions:p.Optional(i4),shortRatio:p.Optional(i4),shortPercentOfFloat:p.Optional(i4),beta:p.Optional(i4),impliedSharesOutstanding:p.Optional(i4),category:p.Union([p.Null(),p.String()]),bookValue:p.Optional(i4),priceToBook:p.Optional(i4),fundFamily:p.Union([p.Null(),p.String()]),legalType:p.Union([p.Null(),p.String()]),lastFiscalYearEnd:p.Optional(i3),nextFiscalYearEnd:p.Optional(i3),mostRecentQuarter:p.Optional(i3),earningsQuarterlyGrowth:p.Optional(i4),netIncomeToCommon:p.Optional(i4),trailingEps:p.Optional(i4),forwardEps:p.Optional(i4),pegRatio:p.Optional(i4),lastSplitFactor:p.Union([p.Null(),p.String()]),lastSplitDate:p.Optional(i4),enterpriseToRevenue:p.Optional(i4),enterpriseToEbitda:p.Optional(i4),"52WeekChange":p.Optional(i4),SandP52WeekChange:p.Optional(i4),lastDividendValue:p.Optional(i4),lastDividendDate:p.Optional(i3),ytdReturn:p.Optional(i4),beta3Year:p.Optional(i4),totalAssets:p.Optional(i4),yield:p.Optional(i4),fundInceptionDate:p.Optional(i3),threeYearAverageReturn:p.Optional(i4),fiveYearAverageReturn:p.Optional(i4),morningStarOverallRating:p.Optional(i4),morningStarRiskRating:p.Optional(i4),annualReportExpenseRatio:p.Optional(i4),lastCapGain:p.Optional(i4),annualHoldingsTurnover:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryDefaultKeyStatistics"}),st=p.Object({maxAge:i4,endDate:i3,netIncome:i4,depreciation:p.Optional(i4),changeToNetincome:p.Optional(i4),changeToAccountReceivables:p.Optional(i4),changeToLiabilities:p.Optional(i4),changeToInventory:p.Optional(i4),changeToOperatingActivities:p.Optional(i4),totalCashFromOperatingActivities:p.Optional(i4),capitalExpenditures:p.Optional(i4),investments:p.Optional(i4),otherCashflowsFromInvestingActivities:p.Optional(i4),totalCashflowsFromInvestingActivities:p.Optional(i4),dividendsPaid:p.Optional(i4),netBorrowings:p.Optional(i4),otherCashflowsFromFinancingActivities:p.Optional(i4),totalCashFromFinancingActivities:p.Optional(i4),changeInCash:p.Optional(i4),repurchaseOfStock:p.Optional(i4),issuanceOfStock:p.Optional(i4),effectOfExchangeRate:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryCashflowStatement"}),so=p.Object({cashflowStatements:p.Array(st),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryCashflowStatementHistory"}),si=p.Object({earningsDate:p.Array(i3),earningsAverage:p.Optional(i4),earningsLow:p.Optional(i4),earningsHigh:p.Optional(i4),revenueAverage:p.Optional(i4),revenueLow:p.Optional(i4),revenueHigh:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSumamryCalendarEventsEarnings"}),sn=p.Object({maxAge:i4,earnings:si,exDividendDate:p.Optional(i3),dividendDate:p.Optional(i3)},{additionalProperties:p.Any(),title:"QuoteSummaryCalendarEvents"}),sr=p.Object({maxAge:i4,endDate:i3,cash:p.Optional(i4),shortTermInvestments:p.Optional(i4),netReceivables:p.Optional(i4),inventory:p.Optional(i4),otherCurrentAssets:p.Optional(i4),totalCurrentAssets:p.Optional(i4),longTermInvestments:p.Optional(i4),propertyPlantEquipment:p.Optional(i4),otherAssets:p.Optional(i4),totalAssets:p.Optional(i4),accountsPayable:p.Optional(i4),shortLongTermDebt:p.Optional(i4),otherCurrentLiab:p.Optional(i4),longTermDebt:p.Optional(i4),otherLiab:p.Optional(i4),totalCurrentLiabilities:p.Optional(i4),totalLiab:p.Optional(i4),commonStock:p.Optional(i4),retainedEarnings:p.Optional(i4),treasuryStock:p.Optional(i4),otherStockholderEquity:p.Optional(i4),totalStockholderEquity:p.Optional(i4),netTangibleAssets:p.Optional(i4),goodWill:p.Optional(i4),intangibleAssets:p.Optional(i4),deferredLongTermAssetCharges:p.Optional(i4),deferredLongTermLiab:p.Optional(i4),minorityInterest:p.Optional(i9),capitalSurplus:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryBalanceSheetStatement"}),ss=p.Object({balanceSheetStatements:p.Array(sr),maxAge:i4},{additionalProperties:p.Any(),title:"QuoteSummaryBalanceSheetHistory"}),sl=p.Object({maxAge:i4,name:p.String(),age:p.Optional(i4),title:p.String(),yearBorn:p.Optional(i4),fiscalYear:p.Optional(i4),totalPay:p.Optional(i4),exercisedValue:p.Optional(i4),unexercisedValue:p.Optional(i4)},{additionalProperties:p.Any(),title:"QuoteSummaryCompanyOfficer"}),su=p.Object({maxAge:i4,address1:p.Optional(p.String()),address2:p.Optional(p.String()),address3:p.Optional(p.String()),city:p.Optional(p.String()),state:p.Optional(p.String()),zip:p.Optional(p.String()),country:p.Optional(p.String()),phone:p.Optional(p.String()),fax:p.Optional(p.String()),website:p.Optional(p.String()),industry:p.Optional(p.String()),industryDisp:p.Optional(p.String()),industryKey:p.Optional(p.String()),industrySymbol:p.Optional(p.String()),sector:p.Optional(p.String()),sectorDisp:p.Optional(p.String()),sectorKey:p.Optional(p.String()),longBusinessSummary:p.Optional(p.String()),fullTimeEmployees:p.Optional(i4),companyOfficers:p.Array(sl),auditRisk:p.Optional(i4),boardRisk:p.Optional(i4),compensationRisk:p.Optional(i4),shareHolderRightsRisk:p.Optional(i4),overallRisk:p.Optional(i4),governanceEpochDate:p.Optional(i3),compensationAsOfEpochDate:p.Optional(i3),name:p.Optional(p.String()),startDate:p.Optional(i3),description:p.Optional(p.String()),twitter:p.Optional(p.String())},{additionalProperties:p.Any(),title:"QuoteSummaryAssetProfile"}),sc=p.Object({assetProfile:p.Optional(su),balanceSheetHistory:p.Optional(ss),balanceSheetHistoryQuarterly:p.Optional(ss),calendarEvents:p.Optional(sn),cashflowStatementHistory:p.Optional(so),cashflowStatementHistoryQuarterly:p.Optional(so),defaultKeyStatistics:p.Optional(sa),earnings:p.Optional(se),earningsHistory:p.Optional(r4),earningsTrend:p.Optional(r3),financialData:p.Optional(rJ),fundOwnership:p.Optional(rG),fundPerformance:p.Optional(rV),fundProfile:p.Optional(rM),incomeStatementHistory:p.Optional(rE),incomeStatementHistoryQuarterly:p.Optional(rE),indexTrend:p.Optional(rT),industryTrend:p.Optional(rC),institutionOwnership:p.Optional(rG),majorDirectHolders:p.Optional(rA),majorHoldersBreakdown:p.Optional(rv),netSharePurchaseActivity:p.Optional(rw),price:p.Optional(rj),quoteType:p.Optional(rb),recommendationTrend:p.Optional(ry),secFilings:p.Optional(rk),sectorTrend:p.Optional(rC),summaryDetail:p.Optional(rd),summaryProfile:p.Optional(rp),topHoldings:p.Optional(rm),upgradeDowngradeHistory:p.Optional(rr)},{additionalProperties:p.Any(),title:"QuoteSummaryResult"}),sm=p.Union([p.Literal("assetProfile"),p.Literal("balanceSheetHistory"),p.Literal("balanceSheetHistoryQuarterly"),p.Literal("calendarEvents"),p.Literal("cashflowStatementHistory"),p.Literal("cashflowStatementHistoryQuarterly"),p.Literal("defaultKeyStatistics"),p.Literal("earnings"),p.Literal("earningsHistory"),p.Literal("earningsTrend"),p.Literal("financialData"),p.Literal("fundOwnership"),p.Literal("fundPerformance"),p.Literal("fundProfile"),p.Literal("incomeStatementHistory"),p.Literal("incomeStatementHistoryQuarterly"),p.Literal("indexTrend"),p.Literal("industryTrend"),p.Literal("insiderHolders"),p.Literal("insiderTransactions"),p.Literal("institutionOwnership"),p.Literal("majorDirectHolders"),p.Literal("majorHoldersBreakdown"),p.Literal("netSharePurchaseActivity"),p.Literal("price"),p.Literal("quoteType"),p.Literal("recommendationTrend"),p.Literal("secFilings"),p.Literal("sectorTrend"),p.Literal("summaryDetail"),p.Literal("summaryProfile"),p.Literal("topHoldings"),p.Literal("upgradeDowngradeHistory")]),sp=["assetProfile","balanceSheetHistory","balanceSheetHistoryQuarterly","calendarEvents","cashflowStatementHistory","cashflowStatementHistoryQuarterly","defaultKeyStatistics","earnings","earningsHistory","earningsTrend","financialData","fundOwnership","fundPerformance","fundProfile","incomeStatementHistory","incomeStatementHistoryQuarterly","indexTrend","industryTrend","insiderHolders","insiderTransactions","institutionOwnership","majorDirectHolders","majorHoldersBreakdown","netSharePurchaseActivity","price","quoteType","recommendationTrend","secFilings","sectorTrend","summaryDetail","summaryProfile","topHoldings","upgradeDowngradeHistory"],sd=p.Object({formatted:p.Optional(p.Boolean()),modules:p.Optional(p.Union([p.Array(sm),p.Literal("all")]))}),sg={formatted:!1,modules:["price","summaryDetail"]},sh=JSON.parse('{"financials":["TotalRevenue","OperatingRevenue","CostOfRevenue","GrossProfit","SellingGeneralAndAdministration","SellingAndMarketingExpense","GeneralAndAdministrativeExpense","OtherGandA","ResearchAndDevelopment","DepreciationAmortizationDepletionIncomeStatement","DepletionIncomeStatement","DepreciationAndAmortizationInIncomeStatement","Amortization","AmortizationOfIntangiblesIncomeStatement","DepreciationIncomeStatement","OtherOperatingExpenses","OperatingExpense","OperatingIncome","InterestExpenseNonOperating","InterestIncomeNonOperating","TotalOtherFinanceCost","NetNonOperatingInterestIncomeExpense","WriteOff","SpecialIncomeCharges","GainOnSaleOfPPE","GainOnSaleOfBusiness","GainOnSaleOfSecurity","OtherSpecialCharges","OtherIncomeExpense","OtherNonOperatingIncomeExpenses","TotalExpenses","PretaxIncome","TaxProvision","NetIncomeContinuousOperations","NetIncomeIncludingNoncontrollingInterests","MinorityInterests","NetIncomeFromTaxLossCarryforward","NetIncomeExtraordinary","NetIncomeDiscontinuousOperations","PreferredStockDividends","OtherunderPreferredStockDividend","NetIncomeCommonStockholders","NetIncome","BasicAverageShares","DilutedAverageShares","DividendPerShare","ReportedNormalizedBasicEPS","ContinuingAndDiscontinuedBasicEPS","BasicEPSOtherGainsLosses","TaxLossCarryforwardBasicEPS","NormalizedBasicEPS","BasicEPS","BasicAccountingChange","BasicExtraordinary","BasicDiscontinuousOperations","BasicContinuousOperations","ReportedNormalizedDilutedEPS","ContinuingAndDiscontinuedDilutedEPS","TaxLossCarryforwardDilutedEPS","AverageDilutionEarnings","NormalizedDilutedEPS","DilutedEPS","DilutedAccountingChange","DilutedExtraordinary","DilutedContinuousOperations","DilutedDiscontinuousOperations","DilutedNIAvailtoComStockholders","DilutedEPSOtherGainsLosses","TotalOperatingIncomeAsReported","NetIncomeFromContinuingAndDiscontinuedOperation","NormalizedIncome","NetInterestIncome","EBIT","EBITDA","ReconciledCostOfRevenue","ReconciledDepreciation","NetIncomeFromContinuingOperationNetMinorityInterest","TotalUnusualItemsExcludingGoodwill","TotalUnusualItems","NormalizedEBITDA","TaxRateForCalcs","TaxEffectOfUnusualItems","RentExpenseSupplemental","EarningsFromEquityInterestNetOfTax","ImpairmentOfCapitalAssets","RestructuringAndMergernAcquisition","SecuritiesAmortization","EarningsFromEquityInterest","OtherTaxes","ProvisionForDoubtfulAccounts","InsuranceAndClaims","RentAndLandingFees","SalariesAndWages","ExciseTaxes","InterestExpense","InterestIncome","TotalMoneyMarketInvestments","InterestIncomeAfterProvisionForLoanLoss","OtherThanPreferredStockDividend","LossonExtinguishmentofDebt","IncomefromAssociatesandOtherParticipatingInterests","NonInterestExpense","OtherNonInterestExpense","ProfessionalExpenseAndContractServicesExpense","OccupancyAndEquipment","Equipment","NetOccupancyExpense","CreditLossesProvision","NonInterestIncome","OtherNonInterestIncome","GainLossonSaleofAssets","GainonSaleofInvestmentProperty","GainonSaleofLoans","ForeignExchangeTradingGains","TradingGainLoss","InvestmentBankingProfit","DividendIncome","FeesAndCommissions","FeesandCommissionExpense","FeesandCommissionIncome","OtherCustomerServices","CreditCard","SecuritiesActivities","TrustFeesbyCommissions","ServiceChargeOnDepositorAccounts","TotalPremiumsEarned","OtherInterestExpense","InterestExpenseForFederalFundsSoldAndSecuritiesPurchaseUnderAgreementsToResell","InterestExpenseForLongTermDebtAndCapitalSecurities","InterestExpenseForShortTermDebt","InterestExpenseForDeposit","OtherInterestIncome","InterestIncomeFromFederalFundsSoldAndSecuritiesPurchaseUnderAgreementsToResell","InterestIncomeFromDeposits","InterestIncomeFromSecurities","InterestIncomeFromLoansAndLease","InterestIncomeFromLeases","InterestIncomeFromLoans","DepreciationDepreciationIncomeStatement","OperationAndMaintenance","OtherCostofRevenue","ExplorationDevelopmentAndMineralPropertyLeaseExpenses"],"balance-sheet":["NetDebt","TreasurySharesNumber","PreferredSharesNumber","OrdinarySharesNumber","ShareIssued","TotalDebt","TangibleBookValue","InvestedCapital","WorkingCapital","NetTangibleAssets","CapitalLeaseObligations","CommonStockEquity","PreferredStockEquity","TotalCapitalization","TotalEquityGrossMinorityInterest","MinorityInterest","StockholdersEquity","OtherEquityInterest","GainsLossesNotAffectingRetainedEarnings","OtherEquityAdjustments","FixedAssetsRevaluationReserve","ForeignCurrencyTranslationAdjustments","MinimumPensionLiabilities","UnrealizedGainLoss","TreasuryStock","RetainedEarnings","AdditionalPaidInCapital","CapitalStock","OtherCapitalStock","CommonStock","PreferredStock","TotalPartnershipCapital","GeneralPartnershipCapital","LimitedPartnershipCapital","TotalLiabilitiesNetMinorityInterest","TotalNonCurrentLiabilitiesNetMinorityInterest","OtherNonCurrentLiabilities","LiabilitiesHeldforSaleNonCurrent","RestrictedCommonStock","PreferredSecuritiesOutsideStockEquity","DerivativeProductLiabilities","EmployeeBenefits","NonCurrentPensionAndOtherPostretirementBenefitPlans","NonCurrentAccruedExpenses","DuetoRelatedPartiesNonCurrent","TradeandOtherPayablesNonCurrent","NonCurrentDeferredLiabilities","NonCurrentDeferredRevenue","NonCurrentDeferredTaxesLiabilities","LongTermDebtAndCapitalLeaseObligation","LongTermCapitalLeaseObligation","LongTermDebt","LongTermProvisions","CurrentLiabilities","OtherCurrentLiabilities","CurrentDeferredLiabilities","CurrentDeferredRevenue","CurrentDeferredTaxesLiabilities","CurrentDebtAndCapitalLeaseObligation","CurrentCapitalLeaseObligation","CurrentDebt","OtherCurrentBorrowings","LineOfCredit","CommercialPaper","CurrentNotesPayable","PensionandOtherPostRetirementBenefitPlansCurrent","CurrentProvisions","PayablesAndAccruedExpenses","CurrentAccruedExpenses","InterestPayable","Payables","OtherPayable","DuetoRelatedPartiesCurrent","DividendsPayable","TotalTaxPayable","IncomeTaxPayable","AccountsPayable","TotalAssets","TotalNonCurrentAssets","OtherNonCurrentAssets","DefinedPensionBenefit","NonCurrentPrepaidAssets","NonCurrentDeferredAssets","NonCurrentDeferredTaxesAssets","DuefromRelatedPartiesNonCurrent","NonCurrentNoteReceivables","NonCurrentAccountsReceivable","FinancialAssets","InvestmentsAndAdvances","OtherInvestments","InvestmentinFinancialAssets","HeldToMaturitySecurities","AvailableForSaleSecurities","FinancialAssetsDesignatedasFairValueThroughProfitorLossTotal","TradingSecurities","LongTermEquityInvestment","InvestmentsinJointVenturesatCost","InvestmentsInOtherVenturesUnderEquityMethod","InvestmentsinAssociatesatCost","InvestmentsinSubsidiariesatCost","InvestmentProperties","GoodwillAndOtherIntangibleAssets","OtherIntangibleAssets","Goodwill","NetPPE","AccumulatedDepreciation","GrossPPE","Leases","ConstructionInProgress","OtherProperties","MachineryFurnitureEquipment","BuildingsAndImprovements","LandAndImprovements","Properties","CurrentAssets","OtherCurrentAssets","HedgingAssetsCurrent","AssetsHeldForSaleCurrent","CurrentDeferredAssets","CurrentDeferredTaxesAssets","RestrictedCash","PrepaidAssets","Inventory","InventoriesAdjustmentsAllowances","OtherInventories","FinishedGoods","WorkInProcess","RawMaterials","Receivables","ReceivablesAdjustmentsAllowances","OtherReceivables","DuefromRelatedPartiesCurrent","TaxesReceivable","AccruedInterestReceivable","NotesReceivable","LoansReceivable","AccountsReceivable","AllowanceForDoubtfulAccountsReceivable","GrossAccountsReceivable","CashCashEquivalentsAndShortTermInvestments","OtherShortTermInvestments","CashAndCashEquivalents","CashEquivalents","CashFinancial","OtherLiabilities","LiabilitiesOfDiscontinuedOperations","SubordinatedLiabilities","AdvanceFromFederalHomeLoanBanks","TradingLiabilities","DuetoRelatedParties","SecuritiesLoaned","FederalFundsPurchasedAndSecuritiesSoldUnderAgreementToRepurchase","FinancialInstrumentsSoldUnderAgreementsToRepurchase","FederalFundsPurchased","TotalDeposits","NonInterestBearingDeposits","InterestBearingDepositsLiabilities","CustomerAccounts","DepositsbyBank","OtherAssets","AssetsHeldForSale","DeferredAssets","DeferredTaxAssets","DueFromRelatedParties","AllowanceForNotesReceivable","GrossNotesReceivable","NetLoan","UnearnedIncome","AllowanceForLoansAndLeaseLosses","GrossLoan","OtherLoanAssets","MortgageLoan","ConsumerLoan","CommercialLoan","LoansHeldForSale","DerivativeAssets","SecuritiesAndInvestments","BankOwnedLifeInsurance","OtherRealEstateOwned","ForeclosedAssets","CustomerAcceptances","FederalHomeLoanBankStock","SecurityBorrowed","CashCashEquivalentsAndFederalFundsSold","MoneyMarketInvestments","FederalFundsSoldAndSecuritiesPurchaseUnderAgreementsToResell","SecurityAgreeToBeResell","FederalFundsSold","RestrictedCashAndInvestments","RestrictedInvestments","RestrictedCashAndCashEquivalents","InterestBearingDepositsAssets","CashAndDueFromBanks","BankIndebtedness","MineralProperties"],"cash-flow":["FreeCashFlow","ForeignSales","DomesticSales","AdjustedGeographySegmentData","RepurchaseOfCapitalStock","RepaymentOfDebt","IssuanceOfDebt","IssuanceOfCapitalStock","CapitalExpenditure","InterestPaidSupplementalData","IncomeTaxPaidSupplementalData","EndCashPosition","OtherCashAdjustmentOutsideChangeinCash","BeginningCashPosition","EffectOfExchangeRateChanges","ChangesInCash","OtherCashAdjustmentInsideChangeinCash","CashFlowFromDiscontinuedOperation","FinancingCashFlow","CashFromDiscontinuedFinancingActivities","CashFlowFromContinuingFinancingActivities","NetOtherFinancingCharges","InterestPaidCFF","ProceedsFromStockOptionExercised","CashDividendsPaid","PreferredStockDividendPaid","CommonStockDividendPaid","NetPreferredStockIssuance","PreferredStockPayments","PreferredStockIssuance","NetCommonStockIssuance","CommonStockPayments","CommonStockIssuance","NetIssuancePaymentsOfDebt","NetShortTermDebtIssuance","ShortTermDebtPayments","ShortTermDebtIssuance","NetLongTermDebtIssuance","LongTermDebtPayments","LongTermDebtIssuance","InvestingCashFlow","CashFromDiscontinuedInvestingActivities","CashFlowFromContinuingInvestingActivities","NetOtherInvestingChanges","InterestReceivedCFI","DividendsReceivedCFI","NetInvestmentPurchaseAndSale","SaleOfInvestment","PurchaseOfInvestment","NetInvestmentPropertiesPurchaseAndSale","SaleOfInvestmentProperties","PurchaseOfInvestmentProperties","NetBusinessPurchaseAndSale","SaleOfBusiness","PurchaseOfBusiness","NetIntangiblesPurchaseAndSale","SaleOfIntangibles","PurchaseOfIntangibles","NetPPEPurchaseAndSale","SaleOfPPE","PurchaseOfPPE","CapitalExpenditureReported","OperatingCashFlow","CashFromDiscontinuedOperatingActivities","CashFlowFromContinuingOperatingActivities","TaxesRefundPaid","InterestReceivedCFO","InterestPaidCFO","DividendReceivedCFO","DividendPaidCFO","ChangeInWorkingCapital","ChangeInOtherWorkingCapital","ChangeInOtherCurrentLiabilities","ChangeInOtherCurrentAssets","ChangeInPayablesAndAccruedExpense","ChangeInAccruedExpense","ChangeInInterestPayable","ChangeInPayable","ChangeInDividendPayable","ChangeInAccountPayable","ChangeInTaxPayable","ChangeInIncomeTaxPayable","ChangeInPrepaidAssets","ChangeInInventory","ChangeInReceivables","ChangesInAccountReceivables","OtherNonCashItems","ExcessTaxBenefitFromStockBasedCompensation","StockBasedCompensation","UnrealizedGainLossOnInvestmentSecurities","ProvisionandWriteOffofAssets","AssetImpairmentCharge","AmortizationOfSecurities","DeferredTax","DeferredIncomeTax","Depletion","DepreciationAndAmortization","AmortizationCashFlow","AmortizationOfIntangibles","Depreciation","OperatingGainsLosses","PensionAndEmployeeBenefitExpense","EarningsLossesFromEquityInvestments","GainLossOnInvestmentSecurities","NetForeignCurrencyExchangeGainLoss","GainLossOnSaleOfPPE","GainLossOnSaleOfBusiness","NetIncomeFromContinuingOperations","CashFlowsfromusedinOperatingActivitiesDirect","TaxesRefundPaidDirect","InterestReceivedDirect","InterestPaidDirect","DividendsReceivedDirect","DividendsPaidDirect","ClassesofCashPayments","OtherCashPaymentsfromOperatingActivities","PaymentsonBehalfofEmployees","PaymentstoSuppliersforGoodsandServices","ClassesofCashReceiptsfromOperatingActivities","OtherCashReceiptsfromOperatingActivities","ReceiptsfromGovernmentGrants","ReceiptsfromCustomers","IncreaseDecreaseInDeposit","ChangeInFederalFundsAndSecuritiesSoldForRepurchase","NetProceedsPaymentForLoan","PaymentForLoans","ProceedsFromLoans","ProceedsPaymentInInterestBearingDepositsInBank","IncreaseinInterestBearingDepositsinBank","DecreaseinInterestBearingDepositsinBank","ProceedsPaymentFederalFundsSoldAndSecuritiesPurchasedUnderAgreementToResell","ChangeInLoans","ChangeInDeferredCharges","ProvisionForLoanLeaseAndOtherLosses","AmortizationOfFinancingCostsAndDiscounts","DepreciationAmortizationDepletion","RealizedGainLossOnSaleOfLoansAndLease","AllTaxesPaid","InterestandCommissionPaid","CashPaymentsforLoans","CashPaymentsforDepositsbyBanksandCustomers","CashReceiptsfromFeesandCommissions","CashReceiptsfromSecuritiesRelatedActivities","CashReceiptsfromLoans","CashReceiptsfromDepositsbyBanksandCustomers","CashReceiptsfromTaxRefunds","AmortizationAmortizationCashFlow"]}'),sk=["quarterly","annual","trailing"],sf=["financials","balance-sheet","cash-flow","all"],sy=p.Object({date:i3},{additionalProperties:p.Unknown(),title:"FundamentalsTimeSeriesResult"}),sb=p.Object({period1:p.Union([i3,i4,p.String()]),period2:p.Optional(p.Union([i3,i4,p.String()])),type:p.Optional(p.String()),merge:p.Optional(p.Boolean()),padTimeSeries:p.Optional(p.Boolean()),lang:p.Optional(p.String()),region:p.Optional(p.String()),module:p.String()},{title:"FundamentalsTimeSeriesOptions"}),sj=p.Array(sy),sw={merge:!1,padTimeSeries:!0,lang:"en-US",region:"US",type:"quarterly"},sv=function(e){for(let a of(e.period2||(e.period2=new Date),["period1","period2"])){let t=e[a];if(t instanceof Date)e[a]=Math.floor(t.getTime()/1e3);else if("string"==typeof t){let o=new Date(t).getTime();if(isNaN(o))throw Error("yahooFinance.fundamentalsTimeSeries() option '"+a+"' invalid date provided: '"+t+"'");e[a]=Math.floor(o/1e3)}}if(e.period1===e.period2)throw Error("yahooFinance.fundamentalsTimeSeries() options `period1` and `period2` cannot share the same value.");if(sk.includes(e.type||"")){if(!sf.includes(e.module||""))throw Error("yahooFinance.fundamentalsTimeSeries() option module invalid.")}else throw Error("yahooFinance.fundamentalsTimeSeries() option type invalid.");let a=Object.entries(sh).reduce((a,[t,o])=>"all"==e.module||t==e.module?a.concat(o):a,[]),t=e.type+a.join(`,${e.type}`);return{period1:e.period1,period2:e.period2,type:t}},sO=function(e){let a={},t=new RegExp(sk.join("|"));for(let o=0;o<e.timeseries.result.length;o++){let i=e.timeseries.result[o];if(i.timestamp&&i.timestamp.length)for(let e=0;e<i.timestamp.length;e++){let o=i.timestamp[e],n=Object.keys(i)[2];if(a[o]||(a[o]={date:o}),!i[n][e]||!i[n][e].reportedValue||!i[n][e].reportedValue.raw)continue;let r=n.replace(t,""),s=r==r.toUpperCase()?r:r[0].toLowerCase()+r.slice(1);a[o][s]=i[n][e].reportedValue.raw}}return Object.keys(a).map(e=>a[e])},sz=p.Object({recommendedSymbols:p.Array(p.Object({score:i4,symbol:p.String()},{additionalProperties:p.Any()})),symbol:p.String()},{additionalProperties:p.Any()}),sS=p.Array(sz),sx=p.Object({}),sA={},sC=p.Object({symbol:p.String(),isYahooFinance:p.Literal(!0),exchange:p.String(),exchDisp:p.Optional(p.String()),shortname:p.Optional(p.String()),longname:p.Optional(p.String()),index:p.Literal("quotes"),score:i4,newListingDate:p.Optional(i3),prevName:p.Optional(p.String()),nameChangeDate:p.Optional(i3),sector:p.Optional(p.String()),industry:p.Optional(p.String()),dispSecIndFlag:p.Optional(p.Boolean())},{additionalProperties:p.Any()}),sP=p.Composite([sC,p.Object({quoteType:p.Literal("EQUITY"),typeDisp:p.Literal("Equity")})],{title:"SearchQuoteYahooEntity"}),sT=p.Composite([sC,p.Object({quoteType:p.Literal("OPTION"),typeDisp:p.Literal("Option")})],{title:"SearchQuoteYahooOption"}),sI=p.Composite([sC,p.Object({quoteType:p.Literal("ETF"),typeDisp:p.Literal("ETF")})],{title:"SearchQuoteYahooETF"}),sE=p.Composite([sC,p.Object({quoteType:p.Literal("MUTUALFUND"),typeDisp:p.Literal("Fund")})],{title:"SearchQuoteYahooFund"}),sD=p.Composite([sC,p.Object({quoteType:p.Literal("INDEX"),typeDisp:p.Literal("Index")})],{title:"SearchQuoteYahooIndex"}),sN=p.Composite([sC,p.Object({quoteType:p.Literal("CURRENCY"),typeDisp:p.Literal("Currency")})],{title:"SearchQuoteYahooCurrency"}),sL=p.Composite([sC,p.Object({quoteType:p.Literal("CRYPTOCURRENCY"),typeDisp:p.Literal("Cryptocurrency")})]),sR=p.Composite([sC,p.Object({quoteType:p.Literal("FUTURE"),typeDisp:p.Union([p.Literal("Future"),p.Literal("Futures")])})],{title:"SearchQuoteYahooFuture"}),sM=p.Object({index:p.String(),name:p.String(),permalink:p.String(),isYahooFinance:p.Literal(!1)},{additionalProperties:p.Any(),title:"SearchQuoteNonYahoo"}),sF=p.Object({url:p.String(),width:i4,height:i4,tag:p.String()},{title:"SearchNewsThumbnailResolution"}),sU=p.Object({uuid:p.String(),title:p.String(),publisher:p.String(),link:p.String(),providerPublishTime:i3,type:p.String(),thumbnail:p.Optional(p.Object({resolutions:p.Array(sF)})),relatedTickers:p.Optional(p.Array(p.String()))},{additionalProperties:p.Any(),title:"SearchNews"}),sq=p.Object({explains:p.Array(p.Any()),count:i4,quotes:p.Array(p.Union([sP,sT,sI,sE,sD,sN,sL,sM,sR])),news:p.Array(sU),nav:p.Array(p.Any()),lists:p.Array(p.Any()),researchReports:p.Array(p.Any()),totalTime:i4,screenerFieldResults:p.Optional(p.Array(p.Any())),culturalAssets:p.Optional(p.Array(p.Any())),timeTakenForQuotes:i4,timeTakenForNews:i4,timeTakenForAlgowatchlist:i4,timeTakenForPredefinedScreener:i4,timeTakenForCrunchbase:i4,timeTakenForNav:i4,timeTakenForResearchReports:i4,timeTakenForScreenerField:p.Optional(i4),timeTakenForCulturalAssets:p.Optional(i4)},{additionalProperties:p.Any(),title:"SearchResults"}),s$=p.Object({lang:p.Optional(p.String()),region:p.Optional(p.String()),quotesCount:p.Optional(i4),newsCount:p.Optional(i4),enableFuzzyQuery:p.Optional(p.Boolean()),quotesQueryId:p.Optional(p.String()),multiQuoteQueryId:p.Optional(p.String()),newsQueryId:p.Optional(p.String()),enableCb:p.Optional(p.Boolean()),enableNavLinks:p.Optional(p.Boolean()),enableEnhancedTrivialQuery:p.Optional(p.Boolean())},{title:"SearchOptions",additionalProperties:!1}),sB={lang:"en-US",region:"US",quotesCount:6,newsCount:4,enableFuzzyQuery:!1,quotesQueryId:"tss_match_phrase_query",multiQuoteQueryId:"multi_quote_single_token_query",newsQueryId:"news_cie_vespa",enableCb:!0,enableNavLinks:!0,enableEnhancedTrivialQuery:!0},sH=p.Object({symbol:p.String()},{additionalProperties:p.Any()}),s_=p.Object({count:i4,quotes:p.Array(sH),jobTimestamp:i4,startInterval:i4},{additionalProperties:p.Any(),title:"TrendingSymbolsResult"}),sQ=p.Optional(p.Object({lang:p.Optional(p.String()),region:p.Optional(p.String()),count:p.Optional(i4)},{title:"TrendingSymbolsOptions"})),sY={lang:"en-US",count:5},sV=p.Object({field:p.String(),operators:p.Array(p.String()),values:p.Array(i4),labelsSelected:p.Array(i4),dependentValues:p.Array(p.Any())},{title:"DailyGainersCriterium"}),sW=p.Object({language:p.String(),region:p.String(),quoteType:p.String(),typeDisp:p.String(),quoteSourceName:p.String(),triggerable:p.Boolean(),customPriceAlertConfidence:p.String(),lastCloseTevEbitLtm:p.Optional(i4),lastClosePriceToNNWCPerShare:p.Optional(i4),firstTradeDateMilliseconds:i4,priceHint:i4,postMarketChangePercent:p.Optional(i4),postMarketTime:p.Optional(i4),postMarketPrice:p.Optional(i4),postMarketChange:p.Optional(i4),regularMarketChange:i4,regularMarketTime:i4,regularMarketPrice:i4,regularMarketDayHigh:i4,regularMarketDayRange:p.String(),currency:p.String(),regularMarketDayLow:i4,regularMarketVolume:i4,regularMarketPreviousClose:i4,bid:p.Optional(i4),ask:p.Optional(i4),bidSize:p.Optional(i4),askSize:p.Optional(i4),market:p.String(),messageBoardId:p.String(),fullExchangeName:p.String(),longName:p.String(),financialCurrency:p.Optional(p.String()),regularMarketOpen:i4,averageDailyVolume3Month:i4,averageDailyVolume10Day:i4,fiftyTwoWeekLowChange:i4,fiftyTwoWeekLowChangePercent:i4,fiftyTwoWeekRange:p.String(),fiftyTwoWeekHighChange:i4,fiftyTwoWeekHighChangePercent:i4,fiftyTwoWeekChangePercent:i4,earningsTimestamp:p.Optional(i4),earningsTimestampStart:p.Optional(i4),earningsTimestampEnd:p.Optional(i4),trailingAnnualDividendRate:i4,trailingAnnualDividendYield:i4,marketState:p.String(),epsTrailingTwelveMonths:p.Optional(i4),epsForward:p.Optional(i4),epsCurrentYear:p.Optional(i4),priceEpsCurrentYear:p.Optional(i4),sharesOutstanding:i4,bookValue:p.Optional(i4),fiftyDayAverage:i4,fiftyDayAverageChange:i4,fiftyDayAverageChangePercent:i4,twoHundredDayAverage:i4,twoHundredDayAverageChange:i4,twoHundredDayAverageChangePercent:i4,marketCap:i4,forwardPE:p.Optional(i4),priceToBook:p.Optional(i4),sourceInterval:i4,exchangeDataDelayedBy:i4,exchangeTimezoneName:p.String(),exchangeTimezoneShortName:p.String(),gmtOffSetMilliseconds:i4,esgPopulated:p.Boolean(),tradeable:p.Boolean(),cryptoTradeable:p.Boolean(),exchange:p.String(),fiftyTwoWeekLow:i4,fiftyTwoWeekHigh:i4,shortName:p.String(),averageAnalystRating:p.Optional(p.String()),regularMarketChangePercent:i4,symbol:p.String(),dividendDate:p.Optional(i4),displayName:p.Optional(p.String()),trailingPE:p.Optional(i4),prevName:p.Optional(p.String()),nameChangeDate:p.Optional(i4),ipoExpectedDate:p.Optional(i4),dividendYield:p.Optional(i4),dividendRate:p.Optional(i4)},{title:"DailyGainersQuote"}),sG=p.Object({lang:p.Optional(p.String()),region:p.Optional(p.String()),count:p.Optional(i4)},{title:"DailyGainersOptions"}),sJ=p.Object({size:i4,offset:i4,sortField:p.String(),sortType:p.String(),quoteType:p.String(),criteria:p.Array(sV),topOperator:p.String()},{title:"DailyGainersCriteriaMeta"}),sK=p.Object({id:p.String(),title:p.String(),description:p.String(),canonicalName:p.String(),criteriaMeta:sJ,rawCriteria:p.String(),start:i4,count:i4,total:i4,quotes:p.Array(sW),useRecords:p.Boolean(),predefinedScr:p.Boolean(),versionId:i4,creationDate:i4,lastUpdated:i4,isPremium:p.Boolean(),iconUrl:p.String()},{title:"DailyGainersResult"}),sZ={lang:"en-US",region:"US",scrIds:"day_gainers",count:5},sX=p.Object({field:p.String(),operators:p.Array(p.String()),values:p.Array(i4),labelsSelected:p.Array(i4),dependentValues:p.Array(p.Any())},{title:"ScreenerCriterum"}),s1=p.Object({size:i4,offset:i4,sortField:p.String(),sortType:p.String(),quoteType:p.String(),criteria:p.Array(sX),topOperator:p.String()},{title:"ScreenerCriteriaMeta"}),s0=p.Object({language:p.String(),region:p.String(),quoteType:p.String(),typeDisp:p.String(),quoteSourceName:p.String(),triggerable:p.Boolean(),customPriceAlertConfidence:p.String(),lastCloseTevEbitLtm:p.Optional(i4),lastClosePriceToNNWCPerShare:p.Optional(i4),firstTradeDateMilliseconds:i4,priceHint:i4,postMarketChangePercent:p.Optional(i4),postMarketTime:p.Optional(i4),postMarketPrice:p.Optional(i4),postMarketChange:p.Optional(i4),regularMarketChange:i4,regularMarketTime:i4,regularMarketPrice:i4,regularMarketDayHigh:p.Optional(i4),regularMarketDayRange:i6,currency:p.String(),regularMarketDayLow:p.Optional(i4),regularMarketVolume:p.Optional(i4),regularMarketPreviousClose:i4,bid:p.Optional(i4),ask:p.Optional(i4),bidSize:p.Optional(i4),askSize:p.Optional(i4),market:p.String(),messageBoardId:p.String(),fullExchangeName:p.String(),longName:p.String(),financialCurrency:p.Optional(p.String()),regularMarketOpen:p.Optional(i4),averageDailyVolume3Month:i4,averageDailyVolume10Day:i4,fiftyTwoWeekLowChange:i4,fiftyTwoWeekLowChangePercent:i4,fiftyTwoWeekRange:i6,fiftyTwoWeekHighChange:i4,fiftyTwoWeekHighChangePercent:i4,fiftyTwoWeekChangePercent:i4,earningsTimestamp:p.Optional(i4),earningsTimestampStart:p.Optional(i4),earningsTimestampEnd:p.Optional(i4),trailingAnnualDividendRate:p.Optional(i4),trailingAnnualDividendYield:p.Optional(i4),marketState:p.String(),epsTrailingTwelveMonths:p.Optional(i4),epsForward:p.Optional(i4),epsCurrentYear:p.Optional(i4),priceEpsCurrentYear:p.Optional(i4),sharesOutstanding:p.Optional(i4),bookValue:p.Optional(i4),fiftyDayAverage:i4,fiftyDayAverageChange:i4,fiftyDayAverageChangePercent:i4,twoHundredDayAverage:i4,twoHundredDayAverageChange:i4,twoHundredDayAverageChangePercent:i4,marketCap:p.Optional(i4),forwardPE:p.Optional(i4),priceToBook:p.Optional(i4),sourceInterval:i4,exchangeDataDelayedBy:i4,exchangeTimezoneName:p.String(),exchangeTimezoneShortName:p.String(),gmtOffSetMilliseconds:i4,esgPopulated:p.Boolean(),tradeable:p.Boolean(),cryptoTradeable:p.Boolean(),exchange:p.String(),fiftyTwoWeekLow:i4,fiftyTwoWeekHigh:i4,shortName:p.String(),averageAnalystRating:p.Optional(p.String()),regularMarketChangePercent:i4,symbol:p.String(),dividendDate:p.Optional(i3),displayName:p.Optional(p.String()),trailingPE:p.Optional(i4),prevName:p.Optional(p.String()),nameChangeDate:p.Optional(i3),ipoExpectedDate:p.Optional(i3),dividendYield:p.Optional(i4),dividendRate:p.Optional(i4),yieldTTM:p.Optional(i4),peTTM:p.Optional(i4),annualReturnNavY3:p.Optional(i4),annualReturnNavY5:p.Optional(i4),ytdReturn:p.Optional(i4),trailingThreeMonthReturns:p.Optional(i4),netAssets:p.Optional(i4),netExpenseRatio:p.Optional(i4)},{title:"ScreenerQuote"}),s3=p.Object({id:p.String(),title:p.String(),description:p.String(),canonicalName:p.String(),criteriaMeta:s1,rawCriteria:p.String(),start:i4,count:i4,total:i4,quotes:p.Array(s0),useRecords:p.Boolean(),predefinedScr:p.Boolean(),versionId:i4,creationDate:i3,lastUpdated:i3,isPremium:p.Boolean(),iconUrl:p.String()},{title:"ScreenerResult"}),s2=p.Union([p.Literal("aggressive_small_caps"),p.Literal("conservative_foreign_funds"),p.Literal("day_gainers"),p.Literal("day_losers"),p.Literal("growth_technology_stocks"),p.Literal("high_yield_bond"),p.Literal("most_actives"),p.Literal("most_shorted_stocks"),p.Literal("portfolio_anchors"),p.Literal("small_cap_gainers"),p.Literal("solid_large_growth_funds"),p.Literal("solid_midcap_growth_funds"),p.Literal("top_mutual_funds"),p.Literal("undervalued_growth_stocks"),p.Literal("undervalued_large_caps")],{title:"ScreenerPredefinedScreenerModules"}),s4={lang:"en-US",region:"US",scrIds:"day_gainers",count:5},s5=p.Object({lang:p.Optional(p.String()),region:p.Optional(p.String()),scrIds:s2,count:p.Optional(p.Number())}),s9=new Map,s6={_env:{},_fetch:op,_moduleExec:iq,_opts:oe,errors:t2,setGlobalConfig:function(e){let{cookieJar:a,...t}=iF({data:e,type:"options",options:this._opts.validation,schema:t8});if(function e(a,t){for(let o of Reflect.ownKeys(t))"object"==typeof t[o]?e(a[o],t[o]):a[o]=t[o]}(this._opts,t),a){if(!(a instanceof t9))throw Error("cookieJar must be an instance of ExtendedCookieJar");this._opts.cookieJar=a}},suppressNotices:function(e){e.forEach(e=>{let a=ot[e];a||oa.error(`Unknown notice id: ${e}`),a.suppress=!0})},autoc:i$,chart:ng,_chart:ng,historical:nO,insights:function(e,a,t){return this._moduleExec({moduleName:"insights",query:{assertSymbol:e,url:"https://${YF_QUERY_HOST}/ws/insights/v2/finance/insights",schema:nL,defaults:nR,overrides:a,runtime:{symbol:e}},result:{schema:nN,transformWith(e){if(!e.finance)throw Error("Unexpected result: "+JSON.stringify(e));return e.finance.result}},moduleOptions:t})},options:function(e,a,t){return this._moduleExec({moduleName:"options",query:{assertSymbol:e,url:"https://${YF_QUERY_HOST}/v7/finance/options/"+e,needsCrumb:!0,schema:ra,defaults:rt,overrides:a,transformWith(e){let a=iN(ra,e);return a.date?{...a,date:Math.floor(a.date.getTime()/1e3)}:a}},result:{schema:re,transformWith(e){if(!e.optionChain)throw Error("Unexpected result: "+JSON.stringify(e));return e.optionChain.result[0]}},moduleOptions:t})},quote:nZ,quoteSummary:function(e,a,t){return this._moduleExec({moduleName:"quoteSummary",query:{assertSymbol:e,url:"https://${YF_QUERY_HOST}/v10/finance/quoteSummary/"+e,needsCrumb:!0,schema:sd,defaults:sg,overrides:a,transformWith:e=>("object"==typeof e&&null!=e&&"modules"in e&&"all"===e.modules&&(e.modules=sp),e)},result:{schema:sc,transformWith(e){if(!e.quoteSummary)throw Error("Unexpected result: "+JSON.stringify(e));return e.quoteSummary.result[0]}},moduleOptions:t})},fundamentalsTimeSeries:function(e,a,t){return this._moduleExec({moduleName:"options",query:{assertSymbol:e,url:`https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/${e}`,needsCrumb:!1,schema:sb,defaults:sw,overrides:a,transformWith:sv},result:{schema:sj,transformWith(e){if(!e||!e.timeseries)throw Error(`Unexpected result: ${JSON.stringify(e)}`);return sO(e)}},moduleOptions:t})},recommendationsBySymbol:function(e,a,t){let o="string"==typeof e?e:e.join(",");return this._moduleExec({moduleName:"recommendationsBySymbol",query:{url:"https://${YF_QUERY_HOST}/v6/finance/recommendationsbysymbol/"+o,schema:sx,defaults:sA,overrides:a},result:{schema:sS,transformWith(e){if(!e.finance)throw Error("Unexpected result: "+JSON.stringify(e));return e.finance.result}},moduleOptions:t}).then(a=>"string"==typeof e?a[0]:a)},search:function(e,a,t){return this._moduleExec({moduleName:"searchTypebox",query:{url:"https://${YF_QUERY_HOST}/v1/finance/search",schema:s$,defaults:sB,runtime:{q:e},overrides:a,needsCrumb:!1},result:{schema:sq},moduleOptions:t})},trendingSymbols:function(e,a,t){return this._moduleExec({moduleName:"trendingSymbols",query:{url:"https://${YF_QUERY_HOST}/v1/finance/trending/"+e,schema:sQ,defaults:sY,overrides:a},result:{schema:s_,transformWith(e){if(!e.finance)throw Error("Unexpected result: "+JSON.stringify(e));return e.finance.result[0]}},moduleOptions:t})},dailyGainers:function(e,a){return this._moduleExec({moduleName:"dailyGainers",query:{url:"https://${YF_QUERY_HOST}/v1/finance/screener/predefined/saved",schema:sG,defaults:sZ,overrides:e,needsCrumb:!0},result:{schema:sK,transformWith(e){if(!e.finance)throw Error("Unexpected result: "+JSON.stringify(e));return e.finance.result[0]}},moduleOptions:a})},screener:function(e,a){return this._moduleExec({moduleName:"screener",query:{url:"https://${YF_QUERY_HOST}/v1/finance/screener/predefined/saved",schema:s5,defaults:s4,overrides:e,needsCrumb:!0},result:{schema:s3,transformWith(e){if(!e.finance)throw Error("Unexpected result: "+JSON.stringify(e));return e.finance.result[0]}},moduleOptions:a})},quoteCombine:function(e,a={},t){if("string"!=typeof e)throw Error("quoteCombine expects a string query parameter, received: "+JSON.stringify(e,null,2));iF({type:"options",data:a,schema:nJ,options:this._opts.validation});let o=JSON.stringify(a),i=s9.get(o);i||(i={timeout:null,queryOptionsOverrides:a,symbols:new Map},s9.set(o,i)),i.timeout&&clearTimeout(i.timeout);let n=nZ.bind(this);return new Promise((r,s)=>{let l=i.symbols.get(e);l||(l=[],i.symbols.set(e,l)),l.push({resolve:r,reject:s}),i.timeout=setTimeout(()=>{s9.delete(o),n(Array.from(i.symbols.keys()),a,t).then(e=>{for(let a of e)for(let e of i.symbols.get(a.symbol))e.resolve(a),e.resolved=!0;for(let[e,a]of i.symbols)for(let e of a)e.resolved||e.resolve(void 0)}).catch(e=>{for(let a of i.symbols.values())for(let t of a)t.reject(e)})},50)})}};s6._env={fetch,URLSearchParams:t(79551).URLSearchParams};let s7=s6},49450:(e,a,t)=>{"use strict";var o=t(16922),i=t(92347),n=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,r=/[\n\r\t]/g,s=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,l=/:\d+$/,u=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,c=/^[a-zA-Z]:/;function m(e){return(e||"").toString().replace(n,"")}var p=[["#","hash"],["?","query"],function(e,a){return h(a.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],d={hash:1,query:1};function g(e){var a,t,o=("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}).location||{},i={},n=typeof(e=e||o);if("blob:"===e.protocol)i=new f(unescape(e.pathname),{});else if("string"===n)for(t in i=new f(e,{}),d)delete i[t];else if("object"===n){for(t in e)t in d||(i[t]=e[t]);void 0===i.slashes&&(i.slashes=s.test(e.href))}return i}function h(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function k(e,a){e=(e=m(e)).replace(r,""),a=a||{};var t,o=u.exec(e),i=o[1]?o[1].toLowerCase():"",n=!!o[2],s=!!o[3],l=0;return n?s?(t=o[2]+o[3]+o[4],l=o[2].length+o[3].length):(t=o[2]+o[4],l=o[2].length):s?(t=o[3]+o[4],l=o[3].length):t=o[4],"file:"===i?l>=2&&(t=t.slice(2)):h(i)?t=o[4]:i?n&&(t=t.slice(2)):l>=2&&h(a.protocol)&&(t=o[4]),{protocol:i,slashes:n||h(i),slashesCount:l,rest:t}}function f(e,a,t){if(e=(e=m(e)).replace(r,""),!(this instanceof f))return new f(e,a,t);var n,s,l,u,d,y,b=p.slice(),j=typeof a,w=0;for("object"!==j&&"string"!==j&&(t=a,a=null),t&&"function"!=typeof t&&(t=i.parse),n=!(s=k(e||"",a=g(a))).protocol&&!s.slashes,this.slashes=s.slashes||n&&a.slashes,this.protocol=s.protocol||a.protocol||"",e=s.rest,("file:"===s.protocol&&(2!==s.slashesCount||c.test(e))||!s.slashes&&(s.protocol||s.slashesCount<2||!h(this.protocol)))&&(b[3]=[/(.*)/,"pathname"]);w<b.length;w++){if("function"==typeof(u=b[w])){e=u(e,this);continue}l=u[0],y=u[1],l!=l?this[y]=e:"string"==typeof l?~(d="@"===l?e.lastIndexOf(l):e.indexOf(l))&&("number"==typeof u[2]?(this[y]=e.slice(0,d),e=e.slice(d+u[2])):(this[y]=e.slice(d),e=e.slice(0,d))):(d=l.exec(e))&&(this[y]=d[1],e=e.slice(0,d.index)),this[y]=this[y]||n&&u[3]&&a[y]||"",u[4]&&(this[y]=this[y].toLowerCase())}t&&(this.query=t(this.query)),n&&a.slashes&&"/"!==this.pathname.charAt(0)&&(""!==this.pathname||""!==a.pathname)&&(this.pathname=function(e,a){if(""===e)return a;for(var t=(a||"/").split("/").slice(0,-1).concat(e.split("/")),o=t.length,i=t[o-1],n=!1,r=0;o--;)"."===t[o]?t.splice(o,1):".."===t[o]?(t.splice(o,1),r++):r&&(0===o&&(n=!0),t.splice(o,1),r--);return n&&t.unshift(""),("."===i||".."===i)&&t.push(""),t.join("/")}(this.pathname,a.pathname)),"/"!==this.pathname.charAt(0)&&h(this.protocol)&&(this.pathname="/"+this.pathname),o(this.port,this.protocol)||(this.host=this.hostname,this.port=""),this.username=this.password="",this.auth&&(~(d=this.auth.indexOf(":"))?(this.username=this.auth.slice(0,d),this.username=encodeURIComponent(decodeURIComponent(this.username)),this.password=this.auth.slice(d+1),this.password=encodeURIComponent(decodeURIComponent(this.password))):this.username=encodeURIComponent(decodeURIComponent(this.auth)),this.auth=this.password?this.username+":"+this.password:this.username),this.origin="file:"!==this.protocol&&h(this.protocol)&&this.host?this.protocol+"//"+this.host:"null",this.href=this.toString()}f.prototype={set:function(e,a,t){switch(e){case"query":"string"==typeof a&&a.length&&(a=(t||i.parse)(a)),this[e]=a;break;case"port":this[e]=a,o(a,this.protocol)?a&&(this.host=this.hostname+":"+a):(this.host=this.hostname,this[e]="");break;case"hostname":this[e]=a,this.port&&(a+=":"+this.port),this.host=a;break;case"host":this[e]=a,l.test(a)?(a=a.split(":"),this.port=a.pop(),this.hostname=a.join(":")):(this.hostname=a,this.port="");break;case"protocol":this.protocol=a.toLowerCase(),this.slashes=!t;break;case"pathname":case"hash":if(a){var n="pathname"===e?"/":"#";this[e]=a.charAt(0)!==n?n+a:a}else this[e]=a;break;case"username":case"password":this[e]=encodeURIComponent(a);break;case"auth":var r=a.indexOf(":");~r?(this.username=a.slice(0,r),this.username=encodeURIComponent(decodeURIComponent(this.username)),this.password=a.slice(r+1),this.password=encodeURIComponent(decodeURIComponent(this.password))):this.username=encodeURIComponent(decodeURIComponent(a))}for(var s=0;s<p.length;s++){var u=p[s];u[4]&&(this[u[1]]=this[u[1]].toLowerCase())}return this.auth=this.password?this.username+":"+this.password:this.username,this.origin="file:"!==this.protocol&&h(this.protocol)&&this.host?this.protocol+"//"+this.host:"null",this.href=this.toString(),this},toString:function(e){e&&"function"==typeof e||(e=i.stringify);var a,t=this.host,o=this.protocol;o&&":"!==o.charAt(o.length-1)&&(o+=":");var n=o+(this.protocol&&this.slashes||h(this.protocol)?"//":"");return this.username?(n+=this.username,this.password&&(n+=":"+this.password),n+="@"):this.password?(n+=":"+this.password,n+="@"):"file:"!==this.protocol&&h(this.protocol)&&!t&&"/"!==this.pathname&&(n+="@"),(":"===t[t.length-1]||l.test(this.hostname)&&!this.port)&&(t+=":"),n+=t+this.pathname,(a="object"==typeof this.query?e(this.query):this.query)&&(n+="?"!==a.charAt(0)?"?"+a:a),this.hash&&(n+=this.hash),n}},f.extractProtocol=k,f.location=g,f.trimLeft=m,f.qs=i,e.exports=f},64560:(e,a,t)=>{"use strict";let{fromCallback:o}=t(5837),i=t(43229).i,n=t(10018).$,r=t(99600).z,{getCustomInspectSymbol:s,getUtilInspect:l}=t(41394);class u extends i{constructor(){super(),this.synchronous=!0,this.idx=Object.create(null);let e=s();e&&(this[e]=this.inspect)}inspect(){let e={inspect:l(c)};return`{ idx: ${e.inspect(this.idx,!1,2)} }`}findCookie(e,a,t,o){return this.idx[e]&&this.idx[e][a]?o(null,this.idx[e][a][t]||null):o(null,void 0)}findCookies(e,a,t,o){let i,s=[];if("function"==typeof t&&(o=t,t=!0),!e)return o(null,[]);i=a?function(e){Object.keys(e).forEach(t=>{if(r(a,t)){let a=e[t];for(let e in a)s.push(a[e])}})}:function(e){for(let a in e){let t=e[a];for(let e in t)s.push(t[e])}};let l=n(e,t)||[e],u=this.idx;l.forEach(e=>{let a=u[e];a&&i(a)}),o(null,s)}putCookie(e,a){this.idx[e.domain]||(this.idx[e.domain]=Object.create(null)),this.idx[e.domain][e.path]||(this.idx[e.domain][e.path]=Object.create(null)),this.idx[e.domain][e.path][e.key]=e,a(null)}updateCookie(e,a,t){this.putCookie(a,t)}removeCookie(e,a,t,o){this.idx[e]&&this.idx[e][a]&&this.idx[e][a][t]&&delete this.idx[e][a][t],o(null)}removeCookies(e,a,t){return this.idx[e]&&(a?delete this.idx[e][a]:delete this.idx[e]),t(null)}removeAllCookies(e){return this.idx=Object.create(null),e(null)}getAllCookies(e){let a=[],t=this.idx;Object.keys(t).forEach(e=>{Object.keys(t[e]).forEach(o=>{Object.keys(t[e][o]).forEach(i=>{null!==i&&a.push(t[e][o][i])})})}),a.sort((e,a)=>(e.creationIndex||0)-(a.creationIndex||0)),e(null,a)}}function c(e){let a=Object.keys(e);if(0===a.length)return"[Object: null prototype] {}";let t="[Object: null prototype] {\n";return Object.keys(e).forEach((o,i)=>{var n,r;let s;t+=(n=o,r=e[o],s=`  '${n}': [Object: null prototype] {
`,Object.keys(r).forEach((e,a,t)=>{s+=function(e,a){let t="    ",o=`${t}'${e}': [Object: null prototype] {
`;return Object.keys(a).forEach((e,t,i)=>{let n=a[e];o+=`      ${e}: ${n.inspect()}`,t<i.length-1&&(o+=","),o+="\n"}),o+=`${t}}`}(e,r[e]),a<t.length-1&&(s+=","),s+="\n"}),s+="  }"),i<a.length-1&&(t+=","),t+="\n"}),t+="}"}["findCookie","findCookies","putCookie","updateCookie","removeCookie","removeCookies","removeAllCookies","getAllCookies"].forEach(e=>{u.prototype[e]=o(u.prototype[e])}),a.n=u},84912:(e,a,t)=>{"use strict";t.r(a),t.d(a,{decode:()=>h,default:()=>b,encode:()=>k,toASCII:()=>y,toUnicode:()=>f,ucs2decode:()=>m,ucs2encode:()=>p});let o=/^xn--/,i=/[^\0-\x7F]/,n=/[\x2E\u3002\uFF0E\uFF61]/g,r={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},s=Math.floor,l=String.fromCharCode;function u(e){throw RangeError(r[e])}function c(e,a){let t=e.split("@"),o="";return t.length>1&&(o=t[0]+"@",e=t[1]),o+(function(e,a){let t=[],o=e.length;for(;o--;)t[o]=a(e[o]);return t})((e=e.replace(n,".")).split("."),a).join(".")}function m(e){let a=[],t=0,o=e.length;for(;t<o;){let i=e.charCodeAt(t++);if(i>=55296&&i<=56319&&t<o){let o=e.charCodeAt(t++);(64512&o)==56320?a.push(((1023&i)<<10)+(1023&o)+65536):(a.push(i),t--)}else a.push(i)}return a}let p=e=>String.fromCodePoint(...e),d=function(e,a){return e+22+75*(e<26)-((0!=a)<<5)},g=function(e,a,t){let o=0;for(e=t?s(e/700):e>>1,e+=s(e/a);e>455;o+=36)e=s(e/35);return s(o+36*e/(e+38))},h=function(e){let a=[],t=e.length,o=0,i=128,n=72,r=e.lastIndexOf("-");r<0&&(r=0);for(let t=0;t<r;++t)e.charCodeAt(t)>=128&&u("not-basic"),a.push(e.charCodeAt(t));for(let c=r>0?r+1:0;c<t;){let r=o;for(let a=1,i=36;;i+=36){var l;c>=t&&u("invalid-input");let r=(l=e.charCodeAt(c++))>=48&&l<58?26+(l-48):l>=65&&l<91?l-65:l>=97&&l<123?l-97:36;r>=36&&u("invalid-input"),r>s((0x7fffffff-o)/a)&&u("overflow"),o+=r*a;let m=i<=n?1:i>=n+26?26:i-n;if(r<m)break;let p=36-m;a>s(0x7fffffff/p)&&u("overflow"),a*=p}let m=a.length+1;n=g(o-r,m,0==r),s(o/m)>0x7fffffff-i&&u("overflow"),i+=s(o/m),o%=m,a.splice(o++,0,i)}return String.fromCodePoint(...a)},k=function(e){let a=[],t=(e=m(e)).length,o=128,i=0,n=72;for(let t of e)t<128&&a.push(l(t));let r=a.length,c=r;for(r&&a.push("-");c<t;){let t=0x7fffffff;for(let a of e)a>=o&&a<t&&(t=a);let m=c+1;for(let p of(t-o>s((0x7fffffff-i)/m)&&u("overflow"),i+=(t-o)*m,o=t,e))if(p<o&&++i>0x7fffffff&&u("overflow"),p===o){let e=i;for(let t=36;;t+=36){let o=t<=n?1:t>=n+26?26:t-n;if(e<o)break;let i=e-o,r=36-o;a.push(l(d(o+i%r,0))),e=s(i/r)}a.push(l(d(e,0))),n=g(i,m,c===r),i=0,++c}++i,++o}return a.join("")},f=function(e){return c(e,function(e){return o.test(e)?h(e.slice(4).toLowerCase()):e})},y=function(e){return c(e,function(e){return i.test(e)?"xn--"+k(e):e})},b={version:"2.3.1",ucs2:{decode:m,encode:p},decode:h,encode:k,toASCII:y,toUnicode:f}},92347:(e,a)=>{"use strict";var t,o=Object.prototype.hasOwnProperty;function i(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function n(e){try{return encodeURIComponent(e)}catch(e){return null}}a.stringify=function(e,a){var i,r,s=[];for(r in"string"!=typeof(a=a||"")&&(a="?"),e)if(o.call(e,r)){if(!(i=e[r])&&(null===i||i===t||isNaN(i))&&(i=""),r=n(r),i=n(i),null===r||null===i)continue;s.push(r+"="+i)}return s.length?a+s.join("&"):""},a.parse=function(e){for(var a,t=/([^=?#&]+)=?([^&]*)/g,o={};a=t.exec(e);){var n=i(a[1]),r=i(a[2]);null===n||null===r||n in o||(o[n]=r)}return o}},93520:(e,a,t)=>{"use strict";let o=t(84912),i=t(49450),n=t(1608),r=t(43229).i,s=t(64560).n,l=t(99600).z,u=t(12757),c=t(40376),{fromCallback:m}=t(5837),{getCustomInspectSymbol:p}=t(41394),d=/^[\x21\x23-\x2B\x2D-\x3A\x3C-\x5B\x5D-\x7E]+$/,g=/[\x00-\x1F]/,h=["\n","\r","\0"],k=/[\x20-\x3A\x3C-\x7E]+/,f=/[\x09\x20-\x2F\x3B-\x40\x5B-\x60\x7B-\x7E]/,y={jan:0,feb:1,mar:2,apr:3,may:4,jun:5,jul:6,aug:7,sep:8,oct:9,nov:10,dec:11},b='Invalid sameSiteContext option for getCookies(); expected one of "strict", "lax", or "none"';function j(e){u.validate(u.isNonEmptyString(e),e);let a=String(e).toLowerCase();return"none"===a||"lax"===a||"strict"===a?a:null}let w=Object.freeze({SILENT:"silent",STRICT:"strict",DISABLED:"unsafe-disabled"}),v=/(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-f\d]{1,4}:){7}(?:[a-f\d]{1,4}|:)|(?:[a-f\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-f\d]{1,4}|:)|(?:[a-f\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,2}|:)|(?:[a-f\d]{1,4}:){4}(?:(?::[a-f\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,3}|:)|(?:[a-f\d]{1,4}:){3}(?:(?::[a-f\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,4}|:)|(?:[a-f\d]{1,4}:){2}(?:(?::[a-f\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,5}|:)|(?:[a-f\d]{1,4}:){1}(?:(?::[a-f\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,6}|:)|(?::(?:(?::[a-f\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,7}|:)))$)/,O=`
\\[?(?:
(?:[a-fA-F\\d]{1,4}:){7}(?:[a-fA-F\\d]{1,4}|:)|
(?:[a-fA-F\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|:[a-fA-F\\d]{1,4}|:)|
(?:[a-fA-F\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,2}|:)|
(?:[a-fA-F\\d]{1,4}:){4}(?:(?::[a-fA-F\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,3}|:)|
(?:[a-fA-F\\d]{1,4}:){3}(?:(?::[a-fA-F\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,4}|:)|
(?:[a-fA-F\\d]{1,4}:){2}(?:(?::[a-fA-F\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,5}|:)|
(?:[a-fA-F\\d]{1,4}:){1}(?:(?::[a-fA-F\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,6}|:)|
(?::(?:(?::[a-fA-F\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,7}|:))
)(?:%[0-9a-zA-Z]{1,})?\\]?
`.replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),z=RegExp(`^${O}$`);function S(e,a,t,o){let i=0;for(;i<e.length;){let a=e.charCodeAt(i);if(a<=47||a>=58)break;i++}return i<a||i>t||!o&&i!=e.length?null:parseInt(e.substr(0,i),10)}function x(e){if(!e)return;let a=e.split(f);if(!a)return;let t=null,o=null,i=null,n=null,r=null,s=null;for(let e=0;e<a.length;e++){let l,u=a[e].trim();if(u.length){if(null===i&&(l=function(e){let a=e.split(":"),t=[0,0,0];if(3!==a.length)return null;for(let e=0;e<3;e++){let o=2==e,i=S(a[e],1,2,o);if(null===i)return null;t[e]=i}return t}(u))){t=l[0],o=l[1],i=l[2];continue}if(null===n&&null!==(l=S(u,1,2,!0))){n=l;continue}if(null===r&&null!==(l=function(e){let a=y[e=String(e).substr(0,3).toLowerCase()];return a>=0?a:null}(u))){r=l;continue}null===s&&null!==(l=S(u,2,4,!0))&&((s=l)>=70&&s<=99?s+=1900:s>=0&&s<=69&&(s+=2e3))}}if(null!==n&&null!==r&&null!==s&&null!==i&&!(n<1)&&!(n>31)&&!(s<1601)&&!(t>23)&&!(o>59)&&!(i>59))return new Date(Date.UTC(s,r,n,t,o,i))}function A(e){return null==e?null:(e=e.trim().replace(/^\./,""),z.test(e)&&(e=e.replace("[","").replace("]","")),o&&/[^\u0001-\u007f]/.test(e)&&(e=o.toASCII(e)),e.toLowerCase())}function C(e,a,t){if(null==e||null==a)return null;if(!1!==t&&(e=A(e),a=A(a)),e==a)return!0;let o=e.lastIndexOf(a);return!(o<=0||e.length!==a.length+o||"."!==e.substr(o-1,1)||v.test(e))}function P(e){let a;try{a=JSON.parse(e)}catch(e){return e}return a}function T(e){let a;if(!e||u.isEmptyString(e))return null;if("string"==typeof e){if((a=P(e))instanceof Error)return null}else a=e;let t=new N;for(let e=0;e<N.serializableProperties.length;e++){let o=N.serializableProperties[e];void 0!==a[o]&&a[o]!==D[o]&&("expires"===o||"creation"===o||"lastAccessed"===o?null===a[o]?t[o]=null:t[o]="Infinity"==a[o]?"Infinity":new Date(a[o]):t[o]=a[o])}return t}function I(e,a){u.validate(u.isObject(e),e),u.validate(u.isObject(a),a);let t=0,o=e.path?e.path.length:0;return 0!=(t=(a.path?a.path.length:0)-o)?t:0!=(t=(e.creation?e.creation.getTime():2147483647e3)-(a.creation?a.creation.getTime():2147483647e3))?t:t=e.creationIndex-a.creationIndex}function E(e){if(e instanceof Object)return e;try{e=decodeURI(e)}catch(e){}return i(e)}let D={key:"",value:"",expires:"Infinity",maxAge:null,domain:null,path:null,secure:!1,httpOnly:!1,extensions:null,hostOnly:null,pathIsDefault:null,creation:null,lastAccessed:null,sameSite:void 0};class N{constructor(e={}){let a=p();a&&(this[a]=this.inspect),Object.assign(this,D,e),this.creation=this.creation||new Date,Object.defineProperty(this,"creationIndex",{configurable:!1,enumerable:!1,writable:!0,value:++N.cookiesCreated})}inspect(){let e=Date.now(),a=null!=this.hostOnly?this.hostOnly:"?",t=this.creation?`${e-this.creation.getTime()}ms`:"?",o=this.lastAccessed?`${e-this.lastAccessed.getTime()}ms`:"?";return`Cookie="${this.toString()}; hostOnly=${a}; aAge=${o}; cAge=${t}"`}toJSON(){let e={};for(let a of N.serializableProperties)this[a]!==D[a]&&("expires"===a||"creation"===a||"lastAccessed"===a?null===this[a]?e[a]=null:e[a]="Infinity"==this[a]?"Infinity":this[a].toISOString():"maxAge"===a?null!==this[a]&&(e[a]=this[a]==1/0||this[a]==-1/0?this[a].toString():this[a]):this[a]!==D[a]&&(e[a]=this[a]));return e}clone(){return T(this.toJSON())}validate(){if(!d.test(this.value)||this.expires!=1/0&&!(this.expires instanceof Date)&&!x(this.expires)||null!=this.maxAge&&this.maxAge<=0||null!=this.path&&!k.test(this.path))return!1;let e=this.cdomain();return!(e&&(e.match(/\.$/)||null==n.getPublicSuffix(e)))&&!0}setExpires(e){e instanceof Date?this.expires=e:this.expires=x(e)||"Infinity"}setMaxAge(e){e===1/0||e===-1/0?this.maxAge=e.toString():this.maxAge=e}cookieString(){let e=this.value;return(null==e&&(e=""),""===this.key)?e:`${this.key}=${e}`}toString(){var e;let a=this.cookieString();if(this.expires!=1/0&&(this.expires instanceof Date?a+=`; Expires=${e=this.expires,u.validate(u.isDate(e),e),e.toUTCString()}`:a+=`; Expires=${this.expires}`),null!=this.maxAge&&this.maxAge!=1/0&&(a+=`; Max-Age=${this.maxAge}`),this.domain&&!this.hostOnly&&(a+=`; Domain=${this.domain}`),this.path&&(a+=`; Path=${this.path}`),this.secure&&(a+="; Secure"),this.httpOnly&&(a+="; HttpOnly"),this.sameSite&&"none"!==this.sameSite){let e=N.sameSiteCanonical[this.sameSite.toLowerCase()];a+=`; SameSite=${e||this.sameSite}`}return this.extensions&&this.extensions.forEach(e=>{a+=`; ${e}`}),a}TTL(e){if(null!=this.maxAge)return this.maxAge<=0?0:1e3*this.maxAge;let a=this.expires;return a!=1/0?(a instanceof Date||(a=x(a)||1/0),a==1/0)?1/0:a.getTime()-(e||Date.now()):1/0}expiryTime(e){if(null!=this.maxAge){let a=e||this.creation||new Date,t=this.maxAge<=0?-1/0:1e3*this.maxAge;return a.getTime()+t}return this.expires==1/0?1/0:this.expires.getTime()}expiryDate(e){let a=this.expiryTime(e);return new Date(a==1/0?2147483647e3:a==-1/0?0:a)}isPersistent(){return null!=this.maxAge||this.expires!=1/0}canonicalizedDomain(){return null==this.domain?null:A(this.domain)}cdomain(){return this.canonicalizedDomain()}}function L(e){if(null!=e){let a=e.toLowerCase();switch(a){case w.STRICT:case w.SILENT:case w.DISABLED:return a}}return w.SILENT}N.cookiesCreated=0,N.parse=function(e,a){if(a&&"object"==typeof a||(a={}),u.isEmptyString(e)||!u.isString(e))return null;let t=(e=e.trim()).indexOf(";"),o=function(e,a){let t,o;e=function(e){if(u.isEmptyString(e))return e;for(let a=0;a<h.length;a++){let t=e.indexOf(h[a]);-1!==t&&(e=e.substr(0,t))}return e}(e),u.validate(u.isString(e),e);let i=e.indexOf("=");if(a)0===i&&(i=(e=e.substr(1)).indexOf("="));else if(i<=0)return;if(i<=0?(t="",o=e.trim()):(t=e.substr(0,i).trim(),o=e.substr(i+1).trim()),g.test(t)||g.test(o))return;let n=new N;return n.key=t,n.value=o,n}(-1===t?e:e.substr(0,t),!!a.loose);if(!o)return;if(-1===t)return o;let i=e.slice(t+1).trim();if(0===i.length)return o;let n=i.split(";");for(;n.length;){let e,a,t=n.shift().trim();if(0===t.length)continue;let i=t.indexOf("=");switch(-1===i?(e=t,a=null):(e=t.substr(0,i),a=t.substr(i+1)),e=e.trim().toLowerCase(),a&&(a=a.trim()),e){case"expires":if(a){let e=x(a);e&&(o.expires=e)}break;case"max-age":if(a&&/^-?[0-9]+$/.test(a)){let e=parseInt(a,10);o.setMaxAge(e)}break;case"domain":if(a){let e=a.trim().replace(/^\./,"");e&&(o.domain=e.toLowerCase())}break;case"path":o.path=a&&"/"===a[0]?a:null;break;case"secure":o.secure=!0;break;case"httponly":o.httpOnly=!0;break;case"samesite":switch(a?a.toLowerCase():""){case"strict":o.sameSite="strict";break;case"lax":o.sameSite="lax";break;case"none":o.sameSite="none";break;default:o.sameSite=void 0}break;default:o.extensions=o.extensions||[],o.extensions.push(t)}}return o},N.fromJSON=T,N.serializableProperties=Object.keys(D),N.sameSiteLevel={strict:3,lax:2,none:1},N.sameSiteCanonical={strict:"Strict",lax:"Lax"};class R{constructor(e,a={rejectPublicSuffixes:!0}){"boolean"==typeof a&&(a={rejectPublicSuffixes:a}),u.validate(u.isObject(a),a),this.rejectPublicSuffixes=a.rejectPublicSuffixes,this.enableLooseMode=!!a.looseMode,this.allowSpecialUseDomain="boolean"!=typeof a.allowSpecialUseDomain||a.allowSpecialUseDomain,this.store=e||new s,this.prefixSecurity=L(a.prefixSecurity),this._cloneSync=M("clone"),this._importCookiesSync=M("_importCookies"),this.getCookiesSync=M("getCookies"),this.getCookieStringSync=M("getCookieString"),this.getSetCookieStringsSync=M("getSetCookieStrings"),this.removeAllCookiesSync=M("removeAllCookies"),this.setCookieSync=M("setCookie"),this.serializeSync=M("serialize")}setCookie(e,a,t,o){let i;if(u.validate(u.isUrlStringOrObject(a),o,t),u.isFunction(a))return(o=a)(Error("No URL was specified"));let r=E(a);if(u.isFunction(t)&&(o=t,t={}),u.validate(u.isFunction(o),o),!u.isNonEmptyString(e)&&!u.isObject(e)&&e instanceof String&&0==e.length)return o(null);let s=A(r.hostname),l=t.loose||this.enableLooseMode,c=null;if(t.sameSiteContext&&!(c=j(t.sameSiteContext)))return o(Error(b));if("string"==typeof e||e instanceof String){if(!(e=N.parse(e,{loose:l})))return i=Error("Cookie failed to parse"),o(t.ignoreError?null:i)}else if(!(e instanceof N))return i=Error("First argument to setCookie must be a Cookie object or string"),o(t.ignoreError?null:i);let m=t.now||new Date;if(this.rejectPublicSuffixes&&e.domain&&null==n.getPublicSuffix(e.cdomain(),{allowSpecialUseDomain:this.allowSpecialUseDomain,ignoreError:t.ignoreError})&&!z.test(e.domain))return i=Error("Cookie has domain set to a public suffix"),o(t.ignoreError?null:i);if(e.domain){if(!C(s,e.cdomain(),!1))return i=Error(`Cookie not in this host's domain. Cookie:${e.cdomain()} Request:${s}`),o(t.ignoreError?null:i);null==e.hostOnly&&(e.hostOnly=!1)}else e.hostOnly=!0,e.domain=s;if(e.path&&"/"===e.path[0]||(e.path=function(e){if(!e||"/"!==e.substr(0,1))return"/";if("/"===e)return e;let a=e.lastIndexOf("/");return 0===a?"/":e.slice(0,a)}(r.pathname),e.pathIsDefault=!0),!1===t.http&&e.httpOnly)return i=Error("Cookie is HttpOnly and this isn't an HTTP API"),o(t.ignoreError?null:i);if("none"!==e.sameSite&&void 0!==e.sameSite&&c&&"none"===c)return i=Error("Cookie is SameSite but this is a cross-origin request"),o(t.ignoreError?null:i);let p=this.prefixSecurity===w.SILENT;if(this.prefixSecurity!==w.DISABLED){var d,g;let a,i=!1;if((d=e,u.validate(u.isObject(d),d),!d.key.startsWith("__Secure-")||d.secure)?(g=e,u.validate(u.isObject(g)),!g.key.startsWith("__Host-")||g.secure&&g.hostOnly&&null!=g.path&&"/"===g.path||(i=!0,a="Cookie has __Host prefix but either Secure or HostOnly attribute is not set or Path is not '/'")):(i=!0,a="Cookie has __Secure prefix but Secure attribute is not set"),i)return o(t.ignoreError||p?null:Error(a))}let h=this.store;h.updateCookie||(h.updateCookie=function(e,a,t){this.putCookie(a,t)}),h.findCookie(e.domain,e.path,e.key,function(a,i){if(a)return o(a);let n=function(a){if(a)return o(a);o(null,e)};if(i){if(!1===t.http&&i.httpOnly)return a=Error("old Cookie is HttpOnly and this isn't an HTTP API"),o(t.ignoreError?null:a);e.creation=i.creation,e.creationIndex=i.creationIndex,e.lastAccessed=m,h.updateCookie(i,e,n)}else e.creation=e.lastAccessed=m,h.putCookie(e,n)})}getCookies(e,a,t){u.validate(u.isUrlStringOrObject(e),t,e);let o=E(e);u.isFunction(a)&&(t=a,a={}),u.validate(u.isObject(a),t,a),u.validate(u.isFunction(t),t);let i=A(o.hostname),n=o.pathname||"/",r=a.secure;null==r&&o.protocol&&("https:"==o.protocol||"wss:"==o.protocol)&&(r=!0);let s=0;if(a.sameSiteContext){let e=j(a.sameSiteContext);if(!(s=N.sameSiteLevel[e]))return t(Error(b))}let c=a.http;null==c&&(c=!0);let m=a.now||Date.now(),p=!1!==a.expire,d=!!a.allPaths,g=this.store;function h(e){if(e.hostOnly){if(e.domain!=i)return!1}else if(!C(i,e.domain,!1))return!1;return(!!d||!!l(n,e.path))&&(!e.secure||!!r)&&(!e.httpOnly||!!c)&&(!s||!(N.sameSiteLevel[e.sameSite||"none"]>s))&&(!(p&&e.expiryTime()<=m)||(g.removeCookie(e.domain,e.path,e.key,()=>{}),!1))}g.findCookies(i,d?null:n,this.allowSpecialUseDomain,(e,o)=>{if(e)return t(e);o=o.filter(h),!1!==a.sort&&(o=o.sort(I));let i=new Date;for(let e of o)e.lastAccessed=i;t(null,o)})}getCookieString(...e){let a=e.pop();u.validate(u.isFunction(a),a),e.push(function(e,t){e?a(e):a(null,t.sort(I).map(e=>e.cookieString()).join("; "))}),this.getCookies.apply(this,e)}getSetCookieStrings(...e){let a=e.pop();u.validate(u.isFunction(a),a),e.push(function(e,t){e?a(e):a(null,t.map(e=>e.toString()))}),this.getCookies.apply(this,e)}serialize(e){u.validate(u.isFunction(e),e);let a=this.store.constructor.name;u.isObject(a)&&(a=null);let t={version:`tough-cookie@${c}`,storeType:a,rejectPublicSuffixes:!!this.rejectPublicSuffixes,enableLooseMode:!!this.enableLooseMode,allowSpecialUseDomain:!!this.allowSpecialUseDomain,prefixSecurity:L(this.prefixSecurity),cookies:[]};if(!(this.store.getAllCookies&&"function"==typeof this.store.getAllCookies))return e(Error("store does not support getAllCookies and cannot be serialized"));this.store.getAllCookies((a,o)=>a?e(a):(t.cookies=o.map(e=>(e=e instanceof N?e.toJSON():e,delete e.creationIndex,e)),e(null,t)))}toJSON(){return this.serializeSync()}_importCookies(e,a){let t=e.cookies;if(!t||!Array.isArray(t))return a(Error("serialized jar has no cookies array"));t=t.slice();let o=e=>{let i;if(e)return a(e);if(!t.length)return a(e,this);try{i=T(t.shift())}catch(e){return a(e)}if(null===i)return o(null);this.store.putCookie(i,o)};o()}clone(e,a){1==arguments.length&&(a=e,e=null),this.serialize((t,o)=>{if(t)return a(t);R.deserialize(o,e,a)})}cloneSync(e){if(0==arguments.length)return this._cloneSync();if(!e.synchronous)throw Error("CookieJar clone destination store is not synchronous; use async API instead.");return this._cloneSync(e)}removeAllCookies(e){u.validate(u.isFunction(e),e);let a=this.store;if("function"==typeof a.removeAllCookies&&a.removeAllCookies!==r.prototype.removeAllCookies)return a.removeAllCookies(e);a.getAllCookies((t,o)=>{if(t)return e(t);if(0===o.length)return e(null);let i=0,n=[];function r(a){if(a&&n.push(a),++i===o.length)return e(n.length?n[0]:null)}o.forEach(e=>{a.removeCookie(e.domain,e.path,e.key,r)})})}static deserialize(e,a,t){let o;if(3!=arguments.length&&(t=a,a=null),u.validate(u.isFunction(t),t),"string"==typeof e){if((o=P(e))instanceof Error)return t(o)}else o=e;let i=new R(a,{rejectPublicSuffixes:o.rejectPublicSuffixes,looseMode:o.enableLooseMode,allowSpecialUseDomain:o.allowSpecialUseDomain,prefixSecurity:o.prefixSecurity});i._importCookies(o,e=>{if(e)return t(e);t(null,i)})}static deserializeSync(e,a){let t="string"==typeof e?JSON.parse(e):e,o=new R(a,{rejectPublicSuffixes:t.rejectPublicSuffixes,looseMode:t.enableLooseMode});if(!o.store.synchronous)throw Error("CookieJar store is not synchronous; use async API instead.");return o._importCookiesSync(t),o}}function M(e){return function(...a){let t,o;if(!this.store.synchronous)throw Error("CookieJar store is not synchronous; use async API instead.");if(this[e](...a,(e,a)=>{t=e,o=a}),t)throw t;return o}}R.fromJSON=R.deserializeSync,["_importCookies","clone","getCookies","getCookieString","getSetCookieStrings","removeAllCookies","serialize","setCookie"].forEach(e=>{R.prototype[e]=m(R.prototype[e])}),R.deserialize=m(R.deserialize),a.cP=R,a.Hk=N,n.getPublicSuffix,t(10018).$,u.ParameterError},99600:(e,a)=>{"use strict";a.z=function(e,a){return a===e||0===e.indexOf(a)&&("/"===a.substr(-1)||"/"===e.substr(a.length,1))||!1}}};