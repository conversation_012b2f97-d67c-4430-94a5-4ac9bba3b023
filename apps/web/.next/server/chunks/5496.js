exports.id=5496,exports.ids=[5496],exports.modules={1911:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&i(e,t,r);return n(e,t),e},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.HttpsProxyAgent=void 0;let u=o(r(91645)),a=o(r(34631)),h=s(r(12412)),f=s(r(40001)),l=r(2349),c=r(79551),p=r(87073),d=(0,f.default)("https-proxy-agent"),m=t=>void 0===t.servername&&t.host&&!u.isIP(t.host)?{...t,servername:t.host}:t;class g extends l.Agent{constructor(t,e){super(e),this.options={path:void 0},this.proxy="string"==typeof t?new c.URL(t):t,this.proxyHeaders=e?.headers??{},d("Creating new HttpsProxyAgent instance: %o",this.proxy.href);let r=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),i=this.proxy.port?parseInt(this.proxy.port,10):"https:"===this.proxy.protocol?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...e?v(e,"headers"):null,host:r,port:i}}async connect(t,e){let r,{proxy:i}=this;if(!e.host)throw TypeError('No "host" provided');"https:"===i.protocol?(d("Creating `tls.Socket`: %o",this.connectOpts),r=a.connect(m(this.connectOpts))):(d("Creating `net.Socket`: %o",this.connectOpts),r=u.connect(this.connectOpts));let n="function"==typeof this.proxyHeaders?this.proxyHeaders():{...this.proxyHeaders},o=u.isIPv6(e.host)?`[${e.host}]`:e.host,s=`CONNECT ${o}:${e.port} HTTP/1.1\r
`;if(i.username||i.password){let t=`${decodeURIComponent(i.username)}:${decodeURIComponent(i.password)}`;n["Proxy-Authorization"]=`Basic ${Buffer.from(t).toString("base64")}`}for(let t of(n.Host=`${o}:${e.port}`,n["Proxy-Connection"]||(n["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close"),Object.keys(n)))s+=`${t}: ${n[t]}\r
`;let f=(0,p.parseProxyResponse)(r);r.write(`${s}\r
`);let{connect:l,buffered:c}=await f;if(t.emit("proxyConnect",l),this.emit("proxyConnect",l,t),200===l.statusCode)return(t.once("socket",y),e.secureEndpoint)?(d("Upgrading socket connection to TLS"),a.connect({...v(m(e),"host","path","port"),socket:r})):r;r.destroy();let g=new u.Socket({writable:!1});return g.readable=!0,t.once("socket",t=>{d("Replaying proxy buffer for failed request"),(0,h.default)(t.listenerCount("data")>0),t.push(c),t.push(null)}),g}}function y(t){t.resume()}function v(t,...e){let r,i={};for(r in t)e.includes(r)||(i[r]=t[r]);return i}g.protocols=["http","https"],e.HttpsProxyAgent=g},2349:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&i(e,t,r);return n(e,t),e},s=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||i(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),e.Agent=void 0;let u=o(r(91645)),a=o(r(81630)),h=r(55591);s(r(30760),e);let f=Symbol("AgentBaseInternalState");class l extends a.Agent{constructor(t){super(t),this[f]={}}isSecureEndpoint(t){if(t){if("boolean"==typeof t.secureEndpoint)return t.secureEndpoint;if("string"==typeof t.protocol)return"https:"===t.protocol}let{stack:e}=Error();return"string"==typeof e&&e.split("\n").some(t=>-1!==t.indexOf("(https.js:")||-1!==t.indexOf("node:https:"))}incrementSockets(t){if(this.maxSockets===1/0&&this.maxTotalSockets===1/0)return null;this.sockets[t]||(this.sockets[t]=[]);let e=new u.Socket({writable:!1});return this.sockets[t].push(e),this.totalSocketCount++,e}decrementSockets(t,e){if(!this.sockets[t]||null===e)return;let r=this.sockets[t],i=r.indexOf(e);-1!==i&&(r.splice(i,1),this.totalSocketCount--,0===r.length&&delete this.sockets[t])}getName(t){return("boolean"==typeof t.secureEndpoint?t.secureEndpoint:this.isSecureEndpoint(t))?h.Agent.prototype.getName.call(this,t):super.getName(t)}createSocket(t,e,r){let i={...e,secureEndpoint:this.isSecureEndpoint(e)},n=this.getName(i),o=this.incrementSockets(n);Promise.resolve().then(()=>this.connect(t,i)).then(s=>{if(this.decrementSockets(n,o),s instanceof a.Agent)try{return s.addRequest(t,i)}catch(t){return r(t)}this[f].currentSocket=s,super.createSocket(t,e,r)},t=>{this.decrementSockets(n,o),r(t)})}createConnection(){let t=this[f].currentSocket;if(this[f].currentSocket=void 0,!t)throw Error("No socket was returned in the `connect()` function");return t}get defaultPort(){return this[f].defaultPort??("https:"===this.protocol?443:80)}set defaultPort(t){this[f]&&(this[f].defaultPort=t)}get protocol(){return this[f].protocol??(this.isSecureEndpoint()?"https:":"http:")}set protocol(t){this[f]&&(this[f].protocol=t)}}e.Agent=l},10172:(t,e,r)=>{"use strict";function i(t,e,r,i,n){Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.message=t,this.statusCode=e,this.headers=r,this.body=i,this.endpoint=n}r(28354).inherits(i,Error),t.exports=i},11252:(t,e,r)=>{var i,n=r(86417).Buffer,o=r(55511),s=r(61090),u=r(28354),a="secret must be a string or buffer",h="key must be a string or a buffer",f="function"==typeof o.createPublicKey;function l(t){if(!n.isBuffer(t)&&"string"!=typeof t&&(!f||"object"!=typeof t||"string"!=typeof t.type||"string"!=typeof t.asymmetricKeyType||"function"!=typeof t.export))throw m(h)}function c(t){if(!n.isBuffer(t)&&"string"!=typeof t&&"object"!=typeof t)throw m("key must be a string, a buffer or an object")}function p(t){return t.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function d(t){var e=4-(t=t.toString()).length%4;if(4!==e)for(var r=0;r<e;++r)t+="=";return t.replace(/\-/g,"+").replace(/_/g,"/")}function m(t){var e=[].slice.call(arguments,1);return TypeError(u.format.bind(u,t).apply(null,e))}function g(t){var e;return e=t,n.isBuffer(e)||"string"==typeof e||(t=JSON.stringify(t)),t}function y(t){return function(e,r){!function(t){if(!n.isBuffer(t)){if("string"!=typeof t){if(!f||"object"!=typeof t||"secret"!==t.type||"function"!=typeof t.export)throw m(a)}}}(r),e=g(e);var i=o.createHmac("sha"+t,r);return p((i.update(e),i.digest("base64")))}}f&&(h+=" or a KeyObject",a+="or a KeyObject");var v="timingSafeEqual"in o?function(t,e){return t.byteLength===e.byteLength&&o.timingSafeEqual(t,e)}:function(t,e){return i||(i=r(93823)),i(t,e)};function b(t){return function(e,r,i){var o=y(t)(e,i);return v(n.from(r),n.from(o))}}function w(t){return function(e,r){c(r),e=g(e);var i=o.createSign("RSA-SHA"+t);return p((i.update(e),i.sign(r,"base64")))}}function M(t){return function(e,r,i){l(i),e=g(e),r=d(r);var n=o.createVerify("RSA-SHA"+t);return n.update(e),n.verify(i,r,"base64")}}function _(t){return function(e,r){c(r),e=g(e);var i=o.createSign("RSA-SHA"+t);return p((i.update(e),i.sign({key:r,padding:o.constants.RSA_PKCS1_PSS_PADDING,saltLength:o.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function x(t){return function(e,r,i){l(i),e=g(e),r=d(r);var n=o.createVerify("RSA-SHA"+t);return n.update(e),n.verify({key:i,padding:o.constants.RSA_PKCS1_PSS_PADDING,saltLength:o.constants.RSA_PSS_SALTLEN_DIGEST},r,"base64")}}function E(t){var e=w(t);return function(){var r=e.apply(null,arguments);return s.derToJose(r,"ES"+t)}}function S(t){var e=M(t);return function(r,i,n){return e(r,i=s.joseToDer(i,"ES"+t).toString("base64"),n)}}function C(){return function(){return""}}function k(){return function(t,e){return""===e}}t.exports=function(t){var e=t.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/);if(!e)throw m('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',t);var r=(e[1]||e[3]).toLowerCase(),i=e[2];return{sign:({hs:y,rs:w,ps:_,es:E,none:C})[r](i),verify:({hs:b,rs:M,ps:x,es:S,none:k})[r](i)}}},12187:(t,e,r)=>{var i=r(79428).Buffer;t.exports=function(t){return"string"==typeof t?t:"number"==typeof t||i.isBuffer(t)?t.toString():JSON.stringify(t)}},13891:(t,e,r)=>{var i=r(99827),n=r(26023);e.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],e.sign=i.sign,e.verify=n.verify,e.decode=n.decode,e.isValid=n.isValid,e.createSign=function(t){return new i(t)},e.createVerify=function(t){return new n(t)}},14186:(t,e,r)=>{"use strict";let i=r(85849),n=r(88409).Buffer,o=r(61460);function s(t){o.call(this,t),this.enc="pem"}i(s,o),t.exports=s,s.prototype.decode=function(t,e){let r=t.toString().split(/[\r\n]+/g),i=e.label.toUpperCase(),s=/^-----(BEGIN|END) ([^-]+)-----$/,u=-1,a=-1;for(let t=0;t<r.length;t++){let e=r[t].match(s);if(null!==e&&e[2]===i)if(-1===u){if("BEGIN"!==e[1])break;u=t}else{if("END"!==e[1])break;a=t;break}}if(-1===u||-1===a)throw Error("PEM section not found for: "+i);let h=r.slice(u+1,a).join("");h.replace(/[^a-z0-9+/=]+/gi,"");let f=n.from(h,"base64");return o.prototype.decode.call(this,f,e)}},15795:(t,e,r)=>{"use strict";e.der=r(61460),e.pem=r(14186)},17304:(t,e,r)=>{"use strict";let i=r(94515),n=r(96580),o=r(22745),s=r(10172),u=r(39627),a=new o;t.exports={WebPushError:s,supportedContentEncodings:u.supportedContentEncodings,encrypt:n.encrypt,getVapidHeaders:i.getVapidHeaders,generateVAPIDKeys:i.generateVAPIDKeys,setGCMAPIKey:a.setGCMAPIKey,setVapidDetails:a.setVapidDetails,generateRequestDetails:a.generateRequestDetails,sendNotification:a.sendNotification.bind(a)}},22745:(t,e,r)=>{"use strict";let i,n=r(79551),o=r(55591),s=r(10172),u=r(94515),a=r(96580),h=r(39627),f=r(94099),l="";function c(){}c.prototype.setGCMAPIKey=function(t){if(null===t){l=null;return}if(void 0===t||"string"!=typeof t||0===t.length)throw Error("The GCM API Key should be a non-empty string or null.");l=t},c.prototype.setVapidDetails=function(t,e,r){if(1==arguments.length&&null===arguments[0]){i=null;return}u.validateSubject(t),u.validatePublicKey(e),u.validatePrivateKey(r),i={subject:t,publicKey:e,privateKey:r}},c.prototype.generateRequestDetails=function(t,e,r){let s,c,p,d;if(!t||!t.endpoint)throw Error("You must pass in a subscription with at least an endpoint.");if("string"!=typeof t.endpoint||0===t.endpoint.length)throw Error("The subscription endpoint must be a string with a valid URL.");if(e&&("object"!=typeof t||!t.keys||!t.keys.p256dh||!t.keys.auth))throw Error("To send a message with a payload, the subscription must have 'auth' and 'p256dh' keys.");let m=l,g=i,y=2419200,v={},b=h.supportedContentEncodings.AES_128_GCM,w=h.supportedUrgency.NORMAL;if(r){let t=["headers","gcmAPIKey","vapidDetails","TTL","contentEncoding","urgency","topic","proxy","agent","timeout"],e=Object.keys(r);for(let r=0;r<e.length;r+=1){let i=e[r];if(!t.includes(i))throw Error("'"+i+"' is an invalid option. The valid options are ['"+t.join("', '")+"'].")}if(r.headers){let t=Object.keys(v=r.headers).filter(function(t){return void 0!==r[t]});if(t.length>0)throw Error("Duplicated headers defined ["+t.join(",")+"]. Please either define the header in thetop level options OR in the 'headers' key.")}if(r.gcmAPIKey&&(m=r.gcmAPIKey),void 0!==r.vapidDetails&&(g=r.vapidDetails),void 0!==r.TTL&&(y=Number(r.TTL))<0)throw Error("TTL should be a number and should be at least 0");if(r.contentEncoding)if(r.contentEncoding===h.supportedContentEncodings.AES_128_GCM||r.contentEncoding===h.supportedContentEncodings.AES_GCM)b=r.contentEncoding;else throw Error("Unsupported content encoding specified.");if(r.urgency)if(r.urgency===h.supportedUrgency.VERY_LOW||r.urgency===h.supportedUrgency.LOW||r.urgency===h.supportedUrgency.NORMAL||r.urgency===h.supportedUrgency.HIGH)w=r.urgency;else throw Error("Unsupported urgency specified.");if(r.topic){if(!f.validate(r.topic))throw Error("Unsupported characters set use the URL or filename-safe Base64 characters set");if(r.topic.length>32)throw Error("use maximum of 32 characters from the URL or filename-safe Base64 characters set");s=r.topic}r.proxy&&("string"==typeof r.proxy||"string"==typeof r.proxy.host?c=r.proxy:console.warn("Attempt to use proxy option, but invalid type it should be a string or proxy options object.")),r.agent&&(r.agent instanceof o.Agent?(c&&console.warn("Agent option will be ignored because proxy option is defined."),p=r.agent):console.warn("Wrong type for the agent option, it should be an instance of https.Agent.")),"number"==typeof r.timeout&&(d=r.timeout)}void 0===y&&(y=2419200);let M={method:"POST",headers:{TTL:y}};Object.keys(v).forEach(function(t){M.headers[t]=v[t]});let _=null;if(e){let r=a.encrypt(t.keys.p256dh,t.keys.auth,e,b);M.headers["Content-Length"]=r.cipherText.length,M.headers["Content-Type"]="application/octet-stream",b===h.supportedContentEncodings.AES_128_GCM?M.headers["Content-Encoding"]=h.supportedContentEncodings.AES_128_GCM:b===h.supportedContentEncodings.AES_GCM&&(M.headers["Content-Encoding"]=h.supportedContentEncodings.AES_GCM,M.headers.Encryption="salt="+r.salt,M.headers["Crypto-Key"]="dh="+r.localPublicKey.toString("base64url")),_=r.cipherText}else M.headers["Content-Length"]=0;let x=t.endpoint.startsWith("https://android.googleapis.com/gcm/send"),E=t.endpoint.startsWith("https://fcm.googleapis.com/fcm/send");if(x)m?M.headers.Authorization="key="+m:console.warn("Attempt to send push notification to GCM endpoint, but no GCM key is defined. Please use setGCMApiKey() or add 'gcmAPIKey' as an option.");else if(g){let e=n.parse(t.endpoint),r=e.protocol+"//"+e.host,i=u.getVapidHeaders(r,g.subject,g.publicKey,g.privateKey,b);M.headers.Authorization=i.Authorization,b===h.supportedContentEncodings.AES_GCM&&(M.headers["Crypto-Key"]?M.headers["Crypto-Key"]+=";"+i["Crypto-Key"]:M.headers["Crypto-Key"]=i["Crypto-Key"])}else E&&m&&(M.headers.Authorization="key="+m);return M.headers.Urgency=w,s&&(M.headers.Topic=s),M.body=_,M.endpoint=t.endpoint,c&&(M.proxy=c),p&&(M.agent=p),d&&(M.timeout=d),M},c.prototype.sendNotification=function(t,e,i){let u;try{u=this.generateRequestDetails(t,e,i)}catch(t){return Promise.reject(t)}return new Promise(function(t,e){let i={},a=n.parse(u.endpoint);if(i.hostname=a.hostname,i.port=a.port,i.path=a.path,i.headers=u.headers,i.method=u.method,u.timeout&&(i.timeout=u.timeout),u.agent&&(i.agent=u.agent),u.proxy){let{HttpsProxyAgent:t}=r(1911);i.agent=new t(u.proxy)}let h=o.request(i,function(r){let i="";r.on("data",function(t){i+=t}),r.on("end",function(){r.statusCode<200||r.statusCode>299?e(new s("Received unexpected response code",r.statusCode,r.headers,i,u.endpoint)):t({statusCode:r.statusCode,body:i,headers:r.headers})})});u.timeout&&h.on("timeout",function(){h.destroy(Error("Socket timeout"))}),h.on("error",function(t){e(t)}),u.body&&h.write(u.body),h.end()})},t.exports=c},23294:t=>{"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}},23770:(t,e,r)=>{"use strict";let i=r(85849);function n(t){this._reporterState={obj:null,path:[],options:t||{},errors:[]}}function o(t,e){this.path=t,this.rethrow(e)}e.a=n,n.prototype.isError=function(t){return t instanceof o},n.prototype.save=function(){let t=this._reporterState;return{obj:t.obj,pathLen:t.path.length}},n.prototype.restore=function(t){let e=this._reporterState;e.obj=t.obj,e.path=e.path.slice(0,t.pathLen)},n.prototype.enterKey=function(t){return this._reporterState.path.push(t)},n.prototype.exitKey=function(t){let e=this._reporterState;e.path=e.path.slice(0,t-1)},n.prototype.leaveKey=function(t,e,r){let i=this._reporterState;this.exitKey(t),null!==i.obj&&(i.obj[e]=r)},n.prototype.path=function(){return this._reporterState.path.join("/")},n.prototype.enterObject=function(){let t=this._reporterState,e=t.obj;return t.obj={},e},n.prototype.leaveObject=function(t){let e=this._reporterState,r=e.obj;return e.obj=t,r},n.prototype.error=function(t){let e,r=this._reporterState,i=t instanceof o;if(e=i?t:new o(r.path.map(function(t){return"["+JSON.stringify(t)+"]"}).join(""),t.message||t,t.stack),!r.options.partial)throw e;return i||r.errors.push(e),e},n.prototype.wrapResult=function(t){let e=this._reporterState;return e.options.partial?{result:this.isError(t)?null:t,errors:e.errors}:t},i(o,Error),o.prototype.rethrow=function(t){if(this.message=t+" at: "+(this.path||"(shallow)"),Error.captureStackTrace&&Error.captureStackTrace(this,o),!this.stack)try{throw Error(this.message)}catch(t){this.stack=t.stack}return this}},26023:(t,e,r)=>{var i=r(86417).Buffer,n=r(57592),o=r(11252),s=r(27910),u=r(12187),a=r(28354),h=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function f(t){var e=t.split(".",1)[0],r=i.from(e,"base64").toString("binary");if("[object Object]"===Object.prototype.toString.call(r))return r;try{return JSON.parse(r)}catch(t){return}}function l(t){return t.split(".")[2]}function c(t){return h.test(t)&&!!f(t)}function p(t,e,r){if(!e){var i=Error("Missing algorithm parameter for jws.verify");throw i.code="MISSING_ALGORITHM",i}var n=l(t=u(t)),s=t.split(".",2).join(".");return o(e).verify(s,n,r)}function d(t,e){if(e=e||{},!c(t=u(t)))return null;var r,n,o=f(t);if(!o)return null;var s=(r=r||"utf8",n=t.split(".")[1],i.from(n,"base64").toString(r));return("JWT"===o.typ||e.json)&&(s=JSON.parse(s,e.encoding)),{header:o,payload:s,signature:l(t)}}function m(t){var e=new n((t=t||{}).secret||t.publicKey||t.key);this.readable=!0,this.algorithm=t.algorithm,this.encoding=t.encoding,this.secret=this.publicKey=this.key=e,this.signature=new n(t.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}a.inherits(m,s),m.prototype.verify=function(){try{var t=p(this.signature.buffer,this.algorithm,this.key.buffer),e=d(this.signature.buffer,this.encoding);return this.emit("done",t,e),this.emit("data",t),this.emit("end"),this.readable=!1,t}catch(t){this.readable=!1,this.emit("error",t),this.emit("close")}},m.decode=d,m.isValid=c,m.verify=p,t.exports=m},26763:(t,e,r)=>{"use strict";e._reverse=function(t){let e={};return Object.keys(t).forEach(function(r){(0|r)==r&&(r|=0),e[t[r]]=r}),e},e.der=r(43068)},27335:(t,e,r)=>{"use strict";e.der=r(44240),e.pem=r(91017)},30760:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&i(e,t,r);return n(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.req=e.json=e.toBuffer=void 0;let s=o(r(81630)),u=o(r(55591));async function a(t){let e=0,r=[];for await(let i of t)e+=i.length,r.push(i);return Buffer.concat(r,e)}e.toBuffer=a,e.json=async function(t){let e=(await a(t)).toString("utf8");try{return JSON.parse(e)}catch(t){throw t.message+=` (input: ${e})`,t}},e.req=function(t,e={}){let r=(("string"==typeof t?t:t.href).startsWith("https:")?u:s).request(t,e),i=new Promise((t,e)=>{r.once("response",t).once("error",e).end()});return r.then=i.then.bind(i),r}},38663:t=>{"use strict";function e(t){return(t/8|0)+ +(t%8!=0)}var r={ES256:e(256),ES384:e(384),ES512:e(521)};t.exports=function(t){var e=r[t];if(e)return e;throw Error('Unknown algorithm "'+t+'"')}},39627:t=>{"use strict";let e={};e.supportedContentEncodings={AES_GCM:"aesgcm",AES_128_GCM:"aes128gcm"},e.supportedUrgency={VERY_LOW:"very-low",LOW:"low",NORMAL:"normal",HIGH:"high"},t.exports=e},40001:(t,e,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?t.exports=r(46225):t.exports=r(42425)},40016:function(t,e,r){!function(t,e){"use strict";function i(t,e){if(!t)throw Error(e||"Assertion failed")}function n(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function o(t,e,r){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&(("le"===e||"be"===e)&&(r=e,e=10),this._init(t||0,e||10,r||"be"))}"object"==typeof t?t.exports=o:e.BN=o,o.BN=o,o.wordSize=26;try{h="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:r(79428).Buffer}catch(t){}function s(t,e){var r=t.charCodeAt(e);return r>=65&&r<=70?r-55:r>=97&&r<=102?r-87:r-48&15}function u(t,e,r){var i=s(t,r);return r-1>=e&&(i|=s(t,r-1)<<4),i}function a(t,e,r,i){for(var n=0,o=Math.min(t.length,r),s=e;s<o;s++){var u=t.charCodeAt(s)-48;n*=i,u>=49?n+=u-49+10:u>=17?n+=u-17+10:n+=u}return n}o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,e){return t.cmp(e)>0?t:e},o.min=function(t,e){return 0>t.cmp(e)?t:e},o.prototype._init=function(t,e,r){if("number"==typeof t)return this._initNumber(t,e,r);if("object"==typeof t)return this._initArray(t,e,r);"hex"===e&&(e=16),i(e===(0|e)&&e>=2&&e<=36);var n=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(n++,this.negative=1),n<t.length&&(16===e?this._parseHex(t,n,r):(this._parseBase(t,e,n),"le"===r&&this._initArray(this.toArray(),e,r)))},o.prototype._initNumber=function(t,e,r){t<0&&(this.negative=1,t=-t),t<0x4000000?(this.words=[0x3ffffff&t],this.length=1):t<0x10000000000000?(this.words=[0x3ffffff&t,t/0x4000000&0x3ffffff],this.length=2):(i(t<0x20000000000000),this.words=[0x3ffffff&t,t/0x4000000&0x3ffffff,1],this.length=3),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initArray=function(t,e,r){if(i("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=Array(this.length);for(var n,o,s=0;s<this.length;s++)this.words[s]=0;var u=0;if("be"===r)for(s=t.length-1,n=0;s>=0;s-=3)o=t[s]|t[s-1]<<8|t[s-2]<<16,this.words[n]|=o<<u&0x3ffffff,this.words[n+1]=o>>>26-u&0x3ffffff,(u+=24)>=26&&(u-=26,n++);else if("le"===r)for(s=0,n=0;s<t.length;s+=3)o=t[s]|t[s+1]<<8|t[s+2]<<16,this.words[n]|=o<<u&0x3ffffff,this.words[n+1]=o>>>26-u&0x3ffffff,(u+=24)>=26&&(u-=26,n++);return this.strip()},o.prototype._parseHex=function(t,e,r){this.length=Math.ceil((t.length-e)/6),this.words=Array(this.length);for(var i,n=0;n<this.length;n++)this.words[n]=0;var o=0,s=0;if("be"===r)for(n=t.length-1;n>=e;n-=2)i=u(t,e,n)<<o,this.words[s]|=0x3ffffff&i,o>=18?(o-=18,s+=1,this.words[s]|=i>>>26):o+=8;else for(n=(t.length-e)%2==0?e+1:e;n<t.length;n+=2)i=u(t,e,n)<<o,this.words[s]|=0x3ffffff&i,o>=18?(o-=18,s+=1,this.words[s]|=i>>>26):o+=8;this.strip()},o.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var i=0,n=1;n<=0x3ffffff;n*=e)i++;i--,n=n/e|0;for(var o=t.length-r,s=o%i,u=Math.min(o,o-s)+r,h=0,f=r;f<u;f+=i)h=a(t,f,f+i,e),this.imuln(n),this.words[0]+h<0x4000000?this.words[0]+=h:this._iaddn(h);if(0!==s){var l=1;for(h=a(t,f,t.length,e),f=0;f<s;f++)l*=e;this.imuln(l),this.words[0]+h<0x4000000?this.words[0]+=h:this._iaddn(h)}this.strip()},o.prototype.copy=function(t){t.words=Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var h,f=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],l=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],c=[0,0,0x2000000,0x290d741,0x1000000,0x2e90edd,0x39aa400,0x267bf47,0x1000000,0x290d741,1e7,0x12959c3,0x222c000,0x3bd7765,7529536,0xadcea1,0x1000000,0x1704f61,0x206fc40,0x2cddcf9,64e6,4084101,5153632,6436343,7962624,9765625,0xb54ba0,0xdaf26b,0x1069c00,0x138f9ad,243e5,0x1b4d89f,0x2000000,0x25528a1,0x2b54a20,0x3216b93,0x39aa400];function p(t,e,r){r.negative=e.negative^t.negative;var i=t.length+e.length|0;r.length=i,i=i-1|0;var n=0|t.words[0],o=0|e.words[0],s=n*o,u=0x3ffffff&s,a=s/0x4000000|0;r.words[0]=u;for(var h=1;h<i;h++){for(var f=a>>>26,l=0x3ffffff&a,c=Math.min(h,e.length-1),p=Math.max(0,h-t.length+1);p<=c;p++){var d=h-p|0;f+=(s=(n=0|t.words[d])*(o=0|e.words[p])+l)/0x4000000|0,l=0x3ffffff&s}r.words[h]=0|l,a=0|f}return 0!==a?r.words[h]=0|a:r.length--,r.strip()}o.prototype.toString=function(t,e){if(e=0|e||1,16===(t=t||10)||"hex"===t){for(var r="",n=0,o=0,s=0;s<this.length;s++){var u=this.words[s],a=((u<<n|o)&0xffffff).toString(16);o=u>>>24-n&0xffffff,(n+=2)>=26&&(n-=26,s--),r=0!==o||s!==this.length-1?f[6-a.length]+a+r:a+r}for(0!==o&&(r=o.toString(16)+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(t===(0|t)&&t>=2&&t<=36){var h=l[t],p=c[t];r="";var d=this.clone();for(d.negative=0;!d.isZero();){var m=d.modn(p).toString(t);r=(d=d.idivn(p)).isZero()?m+r:f[h-m.length]+m+r}for(this.isZero()&&(r="0"+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}i(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=0x4000000*this.words[1]:3===this.length&&1===this.words[2]?t+=0x10000000000000+0x4000000*this.words[1]:this.length>2&&i(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,e){return i(void 0!==h),this.toArrayLike(h,t,e)},o.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},o.prototype.toArrayLike=function(t,e,r){var n,o,s=this.byteLength(),u=r||Math.max(1,s);i(s<=u,"byte array longer than desired length"),i(u>0,"Requested array length <= 0"),this.strip();var a=new t(u),h=this.clone();if("le"===e){for(o=0;!h.isZero();o++)n=h.andln(255),h.iushrn(8),a[o]=n;for(;o<u;o++)a[o]=0}else{for(o=0;o<u-s;o++)a[o]=0;for(o=0;!h.isZero();o++)n=h.andln(255),h.iushrn(8),a[u-o-1]=n}return a},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},o.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return(8191&e)==0&&(r+=13,e>>>=13),(127&e)==0&&(r+=7,e>>>=7),(15&e)==0&&(r+=4,e>>>=4),(3&e)==0&&(r+=2,e>>>=2),(1&e)==0&&r++,r},o.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return(this.length-1)*26+e},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this.strip()},o.prototype.ior=function(t){return i((this.negative|t.negative)==0),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this.strip()},o.prototype.iand=function(t){return i((this.negative|t.negative)==0),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){this.length>t.length?(e=this,r=t):(e=t,r=this);for(var e,r,i=0;i<r.length;i++)this.words[i]=e.words[i]^r.words[i];if(this!==e)for(;i<e.length;i++)this.words[i]=e.words[i];return this.length=e.length,this.strip()},o.prototype.ixor=function(t){return i((this.negative|t.negative)==0),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){i("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),r=t%26;this._expand(e),r>0&&e--;for(var n=0;n<e;n++)this.words[n]=0x3ffffff&~this.words[n];return r>0&&(this.words[n]=~this.words[n]&0x3ffffff>>26-r),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,e){i("number"==typeof t&&t>=0);var r=t/26|0,n=t%26;return this._expand(r+1),e?this.words[r]=this.words[r]|1<<n:this.words[r]=this.words[r]&~(1<<n),this.strip()},o.prototype.iadd=function(t){if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,i=t):(r=t,i=this);for(var e,r,i,n=0,o=0;o<i.length;o++)e=(0|r.words[o])+(0|i.words[o])+n,this.words[o]=0x3ffffff&e,n=e>>>26;for(;0!==n&&o<r.length;o++)e=(0|r.words[o])+n,this.words[o]=0x3ffffff&e,n=e>>>26;if(this.length=r.length,0!==n)this.words[this.length]=n,this.length++;else if(r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this},o.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e,r,i=this.iadd(t);return t.negative=1,i._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var n=this.cmp(t);if(0===n)return this.negative=0,this.length=1,this.words[0]=0,this;n>0?(e=this,r=t):(e=t,r=this);for(var o=0,s=0;s<r.length;s++)o=(i=(0|e.words[s])-(0|r.words[s])+o)>>26,this.words[s]=0x3ffffff&i;for(;0!==o&&s<e.length;s++)o=(i=(0|e.words[s])+o)>>26,this.words[s]=0x3ffffff&i;if(0===o&&s<e.length&&e!==this)for(;s<e.length;s++)this.words[s]=e.words[s];return this.length=Math.max(this.length,s),e!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var d=function(t,e,r){var i,n,o,s=t.words,u=e.words,a=r.words,h=0,f=0|s[0],l=8191&f,c=f>>>13,p=0|s[1],d=8191&p,m=p>>>13,g=0|s[2],y=8191&g,v=g>>>13,b=0|s[3],w=8191&b,M=b>>>13,_=0|s[4],x=8191&_,E=_>>>13,S=0|s[5],C=8191&S,k=S>>>13,B=0|s[6],A=8191&B,O=B>>>13,j=0|s[7],T=8191&j,P=j>>>13,R=0|s[8],I=8191&R,N=R>>>13,U=0|s[9],F=8191&U,D=U>>>13,L=0|u[0],K=8191&L,q=L>>>13,G=0|u[1],H=8191&G,V=G>>>13,Z=0|u[2],z=8191&Z,$=Z>>>13,J=0|u[3],W=8191&J,Y=J>>>13,X=0|u[4],Q=8191&X,tt=X>>>13,te=0|u[5],tr=8191&te,ti=te>>>13,tn=0|u[6],to=8191&tn,ts=tn>>>13,tu=0|u[7],ta=8191&tu,th=tu>>>13,tf=0|u[8],tl=8191&tf,tc=tf>>>13,tp=0|u[9],td=8191&tp,tm=tp>>>13;r.negative=t.negative^e.negative,r.length=19,i=Math.imul(l,K);var tg=(h+i|0)+((8191&(n=(n=Math.imul(l,q))+Math.imul(c,K)|0))<<13)|0;h=((o=Math.imul(c,q))+(n>>>13)|0)+(tg>>>26)|0,tg&=0x3ffffff,i=Math.imul(d,K),n=(n=Math.imul(d,q))+Math.imul(m,K)|0,o=Math.imul(m,q),i=i+Math.imul(l,H)|0;var ty=(h+i|0)+((8191&(n=(n=n+Math.imul(l,V)|0)+Math.imul(c,H)|0))<<13)|0;h=((o=o+Math.imul(c,V)|0)+(n>>>13)|0)+(ty>>>26)|0,ty&=0x3ffffff,i=Math.imul(y,K),n=(n=Math.imul(y,q))+Math.imul(v,K)|0,o=Math.imul(v,q),i=i+Math.imul(d,H)|0,n=(n=n+Math.imul(d,V)|0)+Math.imul(m,H)|0,o=o+Math.imul(m,V)|0,i=i+Math.imul(l,z)|0;var tv=(h+i|0)+((8191&(n=(n=n+Math.imul(l,$)|0)+Math.imul(c,z)|0))<<13)|0;h=((o=o+Math.imul(c,$)|0)+(n>>>13)|0)+(tv>>>26)|0,tv&=0x3ffffff,i=Math.imul(w,K),n=(n=Math.imul(w,q))+Math.imul(M,K)|0,o=Math.imul(M,q),i=i+Math.imul(y,H)|0,n=(n=n+Math.imul(y,V)|0)+Math.imul(v,H)|0,o=o+Math.imul(v,V)|0,i=i+Math.imul(d,z)|0,n=(n=n+Math.imul(d,$)|0)+Math.imul(m,z)|0,o=o+Math.imul(m,$)|0,i=i+Math.imul(l,W)|0;var tb=(h+i|0)+((8191&(n=(n=n+Math.imul(l,Y)|0)+Math.imul(c,W)|0))<<13)|0;h=((o=o+Math.imul(c,Y)|0)+(n>>>13)|0)+(tb>>>26)|0,tb&=0x3ffffff,i=Math.imul(x,K),n=(n=Math.imul(x,q))+Math.imul(E,K)|0,o=Math.imul(E,q),i=i+Math.imul(w,H)|0,n=(n=n+Math.imul(w,V)|0)+Math.imul(M,H)|0,o=o+Math.imul(M,V)|0,i=i+Math.imul(y,z)|0,n=(n=n+Math.imul(y,$)|0)+Math.imul(v,z)|0,o=o+Math.imul(v,$)|0,i=i+Math.imul(d,W)|0,n=(n=n+Math.imul(d,Y)|0)+Math.imul(m,W)|0,o=o+Math.imul(m,Y)|0,i=i+Math.imul(l,Q)|0;var tw=(h+i|0)+((8191&(n=(n=n+Math.imul(l,tt)|0)+Math.imul(c,Q)|0))<<13)|0;h=((o=o+Math.imul(c,tt)|0)+(n>>>13)|0)+(tw>>>26)|0,tw&=0x3ffffff,i=Math.imul(C,K),n=(n=Math.imul(C,q))+Math.imul(k,K)|0,o=Math.imul(k,q),i=i+Math.imul(x,H)|0,n=(n=n+Math.imul(x,V)|0)+Math.imul(E,H)|0,o=o+Math.imul(E,V)|0,i=i+Math.imul(w,z)|0,n=(n=n+Math.imul(w,$)|0)+Math.imul(M,z)|0,o=o+Math.imul(M,$)|0,i=i+Math.imul(y,W)|0,n=(n=n+Math.imul(y,Y)|0)+Math.imul(v,W)|0,o=o+Math.imul(v,Y)|0,i=i+Math.imul(d,Q)|0,n=(n=n+Math.imul(d,tt)|0)+Math.imul(m,Q)|0,o=o+Math.imul(m,tt)|0,i=i+Math.imul(l,tr)|0;var tM=(h+i|0)+((8191&(n=(n=n+Math.imul(l,ti)|0)+Math.imul(c,tr)|0))<<13)|0;h=((o=o+Math.imul(c,ti)|0)+(n>>>13)|0)+(tM>>>26)|0,tM&=0x3ffffff,i=Math.imul(A,K),n=(n=Math.imul(A,q))+Math.imul(O,K)|0,o=Math.imul(O,q),i=i+Math.imul(C,H)|0,n=(n=n+Math.imul(C,V)|0)+Math.imul(k,H)|0,o=o+Math.imul(k,V)|0,i=i+Math.imul(x,z)|0,n=(n=n+Math.imul(x,$)|0)+Math.imul(E,z)|0,o=o+Math.imul(E,$)|0,i=i+Math.imul(w,W)|0,n=(n=n+Math.imul(w,Y)|0)+Math.imul(M,W)|0,o=o+Math.imul(M,Y)|0,i=i+Math.imul(y,Q)|0,n=(n=n+Math.imul(y,tt)|0)+Math.imul(v,Q)|0,o=o+Math.imul(v,tt)|0,i=i+Math.imul(d,tr)|0,n=(n=n+Math.imul(d,ti)|0)+Math.imul(m,tr)|0,o=o+Math.imul(m,ti)|0,i=i+Math.imul(l,to)|0;var t_=(h+i|0)+((8191&(n=(n=n+Math.imul(l,ts)|0)+Math.imul(c,to)|0))<<13)|0;h=((o=o+Math.imul(c,ts)|0)+(n>>>13)|0)+(t_>>>26)|0,t_&=0x3ffffff,i=Math.imul(T,K),n=(n=Math.imul(T,q))+Math.imul(P,K)|0,o=Math.imul(P,q),i=i+Math.imul(A,H)|0,n=(n=n+Math.imul(A,V)|0)+Math.imul(O,H)|0,o=o+Math.imul(O,V)|0,i=i+Math.imul(C,z)|0,n=(n=n+Math.imul(C,$)|0)+Math.imul(k,z)|0,o=o+Math.imul(k,$)|0,i=i+Math.imul(x,W)|0,n=(n=n+Math.imul(x,Y)|0)+Math.imul(E,W)|0,o=o+Math.imul(E,Y)|0,i=i+Math.imul(w,Q)|0,n=(n=n+Math.imul(w,tt)|0)+Math.imul(M,Q)|0,o=o+Math.imul(M,tt)|0,i=i+Math.imul(y,tr)|0,n=(n=n+Math.imul(y,ti)|0)+Math.imul(v,tr)|0,o=o+Math.imul(v,ti)|0,i=i+Math.imul(d,to)|0,n=(n=n+Math.imul(d,ts)|0)+Math.imul(m,to)|0,o=o+Math.imul(m,ts)|0,i=i+Math.imul(l,ta)|0;var tx=(h+i|0)+((8191&(n=(n=n+Math.imul(l,th)|0)+Math.imul(c,ta)|0))<<13)|0;h=((o=o+Math.imul(c,th)|0)+(n>>>13)|0)+(tx>>>26)|0,tx&=0x3ffffff,i=Math.imul(I,K),n=(n=Math.imul(I,q))+Math.imul(N,K)|0,o=Math.imul(N,q),i=i+Math.imul(T,H)|0,n=(n=n+Math.imul(T,V)|0)+Math.imul(P,H)|0,o=o+Math.imul(P,V)|0,i=i+Math.imul(A,z)|0,n=(n=n+Math.imul(A,$)|0)+Math.imul(O,z)|0,o=o+Math.imul(O,$)|0,i=i+Math.imul(C,W)|0,n=(n=n+Math.imul(C,Y)|0)+Math.imul(k,W)|0,o=o+Math.imul(k,Y)|0,i=i+Math.imul(x,Q)|0,n=(n=n+Math.imul(x,tt)|0)+Math.imul(E,Q)|0,o=o+Math.imul(E,tt)|0,i=i+Math.imul(w,tr)|0,n=(n=n+Math.imul(w,ti)|0)+Math.imul(M,tr)|0,o=o+Math.imul(M,ti)|0,i=i+Math.imul(y,to)|0,n=(n=n+Math.imul(y,ts)|0)+Math.imul(v,to)|0,o=o+Math.imul(v,ts)|0,i=i+Math.imul(d,ta)|0,n=(n=n+Math.imul(d,th)|0)+Math.imul(m,ta)|0,o=o+Math.imul(m,th)|0,i=i+Math.imul(l,tl)|0;var tE=(h+i|0)+((8191&(n=(n=n+Math.imul(l,tc)|0)+Math.imul(c,tl)|0))<<13)|0;h=((o=o+Math.imul(c,tc)|0)+(n>>>13)|0)+(tE>>>26)|0,tE&=0x3ffffff,i=Math.imul(F,K),n=(n=Math.imul(F,q))+Math.imul(D,K)|0,o=Math.imul(D,q),i=i+Math.imul(I,H)|0,n=(n=n+Math.imul(I,V)|0)+Math.imul(N,H)|0,o=o+Math.imul(N,V)|0,i=i+Math.imul(T,z)|0,n=(n=n+Math.imul(T,$)|0)+Math.imul(P,z)|0,o=o+Math.imul(P,$)|0,i=i+Math.imul(A,W)|0,n=(n=n+Math.imul(A,Y)|0)+Math.imul(O,W)|0,o=o+Math.imul(O,Y)|0,i=i+Math.imul(C,Q)|0,n=(n=n+Math.imul(C,tt)|0)+Math.imul(k,Q)|0,o=o+Math.imul(k,tt)|0,i=i+Math.imul(x,tr)|0,n=(n=n+Math.imul(x,ti)|0)+Math.imul(E,tr)|0,o=o+Math.imul(E,ti)|0,i=i+Math.imul(w,to)|0,n=(n=n+Math.imul(w,ts)|0)+Math.imul(M,to)|0,o=o+Math.imul(M,ts)|0,i=i+Math.imul(y,ta)|0,n=(n=n+Math.imul(y,th)|0)+Math.imul(v,ta)|0,o=o+Math.imul(v,th)|0,i=i+Math.imul(d,tl)|0,n=(n=n+Math.imul(d,tc)|0)+Math.imul(m,tl)|0,o=o+Math.imul(m,tc)|0,i=i+Math.imul(l,td)|0;var tS=(h+i|0)+((8191&(n=(n=n+Math.imul(l,tm)|0)+Math.imul(c,td)|0))<<13)|0;h=((o=o+Math.imul(c,tm)|0)+(n>>>13)|0)+(tS>>>26)|0,tS&=0x3ffffff,i=Math.imul(F,H),n=(n=Math.imul(F,V))+Math.imul(D,H)|0,o=Math.imul(D,V),i=i+Math.imul(I,z)|0,n=(n=n+Math.imul(I,$)|0)+Math.imul(N,z)|0,o=o+Math.imul(N,$)|0,i=i+Math.imul(T,W)|0,n=(n=n+Math.imul(T,Y)|0)+Math.imul(P,W)|0,o=o+Math.imul(P,Y)|0,i=i+Math.imul(A,Q)|0,n=(n=n+Math.imul(A,tt)|0)+Math.imul(O,Q)|0,o=o+Math.imul(O,tt)|0,i=i+Math.imul(C,tr)|0,n=(n=n+Math.imul(C,ti)|0)+Math.imul(k,tr)|0,o=o+Math.imul(k,ti)|0,i=i+Math.imul(x,to)|0,n=(n=n+Math.imul(x,ts)|0)+Math.imul(E,to)|0,o=o+Math.imul(E,ts)|0,i=i+Math.imul(w,ta)|0,n=(n=n+Math.imul(w,th)|0)+Math.imul(M,ta)|0,o=o+Math.imul(M,th)|0,i=i+Math.imul(y,tl)|0,n=(n=n+Math.imul(y,tc)|0)+Math.imul(v,tl)|0,o=o+Math.imul(v,tc)|0,i=i+Math.imul(d,td)|0;var tC=(h+i|0)+((8191&(n=(n=n+Math.imul(d,tm)|0)+Math.imul(m,td)|0))<<13)|0;h=((o=o+Math.imul(m,tm)|0)+(n>>>13)|0)+(tC>>>26)|0,tC&=0x3ffffff,i=Math.imul(F,z),n=(n=Math.imul(F,$))+Math.imul(D,z)|0,o=Math.imul(D,$),i=i+Math.imul(I,W)|0,n=(n=n+Math.imul(I,Y)|0)+Math.imul(N,W)|0,o=o+Math.imul(N,Y)|0,i=i+Math.imul(T,Q)|0,n=(n=n+Math.imul(T,tt)|0)+Math.imul(P,Q)|0,o=o+Math.imul(P,tt)|0,i=i+Math.imul(A,tr)|0,n=(n=n+Math.imul(A,ti)|0)+Math.imul(O,tr)|0,o=o+Math.imul(O,ti)|0,i=i+Math.imul(C,to)|0,n=(n=n+Math.imul(C,ts)|0)+Math.imul(k,to)|0,o=o+Math.imul(k,ts)|0,i=i+Math.imul(x,ta)|0,n=(n=n+Math.imul(x,th)|0)+Math.imul(E,ta)|0,o=o+Math.imul(E,th)|0,i=i+Math.imul(w,tl)|0,n=(n=n+Math.imul(w,tc)|0)+Math.imul(M,tl)|0,o=o+Math.imul(M,tc)|0,i=i+Math.imul(y,td)|0;var tk=(h+i|0)+((8191&(n=(n=n+Math.imul(y,tm)|0)+Math.imul(v,td)|0))<<13)|0;h=((o=o+Math.imul(v,tm)|0)+(n>>>13)|0)+(tk>>>26)|0,tk&=0x3ffffff,i=Math.imul(F,W),n=(n=Math.imul(F,Y))+Math.imul(D,W)|0,o=Math.imul(D,Y),i=i+Math.imul(I,Q)|0,n=(n=n+Math.imul(I,tt)|0)+Math.imul(N,Q)|0,o=o+Math.imul(N,tt)|0,i=i+Math.imul(T,tr)|0,n=(n=n+Math.imul(T,ti)|0)+Math.imul(P,tr)|0,o=o+Math.imul(P,ti)|0,i=i+Math.imul(A,to)|0,n=(n=n+Math.imul(A,ts)|0)+Math.imul(O,to)|0,o=o+Math.imul(O,ts)|0,i=i+Math.imul(C,ta)|0,n=(n=n+Math.imul(C,th)|0)+Math.imul(k,ta)|0,o=o+Math.imul(k,th)|0,i=i+Math.imul(x,tl)|0,n=(n=n+Math.imul(x,tc)|0)+Math.imul(E,tl)|0,o=o+Math.imul(E,tc)|0,i=i+Math.imul(w,td)|0;var tB=(h+i|0)+((8191&(n=(n=n+Math.imul(w,tm)|0)+Math.imul(M,td)|0))<<13)|0;h=((o=o+Math.imul(M,tm)|0)+(n>>>13)|0)+(tB>>>26)|0,tB&=0x3ffffff,i=Math.imul(F,Q),n=(n=Math.imul(F,tt))+Math.imul(D,Q)|0,o=Math.imul(D,tt),i=i+Math.imul(I,tr)|0,n=(n=n+Math.imul(I,ti)|0)+Math.imul(N,tr)|0,o=o+Math.imul(N,ti)|0,i=i+Math.imul(T,to)|0,n=(n=n+Math.imul(T,ts)|0)+Math.imul(P,to)|0,o=o+Math.imul(P,ts)|0,i=i+Math.imul(A,ta)|0,n=(n=n+Math.imul(A,th)|0)+Math.imul(O,ta)|0,o=o+Math.imul(O,th)|0,i=i+Math.imul(C,tl)|0,n=(n=n+Math.imul(C,tc)|0)+Math.imul(k,tl)|0,o=o+Math.imul(k,tc)|0,i=i+Math.imul(x,td)|0;var tA=(h+i|0)+((8191&(n=(n=n+Math.imul(x,tm)|0)+Math.imul(E,td)|0))<<13)|0;h=((o=o+Math.imul(E,tm)|0)+(n>>>13)|0)+(tA>>>26)|0,tA&=0x3ffffff,i=Math.imul(F,tr),n=(n=Math.imul(F,ti))+Math.imul(D,tr)|0,o=Math.imul(D,ti),i=i+Math.imul(I,to)|0,n=(n=n+Math.imul(I,ts)|0)+Math.imul(N,to)|0,o=o+Math.imul(N,ts)|0,i=i+Math.imul(T,ta)|0,n=(n=n+Math.imul(T,th)|0)+Math.imul(P,ta)|0,o=o+Math.imul(P,th)|0,i=i+Math.imul(A,tl)|0,n=(n=n+Math.imul(A,tc)|0)+Math.imul(O,tl)|0,o=o+Math.imul(O,tc)|0,i=i+Math.imul(C,td)|0;var tO=(h+i|0)+((8191&(n=(n=n+Math.imul(C,tm)|0)+Math.imul(k,td)|0))<<13)|0;h=((o=o+Math.imul(k,tm)|0)+(n>>>13)|0)+(tO>>>26)|0,tO&=0x3ffffff,i=Math.imul(F,to),n=(n=Math.imul(F,ts))+Math.imul(D,to)|0,o=Math.imul(D,ts),i=i+Math.imul(I,ta)|0,n=(n=n+Math.imul(I,th)|0)+Math.imul(N,ta)|0,o=o+Math.imul(N,th)|0,i=i+Math.imul(T,tl)|0,n=(n=n+Math.imul(T,tc)|0)+Math.imul(P,tl)|0,o=o+Math.imul(P,tc)|0,i=i+Math.imul(A,td)|0;var tj=(h+i|0)+((8191&(n=(n=n+Math.imul(A,tm)|0)+Math.imul(O,td)|0))<<13)|0;h=((o=o+Math.imul(O,tm)|0)+(n>>>13)|0)+(tj>>>26)|0,tj&=0x3ffffff,i=Math.imul(F,ta),n=(n=Math.imul(F,th))+Math.imul(D,ta)|0,o=Math.imul(D,th),i=i+Math.imul(I,tl)|0,n=(n=n+Math.imul(I,tc)|0)+Math.imul(N,tl)|0,o=o+Math.imul(N,tc)|0,i=i+Math.imul(T,td)|0;var tT=(h+i|0)+((8191&(n=(n=n+Math.imul(T,tm)|0)+Math.imul(P,td)|0))<<13)|0;h=((o=o+Math.imul(P,tm)|0)+(n>>>13)|0)+(tT>>>26)|0,tT&=0x3ffffff,i=Math.imul(F,tl),n=(n=Math.imul(F,tc))+Math.imul(D,tl)|0,o=Math.imul(D,tc),i=i+Math.imul(I,td)|0;var tP=(h+i|0)+((8191&(n=(n=n+Math.imul(I,tm)|0)+Math.imul(N,td)|0))<<13)|0;h=((o=o+Math.imul(N,tm)|0)+(n>>>13)|0)+(tP>>>26)|0,tP&=0x3ffffff,i=Math.imul(F,td);var tR=(h+i|0)+((8191&(n=(n=Math.imul(F,tm))+Math.imul(D,td)|0))<<13)|0;return h=((o=Math.imul(D,tm))+(n>>>13)|0)+(tR>>>26)|0,tR&=0x3ffffff,a[0]=tg,a[1]=ty,a[2]=tv,a[3]=tb,a[4]=tw,a[5]=tM,a[6]=t_,a[7]=tx,a[8]=tE,a[9]=tS,a[10]=tC,a[11]=tk,a[12]=tB,a[13]=tA,a[14]=tO,a[15]=tj,a[16]=tT,a[17]=tP,a[18]=tR,0!==h&&(a[19]=h,r.length++),r};function m(t,e,r){return new g().mulp(t,e,r)}function g(t,e){this.x=t,this.y=e}Math.imul||(d=p),o.prototype.mulTo=function(t,e){var r,i=this.length+t.length;return 10===this.length&&10===t.length?d(this,t,e):i<63?p(this,t,e):i<1024?function(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var i=0,n=0,o=0;o<r.length-1;o++){var s=n;n=0;for(var u=0x3ffffff&i,a=Math.min(o,e.length-1),h=Math.max(0,o-t.length+1);h<=a;h++){var f=o-h,l=(0|t.words[f])*(0|e.words[h]),c=0x3ffffff&l;s=s+(l/0x4000000|0)|0,u=0x3ffffff&(c=c+u|0),n+=(s=s+(c>>>26)|0)>>>26,s&=0x3ffffff}r.words[o]=u,i=s,s=n}return 0!==i?r.words[o]=i:r.length--,r.strip()}(this,t,e):m(this,t,e)},g.prototype.makeRBT=function(t){for(var e=Array(t),r=o.prototype._countBits(t)-1,i=0;i<t;i++)e[i]=this.revBin(i,r,t);return e},g.prototype.revBin=function(t,e,r){if(0===t||t===r-1)return t;for(var i=0,n=0;n<e;n++)i|=(1&t)<<e-n-1,t>>=1;return i},g.prototype.permute=function(t,e,r,i,n,o){for(var s=0;s<o;s++)i[s]=e[t[s]],n[s]=r[t[s]]},g.prototype.transform=function(t,e,r,i,n,o){this.permute(o,t,e,r,i,n);for(var s=1;s<n;s<<=1)for(var u=s<<1,a=Math.cos(2*Math.PI/u),h=Math.sin(2*Math.PI/u),f=0;f<n;f+=u)for(var l=a,c=h,p=0;p<s;p++){var d=r[f+p],m=i[f+p],g=r[f+p+s],y=i[f+p+s],v=l*g-c*y;y=l*y+c*g,g=v,r[f+p]=d+g,i[f+p]=m+y,r[f+p+s]=d-g,i[f+p+s]=m-y,p!==u&&(v=a*l-h*c,c=a*c+h*l,l=v)}},g.prototype.guessLen13b=function(t,e){var r=1|Math.max(e,t),i=1&r,n=0;for(r=r/2|0;r;r>>>=1)n++;return 1<<n+1+i},g.prototype.conjugate=function(t,e,r){if(!(r<=1))for(var i=0;i<r/2;i++){var n=t[i];t[i]=t[r-i-1],t[r-i-1]=n,n=e[i],e[i]=-e[r-i-1],e[r-i-1]=-n}},g.prototype.normalize13b=function(t,e){for(var r=0,i=0;i<e/2;i++){var n=8192*Math.round(t[2*i+1]/e)+Math.round(t[2*i]/e)+r;t[i]=0x3ffffff&n,r=n<0x4000000?0:n/0x4000000|0}return t},g.prototype.convert13b=function(t,e,r,n){for(var o=0,s=0;s<e;s++)o+=0|t[s],r[2*s]=8191&o,o>>>=13,r[2*s+1]=8191&o,o>>>=13;for(s=2*e;s<n;++s)r[s]=0;i(0===o),i((-8192&o)==0)},g.prototype.stub=function(t){for(var e=Array(t),r=0;r<t;r++)e[r]=0;return e},g.prototype.mulp=function(t,e,r){var i=2*this.guessLen13b(t.length,e.length),n=this.makeRBT(i),o=this.stub(i),s=Array(i),u=Array(i),a=Array(i),h=Array(i),f=Array(i),l=Array(i),c=r.words;c.length=i,this.convert13b(t.words,t.length,s,i),this.convert13b(e.words,e.length,h,i),this.transform(s,o,u,a,i,n),this.transform(h,o,f,l,i,n);for(var p=0;p<i;p++){var d=u[p]*f[p]-a[p]*l[p];a[p]=u[p]*l[p]+a[p]*f[p],u[p]=d}return this.conjugate(u,a,i),this.transform(u,a,c,o,i,n),this.conjugate(c,o,i),this.normalize13b(c,i),r.negative=t.negative^e.negative,r.length=t.length+e.length,r.strip()},o.prototype.mul=function(t){var e=new o(null);return e.words=Array(this.length+t.length),this.mulTo(t,e)},o.prototype.mulf=function(t){var e=new o(null);return e.words=Array(this.length+t.length),m(this,t,e)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){i("number"==typeof t),i(t<0x4000000);for(var e=0,r=0;r<this.length;r++){var n=(0|this.words[r])*t,o=(0x3ffffff&n)+(0x3ffffff&e);e>>=26,e+=(n/0x4000000|0)+(o>>>26),this.words[r]=0x3ffffff&o}return 0!==e&&(this.words[r]=e,this.length++),this.length=0===t?1:this.length,this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var e=function(t){for(var e=Array(t.bitLength()),r=0;r<e.length;r++){var i=r/26|0,n=r%26;e[r]=(t.words[i]&1<<n)>>>n}return e}(t);if(0===e.length)return new o(1);for(var r=this,i=0;i<e.length&&0===e[i];i++,r=r.sqr());if(++i<e.length)for(var n=r.sqr();i<e.length;i++,n=n.sqr())0!==e[i]&&(r=r.mul(n));return r},o.prototype.iushln=function(t){i("number"==typeof t&&t>=0);var e,r=t%26,n=(t-r)/26,o=0x3ffffff>>>26-r<<26-r;if(0!==r){var s=0;for(e=0;e<this.length;e++){var u=this.words[e]&o,a=(0|this.words[e])-u<<r;this.words[e]=a|s,s=u>>>26-r}s&&(this.words[e]=s,this.length++)}if(0!==n){for(e=this.length-1;e>=0;e--)this.words[e+n]=this.words[e];for(e=0;e<n;e++)this.words[e]=0;this.length+=n}return this.strip()},o.prototype.ishln=function(t){return i(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,e,r){i("number"==typeof t&&t>=0);var n=e?(e-e%26)/26:0,o=t%26,s=Math.min((t-o)/26,this.length),u=0x3ffffff^0x3ffffff>>>o<<o;if(n-=s,n=Math.max(0,n),r){for(var a=0;a<s;a++)r.words[a]=this.words[a];r.length=s}if(0===s);else if(this.length>s)for(this.length-=s,a=0;a<this.length;a++)this.words[a]=this.words[a+s];else this.words[0]=0,this.length=1;var h=0;for(a=this.length-1;a>=0&&(0!==h||a>=n);a--){var f=0|this.words[a];this.words[a]=h<<26-o|f>>>o,h=f&u}return r&&0!==h&&(r.words[r.length++]=h),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,e,r){return i(0===this.negative),this.iushrn(t,e,r)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){i("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;return!(this.length<=r)&&!!(this.words[r]&1<<e)},o.prototype.imaskn=function(t){i("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;return(i(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)?this:(0!==e&&r++,this.length=Math.min(r,this.length),0!==e&&(this.words[this.length-1]&=0x3ffffff^0x3ffffff>>>e<<e),this.strip())},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return(i("number"==typeof t),i(t<0x4000000),t<0)?this.isubn(-t):0!==this.negative?(1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0):(this.negative=0,this.isubn(t),this.negative=1),this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=0x4000000;e++)this.words[e]-=0x4000000,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},o.prototype.isubn=function(t){if(i("number"==typeof t),i(t<0x4000000),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=0x4000000,this.words[e+1]-=1;return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,e,r){var n,o,s=t.length+r;this._expand(s);var u=0;for(n=0;n<t.length;n++){o=(0|this.words[n+r])+u;var a=(0|t.words[n])*e;o-=0x3ffffff&a,u=(o>>26)-(a/0x4000000|0),this.words[n+r]=0x3ffffff&o}for(;n<this.length-r;n++)u=(o=(0|this.words[n+r])+u)>>26,this.words[n+r]=0x3ffffff&o;if(0===u)return this.strip();for(i(-1===u),u=0,n=0;n<this.length;n++)u=(o=-(0|this.words[n])+u)>>26,this.words[n]=0x3ffffff&o;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,e){var r,i=this.length-t.length,n=this.clone(),s=t,u=0|s.words[s.length-1];0!=(i=26-this._countBits(u))&&(s=s.ushln(i),n.iushln(i),u=0|s.words[s.length-1]);var a=n.length-s.length;if("mod"!==e){(r=new o(null)).length=a+1,r.words=Array(r.length);for(var h=0;h<r.length;h++)r.words[h]=0}var f=n.clone()._ishlnsubmul(s,1,a);0===f.negative&&(n=f,r&&(r.words[a]=1));for(var l=a-1;l>=0;l--){var c=(0|n.words[s.length+l])*0x4000000+(0|n.words[s.length+l-1]);for(c=Math.min(c/u|0,0x3ffffff),n._ishlnsubmul(s,c,l);0!==n.negative;)c--,n.negative=0,n._ishlnsubmul(s,1,l),n.isZero()||(n.negative^=1);r&&(r.words[l]=c)}return r&&r.strip(),n.strip(),"div"!==e&&0!==i&&n.iushrn(i),{div:r||null,mod:n}},o.prototype.divmod=function(t,e,r){var n,s,u;return(i(!t.isZero()),this.isZero())?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(u=this.neg().divmod(t,e),"mod"!==e&&(n=u.div.neg()),"div"!==e&&(s=u.mod.neg(),r&&0!==s.negative&&s.iadd(t)),{div:n,mod:s}):0===this.negative&&0!==t.negative?(u=this.divmod(t.neg(),e),"mod"!==e&&(n=u.div.neg()),{div:n,mod:u.mod}):(this.negative&t.negative)!=0?(u=this.neg().divmod(t.neg(),e),"div"!==e&&(s=u.mod.neg(),r&&0!==s.negative&&s.isub(t)),{div:u.div,mod:s}):t.length>this.length||0>this.cmp(t)?{div:new o(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,e)},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,i=t.ushrn(1),n=t.andln(1),o=r.cmp(i);return o<0||1===n&&0===o?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},o.prototype.modn=function(t){i(t<=0x3ffffff);for(var e=0x4000000%t,r=0,n=this.length-1;n>=0;n--)r=(e*r+(0|this.words[n]))%t;return r},o.prototype.idivn=function(t){i(t<=0x3ffffff);for(var e=0,r=this.length-1;r>=0;r--){var n=(0|this.words[r])+0x4000000*e;this.words[r]=n/t|0,e=n%t}return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){i(0===t.negative),i(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var n=new o(1),s=new o(0),u=new o(0),a=new o(1),h=0;e.isEven()&&r.isEven();)e.iushrn(1),r.iushrn(1),++h;for(var f=r.clone(),l=e.clone();!e.isZero();){for(var c=0,p=1;(e.words[0]&p)==0&&c<26;++c,p<<=1);if(c>0)for(e.iushrn(c);c-- >0;)(n.isOdd()||s.isOdd())&&(n.iadd(f),s.isub(l)),n.iushrn(1),s.iushrn(1);for(var d=0,m=1;(r.words[0]&m)==0&&d<26;++d,m<<=1);if(d>0)for(r.iushrn(d);d-- >0;)(u.isOdd()||a.isOdd())&&(u.iadd(f),a.isub(l)),u.iushrn(1),a.iushrn(1);e.cmp(r)>=0?(e.isub(r),n.isub(u),s.isub(a)):(r.isub(e),u.isub(n),a.isub(s))}return{a:u,b:a,gcd:r.iushln(h)}},o.prototype._invmp=function(t){i(0===t.negative),i(!t.isZero());var e,r=this,n=t.clone();r=0!==r.negative?r.umod(t):r.clone();for(var s=new o(1),u=new o(0),a=n.clone();r.cmpn(1)>0&&n.cmpn(1)>0;){for(var h=0,f=1;(r.words[0]&f)==0&&h<26;++h,f<<=1);if(h>0)for(r.iushrn(h);h-- >0;)s.isOdd()&&s.iadd(a),s.iushrn(1);for(var l=0,c=1;(n.words[0]&c)==0&&l<26;++l,c<<=1);if(l>0)for(n.iushrn(l);l-- >0;)u.isOdd()&&u.iadd(a),u.iushrn(1);r.cmp(n)>=0?(r.isub(n),s.isub(u)):(n.isub(r),u.isub(s))}return 0>(e=0===r.cmpn(1)?s:u).cmpn(0)&&e.iadd(t),e},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var i=0;e.isEven()&&r.isEven();i++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var n=e.cmp(r);if(n<0){var o=e;e=r,r=o}else if(0===n||0===r.cmpn(1))break;e.isub(r)}return r.iushln(i)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return(1&this.words[0])==0},o.prototype.isOdd=function(){return(1&this.words[0])==1},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){i("number"==typeof t);var e=t%26,r=(t-e)/26,n=1<<e;if(this.length<=r)return this._expand(r+1),this.words[r]|=n,this;for(var o=n,s=r;0!==o&&s<this.length;s++){var u=0|this.words[s];u+=o,o=u>>>26,u&=0x3ffffff,this.words[s]=u}return 0!==o&&(this.words[s]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var e,r=t<0;if(0!==this.negative&&!r)return -1;if(0===this.negative&&r)return 1;if(this.strip(),this.length>1)e=1;else{r&&(t=-t),i(t<=0x3ffffff,"Number is too big");var n=0|this.words[0];e=n===t?0:n<t?-1:1}return 0!==this.negative?0|-e:e},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return -1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return -1;for(var e=0,r=this.length-1;r>=0;r--){var i=0|this.words[r],n=0|t.words[r];if(i!==n){i<n?e=-1:i>n&&(e=1);break}}return e},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return -1===this.cmpn(t)},o.prototype.lt=function(t){return -1===this.cmp(t)},o.prototype.lten=function(t){return 0>=this.cmpn(t)},o.prototype.lte=function(t){return 0>=this.cmp(t)},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new x(t)},o.prototype.toRed=function(t){return i(!this.red,"Already a number in reduction context"),i(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return i(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return i(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return i(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return i(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return i(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return i(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return i(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return i(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return i(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return i(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return i(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return i(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return i(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return i(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return i(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var y={k256:null,p224:null,p192:null,p25519:null};function v(t,e){this.name=t,this.p=new o(e,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function b(){v.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function w(){v.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function M(){v.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function _(){v.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function x(t){if("string"==typeof t){var e=o._prime(t);this.m=e.p,this.prime=e}else i(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function E(t){x.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}v.prototype._tmp=function(){var t=new o(null);return t.words=Array(Math.ceil(this.n/13)),t},v.prototype.ireduce=function(t){var e,r=t;do this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength();while(e>this.n);var i=e<this.n?-1:r.ucmp(this.p);return 0===i?(r.words[0]=0,r.length=1):i>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},v.prototype.split=function(t,e){t.iushrn(this.n,0,e)},v.prototype.imulK=function(t){return t.imul(this.k)},n(b,v),b.prototype.split=function(t,e){for(var r=Math.min(t.length,9),i=0;i<r;i++)e.words[i]=t.words[i];if(e.length=r,t.length<=9){t.words[0]=0,t.length=1;return}var n=t.words[9];for(i=10,e.words[e.length++]=4194303&n;i<t.length;i++){var o=0|t.words[i];t.words[i-10]=(4194303&o)<<4|n>>>22,n=o}n>>>=22,t.words[i-10]=n,0===n&&t.length>10?t.length-=10:t.length-=9},b.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var i=0|t.words[r];e+=977*i,t.words[r]=0x3ffffff&e,e=64*i+(e/0x4000000|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},n(w,v),n(M,v),n(_,v),_.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var i=(0|t.words[r])*19+e,n=0x3ffffff&i;i>>>=26,t.words[r]=n,e=i}return 0!==e&&(t.words[t.length++]=e),t},o._prime=function(t){var e;if(y[t])return y[t];if("k256"===t)e=new b;else if("p224"===t)e=new w;else if("p192"===t)e=new M;else if("p25519"===t)e=new _;else throw Error("Unknown prime "+t);return y[t]=e,e},x.prototype._verify1=function(t){i(0===t.negative,"red works only with positives"),i(t.red,"red works only with red numbers")},x.prototype._verify2=function(t,e){i((t.negative|e.negative)==0,"red works only with positives"),i(t.red&&t.red===e.red,"red works only with red numbers")},x.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},x.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},x.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},x.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},x.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return 0>r.cmpn(0)&&r.iadd(this.m),r._forceRed(this)},x.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return 0>r.cmpn(0)&&r.iadd(this.m),r},x.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},x.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},x.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},x.prototype.isqr=function(t){return this.imul(t,t.clone())},x.prototype.sqr=function(t){return this.mul(t,t)},x.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(i(e%2==1),3===e){var r=this.m.add(new o(1)).iushrn(2);return this.pow(t,r)}for(var n=this.m.subn(1),s=0;!n.isZero()&&0===n.andln(1);)s++,n.iushrn(1);i(!n.isZero());var u=new o(1).toRed(this),a=u.redNeg(),h=this.m.subn(1).iushrn(1),f=this.m.bitLength();for(f=new o(2*f*f).toRed(this);0!==this.pow(f,h).cmp(a);)f.redIAdd(a);for(var l=this.pow(f,n),c=this.pow(t,n.addn(1).iushrn(1)),p=this.pow(t,n),d=s;0!==p.cmp(u);){for(var m=p,g=0;0!==m.cmp(u);g++)m=m.redSqr();i(g<d);var y=this.pow(l,new o(1).iushln(d-g-1));c=c.redMul(y),l=y.redSqr(),p=p.redMul(l),d=g}return c},x.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},x.prototype.pow=function(t,e){if(e.isZero())return new o(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=Array(16);r[0]=new o(1).toRed(this),r[1]=t;for(var i=2;i<r.length;i++)r[i]=this.mul(r[i-1],t);var n=r[0],s=0,u=0,a=e.bitLength()%26;for(0===a&&(a=26),i=e.length-1;i>=0;i--){for(var h=e.words[i],f=a-1;f>=0;f--){var l=h>>f&1;if(n!==r[0]&&(n=this.sqr(n)),0===l&&0===s){u=0;continue}s<<=1,s|=l,(4==++u||0===i&&0===f)&&(n=this.mul(n,r[s]),u=0,s=0)}a=26}return n},x.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},x.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},o.mont=function(t){return new E(t)},n(E,x),E.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},E.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},E.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),o=n;return n.cmp(this.m)>=0?o=n.isub(this.m):0>n.cmpn(0)&&(o=n.iadd(this.m)),o._forceRed(this)},E.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new o(0)._forceRed(this);var r=t.mul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),s=n;return n.cmp(this.m)>=0?s=n.isub(this.m):0>n.cmpn(0)&&(s=n.iadd(this.m)),s._forceRed(this)},E.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t=r.nmd(t),this)},40944:(t,e,r)=>{t.exports=function(t){function e(t){let r,n,o,s=null;function u(...t){if(!u.enabled)return;let i=Number(new Date);u.diff=i-(r||i),u.prev=r,u.curr=i,r=i,t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");let n=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(r,i)=>{if("%%"===r)return"%";n++;let o=e.formatters[i];if("function"==typeof o){let e=t[n];r=o.call(u,e),t.splice(n,1),n--}return r}),e.formatArgs.call(u,t),(u.log||e.log).apply(u,t)}return u.namespace=t,u.useColors=e.useColors(),u.color=e.selectColor(t),u.extend=i,u.destroy=e.destroy,Object.defineProperty(u,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(n!==e.namespaces&&(n=e.namespaces,o=e.enabled(t)),o),set:t=>{s=t}}),"function"==typeof e.init&&e.init(u),u}function i(t,r){let i=e(this.namespace+(void 0===r?":":r)+t);return i.log=this.log,i}function n(t,e){let r=0,i=0,n=-1,o=0;for(;r<t.length;)if(i<e.length&&(e[i]===t[r]||"*"===e[i]))"*"===e[i]?(n=i,o=r):r++,i++;else{if(-1===n)return!1;i=n+1,r=++o}for(;i<e.length&&"*"===e[i];)i++;return i===e.length}return e.debug=e,e.default=e,e.coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){let t=[...e.names,...e.skips.map(t=>"-"+t)].join(",");return e.enable(""),t},e.enable=function(t){for(let r of(e.save(t),e.namespaces=t,e.names=[],e.skips=[],("string"==typeof t?t:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===r[0]?e.skips.push(r.slice(1)):e.names.push(r)},e.enabled=function(t){for(let r of e.skips)if(n(t,r))return!1;for(let r of e.names)if(n(t,r))return!0;return!1},e.humanize=r(90979),e.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach(r=>{e[r]=t[r]}),e.names=[],e.skips=[],e.formatters={},e.selectColor=function(t){let r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e)|0;return e.colors[Math.abs(r)%e.colors.length]},e.enable(e.load()),e}},42425:(t,e,r)=>{let i=r(83997),n=r(28354);e.init=function(t){t.inspectOpts={};let r=Object.keys(e.inspectOpts);for(let i=0;i<r.length;i++)t.inspectOpts[r[i]]=e.inspectOpts[r[i]]},e.log=function(...t){return process.stderr.write(n.formatWithOptions(e.inspectOpts,...t)+"\n")},e.formatArgs=function(r){let{namespace:i,useColors:n}=this;if(n){let e=this.color,n="\x1b[3"+(e<8?e:"8;5;"+e),o=`  ${n};1m${i} \u001B[0m`;r[0]=o+r[0].split("\n").join("\n"+o),r.push(n+"m+"+t.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(e.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+r[0]},e.save=function(t){t?process.env.DEBUG=t:delete process.env.DEBUG},e.load=function(){return process.env.DEBUG},e.useColors=function(){return"colors"in e.inspectOpts?!!e.inspectOpts.colors:i.isatty(process.stderr.fd)},e.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),e.colors=[6,2,3,4,5,1];try{let t=r(69732);t&&(t.stderr||t).level>=2&&(e.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(t){}e.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let r=e.substring(6).toLowerCase().replace(/_([a-z])/g,(t,e)=>e.toUpperCase()),i=process.env[e];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),t[r]=i,t},{}),t.exports=r(40944)(e);let{formatters:o}=t.exports;o.o=function(t){return this.inspectOpts.colors=this.useColors,n.inspect(t,this.inspectOpts).split("\n").map(t=>t.trim()).join(" ")},o.O=function(t){return this.inspectOpts.colors=this.useColors,n.inspect(t,this.inspectOpts)}},43068:(t,e)=>{"use strict";function r(t){let e={};return Object.keys(t).forEach(function(r){(0|r)==r&&(r|=0),e[t[r]]=r}),e}e.tagClass={0:"universal",1:"application",2:"context",3:"private"},e.tagClassByName=r(e.tagClass),e.tag={0:"end",1:"bool",2:"int",3:"bitstr",4:"octstr",5:"null_",6:"objid",7:"objDesc",8:"external",9:"real",10:"enum",11:"embed",12:"utf8str",13:"relativeOid",16:"seq",17:"set",18:"numstr",19:"printstr",20:"t61str",21:"videostr",22:"ia5str",23:"utctime",24:"gentime",25:"graphstr",26:"iso646str",27:"genstr",28:"unistr",29:"charstr",30:"bmpstr"},e.tagByName=r(e.tag)},44240:(t,e,r)=>{"use strict";let i=r(85849),n=r(88409).Buffer,o=r(63263),s=r(43068);function u(t){this.enc="der",this.name=t.name,this.entity=t,this.tree=new a,this.tree._init(t.body)}function a(t){o.call(this,"der",t)}function h(t){return t<10?"0"+t:t}t.exports=u,u.prototype.encode=function(t,e){return this.tree._encode(t,e).join()},i(a,o),a.prototype._encodeComposite=function(t,e,r,i){let o=function(t,e,r,i){let n;if("seqof"===t?t="seq":"setof"===t&&(t="set"),s.tagByName.hasOwnProperty(t))n=s.tagByName[t];else{if("number"!=typeof t||(0|t)!==t)return i.error("Unknown tag: "+t);n=t}return n>=31?i.error("Multi-octet tag encoding unsupported"):(e||(n|=32),n|=s.tagClassByName[r||"universal"]<<6)}(t,e,r,this.reporter);if(i.length<128){let t=n.alloc(2);return t[0]=o,t[1]=i.length,this._createEncoderBuffer([t,i])}let u=1;for(let t=i.length;t>=256;t>>=8)u++;let a=n.alloc(2+u);a[0]=o,a[1]=128|u;for(let t=1+u,e=i.length;e>0;t--,e>>=8)a[t]=255&e;return this._createEncoderBuffer([a,i])},a.prototype._encodeStr=function(t,e){if("bitstr"===e)return this._createEncoderBuffer([0|t.unused,t.data]);if("bmpstr"===e){let e=n.alloc(2*t.length);for(let r=0;r<t.length;r++)e.writeUInt16BE(t.charCodeAt(r),2*r);return this._createEncoderBuffer(e)}if("numstr"===e)return this._isNumstr(t)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: numstr supports only digits and space");if("printstr"===e)return this._isPrintstr(t)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: printstr supports only latin upper and lower case letters, digits, space, apostrophe, left and rigth parenthesis, plus sign, comma, hyphen, dot, slash, colon, equal sign, question mark");if(/str$/.test(e))return this._createEncoderBuffer(t);else if("objDesc"===e)return this._createEncoderBuffer(t);else return this.reporter.error("Encoding of string type: "+e+" unsupported")},a.prototype._encodeObjid=function(t,e,r){if("string"==typeof t){if(!e)return this.reporter.error("string objid given, but no values map found");if(!e.hasOwnProperty(t))return this.reporter.error("objid not found in values map");t=e[t].split(/[\s.]+/g);for(let e=0;e<t.length;e++)t[e]|=0}else if(Array.isArray(t)){t=t.slice();for(let e=0;e<t.length;e++)t[e]|=0}if(!Array.isArray(t))return this.reporter.error("objid() should be either array or string, got: "+JSON.stringify(t));if(!r){if(t[1]>=40)return this.reporter.error("Second objid identifier OOB");t.splice(0,2,40*t[0]+t[1])}let i=0;for(let e=0;e<t.length;e++){let r=t[e];for(i++;r>=128;r>>=7)i++}let o=n.alloc(i),s=o.length-1;for(let e=t.length-1;e>=0;e--){let r=t[e];for(o[s--]=127&r;(r>>=7)>0;)o[s--]=128|127&r}return this._createEncoderBuffer(o)},a.prototype._encodeTime=function(t,e){let r,i=new Date(t);return"gentime"===e?r=[h(i.getUTCFullYear()),h(i.getUTCMonth()+1),h(i.getUTCDate()),h(i.getUTCHours()),h(i.getUTCMinutes()),h(i.getUTCSeconds()),"Z"].join(""):"utctime"===e?r=[h(i.getUTCFullYear()%100),h(i.getUTCMonth()+1),h(i.getUTCDate()),h(i.getUTCHours()),h(i.getUTCMinutes()),h(i.getUTCSeconds()),"Z"].join(""):this.reporter.error("Encoding "+e+" time is not supported yet"),this._encodeStr(r,"octstr")},a.prototype._encodeNull=function(){return this._createEncoderBuffer("")},a.prototype._encodeInt=function(t,e){if("string"==typeof t){if(!e)return this.reporter.error("String int or enum given, but no values map");if(!e.hasOwnProperty(t))return this.reporter.error("Values map doesn't contain: "+JSON.stringify(t));t=e[t]}if("number"!=typeof t&&!n.isBuffer(t)){let e=t.toArray();!t.sign&&128&e[0]&&e.unshift(0),t=n.from(e)}if(n.isBuffer(t)){let e=t.length;0===t.length&&e++;let r=n.alloc(e);return t.copy(r),0===t.length&&(r[0]=0),this._createEncoderBuffer(r)}if(t<128)return this._createEncoderBuffer(t);if(t<256)return this._createEncoderBuffer([0,t]);let r=1;for(let e=t;e>=256;e>>=8)r++;let i=Array(r);for(let e=i.length-1;e>=0;e--)i[e]=255&t,t>>=8;return 128&i[0]&&i.unshift(0),this._createEncoderBuffer(n.from(i))},a.prototype._encodeBool=function(t){return this._createEncoderBuffer(255*!!t)},a.prototype._use=function(t,e){return"function"==typeof t&&(t=t(e)),t._getEncoder("der").tree},a.prototype._skipDefault=function(t,e,r){let i,n=this._baseState;if(null===n.default)return!1;let o=t.join();if(void 0===n.defaultBuffer&&(n.defaultBuffer=this._encodeValue(n.default,e,r).join()),o.length!==n.defaultBuffer.length)return!1;for(i=0;i<o.length;i++)if(o[i]!==n.defaultBuffer[i])return!1;return!0}},46225:(t,e,r)=>{e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let i=0,n=0;e[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(i++,"%c"===t&&(n=i))}),e.splice(n,0,r)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch(t){}},e.load=function(){let t;try{t=e.storage.getItem("debug")||e.storage.getItem("DEBUG")}catch(t){}return!t&&"undefined"!=typeof process&&"env"in process&&(t=process.env.DEBUG),t},e.useColors=function(){let t;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},e.storage=function(){try{return localStorage}catch(t){}}(),e.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=r(40944)(e);let{formatters:i}=t.exports;i.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}},57592:(t,e,r)=>{var i=r(86417).Buffer,n=r(27910);function o(t){if(this.buffer=null,this.writable=!0,this.readable=!0,!t)return this.buffer=i.alloc(0),this;if("function"==typeof t.pipe)return this.buffer=i.alloc(0),t.pipe(this),this;if(t.length||"object"==typeof t)return this.buffer=t,this.writable=!1,process.nextTick((function(){this.emit("end",t),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof t+")")}r(28354).inherits(o,n),o.prototype.write=function(t){this.buffer=i.concat([this.buffer,i.from(t)]),this.emit("data",t)},o.prototype.end=function(t){t&&this.write(t),this.emit("end",t),this.emit("close"),this.writable=!1,this.readable=!1},t.exports=o},61090:(t,e,r)=>{"use strict";var i=r(86417).Buffer,n=r(38663);function o(t){if(i.isBuffer(t))return t;if("string"==typeof t)return i.from(t,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function s(t,e,r){for(var i=0;e+i<r&&0===t[e+i];)++i;return t[e+i]>=128&&--i,i}t.exports={derToJose:function(t,e){t=o(t);var r=n(e),s=r+1,u=t.length,a=0;if(48!==t[a++])throw Error('Could not find expected "seq"');var h=t[a++];if(129===h&&(h=t[a++]),u-a<h)throw Error('"seq" specified length of "'+h+'", only "'+(u-a)+'" remaining');if(2!==t[a++])throw Error('Could not find expected "int" for "r"');var f=t[a++];if(u-a-2<f)throw Error('"r" specified length of "'+f+'", only "'+(u-a-2)+'" available');if(s<f)throw Error('"r" specified length of "'+f+'", max of "'+s+'" is acceptable');var l=a;if(a+=f,2!==t[a++])throw Error('Could not find expected "int" for "s"');var c=t[a++];if(u-a!==c)throw Error('"s" specified length of "'+c+'", expected "'+(u-a)+'"');if(s<c)throw Error('"s" specified length of "'+c+'", max of "'+s+'" is acceptable');var p=a;if((a+=c)!==u)throw Error('Expected to consume entire buffer, but "'+(u-a)+'" bytes remain');var d=r-f,m=r-c,g=i.allocUnsafe(d+f+m+c);for(a=0;a<d;++a)g[a]=0;t.copy(g,a,l+Math.max(-d,0),l+f),a=r;for(var y=a;a<y+m;++a)g[a]=0;return t.copy(g,a,p+Math.max(-m,0),p+c),g=(g=g.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(t,e){t=o(t);var r=n(e),u=t.length;if(u!==2*r)throw TypeError('"'+e+'" signatures must be "'+2*r+'" bytes, saw "'+u+'"');var a=s(t,0,r),h=s(t,r,t.length),f=r-a,l=r-h,c=2+f+1+1+l,p=c<128,d=i.allocUnsafe((p?2:3)+c),m=0;return d[m++]=48,p?d[m++]=c:(d[m++]=129,d[m++]=255&c),d[m++]=2,d[m++]=f,a<0?(d[m++]=0,m+=t.copy(d,m,0,r)):m+=t.copy(d,m,a,r),d[m++]=2,d[m++]=l,h<0?(d[m++]=0,t.copy(d,m,r)):t.copy(d,m,r+h),d}}},61460:(t,e,r)=>{"use strict";let i=r(85849),n=r(40016),o=r(91805).t,s=r(63263),u=r(43068);function a(t){this.enc="der",this.name=t.name,this.entity=t,this.tree=new h,this.tree._init(t.body)}function h(t){s.call(this,"der",t)}function f(t,e){let r=t.readUInt8(e);if(t.isError(r))return r;let i=u.tagClass[r>>6],n=(32&r)==0;if((31&r)==31){let i=r;for(r=0;(128&i)==128;){if(i=t.readUInt8(e),t.isError(i))return i;r<<=7,r|=127&i}}else r&=31;let o=u.tag[r];return{cls:i,primitive:n,tag:r,tagStr:o}}function l(t,e,r){let i=t.readUInt8(r);if(t.isError(i))return i;if(!e&&128===i)return null;if((128&i)==0)return i;let n=127&i;if(n>4)return t.error("length octect is too long");i=0;for(let e=0;e<n;e++){i<<=8;let e=t.readUInt8(r);if(t.isError(e))return e;i|=e}return i}t.exports=a,a.prototype.decode=function(t,e){return o.isDecoderBuffer(t)||(t=new o(t,e)),this.tree._decode(t,e)},i(h,s),h.prototype._peekTag=function(t,e,r){if(t.isEmpty())return!1;let i=t.save(),n=f(t,'Failed to peek tag: "'+e+'"');return t.isError(n)?n:(t.restore(i),n.tag===e||n.tagStr===e||n.tagStr+"of"===e||r)},h.prototype._decodeTag=function(t,e,r){let i=f(t,'Failed to decode tag of "'+e+'"');if(t.isError(i))return i;let n=l(t,i.primitive,'Failed to get length of "'+e+'"');if(t.isError(n))return n;if(!r&&i.tag!==e&&i.tagStr!==e&&i.tagStr+"of"!==e)return t.error('Failed to match tag: "'+e+'"');if(i.primitive||null!==n)return t.skip(n,'Failed to match body of: "'+e+'"');let o=t.save(),s=this._skipUntilEnd(t,'Failed to skip indefinite length body: "'+this.tag+'"');return t.isError(s)?s:(n=t.offset-o.offset,t.restore(o),t.skip(n,'Failed to match body of: "'+e+'"'))},h.prototype._skipUntilEnd=function(t,e){for(;;){let r,i=f(t,e);if(t.isError(i))return i;let n=l(t,i.primitive,e);if(t.isError(n))return n;if(r=i.primitive||null!==n?t.skip(n):this._skipUntilEnd(t,e),t.isError(r))return r;if("end"===i.tagStr)break}},h.prototype._decodeList=function(t,e,r,i){let n=[];for(;!t.isEmpty();){let e=this._peekTag(t,"end");if(t.isError(e))return e;let o=r.decode(t,"der",i);if(t.isError(o)&&e)break;n.push(o)}return n},h.prototype._decodeStr=function(t,e){if("bitstr"===e){let e=t.readUInt8();return t.isError(e)?e:{unused:e,data:t.raw()}}if("bmpstr"===e){let e=t.raw();if(e.length%2==1)return t.error("Decoding of string type: bmpstr length mismatch");let r="";for(let t=0;t<e.length/2;t++)r+=String.fromCharCode(e.readUInt16BE(2*t));return r}if("numstr"===e){let e=t.raw().toString("ascii");return this._isNumstr(e)?e:t.error("Decoding of string type: numstr unsupported characters")}if("octstr"===e)return t.raw();if("objDesc"===e)return t.raw();else if("printstr"===e){let e=t.raw().toString("ascii");return this._isPrintstr(e)?e:t.error("Decoding of string type: printstr unsupported characters")}else if(/str$/.test(e))return t.raw().toString();else return t.error("Decoding of string type: "+e+" unsupported")},h.prototype._decodeObjid=function(t,e,r){let i,n=[],o=0,s=0;for(;!t.isEmpty();)s=t.readUInt8(),o<<=7,o|=127&s,(128&s)==0&&(n.push(o),o=0);128&s&&n.push(o);let u=n[0]/40|0,a=n[0]%40;if(i=r?n:[u,a].concat(n.slice(1)),e){let t=e[i.join(" ")];void 0===t&&(t=e[i.join(".")]),void 0!==t&&(i=t)}return i},h.prototype._decodeTime=function(t,e){let r,i,n,o,s,u,a=t.raw().toString();if("gentime"===e)r=0|a.slice(0,4),i=0|a.slice(4,6),n=0|a.slice(6,8),o=0|a.slice(8,10),s=0|a.slice(10,12),u=0|a.slice(12,14);else{if("utctime"!==e)return t.error("Decoding "+e+" time is not supported yet");r=0|a.slice(0,2),i=0|a.slice(2,4),n=0|a.slice(4,6),o=0|a.slice(6,8),s=0|a.slice(8,10),u=0|a.slice(10,12),r=r<70?2e3+r:1900+r}return Date.UTC(r,i-1,n,o,s,u,0)},h.prototype._decodeNull=function(){return null},h.prototype._decodeBool=function(t){let e=t.readUInt8();return t.isError(e)?e:0!==e},h.prototype._decodeInt=function(t,e){let r=new n(t.raw());return e&&(r=e[r.toString(10)]||r),r},h.prototype._use=function(t,e){return"function"==typeof t&&(t=t(e)),t._getDecoder("der").tree}},63263:(t,e,r)=>{"use strict";let i=r(23770).a,n=r(91805).d,o=r(91805).t,s=r(98815),u=["seq","seqof","set","setof","objid","bool","gentime","utctime","null_","enum","int","objDesc","bitstr","bmpstr","charstr","genstr","graphstr","ia5str","iso646str","numstr","octstr","printstr","t61str","unistr","utf8str","videostr"],a=["key","obj","use","optional","explicit","implicit","def","choice","any","contains"].concat(u);function h(t,e,r){let i={};this._baseState=i,i.name=r,i.enc=t,i.parent=e||null,i.children=null,i.tag=null,i.args=null,i.reverseArgs=null,i.choice=null,i.optional=!1,i.any=!1,i.obj=!1,i.use=null,i.useDecoder=null,i.key=null,i.default=null,i.explicit=null,i.implicit=null,i.contains=null,i.parent||(i.children=[],this._wrap())}t.exports=h;let f=["enc","parent","children","tag","args","reverseArgs","choice","optional","any","obj","use","alteredUse","key","default","explicit","implicit","contains"];h.prototype.clone=function(){let t=this._baseState,e={};f.forEach(function(r){e[r]=t[r]});let r=new this.constructor(e.parent);return r._baseState=e,r},h.prototype._wrap=function(){let t=this._baseState;a.forEach(function(e){this[e]=function(){let r=new this.constructor(this);return t.children.push(r),r[e].apply(r,arguments)}},this)},h.prototype._init=function(t){let e=this._baseState;s(null===e.parent),t.call(this),e.children=e.children.filter(function(t){return t._baseState.parent===this},this),s.equal(e.children.length,1,"Root node can have only one child")},h.prototype._useArgs=function(t){let e=this._baseState,r=t.filter(function(t){return t instanceof this.constructor},this);t=t.filter(function(t){return!(t instanceof this.constructor)},this),0!==r.length&&(s(null===e.children),e.children=r,r.forEach(function(t){t._baseState.parent=this},this)),0!==t.length&&(s(null===e.args),e.args=t,e.reverseArgs=t.map(function(t){if("object"!=typeof t||t.constructor!==Object)return t;let e={};return Object.keys(t).forEach(function(r){r==(0|r)&&(r|=0),e[t[r]]=r}),e}))},["_peekTag","_decodeTag","_use","_decodeStr","_decodeObjid","_decodeTime","_decodeNull","_decodeInt","_decodeBool","_decodeList","_encodeComposite","_encodeStr","_encodeObjid","_encodeTime","_encodeNull","_encodeInt","_encodeBool"].forEach(function(t){h.prototype[t]=function(){throw Error(t+" not implemented for encoding: "+this._baseState.enc)}}),u.forEach(function(t){h.prototype[t]=function(){let e=this._baseState,r=Array.prototype.slice.call(arguments);return s(null===e.tag),e.tag=t,this._useArgs(r),this}}),h.prototype.use=function(t){s(t);let e=this._baseState;return s(null===e.use),e.use=t,this},h.prototype.optional=function(){return this._baseState.optional=!0,this},h.prototype.def=function(t){let e=this._baseState;return s(null===e.default),e.default=t,e.optional=!0,this},h.prototype.explicit=function(t){let e=this._baseState;return s(null===e.explicit&&null===e.implicit),e.explicit=t,this},h.prototype.implicit=function(t){let e=this._baseState;return s(null===e.explicit&&null===e.implicit),e.implicit=t,this},h.prototype.obj=function(){let t=this._baseState,e=Array.prototype.slice.call(arguments);return t.obj=!0,0!==e.length&&this._useArgs(e),this},h.prototype.key=function(t){let e=this._baseState;return s(null===e.key),e.key=t,this},h.prototype.any=function(){return this._baseState.any=!0,this},h.prototype.choice=function(t){let e=this._baseState;return s(null===e.choice),e.choice=t,this._useArgs(Object.keys(t).map(function(e){return t[e]})),this},h.prototype.contains=function(t){let e=this._baseState;return s(null===e.use),e.contains=t,this},h.prototype._decode=function(t,e){let r,i=this._baseState;if(null===i.parent)return t.wrapResult(i.children[0]._decode(t,e));let n=i.default,s=!0,u=null;if(null!==i.key&&(u=t.enterKey(i.key)),i.optional){let r=null;if(null!==i.explicit?r=i.explicit:null!==i.implicit?r=i.implicit:null!==i.tag&&(r=i.tag),null!==r||i.any){if(s=this._peekTag(t,r,i.any),t.isError(s))return s}else{let r=t.save();try{null===i.choice?this._decodeGeneric(i.tag,t,e):this._decodeChoice(t,e),s=!0}catch(t){s=!1}t.restore(r)}}if(i.obj&&s&&(r=t.enterObject()),s){if(null!==i.explicit){let e=this._decodeTag(t,i.explicit);if(t.isError(e))return e;t=e}let r=t.offset;if(null===i.use&&null===i.choice){let e;i.any&&(e=t.save());let r=this._decodeTag(t,null!==i.implicit?i.implicit:i.tag,i.any);if(t.isError(r))return r;i.any?n=t.raw(e):t=r}if(e&&e.track&&null!==i.tag&&e.track(t.path(),r,t.length,"tagged"),e&&e.track&&null!==i.tag&&e.track(t.path(),t.offset,t.length,"content"),i.any||(n=null===i.choice?this._decodeGeneric(i.tag,t,e):this._decodeChoice(t,e)),t.isError(n))return n;if(i.any||null!==i.choice||null===i.children||i.children.forEach(function(r){r._decode(t,e)}),i.contains&&("octstr"===i.tag||"bitstr"===i.tag)){let r=new o(n);n=this._getUse(i.contains,t._reporterState.obj)._decode(r,e)}}return i.obj&&s&&(n=t.leaveObject(r)),null!==i.key&&(null!==n||!0===s)?t.leaveKey(u,i.key,n):null!==u&&t.exitKey(u),n},h.prototype._decodeGeneric=function(t,e,r){let i=this._baseState;if("seq"===t||"set"===t)return null;if("seqof"===t||"setof"===t)return this._decodeList(e,t,i.args[0],r);if(/str$/.test(t))return this._decodeStr(e,t,r);if("objid"===t&&i.args)return this._decodeObjid(e,i.args[0],i.args[1],r);if("objid"===t)return this._decodeObjid(e,null,null,r);if("gentime"===t||"utctime"===t)return this._decodeTime(e,t,r);else if("null_"===t)return this._decodeNull(e,r);else if("bool"===t)return this._decodeBool(e,r);else if("objDesc"===t)return this._decodeStr(e,t,r);else if("int"===t||"enum"===t)return this._decodeInt(e,i.args&&i.args[0],r);return null!==i.use?this._getUse(i.use,e._reporterState.obj)._decode(e,r):e.error("unknown tag: "+t)},h.prototype._getUse=function(t,e){let r=this._baseState;return r.useDecoder=this._use(t,e),s(null===r.useDecoder._baseState.parent),r.useDecoder=r.useDecoder._baseState.children[0],r.implicit!==r.useDecoder._baseState.implicit&&(r.useDecoder=r.useDecoder.clone(),r.useDecoder._baseState.implicit=r.implicit),r.useDecoder},h.prototype._decodeChoice=function(t,e){let r=this._baseState,i=null,n=!1;return(Object.keys(r.choice).some(function(o){let s=t.save(),u=r.choice[o];try{let r=u._decode(t,e);if(t.isError(r))return!1;i={type:o,value:r},n=!0}catch(e){return t.restore(s),!1}return!0},this),n)?i:t.error("Choice not matched")},h.prototype._createEncoderBuffer=function(t){return new n(t,this.reporter)},h.prototype._encode=function(t,e,r){let i=this._baseState;if(null!==i.default&&i.default===t)return;let n=this._encodeValue(t,e,r);if(void 0!==n&&!this._skipDefault(n,e,r))return n},h.prototype._encodeValue=function(t,e,r){let n=this._baseState;if(null===n.parent)return n.children[0]._encode(t,e||new i);let o=null;if(this.reporter=e,n.optional&&void 0===t)if(null===n.default)return;else t=n.default;let s=null,u=!1;if(n.any)o=this._createEncoderBuffer(t);else if(n.choice)o=this._encodeChoice(t,e);else if(n.contains)s=this._getUse(n.contains,r)._encode(t,e),u=!0;else if(n.children)s=n.children.map(function(r){if("null_"===r._baseState.tag)return r._encode(null,e,t);if(null===r._baseState.key)return e.error("Child should have a key");let i=e.enterKey(r._baseState.key);if("object"!=typeof t)return e.error("Child expected, but input is not object");let n=r._encode(t[r._baseState.key],e,t);return e.leaveKey(i),n},this).filter(function(t){return t}),s=this._createEncoderBuffer(s);else if("seqof"===n.tag||"setof"===n.tag){if(!(n.args&&1===n.args.length))return e.error("Too many args for : "+n.tag);if(!Array.isArray(t))return e.error("seqof/setof, but data is not Array");let r=this.clone();r._baseState.implicit=null,s=this._createEncoderBuffer(t.map(function(r){let i=this._baseState;return this._getUse(i.args[0],t)._encode(r,e)},r))}else null!==n.use?o=this._getUse(n.use,r)._encode(t,e):(s=this._encodePrimitive(n.tag,t),u=!0);if(!n.any&&null===n.choice){let t=null!==n.implicit?n.implicit:n.tag,r=null===n.implicit?"universal":"context";null===t?null===n.use&&e.error("Tag could be omitted only for .use()"):null===n.use&&(o=this._encodeComposite(t,u,r,s))}return null!==n.explicit&&(o=this._encodeComposite(n.explicit,!1,"context",o)),o},h.prototype._encodeChoice=function(t,e){let r=this._baseState,i=r.choice[t.type];return i||s(!1,t.type+" not found in "+JSON.stringify(Object.keys(r.choice))),i._encode(t.value,e)},h.prototype._encodePrimitive=function(t,e){let r=this._baseState;if(/str$/.test(t))return this._encodeStr(e,t);if("objid"===t&&r.args)return this._encodeObjid(e,r.reverseArgs[0],r.args[1]);if("objid"===t)return this._encodeObjid(e,null,null);if("gentime"===t||"utctime"===t)return this._encodeTime(e,t);if("null_"===t)return this._encodeNull();else if("int"===t||"enum"===t)return this._encodeInt(e,r.args&&r.reverseArgs[0]);else if("bool"===t)return this._encodeBool(e);else if("objDesc"===t)return this._encodeStr(e,t);else throw Error("Unsupported tag: "+t)},h.prototype._isNumstr=function(t){return/^[0-9 ]*$/.test(t)},h.prototype._isPrintstr=function(t){return/^[A-Za-z0-9 '()+,-./:=?]*$/.test(t)}},67487:(t,e,r)=>{"use strict";e.Reporter=r(23770).a,e.DecoderBuffer=r(91805).t,e.EncoderBuffer=r(91805).d,e.Node=r(63263)},69732:(t,e,r)=>{"use strict";let i,n=r(21820),o=r(83997),s=r(79935),{env:u}=process;function a(t){return 0!==t&&{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function h(t,e){if(0===i)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(t&&!e&&void 0===i)return 0;let r=i||0;if("dumb"===u.TERM)return r;if("win32"===process.platform){let t=n.release().split(".");return Number(t[0])>=10&&Number(t[2])>=10586?Number(t[2])>=14931?3:2:1}if("CI"in u)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(t=>t in u)||"codeship"===u.CI_NAME?1:r;if("TEAMCITY_VERSION"in u)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(u.TEAMCITY_VERSION);if("truecolor"===u.COLORTERM)return 3;if("TERM_PROGRAM"in u){let t=parseInt((u.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(u.TERM_PROGRAM){case"iTerm.app":return t>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(u.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(u.TERM)||"COLORTERM"in u?1:r}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?i=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(i=1),"FORCE_COLOR"in u&&(i="true"===u.FORCE_COLOR?1:"false"===u.FORCE_COLOR?0:0===u.FORCE_COLOR.length?1:Math.min(parseInt(u.FORCE_COLOR,10),3)),t.exports={supportsColor:function(t){return a(h(t,t&&t.isTTY))},stdout:a(h(!0,o.isatty(1))),stderr:a(h(!0,o.isatty(2)))}},78649:(t,e,r)=>{"use strict";let i=r(27335),n=r(15795),o=r(85849);function s(t,e){this.name=t,this.body=e,this.decoders={},this.encoders={}}e.define=function(t,e){return new s(t,e)},s.prototype._createNamed=function(t){let e=this.name;function r(t){this._initNamed(t,e)}return o(r,t),r.prototype._initNamed=function(e,r){t.call(this,e,r)},new r(this)},s.prototype._getDecoder=function(t){return t=t||"der",this.decoders.hasOwnProperty(t)||(this.decoders[t]=this._createNamed(n[t])),this.decoders[t]},s.prototype.decode=function(t,e,r){return this._getDecoder(e).decode(t,r)},s.prototype._getEncoder=function(t){return t=t||"der",this.encoders.hasOwnProperty(t)||(this.encoders[t]=this._createNamed(i[t])),this.encoders[t]},s.prototype.encode=function(t,e,r){return this._getEncoder(e).encode(t,r)}},79935:t=>{"use strict";t.exports=(t,e=process.argv)=>{let r=t.startsWith("-")?"":1===t.length?"-":"--",i=e.indexOf(r+t),n=e.indexOf("--");return -1!==i&&(-1===n||i<n)}},85849:(t,e,r)=>{try{var i=r(28354);if("function"!=typeof i.inherits)throw"";t.exports=i.inherits}catch(e){t.exports=r(23294)}},86417:(t,e,r)=>{var i=r(79428),n=i.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return n(t,e,r)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?t.exports=i:(o(i,e),e.Buffer=s),s.prototype=Object.create(n.prototype),o(n,s),s.from=function(t,e,r){if("number"==typeof t)throw TypeError("Argument must not be a number");return n(t,e,r)},s.alloc=function(t,e,r){if("number"!=typeof t)throw TypeError("Argument must be a number");var i=n(t);return void 0!==e?"string"==typeof r?i.fill(e,r):i.fill(e):i.fill(0),i},s.allocUnsafe=function(t){if("number"!=typeof t)throw TypeError("Argument must be a number");return n(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw TypeError("Argument must be a number");return i.SlowBuffer(t)}},87073:function(t,e,r){"use strict";var i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.parseProxyResponse=void 0;let n=(0,i(r(40001)).default)("https-proxy-agent:parse-proxy-response");e.parseProxyResponse=function(t){return new Promise((e,r)=>{let i=0,o=[];function s(){let a=t.read();a?function(a){o.push(a),i+=a.length;let h=Buffer.concat(o,i),f=h.indexOf("\r\n\r\n");if(-1===f){n("have not received end of HTTP headers yet..."),s();return}let l=h.slice(0,f).toString("ascii").split("\r\n"),c=l.shift();if(!c)return t.destroy(),r(Error("No header received from proxy CONNECT response"));let p=c.split(" "),d=+p[1],m=p.slice(2).join(" "),g={};for(let e of l){if(!e)continue;let i=e.indexOf(":");if(-1===i)return t.destroy(),r(Error(`Invalid header from proxy CONNECT response: "${e}"`));let n=e.slice(0,i).toLowerCase(),o=e.slice(i+1).trimStart(),s=g[n];"string"==typeof s?g[n]=[s,o]:Array.isArray(s)?s.push(o):g[n]=o}n("got proxy server response: %o %o",c,g),u(),e({connect:{statusCode:d,statusText:m,headers:g},buffered:h})}(a):t.once("readable",s)}function u(){t.removeListener("end",a),t.removeListener("error",h),t.removeListener("readable",s)}function a(){u(),n("onend"),r(Error("Proxy connection ended before receiving CONNECT response"))}function h(t){u(),n("onerror %o",t),r(t)}t.on("error",h),t.on("end",a),s()})}},88409:(t,e,r)=>{"use strict";var i,n=r(79428),o=n.Buffer,s={};for(i in n)n.hasOwnProperty(i)&&"SlowBuffer"!==i&&"Buffer"!==i&&(s[i]=n[i]);var u=s.Buffer={};for(i in o)o.hasOwnProperty(i)&&"allocUnsafe"!==i&&"allocUnsafeSlow"!==i&&(u[i]=o[i]);if(s.Buffer.prototype=o.prototype,u.from&&u.from!==Uint8Array.from||(u.from=function(t,e,r){if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type '+typeof t);if(t&&void 0===t.length)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);return o(t,e,r)}),u.alloc||(u.alloc=function(t,e,r){if("number"!=typeof t)throw TypeError('The "size" argument must be of type number. Received type '+typeof t);if(t<0||t>=2*0x40000000)throw RangeError('The value "'+t+'" is invalid for option "size"');var i=o(t);return e&&0!==e.length?"string"==typeof r?i.fill(e,r):i.fill(e):i.fill(0),i}),!s.kStringMaxLength)try{s.kStringMaxLength=process.binding("buffer").kStringMaxLength}catch(t){}!s.constants&&(s.constants={MAX_LENGTH:s.kMaxLength},s.kStringMaxLength&&(s.constants.MAX_STRING_LENGTH=s.kStringMaxLength)),t.exports=s},90979:t=>{function e(t,e,r,i){return Math.round(t/r)+" "+i+(e>=1.5*r?"s":"")}t.exports=function(t,r){r=r||{};var i,n,o,s,u=typeof t;if("string"===u&&t.length>0){var a=t;if(!((a=String(a)).length>100)){var h=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(a);if(h){var f=parseFloat(h[1]);switch((h[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*f;case"weeks":case"week":case"w":return 6048e5*f;case"days":case"day":case"d":return 864e5*f;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*f;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*f;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*f;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return f;default:break}}}return}if("number"===u&&isFinite(t)){return r.long?(n=Math.abs(i=t))>=864e5?e(i,n,864e5,"day"):n>=36e5?e(i,n,36e5,"hour"):n>=6e4?e(i,n,6e4,"minute"):n>=1e3?e(i,n,1e3,"second"):i+" ms":(s=Math.abs(o=t))>=864e5?Math.round(o/864e5)+"d":s>=36e5?Math.round(o/36e5)+"h":s>=6e4?Math.round(o/6e4)+"m":s>=1e3?Math.round(o/1e3)+"s":o+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},91017:(t,e,r)=>{"use strict";let i=r(85849),n=r(44240);function o(t){n.call(this,t),this.enc="pem"}i(o,n),t.exports=o,o.prototype.encode=function(t,e){let r=n.prototype.encode.call(this,t).toString("base64"),i=["-----BEGIN "+e.label+"-----"];for(let t=0;t<r.length;t+=64)i.push(r.slice(t,t+64));return i.push("-----END "+e.label+"-----"),i.join("\n")}},91805:(t,e,r)=>{"use strict";let i=r(85849),n=r(23770).a,o=r(88409).Buffer;function s(t,e){if(n.call(this,e),!o.isBuffer(t))return void this.error("Input not Buffer");this.base=t,this.offset=0,this.length=t.length}function u(t,e){if(Array.isArray(t))this.length=0,this.value=t.map(function(t){return u.isEncoderBuffer(t)||(t=new u(t,e)),this.length+=t.length,t},this);else if("number"==typeof t){if(!(0<=t&&t<=255))return e.error("non-byte EncoderBuffer value");this.value=t,this.length=1}else if("string"==typeof t)this.value=t,this.length=o.byteLength(t);else{if(!o.isBuffer(t))return e.error("Unsupported type: "+typeof t);this.value=t,this.length=t.length}}i(s,n),e.t=s,s.isDecoderBuffer=function(t){return t instanceof s||"object"==typeof t&&o.isBuffer(t.base)&&"DecoderBuffer"===t.constructor.name&&"number"==typeof t.offset&&"number"==typeof t.length&&"function"==typeof t.save&&"function"==typeof t.restore&&"function"==typeof t.isEmpty&&"function"==typeof t.readUInt8&&"function"==typeof t.skip&&"function"==typeof t.raw},s.prototype.save=function(){return{offset:this.offset,reporter:n.prototype.save.call(this)}},s.prototype.restore=function(t){let e=new s(this.base);return e.offset=t.offset,e.length=this.offset,this.offset=t.offset,n.prototype.restore.call(this,t.reporter),e},s.prototype.isEmpty=function(){return this.offset===this.length},s.prototype.readUInt8=function(t){return this.offset+1<=this.length?this.base.readUInt8(this.offset++,!0):this.error(t||"DecoderBuffer overrun")},s.prototype.skip=function(t,e){if(!(this.offset+t<=this.length))return this.error(e||"DecoderBuffer overrun");let r=new s(this.base);return r._reporterState=this._reporterState,r.offset=this.offset,r.length=this.offset+t,this.offset+=t,r},s.prototype.raw=function(t){return this.base.slice(t?t.offset:this.offset,this.length)},e.d=u,u.isEncoderBuffer=function(t){return t instanceof u||"object"==typeof t&&"EncoderBuffer"===t.constructor.name&&"number"==typeof t.length&&"function"==typeof t.join},u.prototype.join=function(t,e){return t||(t=o.alloc(this.length)),e||(e=0),0===this.length||(Array.isArray(this.value)?this.value.forEach(function(r){r.join(t,e),e+=r.length}):("number"==typeof this.value?t[e]=this.value:"string"==typeof this.value?t.write(this.value,e):o.isBuffer(this.value)&&this.value.copy(t,e),e+=this.length)),t}},93823:(t,e,r)=>{"use strict";var i=r(79428).Buffer,n=r(79428).SlowBuffer;function o(t,e){if(!i.isBuffer(t)||!i.isBuffer(e)||t.length!==e.length)return!1;for(var r=0,n=0;n<t.length;n++)r|=t[n]^e[n];return 0===r}t.exports=o,o.install=function(){i.prototype.equal=n.prototype.equal=function(t){return o(this,t)}};var s=i.prototype.equal,u=n.prototype.equal;o.restore=function(){i.prototype.equal=s,n.prototype.equal=u}},94099:t=>{"use strict";t.exports={validate:function(t){return/^[A-Za-z0-9\-_]+$/.test(t)}}},94515:(t,e,r)=>{"use strict";let i=r(55511),n=r(95118),o=r(13891),{URL:s}=r(79551),u=r(39627),a=r(94099),h=n.define("ECPrivateKey",function(){this.seq().obj(this.key("version").int(),this.key("privateKey").octstr(),this.key("parameters").explicit(0).objid().optional(),this.key("publicKey").explicit(1).bitstr().optional())});function f(t){if(!t)throw Error("No subject set in vapidDetails.subject.");if("string"!=typeof t||0===t.length)throw Error("The subject value must be a string containing an https: URL or mailto: address. "+t);let e=null;try{e=new s(t)}catch(e){throw Error("Vapid subject is not a valid URL. "+t)}if(!["https:","mailto:"].includes(e.protocol))throw Error("Vapid subject is not an https: or mailto: URL. "+t);"localhost"===e.hostname&&console.warn("Vapid subject points to a localhost web URI, which is unsupported by Apple's push notification server and will result in a BadJwtToken error when sending notifications.")}function l(t){if(!t)throw Error("No key set vapidDetails.publicKey");if("string"!=typeof t)throw Error("Vapid public key is must be a URL safe Base 64 encoded string.");if(!a.validate(t))throw Error('Vapid public key must be a URL safe Base 64 (without "=")');if(65!==(t=Buffer.from(t,"base64url")).length)throw Error("Vapid public key should be 65 bytes long when decoded.")}function c(t){if(!t)throw Error("No key set in vapidDetails.privateKey");if("string"!=typeof t)throw Error("Vapid private key must be a URL safe Base 64 encoded string.");if(!a.validate(t))throw Error('Vapid private key must be a URL safe Base 64 (without "=")');if(32!==(t=Buffer.from(t,"base64url")).length)throw Error("Vapid private key should be 32 bytes long when decoded.")}function p(t){let e=new Date;return e.setSeconds(e.getSeconds()+t),Math.floor(e.getTime()/1e3)}function d(t){if(!Number.isInteger(t))throw Error("`expiration` value must be a number");if(t<0)throw Error("`expiration` must be a positive integer");if(t>=p(86400))throw Error("`expiration` value is greater than maximum of 24 hours")}t.exports={generateVAPIDKeys:function(){let t=i.createECDH("prime256v1");t.generateKeys();let e=t.getPublicKey(),r=t.getPrivateKey();if(r.length<32){let t=Buffer.alloc(32-r.length);t.fill(0),r=Buffer.concat([t,r])}if(e.length<65){let t=Buffer.alloc(65-e.length);t.fill(0),e=Buffer.concat([t,e])}return{publicKey:e.toString("base64url"),privateKey:r.toString("base64url")}},getFutureExpirationTimestamp:p,getVapidHeaders:function(t,e,r,i,n,a){var m;if(!t)throw Error("No audience could be generated for VAPID.");if("string"!=typeof t||0===t.length)throw Error("The audience value must be a string containing the origin of a push service. "+t);try{new s(t)}catch(e){throw Error("VAPID audience is not a url. "+t)}f(e),l(r),c(i),i=Buffer.from(i,"base64url"),a?d(a):a=p(43200);let g={aud:t,exp:a,sub:e},y=o.sign({header:{typ:"JWT",alg:"ES256"},payload:g,privateKey:(m=i,h.encode({version:1,privateKey:m,parameters:[1,2,840,10045,3,1,7]},"pem",{label:"EC PRIVATE KEY"}))});if(n===u.supportedContentEncodings.AES_128_GCM)return{Authorization:"vapid t="+y+", k="+r};if(n===u.supportedContentEncodings.AES_GCM)return{Authorization:"WebPush "+y,"Crypto-Key":"p256ecdsa="+r};throw Error("Unsupported encoding type specified.")},validateSubject:f,validatePublicKey:l,validatePrivateKey:c,validateExpiration:d}},95118:(t,e,r)=>{"use strict";e.bignum=r(40016),e.define=r(78649).define,e.base=r(67487),e.constants=r(26763),e.decoders=r(15795),e.encoders=r(27335)},96580:(t,e,r)=>{"use strict";let i=r(55511),n=r(98541);t.exports={encrypt:function(t,e,r,o){if(!t)throw Error("No user public key provided for encryption.");if("string"!=typeof t)throw Error("The subscription p256dh value must be a string.");if(65!==Buffer.from(t,"base64url").length)throw Error("The subscription p256dh value should be 65 bytes long.");if(!e)throw Error("No user auth provided for encryption.");if("string"!=typeof e)throw Error("The subscription auth key must be a string.");if(Buffer.from(e,"base64url").length<16)throw Error("The subscription auth key should be at least 16 bytes long");if("string"!=typeof r&&!Buffer.isBuffer(r))throw Error("Payload must be either a string or a Node Buffer.");("string"==typeof r||r instanceof String)&&(r=Buffer.from(r));let s=i.createECDH("prime256v1"),u=s.generateKeys(),a=i.randomBytes(16).toString("base64url"),h=n.encrypt(r,{version:o,dh:t,privateKey:s,salt:a,authSecret:e});return{localPublicKey:u,salt:a,cipherText:h}}}},96849:(t,e,r)=>{"use strict";t.exports=r(44870)},98541:(t,e,r)=>{"use strict";var i,n=r(55511),o="aes-128-gcm",s={aes128gcm:1,aesgcm:2},u="encrypt",a="decrypt";function h(t){return"string"==typeof t?Buffer.from(t,"base64url"):t}function f(t,e){var r=n.createHmac("sha256",t);return r.update(e),r.digest()}function l(t,e){return i("salt",t),i("ikm",e),i("extract",f(t,e))}function c(t,e,r){i("prk",t),i("info",e);var n=Buffer.alloc(0),o=Buffer.alloc(0);e=Buffer.from(e,"ascii");for(var s=0,u=Buffer.alloc(1);n.length<r;)u.writeUIntBE(++s,0,1),o=f(t,Buffer.concat([o,e,u])),n=Buffer.concat([n,o]);return i("expand",n.slice(0,r))}function p(t,e){var r=Buffer.concat([Buffer.from("Content-Encoding: "+t+"\0","ascii"),e]);return i("info "+t,r),r}function d(t){var e=Buffer.concat([Buffer.alloc(2),t]);return e.writeUIntBE(t.length,0,2),e}function m(t,e,r){if(!t.salt)throw Error("must include a salt parameter for "+t.version);if("aesgcm"===t.version){var n,o,s,h=function(t,e){var r,n,o,s={secret:null,context:Buffer.alloc(0)};if(t.key){if(s.secret=t.key,16!==s.secret.length)throw Error("An explicit key must be 16 bytes")}else t.dh?s=function(t,e){var r,i,n=t.privateKey;if(e===u)r=n.getPublicKey(),i=t.dh;else if(e===a)r=t.dh,i=n.getPublicKey();else throw Error("Unknown mode only "+u+" and "+a+" supported");return{secret:n.computeSecret(t.dh),context:Buffer.concat([Buffer.from(t.keylabel,"ascii"),Buffer.from([0]),d(i),d(r)])}}(t,e):(t.keyid,s.secret=t.keymap[t.keyid]);if(!s.secret)throw Error("Unable to determine key");return i("secret",s.secret),i("context",s.context),t.authSecret&&(s.secret=(r=t.authSecret,n=s.secret,o=p("auth",Buffer.alloc(0)),c(l(r,n),o,32)),i("authsecret",s.secret)),s}(t,e,r);n=p("aesgcm",h.context),o=p("nonce",h.context),s=h.secret}else if("aes128gcm"===t.version)n=Buffer.from("Content-Encoding: aes128gcm\0"),o=Buffer.from("Content-Encoding: nonce\0"),s=function(t,e,r){if(r&&"function"!=typeof r)throw Error("Callback is not a function");if(t.key){if(16!==t.key.length)throw Error("An explicit key must be 16 bytes");return i("secret key",t.key)}if(!t.privateKey){if(r)var n=r(t.keyid);else var n=t.keymap&&t.keymap[t.keyid];if(!n)throw Error('No saved key (keyid: "'+t.keyid+'")');return n}return function(t,e){var r,n,o,s,h,f;if(!t.authSecret)throw Error("No authentication secret for webpush");if(i("authsecret",t.authSecret),e===u)n=t.privateKey.getPublicKey(),r=o=t.dh;else if(e===a)r=n=t.keyid,o=t.privateKey.getPublicKey();else throw Error("Unknown mode only "+u+" and "+a+" supported");return i("remote pubkey",r),i("sender pubkey",n),i("receiver pubkey",o),i("secret dh",(s=t.authSecret,h=t.privateKey.computeSecret(r),f=Buffer.concat([Buffer.from("WebPush: info\0"),o,n]),c(l(s,h),f,32)))}(t,e)}(t,e,r);else throw Error("Unable to set context for mode "+t.version);var f=l(t.salt,s),m={key:c(f,n,16),nonce:c(f,o,12)};return i("key",m.key),i("nonce base",m.nonce),m}function g(t){var e={};e.version=t.version||"aes128gcm",e.rs=parseInt(t.rs,10),isNaN(e.rs)&&(e.rs=4096);var r=s[e.version];if("aes128gcm"===e.version&&(r+=16),e.rs<=r)throw Error("The rs parameter has to be greater than "+r);if(t.salt&&(e.salt=h(t.salt),16!==e.salt.length))throw Error("The salt parameter must be 16 bytes");return e.keyid=t.keyid,t.key?e.key=h(t.key):(e.privateKey=t.privateKey,e.privateKey||(e.keymap=t.keymap),"aes128gcm"!==e.version&&(e.keylabel=t.keylabel||"P-256"),t.dh&&(e.dh=h(t.dh))),t.authSecret&&(e.authSecret=h(t.authSecret)),e}function y(t,e){var r=Buffer.from(t),n=r.readUIntBE(r.length-6,6);return r.writeUIntBE(((n^e)&0xffffff)+((n/0x1000000^e/0x1000000)&0xffffff)*0x1000000,r.length-6,6),i("nonce"+e,r),r}i="1"===process.env.ECE_KEYLOG?function(t,e){return console.warn(t+" ["+e.length+"]: "+e.toString("base64url")),e}:function(t,e){return e},t.exports={decrypt:function(t,e,r){var u=g(e);if("aes128gcm"===u.version){var h,f,l=(f=(h=t).readUIntBE(20,1),u.salt=h.slice(0,16),u.rs=h.readUIntBE(16,4),u.keyid=h.slice(21,21+f),21+f);t=t.slice(l)}var c=m(u,a,r),p=0,d=Buffer.alloc(0),v=u.rs;"aes128gcm"!==u.version&&(v+=16);for(var b=0;p<t.length;++b){var w=p+v;if("aes128gcm"!==u.version&&w===t.length)throw Error("Truncated payload");if((w=Math.min(w,t.length))-p<=16)throw Error("Invalid block: too small at "+b);var M=function(t,e,r,u,a){i("decrypt",r);var h=y(t.nonce,e),f=n.createDecipheriv(o,t.key,h);f.setAuthTag(r.slice(r.length-16));var l=f.update(r.slice(0,r.length-16));if(l=Buffer.concat([l,f.final()]),i("decrypted",l),"aes128gcm"!==u.version)return function(t,e){var r=s[e],n=t.readUIntBE(0,r);if(n+r>t.length)throw Error("padding exceeds block size");i("padding",t.slice(0,r+n));var o=Buffer.alloc(n);if(o.fill(0),0!==o.compare(t.slice(r,r+n)))throw Error("invalid padding");return t.slice(r+n)}(l,u.version);for(var c=l,p=c.length-1;p>=0;){if(c[p]){if(a){if(2!==c[p])throw Error("last record needs to start padding with a 2")}else if(1!==c[p])throw Error("last record needs to start padding with a 2");return c.slice(0,p)}--p}throw Error("all zero plaintext")}(c,b,t.slice(p,w),u,w>=t.length);d=Buffer.concat([d,M]),p=w}return d},encrypt:function(t,e,r){if(!Buffer.isBuffer(t))throw Error("buffer argument must be a Buffer");var a,h=g(e);h.salt||(h.salt=n.randomBytes(16)),"aes128gcm"===h.version?(h.privateKey&&!h.keyid&&(h.keyid=h.privateKey.getPublicKey()),a=function(t){var e=Buffer.alloc(5),r=Buffer.from(t.keyid||[]);if(r.length>255)throw Error("keyid is too large");return e.writeUIntBE(t.rs,0,4),e.writeUIntBE(r.length,4,1),Buffer.concat([t.salt,e,r])}(h)):a=Buffer.alloc(0);var f=m(h,u,r),l=0,c=s[h.version],p=c;"aes128gcm"===h.version&&(p+=16);for(var d=isNaN(parseInt(e.pad,10))?0:parseInt(e.pad,10),v=0,b=!1;!b;){var w=Math.min(h.rs-p-1,d);"aes128gcm"!==h.version&&(w=Math.min((1<<8*c)-1,w)),d>0&&0===w&&++w,d-=w;var M=l+h.rs-p-w;b=(b="aes128gcm"!==h.version?M>t.length:M>=t.length)&&d<=0;var _=function(t,e,r,u,a,h){i("encrypt",r),u=u||0;var f=y(t.nonce,e),l=n.createCipheriv(o,t.key,f),c=[],p=s[a.version],d=Buffer.alloc(u+p);if(d.fill(0),"aes128gcm"!==a.version){if(d.writeUIntBE(u,0,p),i("padding",d),c.push(l.update(d)),c.push(l.update(r)),!h&&d.length+r.length<a.rs)throw Error("Unable to pad to record size")}else c.push(l.update(r)),d.writeUIntBE(h?2:1,0,1),i("padding",d),c.push(l.update(d));l.final();var m=l.getAuthTag();if(16!==m.length)throw Error("invalid tag generated");return c.push(m),i("encrypted",Buffer.concat(c))}(f,v,t.slice(l,M),w,h,b);a=Buffer.concat([a,_]),l=M,++v}return a}}},98815:t=>{function e(t,e){if(!t)throw Error(e||"Assertion failed")}t.exports=e,e.equal=function(t,e,r){if(t!=e)throw Error(r||"Assertion failed: "+t+" != "+e)}},99827:(t,e,r)=>{var i=r(86417).Buffer,n=r(57592),o=r(11252),s=r(27910),u=r(12187),a=r(28354);function h(t,e){return i.from(t,e).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function f(t){var e,r,i,n=t.header,s=t.payload,f=t.secret||t.privateKey,l=t.encoding,c=o(n.alg),p=(e=(e=l)||"utf8",r=h(u(n),"binary"),i=h(u(s),e),a.format("%s.%s",r,i)),d=c.sign(p,f);return a.format("%s.%s",p,d)}function l(t){var e=new n(t.secret||t.privateKey||t.key);this.readable=!0,this.header=t.header,this.encoding=t.encoding,this.secret=this.privateKey=this.key=e,this.payload=new n(t.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}a.inherits(l,s),l.prototype.sign=function(){try{var t=f({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",t),this.emit("data",t),this.emit("end"),this.readable=!1,t}catch(t){this.readable=!1,this.emit("error",t),this.emit("close")}},l.sign=f,t.exports=l}};