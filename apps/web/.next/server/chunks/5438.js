"use strict";exports.id=5438,exports.ids=[5438],exports.modules={3472:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(60154));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}let i={current:null},a="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}a(e=>{try{s(i.current)}finally{i.current=null}})},11695:(e,t,r)=>{Object.defineProperty(t,"U",{enumerable:!0,get:function(){return f}});let n=r(69599),o=r(26008),i=r(29294),a=r(63033),s=r(349),c=r(79893),l=r(12374),u=r(3472),d=(r(74969),r(27337));function f(){let e="cookies",t=i.workAsyncStorage.getStore(),r=a.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new o.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var u=t.route,f=r;let e=p.get(f);if(e)return e;let n=(0,l.makeHangingPromise)(f.renderSignal,"`cookies()`");return p.set(f,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},size:{get(){let e="`cookies().size`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${y(t)}, ...)\``:"`cookies().set(...)`"}let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},clear:{value:function(){let e="`cookies().clear()`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},toString:{value:function(){let e="`cookies().toString()`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}}}),n}else"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r);(0,s.trackDynamicDataInDynamicRender)(t,r)}let m=(0,a.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(m)?m.userspaceMutableCookies:m.cookies)}let p=new WeakMap;function h(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):b.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):_.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function b(){return this.getAll().map(e=>[e.name,e]).values()}function _(e){for(let e of this.getAll())this.delete(e.name);return e}},19614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return o}});let n=r(75013);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return n.ReflectAdapter.get(t,a,o)},set(t,r,o,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,o,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,s??r,o,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},27095:(e,t,r)=>{let n=r(63033),o=r(29294),i=r(349),a=r(3472),s=r(79893),c=r(78721);function l(){let e=o.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return u(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return u(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function u(e,t){let r,n=d.get(l);return n||(r=f(e),d.set(e,r),r)}let d=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){y("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){y("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function y(e){let t=o.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},30900:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(12374),o=r(71475),i=r(41240),a=r(48124),s=r(349),c=r(78721);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30935:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}}},33054:(e,t,r)=>{Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(19614),o=r(29294),i=r(63033),a=r(349),s=r(79893),c=r(12374),l=r(3472),u=(r(74969),r(27337));function d(){let e=o.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=f.get(l);if(n)return n;let o=(0,c.makeHangingPromise)(l.renderSignal,"`headers()`");return f.set(l,o),Object.defineProperties(o,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}}}),o}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let y=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},33889:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(47630).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41038:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41240:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return o}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},42122:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u},RedirectType:function(){return o.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(64243),o=r(66372),i=r(76883),a=r(67062),s=r(33889),c=r(57957);class l extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class u extends URLSearchParams{append(){throw new l}delete(){throw new l}set(){throw new l}sort(){throw new l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47630:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return i}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(47630),o=r(66372);function i(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52058:(e,t,r)=>{var n=r(42122);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},57957:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(30900).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64243:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return l},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(41038),o=r(66372),i=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?o.RedirectType.push:o.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=o.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function l(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function u(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66372:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return o},RedirectType:function(){return i},isRedirectError:function(){return a}});let n=r(41038),o="NEXT_REDIRECT";var i=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,a=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===o&&("replace"===i||"push"===i)&&"string"==typeof a&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67062:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(47630).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69599:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return u},responseCookiesToRequestCookies:function(){return m},wrapWithMutableAccessCheck:function(){return p}});let n=r(26008),o=r(75013),i=r(29294),a=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let o=new n.ResponseCookies(e),i=o.getAll();for(let e of r)o.set(e);for(let e of i)o.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case l:return a;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{c()}};default:return o.ReflectAdapter.get(e,t,r)}}});return u}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return o.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function y(e){if(!h((0,a.getExpectedRequestStore)(e)))throw new s}function m(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},71475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},74723:(e,t,r)=>{let n,o,i,a,s;r.d(t,{Ay:()=>nL});var c=r(53619),l=r(31096);let u=!1;function d(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let f=!1,p=!1,h=!1,y=["createVerificationToken","useVerificationToken","getUserByEmail"],m=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],g=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var b=r(95045);async function _({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:o}=e,i=n.origin;return t?i=await o.redirect({url:t,baseUrl:n.origin}):r&&(i=await o.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}var w=r(22221);let v="\x1b[31m",k="\x1b[0m",S={error(e){let t=e instanceof l.lR?e.type:e.name;if(console.error(`${v}[auth][error]${k} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${v}[auth][cause]${k}:`,t.stack),r&&console.error(`${v}[auth][details]${k}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){let t=`https://warnings.authjs.dev#${e}`;console.warn(`\x1b[33m[auth][warn][${e}]${k}`,`Read more: ${t}`)},debug(e,t){console.log(`\x1b[90m[auth][debug]:${k} ${e}`,JSON.stringify(t,null,2))}};function R(e){let t={...S};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let T=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function A(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function E(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new l.P8("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:o}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new l.P8(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new l.P8(`Cannot parse action at ${e}`);let[o,i]=n;if(!T.includes(o)||i&&!["signin","callback","webauthn-options"].includes(o))throw new l.P8(`Cannot parse action at ${e}`);return{action:o,providerId:i}}(r.pathname,t.basePath);return{url:r,action:n,providerId:o,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await A(e):void 0,cookies:(0,w.q)(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=R(t);r.error(n),r.debug("request",e)}}function x(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:o}=e,i=(0,w.l)(r,n,o);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function O(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function P(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function U({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[o,i]=t.split("|");if(i===await O(`${o}${e.secret}`))return{csrfTokenVerified:r&&o===n,csrfToken:o}}let o=P(32),i=await O(`${o}${e.secret}`);return{cookie:`${o}|${i}`,csrfToken:o}}function j(e,t){if(!t)throw new l.dy(`CSRF token was missing during an action ${e}`)}function C(e){return null!==e&&"object"==typeof e}function $(e,...t){if(!t.length)return e;let r=t.shift();if(C(e)&&C(r))for(let t in r)C(r[t])?(C(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),$(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return $(e,...t)}let D=Symbol("skip-csrf-check"),I=Symbol("return-type-raw"),L=Symbol("custom-fetch"),N=Symbol("conform-internal"),H=e=>W({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),M=e=>W({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function W(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function q(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let F={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function J({authOptions:e,providerId:t,action:r,url:n,cookies:o,callbackUrl:i,csrfToken:a,csrfDisabled:s,isPost:u}){var d,f;let p=R(e),{providers:h,provider:y}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),o=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:o,...i}=t,a=o?.id??i.id,s=$(i,o,{signinUrl:`${n}/signin/${a}`,callbackUrl:`${n}/callback/${a}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=o?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=q(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=q(e.token,e.issuer),n=q(e.userinfo,e.issuer),o=e.checks??["pkce"];return e.redirectProxyUrl&&(o.includes("state")||o.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:o,userinfo:n,profile:e.profile??H,account:e.account??M}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[L]??(e[L]=o?.[L]),e}return s});return{providers:o,provider:o.find(({id:e})=>e===t)}}({url:n,providerId:t,config:e}),m=!1;if((y?.type==="oauth"||y?.type==="oidc")&&y.redirectProxyUrl)try{m=new URL(y.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${y.redirectProxyUrl}`)}let g={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:y,cookies:$(c.X(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:h,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:b.lF,decode:b.D4,...e.jwt},events:(d=e.events??{},f=p,Object.keys(d).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=d[t];return await r(...e)}catch(e){f.error(new l.PM(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let o=e[n];return await o(...r)}catch(r){let e=new l.om(r);throw t.error(e),e}},r),{})}(e.adapter,p),callbacks:{...F,...e.callbacks},logger:p,callbackUrl:n.origin,isOnRedirectProxy:m,experimental:{...e.experimental}},w=[];if(s)g.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await U({options:g,cookieValue:o?.[g.cookies.csrfToken.name],isPost:u,bodyValue:a});g.csrfToken=e,g.csrfTokenVerified=r,t&&w.push({name:g.cookies.csrfToken.name,value:t,options:g.cookies.csrfToken.options})}let{callbackUrl:v,callbackUrlCookie:k}=await _({options:g,cookieValue:o?.[g.cookies.callbackUrl.name],paramValue:i});return g.callbackUrl=v,k&&w.push({name:g.cookies.callbackUrl.name,value:k,options:g.cookies.callbackUrl.options}),{options:g,cookies:w}}var z,B,V,K,X,G={},Z=[],Y=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function Q(e,t){for(var r in t)e[r]=t[r];return e}function ee(e){var t=e.parentNode;t&&t.removeChild(e)}function et(e,t,r,n,o){var i={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++V:o};return null==o&&null!=B.vnode&&B.vnode(i),i}function er(e){return e.children}function en(e,t){this.props=e,this.context=t}function eo(e,t){if(null==t)return e.__?eo(e.__,e.__.__k.indexOf(e)+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?eo(e):null}function ei(e){(!e.__d&&(e.__d=!0)&&K.push(e)&&!ea.__r++||X!==B.debounceRendering)&&((X=B.debounceRendering)||setTimeout)(ea)}function ea(){for(var e;ea.__r=K.length;)e=K.sort(function(e,t){return e.__v.__b-t.__v.__b}),K=[],e.some(function(e){var t,r,n,o,i;e.__d&&(o=(n=e.__v).__e,(i=e.__P)&&(t=[],(r=Q({},n)).__v=n.__v+1,ep(i,n,r,e.__n,void 0!==i.ownerSVGElement,null!=n.__h?[o]:null,t,null==o?eo(n):o,n.__h),eh(t,n),n.__e!=o&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)))})}function es(e,t,r,n,o,i,a,s,c,l){var u,d,f,p,h,y,m,g=n&&n.__k||Z,b=g.length;for(r.__k=[],u=0;u<t.length;u++)if(null!=(p=r.__k[u]=null==(p=t[u])||"boolean"==typeof p?null:"string"==typeof p||"number"==typeof p||"bigint"==typeof p?et(null,p,null,null,p):Array.isArray(p)?et(er,{children:p},null,null,null):p.__b>0?et(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)){if(p.__=r,p.__b=r.__b+1,null===(f=g[u])||f&&p.key==f.key&&p.type===f.type)g[u]=void 0;else for(d=0;d<b;d++){if((f=g[d])&&p.key==f.key&&p.type===f.type){g[d]=void 0;break}f=null}ep(e,p,f=f||G,o,i,a,s,c,l),h=p.__e,(d=p.ref)&&f.ref!=d&&(m||(m=[]),f.ref&&m.push(f.ref,null,p),m.push(d,p.__c||h,p)),null!=h?(null==y&&(y=h),"function"==typeof p.type&&p.__k===f.__k?p.__d=c=function e(t,r,n){for(var o,i=t.__k,a=0;i&&a<i.length;a++)(o=i[a])&&(o.__=t,r="function"==typeof o.type?e(o,r,n):ec(n,o,o,i,o.__e,r));return r}(p,c,e):c=ec(e,p,f,g,h,c),"function"==typeof r.type&&(r.__d=c)):c&&f.__e==c&&c.parentNode!=e&&(c=eo(f))}for(r.__e=y,u=b;u--;)null!=g[u]&&function e(t,r,n){var o,i;if(B.unmount&&B.unmount(t),(o=t.ref)&&(o.current&&o.current!==t.__e||ey(o,null,r)),null!=(o=t.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){B.__e(e,r)}o.base=o.__P=null,t.__c=void 0}if(o=t.__k)for(i=0;i<o.length;i++)o[i]&&e(o[i],r,n||"function"!=typeof t.type);n||null==t.__e||ee(t.__e),t.__=t.__e=t.__d=void 0}(g[u],g[u]);if(m)for(u=0;u<m.length;u++)ey(m[u],m[++u],m[++u])}function ec(e,t,r,n,o,i){var a,s,c;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==r||o!=i||null==o.parentNode)e:if(null==i||i.parentNode!==e)e.appendChild(o),a=null;else{for(s=i,c=0;(s=s.nextSibling)&&c<n.length;c+=1)if(s==o)break e;e.insertBefore(o,i),a=i}return void 0!==a?a:o.nextSibling}function el(e,t,r){"-"===t[0]?e.setProperty(t,r):e[t]=null==r?"":"number"!=typeof r||Y.test(t)?r:r+"px"}function eu(e,t,r,n,o){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||el(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||el(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n||e.addEventListener(t,i?ef:ed,i):e.removeEventListener(t,i?ef:ed,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,r))}}function ed(e){this.l[e.type+!1](B.event?B.event(e):e)}function ef(e){this.l[e.type+!0](B.event?B.event(e):e)}function ep(e,t,r,n,o,i,a,s,c){var l,u,d,f,p,h,y,m,g,b,_,w,v,k,S,R=t.type;if(void 0!==t.constructor)return null;null!=r.__h&&(c=r.__h,s=t.__e=r.__e,t.__h=null,i=[s]),(l=B.__b)&&l(t);try{e:if("function"==typeof R){if(m=t.props,g=(l=R.contextType)&&n[l.__c],b=l?g?g.props.value:l.__:n,r.__c?y=(u=t.__c=r.__c).__=u.__E:("prototype"in R&&R.prototype.render?t.__c=u=new R(m,b):(t.__c=u=new en(m,b),u.constructor=R,u.render=em),g&&g.sub(u),u.props=m,u.state||(u.state={}),u.context=b,u.__n=n,d=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=R.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=Q({},u.__s)),Q(u.__s,R.getDerivedStateFromProps(m,u.__s))),f=u.props,p=u.state,d)null==R.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==R.getDerivedStateFromProps&&m!==f&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(m,b),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(m,u.__s,b)||t.__v===r.__v){for(u.props=m,u.state=u.__s,t.__v!==r.__v&&(u.__d=!1),u.__v=t,t.__e=r.__e,t.__k=r.__k,t.__k.forEach(function(e){e&&(e.__=t)}),_=0;_<u._sb.length;_++)u.__h.push(u._sb[_]);u._sb=[],u.__h.length&&a.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(m,u.__s,b),null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(f,p,h)})}if(u.context=b,u.props=m,u.__v=t,u.__P=e,w=B.__r,v=0,"prototype"in R&&R.prototype.render){for(u.state=u.__s,u.__d=!1,w&&w(t),l=u.render(u.props,u.state,u.context),k=0;k<u._sb.length;k++)u.__h.push(u._sb[k]);u._sb=[]}else do u.__d=!1,w&&w(t),l=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++v<25);u.state=u.__s,null!=u.getChildContext&&(n=Q(Q({},n),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(h=u.getSnapshotBeforeUpdate(f,p)),S=null!=l&&l.type===er&&null==l.key?l.props.children:l,es(e,Array.isArray(S)?S:[S],t,r,n,o,i,a,s,c),u.base=t.__e,t.__h=null,u.__h.length&&a.push(u),y&&(u.__E=u.__=null),u.__e=!1}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,o,i,a,s){var c,l,u,d=r.props,f=t.props,p=t.type,h=0;if("svg"===p&&(o=!0),null!=i){for(;h<i.length;h++)if((c=i[h])&&"setAttribute"in c==!!p&&(p?c.localName===p:3===c.nodeType)){e=c,i[h]=null;break}}if(null==e){if(null===p)return document.createTextNode(f);e=o?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,f.is&&f),i=null,s=!1}if(null===p)d===f||s&&e.data===f||(e.data=f);else{if(i=i&&z.call(e.childNodes),l=(d=r.props||G).dangerouslySetInnerHTML,u=f.dangerouslySetInnerHTML,!s){if(null!=i)for(d={},h=0;h<e.attributes.length;h++)d[e.attributes[h].name]=e.attributes[h].value;(u||l)&&(u&&(l&&u.__html==l.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,r,n,o){var i;for(i in r)"children"===i||"key"===i||i in t||eu(e,i,null,r[i],n);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||r[i]===t[i]||eu(e,i,t[i],r[i],n)}(e,f,d,o,s),u)t.__k=[];else if(es(e,Array.isArray(h=t.props.children)?h:[h],t,r,n,o&&"foreignObject"!==p,i,a,i?i[0]:r.__k&&eo(r,0),s),null!=i)for(h=i.length;h--;)null!=i[h]&&ee(i[h]);s||("value"in f&&void 0!==(h=f.value)&&(h!==e.value||"progress"===p&&!h||"option"===p&&h!==d.value)&&eu(e,"value",h,d.value,!1),"checked"in f&&void 0!==(h=f.checked)&&h!==e.checked&&eu(e,"checked",h,d.checked,!1))}return e}(r.__e,t,r,n,o,i,a,c);(l=B.diffed)&&l(t)}catch(e){t.__v=null,(c||null!=i)&&(t.__e=s,t.__h=!!c,i[i.indexOf(s)]=null),B.__e(e,t,r)}}function eh(e,t){B.__c&&B.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){B.__e(e,t.__v)}})}function ey(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){B.__e(e,r)}}function em(e,t,r){return this.constructor(e,r)}function eg(e,t){var r,n,o,i;r=e,B.__&&B.__(r,t),o=(n="function"==typeof eg)?null:eg&&eg.__k||t.__k,i=[],ep(t,r=(!n&&eg||t).__k=function(e,t,r){var n,o,i,a={};for(i in t)"key"==i?n=t[i]:"ref"==i?o=t[i]:a[i]=t[i];if(arguments.length>2&&(a.children=arguments.length>3?z.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===a[i]&&(a[i]=e.defaultProps[i]);return et(e,a,n,o,null)}(er,null,[r]),o||G,G,void 0!==t.ownerSVGElement,!n&&eg?[eg]:o?null:t.firstChild?z.call(t.childNodes):null,i,!n&&eg?eg:o?o.__e:t.firstChild,n),eh(i,r)}z=Z.slice,B={__e:function(e,t,r,n){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,n||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},V=0,en.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Q({},this.state),"function"==typeof e&&(e=e(Q({},r),this.props)),e&&Q(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),ei(this))},en.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),ei(this))},en.prototype.render=er,K=[],ea.__r=0;var eb=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,e_=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,ew=/[\s\n\\/='"\0<>]/,ev=/^xlink:?./,ek=/["&<]/;function eS(e){if(!1===ek.test(e+=""))return e;for(var t=0,r=0,n="",o="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=o,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var eR=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},eT=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},eA={},eE=/([A-Z])/g;function ex(e){var t="";for(var r in e){var n=e[r];null!=n&&""!==n&&(t&&(t+=" "),t+="-"==r[0]?r:eA[r]||(eA[r]=r.replace(eE,"-$1").toLowerCase()),t="number"==typeof n&&!1===eb.test(r)?t+": "+n+"px;":t+": "+n+";")}return t||void 0}function eO(e,t){return Array.isArray(t)?t.reduce(eO,e):null!=t&&!1!==t&&e.push(t),e}function eP(){this.__d=!0}function eU(e,t){return{__v:e,context:t,props:e.props,setState:eP,forceUpdate:eP,__d:!0,__h:[]}}function ej(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var eC=[],e$={shallow:!0};eI.render=eI;var eD=[];function eI(e,t,r){t=t||{};var n,o=B.__s;return B.__s=!0,n=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?function e(t,r,n,o,i,a){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return eS(t);var s=n.pretty,c=s&&"string"==typeof s?s:"	";if(Array.isArray(t)){for(var l="",u=0;u<t.length;u++)s&&u>0&&(l+="\n"),l+=e(t[u],r,n,o,i,a);return l}var d,f=t.type,p=t.props,h=!1;if("function"==typeof f){if(h=!0,!n.shallow||!o&&!1!==n.renderRootComponent){if(f===er){var y=[];return eO(y,t.props.children),e(y,r,n,!1!==n.shallowHighOrder,i,a)}var m,g=t.__c=eU(t,r);B.__b&&B.__b(t);var b=B.__r;if(f.prototype&&"function"==typeof f.prototype.render){var _=ej(f,r);(g=t.__c=new f(p,_)).__v=t,g._dirty=g.__d=!0,g.props=p,null==g.state&&(g.state={}),null==g._nextState&&null==g.__s&&(g._nextState=g.__s=g.state),g.context=_,f.getDerivedStateFromProps?g.state=Object.assign({},g.state,f.getDerivedStateFromProps(g.props,g.state)):g.componentWillMount&&(g.componentWillMount(),g.state=g._nextState!==g.state?g._nextState:g.__s!==g.state?g.__s:g.state),b&&b(t),m=g.render(g.props,g.state,g.context)}else for(var w=ej(f,r),v=0;g.__d&&v++<25;)g.__d=!1,b&&b(t),m=f.call(t.__c,p,w);return g.getChildContext&&(r=Object.assign({},r,g.getChildContext())),B.diffed&&B.diffed(t),e(m,r,n,!1!==n.shallowHighOrder,i,a)}f=(d=f).displayName||d!==Function&&d.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=eC.length;n--;)if(eC[n]===e){r=n;break}r<0&&(r=eC.push(e)-1),t="UnnamedComponent"+r}return t}(d)}var k,S,R="<"+f;if(p){var T=Object.keys(p);n&&!0===n.sortAttributes&&T.sort();for(var A=0;A<T.length;A++){var E=T[A],x=p[E];if("children"!==E){if(!ew.test(E)&&(n&&n.allAttributes||"key"!==E&&"ref"!==E&&"__self"!==E&&"__source"!==E)){if("defaultValue"===E)E="value";else if("defaultChecked"===E)E="checked";else if("defaultSelected"===E)E="selected";else if("className"===E){if(void 0!==p.class)continue;E="class"}else i&&ev.test(E)&&(E=E.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===E){if(p.for)continue;E="for"}"style"===E&&x&&"object"==typeof x&&(x=ex(x)),"a"===E[0]&&"r"===E[1]&&"boolean"==typeof x&&(x=String(x));var O=n.attributeHook&&n.attributeHook(E,x,r,n,h);if(O||""===O)R+=O;else if("dangerouslySetInnerHTML"===E)S=x&&x.__html;else if("textarea"===f&&"value"===E)k=x;else if((x||0===x||""===x)&&"function"!=typeof x){if(!(!0!==x&&""!==x||(x=E,n&&n.xml))){R=R+" "+E;continue}if("value"===E){if("select"===f){a=x;continue}"option"===f&&a==x&&void 0===p.selected&&(R+=" selected")}R=R+" "+E+'="'+eS(x)+'"'}}}else k=x}}if(s){var P=R.replace(/\n\s*/," ");P===R||~P.indexOf("\n")?s&&~R.indexOf("\n")&&(R+="\n"):R=P}if(R+=">",ew.test(f))throw Error(f+" is not a valid HTML tag name in "+R);var U,j=e_.test(f)||n.voidElements&&n.voidElements.test(f),C=[];if(S)s&&eT(S)&&(S="\n"+c+eR(S,c)),R+=S;else if(null!=k&&eO(U=[],k).length){for(var $=s&&~R.indexOf("\n"),D=!1,I=0;I<U.length;I++){var L=U[I];if(null!=L&&!1!==L){var N=e(L,r,n,!0,"svg"===f||"foreignObject"!==f&&i,a);if(s&&!$&&eT(N)&&($=!0),N)if(s){var H=N.length>0&&"<"!=N[0];D&&H?C[C.length-1]+=N:C.push(N),D=H}else C.push(N)}}if(s&&$)for(var M=C.length;M--;)C[M]="\n"+c+eR(C[M],c)}if(C.length||S)R+=C.join("");else if(n&&n.xml)return R.substring(0,R.length-1)+" />";return!j||U||S?(s&&~R.indexOf("\n")&&(R+="\n"),R=R+"</"+f+">"):R=R.replace(/>$/," />"),R}(e,t,r):function e(t,r,n,o){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return eS(t);if(eL(t)){for(var i="",a=0;a<t.length;a++)i+=e(t[a],r,n,o);return i}B.__b&&B.__b(t);var s=t.type,c=t.props;if("function"==typeof s){if(s===er)return e(t.props.children,r,n,o);var l,u,d,f,p,h=s.prototype&&"function"==typeof s.prototype.render?(l=r,d=ej(u=t.type,l),f=new u(t.props,d),t.__c=f,f.__v=t,f.__d=!0,f.props=t.props,null==f.state&&(f.state={}),null==f.__s&&(f.__s=f.state),f.context=d,u.getDerivedStateFromProps?f.state=eN({},f.state,u.getDerivedStateFromProps(f.props,f.state)):f.componentWillMount&&(f.componentWillMount(),f.state=f.__s!==f.state?f.__s:f.state),(p=B.__r)&&p(t),f.render(f.props,f.state,f.context)):function(e,t){var r,n=eU(e,t),o=ej(e.type,t);e.__c=n;for(var i=B.__r,a=0;n.__d&&a++<25;)n.__d=!1,i&&i(e),r=e.type.call(n,e.props,o);return r}(t,r),y=t.__c;y.getChildContext&&(r=eN({},r,y.getChildContext()));var m=e(h,r,n,o);return B.diffed&&B.diffed(t),m}var g,b,_="<";if(_+=s,c)for(var w in g=c.children,c){var v,k,S,R=c[w];if(!("key"===w||"ref"===w||"__self"===w||"__source"===w||"children"===w||"className"===w&&"class"in c||"htmlFor"===w&&"for"in c||ew.test(w))){if(k=w="className"===(v=w)?"class":"htmlFor"===v?"for":"defaultValue"===v?"value":"defaultChecked"===v?"checked":"defaultSelected"===v?"selected":n&&ev.test(v)?v.toLowerCase().replace(/^xlink:?/,"xlink:"):v,S=R,R="style"===k&&null!=S&&"object"==typeof S?ex(S):"a"===k[0]&&"r"===k[1]&&"boolean"==typeof S?String(S):S,"dangerouslySetInnerHTML"===w)b=R&&R.__html;else if("textarea"===s&&"value"===w)g=R;else if((R||0===R||""===R)&&"function"!=typeof R){if(!0===R||""===R){R=w,_=_+" "+w;continue}if("value"===w){if("select"===s){o=R;continue}"option"!==s||o!=R||"selected"in c||(_+=" selected")}_=_+" "+w+'="'+eS(R)+'"'}}}var T=_;if(_+=">",ew.test(s))throw Error(s+" is not a valid HTML tag name in "+_);var A="",E=!1;if(b)A+=b,E=!0;else if("string"==typeof g)A+=eS(g),E=!0;else if(eL(g))for(var x=0;x<g.length;x++){var O=g[x];if(null!=O&&!1!==O){var P=e(O,r,"svg"===s||"foreignObject"!==s&&n,o);P&&(A+=P,E=!0)}}else if(null!=g&&!1!==g&&!0!==g){var U=e(g,r,"svg"===s||"foreignObject"!==s&&n,o);U&&(A+=U,E=!0)}if(B.diffed&&B.diffed(t),E)_+=A;else if(e_.test(s))return T+" />";return _+"</"+s+">"}(e,t,!1,void 0),B.__c&&B.__c(e,eD),B.__s=o,eD.length=0,n}var eL=Array.isArray,eN=Object.assign;eI.shallowRender=function(e,t){return eI(e,t,e$)};var eH=0;function eM(e,t,r,n,o){var i,a,s={};for(a in t)"ref"==a?i=t[a]:s[a]=t[a];var c={type:e,props:s,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--eH,__source:o,__self:n};if("function"==typeof e&&(i=e.defaultProps))for(a in i)void 0===s[a]&&(s[a]=i[a]);return B.vnode&&B.vnode(c),c}async function eW(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),i().forEach(e=>{n.searchParams.append(e.name,e.value)});let o=await fetch(n);return o.ok?o.json():void console.error("Failed to fetch options",o)}function o(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(o().querySelectorAll("input[data-form-field]"))}async function a(e,t){let r=o();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await a("authenticate",n)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await a("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=o();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let eq={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},eF=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function eJ({html:e,title:t,status:r,cookies:n,theme:o,headTags:i}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${eF}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${o?.colorScheme??"auto"}"><div class="page">${eI(e)}</div></body></html>`}}function ez(e){let{url:t,theme:r,query:n,cookies:o,pages:i,providers:a}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:o,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:o,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new l.P8("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:o}}let c=a?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),u="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;u=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return eJ({cookies:o,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:o,email:i,error:a}=e;"undefined"!=typeof document&&o?.brandColor&&document.documentElement.style.setProperty("--brand-color",o.brandColor),"undefined"!=typeof document&&o?.buttonText&&document.documentElement.style.setProperty("--button-text-color",o.buttonText);let s=a&&(eq[a]??eq.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return eM("div",{className:"signin",children:[o?.brandColor&&eM("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${o.brandColor}}`}}),o?.buttonText&&eM("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${o.buttonText}
        }
      `}}),eM("div",{className:"card",children:[s&&eM("div",{className:"error",children:eM("p",{children:s})}),o?.logo&&eM("img",{src:o.logo,alt:"Logo",className:"logo"}),r.map((e,o)=>{let a,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:a="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??a??"#fff";return eM("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?eM("form",{action:e.signinUrl,method:"POST",children:[eM("input",{type:"hidden",name:"csrfToken",value:t}),n&&eM("input",{type:"hidden",name:"callbackUrl",value:n}),eM("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${l} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${l} 30%, #000)`},tabIndex:0,children:[eM("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&eM("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&o>0&&"email"!==r[o-1].type&&"credentials"!==r[o-1].type&&"webauthn"!==r[o-1].type&&eM("hr",{}),"email"===e.type&&eM("form",{action:e.signinUrl,method:"POST",children:[eM("input",{type:"hidden",name:"csrfToken",value:t}),eM("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),eM("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),eM("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&eM("form",{action:e.callbackUrl,method:"POST",children:[eM("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>eM("div",{children:[eM("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),eM("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),eM("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&eM("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[eM("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>eM("div",{children:[eM("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),eM("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),eM("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&o+1<r.length&&eM("hr",{})]},e.id)})]}),c&&eM(er,{children:eM("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${eW})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:u})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:o}:eJ({cookies:o,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return eM("div",{className:"signout",children:[n?.brandColor&&eM("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&eM("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),eM("div",{className:"card",children:[n?.logo&&eM("img",{src:n.logo,alt:"Logo",className:"logo"}),eM("h1",{children:"Signout"}),eM("p",{children:"Are you sure you want to sign out?"}),eM("form",{action:t?.toString(),method:"POST",children:[eM("input",{type:"hidden",name:"csrfToken",value:r}),eM("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:i.verifyRequest,cookies:o}:eJ({cookies:o,theme:r,html:function(e){let{url:t,theme:r}=e;return eM("div",{className:"verify-request",children:[r.brandColor&&eM("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),eM("div",{className:"card",children:[r.logo&&eM("img",{src:r.logo,alt:"Logo",className:"logo"}),eM("h1",{children:"Check your email"}),eM("p",{children:"A sign in link has been sent to your email address."}),eM("p",{children:eM("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:o}:eJ({cookies:o,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,o=`${t}/signin`,i={default:{status:200,heading:"Error",message:eM("p",{children:eM("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:eM("div",{children:[eM("p",{children:"There is a problem with the server configuration."}),eM("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:eM("div",{children:[eM("p",{children:"You do not have permission to sign in."}),eM("p",{children:eM("a",{className:"button",href:o,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:eM("div",{children:[eM("p",{children:"The sign in link is no longer valid."}),eM("p",{children:"It may have been used already or it may have expired."})]}),signin:eM("a",{className:"button",href:o,children:"Sign in"})}},{status:a,heading:s,message:c,signin:l}=i[r]??i.default;return{status:a,html:eM("div",{className:"error",children:[n?.brandColor&&eM("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),eM("div",{className:"card",children:[n?.logo&&eM("img",{src:n?.logo,alt:"Logo",className:"logo"}),eM("h1",{children:s}),eM("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function eB(e,t=Date.now()){return new Date(t+1e3*e)}async function eV(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:o,jwt:i,events:a,session:{strategy:s,generateSessionToken:c}}=n;if(!o)return{user:t,account:r};let u=r,{createUser:d,updateUser:f,getUser:p,getUserByAccount:h,getUserByEmail:y,linkAccount:m,createSession:g,getSessionAndUser:b,deleteSession:_}=o,w=null,v=null,k=!1,S="jwt"===s;if(e)if(S)try{let t=n.cookies.sessionToken.name;(w=await i.decode({...i,token:e,salt:t}))&&"sub"in w&&w.sub&&(v=await p(w.sub))}catch{}else{let t=await b(e);t&&(w=t.session,v=t.user)}if("email"===u.type){let r=await y(t.email);return r?(v?.id!==r.id&&!S&&e&&await _(e),v=await f({id:r.id,emailVerified:new Date}),await a.updateUser?.({user:v})):(v=await d({...t,emailVerified:new Date}),await a.createUser?.({user:v}),k=!0),{session:w=S?{}:await g({sessionToken:c(),userId:v.id,expires:eB(n.session.maxAge)}),user:v,isNewUser:k}}if("webauthn"===u.type){let e=await h({providerAccountId:u.providerAccountId,provider:u.provider});if(e){if(v){if(e.id===v.id){let e={...u,userId:v.id};return{session:w,user:v,isNewUser:k,account:e}}throw new l.WS("The account is already associated with another user",{provider:u.provider})}w=S?{}:await g({sessionToken:c(),userId:e.id,expires:eB(n.session.maxAge)});let t={...u,userId:e.id};return{session:w,user:e,isNewUser:k,account:t}}{if(v){await m({...u,userId:v.id}),await a.linkAccount?.({user:v,account:u,profile:t});let e={...u,userId:v.id};return{session:w,user:v,isNewUser:k,account:e}}if(t.email?await y(t.email):null)throw new l.WS("Another account already exists with the same e-mail address",{provider:u.provider});v=await d({...t}),await a.createUser?.({user:v}),await m({...u,userId:v.id}),await a.linkAccount?.({user:v,account:u,profile:t}),w=S?{}:await g({sessionToken:c(),userId:v.id,expires:eB(n.session.maxAge)});let e={...u,userId:v.id};return{session:w,user:v,isNewUser:!0,account:e}}}let R=await h({providerAccountId:u.providerAccountId,provider:u.provider});if(R){if(v){if(R.id===v.id)return{session:w,user:v,isNewUser:k};throw new l.XP("The account is already associated with another user",{provider:u.provider})}return{session:w=S?{}:await g({sessionToken:c(),userId:R.id,expires:eB(n.session.maxAge)}),user:R,isNewUser:k}}{let{provider:e}=n,{type:r,provider:o,providerAccountId:i,userId:s,...f}=u;if(u=Object.assign(e.account(f)??{},{providerAccountId:i,provider:o,type:r,userId:s}),v)return await m({...u,userId:v.id}),await a.linkAccount?.({user:v,account:u,profile:t}),{session:w,user:v,isNewUser:k};let p=t.email?await y(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)v=p,k=!1;else throw new l.XP("Another account already exists with the same e-mail address",{provider:u.provider})}else v=await d({...t,emailVerified:null}),k=!0;return await a.createUser?.({user:v}),await m({...u,userId:v.id}),await a.linkAccount?.({user:v,account:u,profile:t}),{session:w=S?{}:await g({sessionToken:c(),userId:v.id,expires:eB(n.session.maxAge)}),user:v,isNewUser:k}}}function eK(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(n="oauth4webapi/v3.5.2");let eX="ERR_INVALID_ARG_VALUE",eG="ERR_INVALID_ARG_TYPE";function eZ(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let eY=Symbol(),eQ=Symbol(),e0=Symbol(),e1=Symbol(),e2=Symbol(),e3=Symbol(),e5=Symbol(),e4=new TextEncoder,e6=new TextDecoder;function e8(e){return"string"==typeof e?e4.encode(e):e6.decode(e)}function e9(e){return"string"==typeof e?i(e):o(e)}o=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},i=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw eZ("The input to be decoded is not correctly encoded.",eX,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw eZ("The input to be decoded is not correctly encoded.",eX,e)}};class e7 extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rc,Error.captureStackTrace?.(this,this.constructor)}}class te extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function tt(e,t,r){return new te(e,{code:t,cause:r})}function tr(e,t){if(!(e instanceof CryptoKey))throw eZ(`${t} must be a CryptoKey`,eG)}function tn(e,t){if(tr(e,t),"private"!==e.type)throw eZ(`${t} must be a private CryptoKey`,eX)}function to(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function ti(e){eK(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(n&&!t.has("user-agent")&&t.set("user-agent",n),t.has("authorization"))throw eZ('"options.headers" must not include the "authorization" header name',eX);return t}function ta(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw eZ('"options.signal" must return or be an instance of AbortSignal',eG);return e}function ts(e){return e.includes("//")?e.replace("//","/"):e}async function tc(e,t,r,n){if(!(e instanceof URL))throw eZ(`"${t}" must be an instance of URL`,eG);tx(e,n?.[eY]!==!0);let o=r(new URL(e.href)),i=ti(n?.headers);return i.set("accept","application/json"),(n?.[e1]||fetch)(o.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:n?.signal?ta(n.signal):void 0})}async function tl(e,t){return tc(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=ts(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=ts(`${n}/${r.pathname}`);break;default:throw eZ('"options.algorithm" must be "oidc" (default), or "oauth2"',eX)}return e},t)}function tu(e,t,r,n,o){try{if("number"!=typeof e||!Number.isFinite(e))throw eZ(`${r} must be a number`,eG,o);if(e>0)return;if(t){if(0!==e)throw eZ(`${r} must be a non-negative number`,eX,o);return}throw eZ(`${r} must be a positive number`,eX,o)}catch(e){if(n)throw tt(e.message,n,o);throw e}}function td(e,t,r,n){try{if("string"!=typeof e)throw eZ(`${t} must be a string`,eG,n);if(0===e.length)throw eZ(`${t} must not be empty`,eX,n)}catch(e){if(r)throw tt(e.message,r,n);throw e}}async function tf(e,t){if(!(e instanceof URL)&&e!==rB)throw eZ('"expectedIssuerIdentifier" must be an instance of URL',eG);if(!eK(t,Response))throw eZ('"response" must be an instance of Response',eG);if(200!==t.status)throw tt('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',ry,t);rT(t);let r=await rJ(t);if(td(r.issuer,'"response" body "issuer" property',rf,{body:r}),e!==rB&&new URL(r.issuer).href!==e.href)throw tt('"response" body "issuer" property does not match the expected value',rw,{expected:e.href,body:r,attribute:"issuer"});return r}function tp(e){var t=e,r="application/json";if(tK(t)!==r)throw th(t,r)}function th(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return tt(r,rh,e)}function ty(){return e9(crypto.getRandomValues(new Uint8Array(32)))}async function tm(e){return td(e,"codeVerifier"),e9(await crypto.subtle.digest("SHA-256",e8(e)))}function tg(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new e7("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new e7("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new e7("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new e7("unsupported CryptoKey algorithm name",{cause:e})}}function tb(e){let t=e?.[eQ];return"number"==typeof t&&Number.isFinite(t)?t:0}function t_(e){let t=e?.[e0];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function tw(){return Math.floor(Date.now()/1e3)}function tv(e){if("object"!=typeof e||null===e)throw eZ('"as" must be an object',eG);td(e.issuer,'"as.issuer"')}function tk(e){if("object"!=typeof e||null===e)throw eZ('"client" must be an object',eG);td(e.client_id,'"client.client_id"')}function tS(e,t){let r=tw()+tb(t);return{jti:ty(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function tR(e,t,r){if(!r.usages.includes("sign"))throw eZ('CryptoKey instances used for signing assertions must include "sign" in their "usages"',eX);let n=`${e9(e8(JSON.stringify(e)))}.${e9(e8(JSON.stringify(t)))}`,o=e9(await crypto.subtle.sign(rP(r),r,e8(n)));return`${n}.${o}`}async function tT(e){let{kty:t,e:r,n,x:o,y:i,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:o,y:i,crv:s};return a.set(e,c),c}async function tA(e){return(a||=new WeakMap).get(e)||tT(e)}let tE=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function tx(e,t){if(t&&"https:"!==e.protocol)throw tt("only requests to HTTPS are allowed",rm,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw tt("only HTTP and HTTPS requests are allowed",rg,e)}function tO(e,t,r,n){let o;if("string"!=typeof e||!(o=tE(e)))throw tt(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?rk:rS,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return tx(o,n),o}function tP(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?tO(e.mtls_endpoint_aliases[t],t,r,n):tO(e[t],t,r,n)}class tU extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rs,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class tj extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rl,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class tC extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=ra,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let t$="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",tD=RegExp("^[,\\s]*("+t$+")\\s(.*)"),tI=RegExp("^[,\\s]*("+t$+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),tL=RegExp("^[,\\s]*"+("("+t$+")\\s*=\\s*(")+t$+")[,\\s]*(.*)"),tN=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function tH(e){if(e.status>399&&e.status<500){rT(e),tp(e);try{let t=await e.clone().json();if(to(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function tM(e,t,r){if(e.status!==t){let t;if(t=await tH(e))throw await e.body?.cancel(),new tU("server responded with an error in the response body",{cause:t,response:e});throw tt(`"response" is not a conform ${r} response (unexpected HTTP status code)`,ry,e)}}function tW(e){if(!t8.has(e))throw eZ('"options.DPoP" is not a valid DPoPHandle',eX)}async function tq(e,t,r,n,o,i){if(td(e,'"accessToken"'),!(r instanceof URL))throw eZ('"url" must be an instance of URL',eG);tx(r,i?.[eY]!==!0),n=ti(n),i?.DPoP&&(tW(i.DPoP),await i.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let a=await (i?.[e1]||fetch)(r.href,{body:o,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:i?.signal?ta(i.signal):void 0});return i?.DPoP?.cacheNonce(a),a}async function tF(e,t,r,n){tv(e),tk(t);let o=tP(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[eY]!==!0),i=ti(n?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),tq(r,"GET",o,i,null,{...n,[eQ]:tb(t)})}function tJ(e,t,r,n){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return tw()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function tz(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}async function tB(e,t,r){var n;let o,i,a,{alg:c,kid:l}=r;if(function(e){if(!rx(e.alg))throw new e7('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!s?.has(e)&&!("object"!=typeof(n=t?.[e5])||null===n||!("uat"in n)||"number"!=typeof n.uat||tw()-n.uat>=300)&&"jwks"in n&&to(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,to)&&tJ(e,t?.[e5].jwks,t?.[e5].uat),s?.has(e)){if({jwks:o,age:i}=s.get(e),i>=300)return tz(e,t?.[e5]),tB(e,t,r)}else o=await rA(e,t).then(rE),i=0,tJ(e,o,tw(),t?.[e5]);switch(c.slice(0,2)){case"RS":case"PS":a="RSA";break;case"ES":a="EC";break;case"Ed":a="OKP";break;default:throw new e7("unsupported JWS algorithm",{cause:{alg:c}})}let u=o.keys.filter(e=>{if(e.kty!==a||void 0!==l&&l!==e.kid||void 0!==e.alg&&c!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===c&&"P-256"!==e.crv:case"ES384"===c&&"P-384"!==e.crv:case"ES512"===c&&"P-521"!==e.crv:case"Ed25519"===c&&"Ed25519"!==e.crv:case"EdDSA"===c&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:f}=u;if(!f){if(i>=60)return tz(e,t?.[e5]),tB(e,t,r);throw tt("error when selecting a JWT verification key, no applicable keys found",rv,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==f)throw tt('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',rv,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return rq(c,d)}let tV=Symbol();function tK(e){return e.headers.get("content-type")?.split(";")[0]}async function tX(e,t,r,n,o){let i;if(tv(e),tk(t),!eK(n,Response))throw eZ('"response" must be an instance of Response',eG);if(t2(n),200!==n.status)throw tt('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',ry,n);if(rT(n),"application/jwt"===tK(n)){let{claims:r,jwt:a}=await rj(await n.text(),rL.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),tb(t),t_(t),o?.[e3]).then(t3.bind(void 0,t.client_id)).then(t4.bind(void 0,e));tQ.set(n,a),i=r}else{if(t.userinfo_signed_response_alg)throw tt("JWT UserInfo Response expected",ru,n);i=await rJ(n)}if(td(i.sub,'"response" body "sub" property',rf,{body:i}),r===tV);else if(td(r,'"expectedSubject"'),i.sub!==r)throw tt('unexpected "response" body "sub" property value',rw,{expected:r,body:i,attribute:"sub"});return i}async function tG(e,t,r,n,o,i,a){return await r(e,t,o,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(a?.[e1]||fetch)(n.href,{body:o,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:a?.signal?ta(a.signal):void 0})}async function tZ(e,t,r,n,o,i){let a=tP(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[eY]!==!0);o.set("grant_type",n);let s=ti(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(tW(i.DPoP),await i.DPoP.addProof(a,s,"POST"));let c=await tG(e,t,r,a,o,s,i);return i?.DPoP?.cacheNonce(c),c}let tY=new WeakMap,tQ=new WeakMap;function t0(e){if(!e.id_token)return;let t=tY.get(e);if(!t)throw eZ('"ref" was already garbage collected or did not resolve from the proper sources',eX);return t}async function t1(e,t,r,n,o){if(tv(e),tk(t),!eK(r,Response))throw eZ('"response" must be an instance of Response',eG);t2(r),await tM(r,200,"Token Endpoint"),rT(r);let i=await rJ(r);if(td(i.access_token,'"response" body "access_token" property',rf,{body:i}),td(i.token_type,'"response" body "token_type" property',rf,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new e7("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;tu(e,!1,'"response" body "expires_in" property',rf,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&td(i.refresh_token,'"response" body "refresh_token" property',rf,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw tt('"response" body "scope" property must be a string',rf,{body:i});if(void 0!==i.id_token){td(i.id_token,'"response" body "id_token" property',rf,{body:i});let a=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&a.push("auth_time"),void 0!==t.default_max_age&&(tu(t.default_max_age,!1,'"client.default_max_age"'),a.push("auth_time")),n?.length&&a.push(...n);let{claims:s,jwt:c}=await rj(i.id_token,rL.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),tb(t),t_(t),o?.[e3]).then(re.bind(void 0,a)).then(t6.bind(void 0,e)).then(t5.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw tt('ID Token "aud" (audience) claim includes additional untrusted audiences',r_,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw tt('unexpected ID Token "azp" (authorized party) claim value',r_,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&tu(s.auth_time,!1,'ID Token "auth_time" (authentication time)',rf,{claims:s}),tQ.set(r,c),tY.set(i,s)}return i}function t2(e){let t;if(t=function(e){if(!eK(e,Response))throw eZ('"response" must be an instance of Response',eG);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(tD),o=t?.["1"].toLowerCase();if(n=t?.["2"],!o)return;let i={};for(;n;){let r,o;if(t=n.match(tI)){if([,r,o,n]=t,o.includes("\\"))try{o=JSON.parse(`"${o}"`)}catch{}i[r.toLowerCase()]=o;continue}if(t=n.match(tL)){[,r,o,n]=t,i[r.toLowerCase()]=o;continue}if(t=n.match(tN)){if(Object.keys(i).length)break;[,e,n]=t;break}return}let a={scheme:o,parameters:i};e&&(a.token68=e),r.push(a)}if(r.length)return r}(e))throw new tC("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function t3(e,t){return void 0!==t.claims.aud?t5(e,t):t}function t5(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw tt('unexpected JWT "aud" (audience) claim value',r_,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw tt('unexpected JWT "aud" (audience) claim value',r_,{expected:e,claims:t.claims,claim:"aud"});return t}function t4(e,t){return void 0!==t.claims.iss?t6(e,t):t}function t6(e,t){let r=e[rV]?.(t)??e.issuer;if(t.claims.iss!==r)throw tt('unexpected JWT "iss" (issuer) claim value',r_,{expected:r,claims:t.claims,claim:"iss"});return t}let t8=new WeakSet;async function t9(e,t,r,n,o,i,a){if(tv(e),tk(t),!t8.has(n))throw eZ('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',eX);td(o,'"redirectUri"');let s=rN(n,"code");if(!s)throw tt('no authorization code in "callbackParameters"',rf);let c=new URLSearchParams(a?.additionalParameters);return c.set("redirect_uri",o),c.set("code",s),i!==rz&&(td(i,'"codeVerifier"'),c.set("code_verifier",i)),tZ(e,t,r,"authorization_code",c,a)}let t7={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function re(e,t){for(let r of e)if(void 0===t.claims[r])throw tt(`JWT "${r}" (${t7[r]}) claim missing`,rf,{claims:t.claims});return t}let rt=Symbol(),rr=Symbol();async function rn(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?ro(e,t,r,n.expectedNonce,n.maxAge,{[e3]:n[e3]}):ri(e,t,r,n)}async function ro(e,t,r,n,o,i){let a=[];switch(n){case void 0:n=rt;break;case rt:break;default:td(n,'"expectedNonce" argument'),a.push("nonce")}switch(o??=t.default_max_age){case void 0:o=rr;break;case rr:break;default:tu(o,!1,'"maxAge" argument'),a.push("auth_time")}let s=await t1(e,t,r,a,i);td(s.id_token,'"response" body "id_token" property',rf,{body:s});let c=t0(s);if(o!==rr){let e=tw()+tb(t),r=t_(t);if(c.auth_time+o<e-r)throw tt("too much time has elapsed since the last End-User authentication",rb,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===rt){if(void 0!==c.nonce)throw tt('unexpected ID Token "nonce" claim value',r_,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw tt('unexpected ID Token "nonce" claim value',r_,{expected:n,claims:c,claim:"nonce"});return s}async function ri(e,t,r,n){let o=await t1(e,t,r,void 0,n),i=t0(o);if(i){if(void 0!==t.default_max_age){tu(t.default_max_age,!1,'"client.default_max_age"');let e=tw()+tb(t),r=t_(t);if(i.auth_time+t.default_max_age<e-r)throw tt("too much time has elapsed since the last End-User authentication",rb,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw tt('unexpected ID Token "nonce" claim value',r_,{expected:void 0,claims:i,claim:"nonce"})}return o}let ra="OAUTH_WWW_AUTHENTICATE_CHALLENGE",rs="OAUTH_RESPONSE_BODY_ERROR",rc="OAUTH_UNSUPPORTED_OPERATION",rl="OAUTH_AUTHORIZATION_RESPONSE_ERROR",ru="OAUTH_JWT_USERINFO_EXPECTED",rd="OAUTH_PARSE_ERROR",rf="OAUTH_INVALID_RESPONSE",rp="OAUTH_INVALID_REQUEST",rh="OAUTH_RESPONSE_IS_NOT_JSON",ry="OAUTH_RESPONSE_IS_NOT_CONFORM",rm="OAUTH_HTTP_REQUEST_FORBIDDEN",rg="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",rb="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",r_="OAUTH_JWT_CLAIM_COMPARISON_FAILED",rw="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",rv="OAUTH_KEY_SELECTION_FAILED",rk="OAUTH_MISSING_SERVER_METADATA",rS="OAUTH_INVALID_SERVER_METADATA";function rR(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw tt('unexpected JWT "typ" header parameter value',rf,{header:t.header});return t}function rT(e){if(e.bodyUsed)throw eZ('"response" body has been used already',eX)}async function rA(e,t){tv(e);let r=tP(e,"jwks_uri",!1,t?.[eY]!==!0),n=ti(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[e1]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?ta(t.signal):void 0})}async function rE(e){if(!eK(e,Response))throw eZ('"response" must be an instance of Response',eG);if(200!==e.status)throw tt('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',ry,e);rT(e);let t=await rJ(e,e=>(function(e,...t){if(!t.includes(tK(e)))throw th(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw tt('"response" body "keys" property must be an array',rf,{body:t});if(!Array.prototype.every.call(t.keys,to))throw tt('"response" body "keys" property members must be JWK formatted objects',rf,{body:t});return t}function rx(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function rO(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new e7(`unsupported ${t.name} modulusLength`,{cause:e})}function rP(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new e7("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(rO(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new e7("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return rO(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new e7("unsupported CryptoKey algorithm name",{cause:e})}async function rU(e,t,r,n){let o=e8(`${e}.${t}`),i=rP(r);if(!await crypto.subtle.verify(i,r,n,o))throw tt("JWT signature verification failed",rf,{key:r,data:o,signature:n,algorithm:i})}async function rj(e,t,r,n,o){let i,a,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==o)e=await o(e),{0:s,1:c,length:l}=e.split(".");else throw new e7("JWE decryption is not configured",{cause:e});if(3!==l)throw tt("Invalid JWT",rf,e);try{i=JSON.parse(e8(e9(s)))}catch(e){throw tt("failed to parse JWT Header body as base64url encoded JSON",rd,e)}if(!to(i))throw tt("JWT Header must be a top level object",rf,e);if(t(i),void 0!==i.crit)throw new e7('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{a=JSON.parse(e8(e9(c)))}catch(e){throw tt("failed to parse JWT Payload body as base64url encoded JSON",rd,e)}if(!to(a))throw tt("JWT Payload must be a top level object",rf,e);let u=tw()+r;if(void 0!==a.exp){if("number"!=typeof a.exp)throw tt('unexpected JWT "exp" (expiration time) claim type',rf,{claims:a});if(a.exp<=u-n)throw tt('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',rb,{claims:a,now:u,tolerance:n,claim:"exp"})}if(void 0!==a.iat&&"number"!=typeof a.iat)throw tt('unexpected JWT "iat" (issued at) claim type',rf,{claims:a});if(void 0!==a.iss&&"string"!=typeof a.iss)throw tt('unexpected JWT "iss" (issuer) claim type',rf,{claims:a});if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw tt('unexpected JWT "nbf" (not before) claim type',rf,{claims:a});if(a.nbf>u+n)throw tt('unexpected JWT "nbf" (not before) claim value',rb,{claims:a,now:u,tolerance:n,claim:"nbf"})}if(void 0!==a.aud&&"string"!=typeof a.aud&&!Array.isArray(a.aud))throw tt('unexpected JWT "aud" (audience) claim type',rf,{claims:a});return{header:i,claims:a,jwt:e}}async function rC(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new e7(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let o=await crypto.subtle.digest(n,e8(e));return e9(o.slice(0,o.byteLength/2))}async function r$(e,t,r,n){return t===await rC(e,r,n)}async function rD(e){if(e.bodyUsed)throw eZ("form_post Request instances must contain a readable body",eX,{cause:e});return e.text()}async function rI(e){if("POST"!==e.method)throw eZ("form_post responses are expected to use the POST method",eX,{cause:e});if("application/x-www-form-urlencoded"!==tK(e))throw eZ("form_post responses are expected to use the application/x-www-form-urlencoded content-type",eX,{cause:e});return rD(e)}function rL(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw tt('unexpected JWT "alg" header parameter',rf,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw tt('unexpected JWT "alg" header parameter',rf,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw tt('unexpected JWT "alg" header parameter',rf,{header:n,expected:r,reason:"default value"});return}throw tt('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function rN(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw tt(`"${t}" parameter must be provided only once`,rf);return r}let rH=Symbol(),rM=Symbol();function rW(e,t,r,n){var o;if(tv(e),tk(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw eZ('"parameters" must be an instance of URLSearchParams, or URL',eG);if(rN(r,"response"))throw tt('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',rf,{parameters:r});let i=rN(r,"iss"),a=rN(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw tt('response parameter "iss" (issuer) missing',rf,{parameters:r});if(i&&i!==e.issuer)throw tt('unexpected "iss" (issuer) response parameter value',rf,{expected:e.issuer,parameters:r});switch(n){case void 0:case rM:if(void 0!==a)throw tt('unexpected "state" response parameter encountered',rf,{expected:void 0,parameters:r});break;case rH:break;default:if(td(n,'"expectedState" argument'),a!==n)throw tt(void 0===a?'response parameter "state" missing':'unexpected "state" response parameter value',rf,{expected:n,parameters:r})}if(rN(r,"error"))throw new tj("authorization response from the server is an error",{cause:r});let s=rN(r,"id_token"),c=rN(r,"token");if(void 0!==s||void 0!==c)throw new e7("implicit and hybrid flows are not supported");return o=new URLSearchParams(r),t8.add(o),o}async function rq(e,t){let{ext:r,key_ops:n,use:o,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new e7("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function rF(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function rJ(e,t=tp){let r;try{r=await e.json()}catch(r){throw t(e),tt('failed to parse "response" body as JSON',rd,r)}if(!to(r))throw tt('"response" body must be a top level object',rf,{body:r});return r}let rz=Symbol(),rB=Symbol(),rV=Symbol();async function rK(e,t,r){let{cookies:n,logger:o}=r,i=n[e],a=new Date;a.setTime(a.getTime()+9e5),o.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:a});let s=await (0,b.lF)({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:a};return{name:i.name,value:s,options:c}}async function rX(e,t,r){try{let{logger:n,cookies:o,jwt:i}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new l.k9(`${e} cookie was missing`);let a=await (0,b.D4)({...i,token:t,salt:o[e].name});if(a?.value)return a.value;throw Error("Invalid cookie")}catch(t){throw new l.k9(`${e} value could not be parsed`,{cause:t})}}function rG(e,t,r){let{logger:n,cookies:o}=t,i=o[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...o[e].options,maxAge:0}})}function rZ(e,t){return async function(r,n,o){let{provider:i,logger:a}=o;if(!i?.checks?.includes(e))return;let s=r?.[o.cookies[t].name];a.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await rX(t,s,o);return rG(t,o,n),c}}let rY={async create(e){let t=ty(),r=await tm(t);return{cookie:await rK("pkceCodeVerifier",t,e),value:r}},use:rZ("pkce","pkceCodeVerifier")},rQ="encodedState",r0={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new l.k9("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:ty()},o=await (0,b.lF)({secret:e.jwt.secret,token:n,salt:rQ,maxAge:900});return{cookie:await rK("state",o,e),value:o}},use:rZ("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await (0,b.D4)({secret:t.jwt.secret,token:e,salt:rQ});if(r)return r;throw Error("Invalid state")}catch(e){throw new l.k9("State could not be decoded",{cause:e})}}},r1={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=ty();return{cookie:await rK("nonce",t,e),value:t}},use:rZ("nonce","nonce")},r2="encodedWebauthnChallenge",r3={create:async(e,t,r)=>({cookie:await rK("webauthnChallenge",await (0,b.lF)({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:r2,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],o=await rX("webauthnChallenge",n,e),i=await (0,b.D4)({secret:e.jwt.secret,token:o,salt:r2});if(rG("webauthnChallenge",e,r),!i)throw new l.k9("WebAuthn challenge was missing");return i}};var r5=r(16848),r4=r(1663),r6=r(22737),r8=r(23585);function r9(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function r7(e,t,r){let n,o,i,{logger:a,provider:s}=r,{token:c,userinfo:u}=s;if(c?.url&&"authjs.dev"!==c.url.host||u?.url&&"authjs.dev"!==u.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:u?.url.toString()};else{let e=new URL(s.issuer),t=await tl(e,{[eY]:!0,[e1]:s[L]});if(!(n=await tf(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let d={client_id:s.clientId,...s.client};switch(d.token_endpoint_auth_method){case void 0:case"client_secret_basic":o=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=r9(e),n=r9(t),o=btoa(`${r}:${n}`);return`Basic ${o}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var f;td(f=s.clientSecret,'"clientSecret"'),o=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",f)};break;case"client_secret_jwt":o=function(e,t){let r;td(e,'"clientSecret"');let n=void 0;return async(t,o,i,a)=>{r||=await crypto.subtle.importKey("raw",e8(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=tS(t,o);n?.(s,c);let l=`${e9(e8(JSON.stringify(s)))}.${e9(e8(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,e8(l));i.set("client_id",o.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${l}.${e9(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":o=function(e,t){var r;let{key:n,kid:o}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&td(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return tn(n,'"clientPrivateKey.key"'),async(e,r,i,a)=>{let s={alg:tg(n),kid:o},c=tS(e,r);t?.[e2]?.(s,c),i.set("client_id",r.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await tR(s,c,n))}}(s.token.clientPrivateKey,{[e2](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":o=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],h=await r0.use(t,p,r);try{i=rW(n,d,new URLSearchParams(e),s.checks.includes("state")?h:rH)}catch(e){if(e instanceof tj){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw a.debug("OAuthCallbackError",t),new l.rk("OAuth Provider returned an error",t)}throw e}let y=await rY.use(t,p,r),m=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(m=s.redirectProxyUrl);let g=await t9(n,d,o,i,m,y??"decoy",{[eY]:!0,[e1]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[L]??fetch)(...e))});s.token?.conform&&(g=await s.token.conform(g.clone())??g);let b={},_="oidc"===s.type;if(s[N])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let{tid:e}=function(e){let t,r;if("string"!=typeof e)throw new r8.Dp("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:o}=e.split(".");if(5===o)throw new r8.Dp("Only JWTs using Compact JWS serialization can be decoded");if(3!==o)throw new r8.Dp("Invalid JWT");if(!n)throw new r8.Dp("JWTs must contain a payload");try{t=(0,r5.D)(n)}catch{throw new r8.Dp("Failed to base64url decode the payload")}try{r=JSON.parse(r4.D0.decode(t))}catch{throw new r8.Dp("Failed to parse the decoded payload as JSON")}if(!(0,r6.A)(r))throw new r8.Dp("Invalid JWT Claims Set");return r}((await g.clone().json()).id_token);if("string"==typeof e){let t=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(t,e)),o=await tl(r,{[e1]:s[L]});n=await tf(r,o)}}}let w=await rn(n,d,g,{expectedNonce:await r1.use(t,p,r),requireIdToken:_});if(_){let t=t0(w);if(b=t,s[N]&&"apple"===s.id)try{b.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await tF(n,d,w.access_token,{[e1]:s[L],[eY]:!0});b=await tX(n,d,t.sub,e)}}else if(u?.request){let e=await u.request({tokens:w,provider:s});e instanceof Object&&(b=e)}else if(u?.url){let e=await tF(n,d,w.access_token,{[e1]:s[L]});b=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await ne(b,s,w,a),profile:b,cookies:p}}async function ne(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new l._z(r,{provider:t.id}))}}async function nt(e,t,r,n){let o=await na(e,t,r),{cookie:i}=await r3.create(e,o.challenge,r);return{status:200,cookies:[...n??[],i],body:{action:"register",options:o},headers:{"Content-Type":"application/json"}}}async function nr(e,t,r,n){let o=await ni(e,t,r),{cookie:i}=await r3.create(e,o.challenge);return{status:200,cookies:[...n??[],i],body:{action:"authenticate",options:o},headers:{"Content-Type":"application/json"}}}async function nn(e,t,r){let n,{adapter:o,provider:i}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new l.lR("Invalid WebAuthn Authentication response");let s=nl(nc(a.id)),c=await o.getAuthenticator(s);if(!c)throw new l.lR(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:u}=await r3.use(e,t.cookies,r);try{var d;let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:u,response:a,authenticator:{...d=c,credentialDeviceType:d.credentialDeviceType,transports:nu(d.transports),credentialID:nc(d.credentialID),credentialPublicKey:nc(d.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new l.w2(e)}let{verified:f,authenticationInfo:p}=n;if(!f)throw new l.w2("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await o.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new l.om(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let h=await o.getAccount(c.providerAccountId,i.id);if(!h)throw new l.lR(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let y=await o.getUser(h.userId);if(!y)throw new l.lR(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:h.userId})}`);return{account:h,user:y}}async function no(e,t,r){var n;let o,{provider:i}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new l.lR("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await r3.use(e,t.cookies,r);if(!c)throw new l.lR("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);o=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:a,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new l.w2(e)}if(!o.verified||!o.registrationInfo)throw new l.w2("WebAuthn registration response could not be verified");let u={providerAccountId:nl(o.registrationInfo.credentialID),provider:e.provider.id,type:i.type},d={providerAccountId:u.providerAccountId,counter:o.registrationInfo.counter,credentialID:nl(o.registrationInfo.credentialID),credentialPublicKey:nl(o.registrationInfo.credentialPublicKey),credentialBackedUp:o.registrationInfo.credentialBackedUp,credentialDeviceType:o.registrationInfo.credentialDeviceType,transports:(n=a.response.transports,n?.join(","))};return{user:c,account:u,authenticator:d}}async function ni(e,t,r){let{provider:n,adapter:o}=e,i=r&&r.id?await o.listAuthenticatorsByUserId(r.id):null,a=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:a.id,allowCredentials:i?.map(e=>({id:nc(e.credentialID),type:"public-key",transports:nu(e.transports)}))})}async function na(e,t,r){let{provider:n,adapter:o}=e,i=r.id?await o.listAuthenticatorsByUserId(r.id):null,a=P(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:a,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:nc(e.credentialID),type:"public-key",transports:nu(e.transports)}))})}function ns(e){let{provider:t,adapter:r}=e;if(!r)throw new l.OZ("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new l.Hj("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function nc(e){return new Uint8Array(Buffer.from(e,"base64"))}function nl(e){return Buffer.from(e).toString("base64")}function nu(e){return e?e.split(","):void 0}async function nd(e,t,r,n){if(!t.provider)throw new l.Hj("Callback route called without provider");let{query:o,body:i,method:a,headers:s}=e,{provider:c,adapter:u,url:d,callbackUrl:f,pages:p,jwt:h,events:y,callbacks:m,session:{strategy:g,maxAge:b},logger:_}=t,w="jwt"===g;try{if("oauth"===c.type||"oidc"===c.type){let a,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:o;if(t.isOnRedirectProxy&&s?.state){let e=await r0.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return _.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let l=await r7(s,e.cookies,t);l.cookies.length&&n.push(...l.cookies),_.debug("authorization result",l);let{user:g,account:v,profile:k}=l;if(!g||!v||!k)return{redirect:`${d}/signin`,cookies:n};if(u){let{getUserByAccount:e}=u;a=await e({providerAccountId:v.providerAccountId,provider:c.id})}let S=await nf({user:a??g,account:v,profile:k},t);if(S)return{redirect:S,cookies:n};let{user:R,session:T,isNewUser:A}=await eV(r.value,g,v,t);if(w){let e={name:R.name,email:R.email,picture:R.image,sub:R.id?.toString()},o=await m.jwt({token:e,user:R,account:v,profile:k,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===o)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:o,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let s=r.chunk(i,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:T.sessionToken,options:{...t.cookies.sessionToken.options,expires:T.expires}});if(await y.signIn?.({user:R,account:v,profile:k,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:f})}`,cookies:n};return{redirect:f,cookies:n}}if("email"===c.type){let e=o?.token,i=o?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let a=c.secret??t.secret,s=await u.useVerificationToken({identifier:i,token:await O(`${e}${a}`)}),d=!!s,g=d&&s.expires.valueOf()<Date.now();if(!d||g||i&&s.identifier!==i)throw new l.o6({hasInvite:d,expired:g});let{identifier:_}=s,v=await u.getUserByEmail(_)??{id:crypto.randomUUID(),email:_,emailVerified:null},k={providerAccountId:v.email,userId:v.id,type:"email",provider:c.id},S=await nf({user:v,account:k},t);if(S)return{redirect:S,cookies:n};let{user:R,session:T,isNewUser:A}=await eV(r.value,v,k,t);if(w){let e={name:R.name,email:R.email,picture:R.image,sub:R.id?.toString()},o=await m.jwt({token:e,user:R,account:k,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===o)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:o,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let s=r.chunk(i,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:T.sessionToken,options:{...t.cookies.sessionToken.options,expires:T.expires}});if(await y.signIn?.({user:R,account:k,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:f})}`,cookies:n};return{redirect:f,cookies:n}}if("credentials"===c.type&&"POST"===a){let e=i??{};Object.entries(o??{}).forEach(([e,t])=>d.searchParams.set(e,t));let u=await c.authorize(e,new Request(d,{headers:s,method:a,body:JSON.stringify(i)}));if(u)u.id=u.id?.toString()??crypto.randomUUID();else throw new l.xz;let p={providerAccountId:u.id,type:"credentials",provider:c.id},g=await nf({user:u,account:p,credentials:e},t);if(g)return{redirect:g,cookies:n};let _={name:u.name,email:u.email,picture:u.image,sub:u.id},w=await m.jwt({token:_,user:u,account:p,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await h.encode({...h,token:w,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*b);let a=r.chunk(o,{expires:i});n.push(...a)}return await y.signIn?.({user:u,account:p}),{redirect:f,cookies:n}}else if("webauthn"===c.type&&"POST"===a){let o,i,a,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new l.lR("Invalid action parameter");let c=ns(t);switch(s){case"authenticate":{let t=await nn(c,e,n);o=t.user,i=t.account;break}case"register":{let r=await no(t,e,n);o=r.user,i=r.account,a=r.authenticator}}await nf({user:o,account:i},t);let{user:u,isNewUser:d,session:g,account:_}=await eV(r.value,o,i,t);if(!_)throw new l.lR("Error creating or finding account");if(a&&u.id&&await c.adapter.createAuthenticator({...a,userId:u.id}),w){let e={name:u.name,email:u.email,picture:u.image,sub:u.id?.toString()},o=await m.jwt({token:e,user:u,account:_,isNewUser:d,trigger:d?"signUp":"signIn"});if(null===o)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:o,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let s=r.chunk(i,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:g.sessionToken,options:{...t.cookies.sessionToken.options,expires:g.expires}});if(await y.signIn?.({user:u,account:_,isNewUser:d}),d&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:f})}`,cookies:n};return{redirect:f,cookies:n}}throw new l.Hj(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof l.lR)throw t;let e=new l.t3(t,{provider:c.id});throw _.debug("callback route error details",{method:a,query:o,body:i}),e}}async function nf(e,t){let r,{signIn:n,redirect:o}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof l.lR)throw e;throw new l.Oy(e)}if(!r)throw new l.Oy("AccessDenied");if("string"==typeof r)return await o({url:r,baseUrl:t.url.origin})}async function np(e,t,r,n,o){let{adapter:i,jwt:a,events:s,callbacks:c,logger:u,session:{strategy:d,maxAge:f}}=e,p={body:null,headers:{"Content-Type":"application/json"},cookies:r},h=t.value;if(!h)return p;if("jwt"===d){try{let r=e.cookies.sessionToken.name,i=await a.decode({...a,token:h,salt:r});if(!i)throw Error("Invalid JWT");let l=await c.jwt({token:i,...n&&{trigger:"update"},session:o}),u=eB(f);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let o=await a.encode({...a,token:l,salt:r}),i=t.chunk(o,{expires:u});p.cookies?.push(...i),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){u.error(new l.SW(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:a,updateSession:l}=i,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await a(h),u=null),u){let{user:t,session:r}=u,i=e.session.updateAge,a=r.expires.valueOf()-1e3*f+1e3*i,d=eB(f);a<=Date.now()&&await l({sessionToken:h,expires:d});let y=await c.session({session:{...r,user:t},user:t,newSession:o,...n?{trigger:"update"}:{}});p.body=y,p.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:d}}),await s.session?.({session:y})}else h&&p.cookies?.push(...t.clean())}catch(e){u.error(new l.QU(e))}return p}async function nh(e,t){let r,n,{logger:o,provider:i}=t,a=i.authorization?.url;if(!a||"authjs.dev"===a.host){let e=new URL(i.issuer),t=await tl(e,{[e1]:i[L],[eY]:!0}),r=await tf(e,t);if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");a=new URL(r.authorization_endpoint)}let s=a.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,n=i.callbackUrl,o.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await r0.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),i.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await rY.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let f=await r1.create(t);return f&&(s.set("nonce",f.value),u.push(f.cookie)),"oidc"!==i.type||a.searchParams.has("scope")||a.searchParams.set("scope","openid profile email"),o.debug("authorization url is ready",{url:a,cookies:u,provider:i}),{redirect:a.toString(),cookies:u}}async function ny(e,t){let r,{body:n}=e,{provider:o,callbacks:i,adapter:a}=t,s=(o.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},u=await a.getUserByEmail(s)??c,d={providerAccountId:s,userId:u.id,type:"email",provider:o.id};try{r=await i.signIn({user:u,account:d,email:{verificationRequest:!0}})}catch(e){throw new l.Oy(e)}if(!r)throw new l.Oy("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:f,theme:p}=t,h=await o.generateVerificationToken?.()??P(32),y=new Date(Date.now()+(o.maxAge??86400)*1e3),m=o.secret??t.secret,g=new URL(t.basePath,t.url.origin),b=o.sendVerificationRequest({identifier:s,token:h,expires:y,url:`${g}/callback/${o.id}?${new URLSearchParams({callbackUrl:f,token:h,email:s})}`,provider:o,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),_=a.createVerificationToken?.({identifier:s,token:await O(`${h}${m}`),expires:y});return await Promise.all([b,_]),{redirect:`${g}/verify-request?${new URLSearchParams({provider:o.id,type:o.type})}`}}async function nm(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:o}=await nh(e.query,r);return o&&t.push(...o),{redirect:n,cookies:t}}case"email":return{...await ny(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function ng(e,t,r){let{jwt:n,events:o,callbackUrl:i,logger:a,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await o.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await o.signOut?.({session:e})}}catch(e){a.error(new l.eH(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function nb(e,t){let{adapter:r,jwt:n,session:{strategy:o}}=e,i=t.value;if(!i)return null;if("jwt"===o){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function n_(e,t,r,n){let o=ns(t),{provider:i}=o,{action:a}=e.query??{};if("register"!==a&&"authenticate"!==a&&void 0!==a)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await nb(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:o=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===o)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(o)return"authenticate";else return"register"}return null}(a,!!s,c)){case"authenticate":return nr(o,e,l,n);case"register":if("string"==typeof l?.email)return nt(o,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function nw(e,t){let{action:r,providerId:n,error:o,method:i}=e,a=t.skipCSRFCheck===D,{options:s,cookies:u}=await J({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:a}),d=new c.c(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=ez({...s,query:e.query,cookies:u});switch(r){case"callback":return await nd(e,s,d,u);case"csrf":return t.csrf(a,s,u);case"error":return t.error(o);case"providers":return t.providers(s.providers);case"session":return await np(s,d,u);case"signin":return t.signin(n,o);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await n_(e,s,d,u)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&j(r,t),await nd(e,s,d,u);case"session":return j(r,t),await np(s,d,u,!0,e.body?.data);case"signin":return j(r,t),await nm(e,u,s);case"signout":return j(r,t),await ng(u,d,s)}}throw new l.P8(`Cannot handle action: ${r}`)}function nv(e,t,r,n,o){let i,a=o?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)i=new URL(s),a&&"/"!==a&&"/"!==i.pathname&&(i.pathname!==a&&R(o).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",o=n.endsWith(":")?n:n+":";i=new URL(`${o}//${e}`)}let c=i.toString().replace(/\/$/,"");if(a){let t=a?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function nk(e,t){let r=R(t),n=await E(e,t);if(!n)return Response.json("Bad request.",{status:400});let o=function(e,t){let{url:r}=e,n=[];if(!u&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new l.tP(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new l.jo("Please define a `secret`");let o=e.query?.callbackUrl;if(o&&!d(o,r.origin))return new l.me(`Invalid callback URL. Received: ${o}`);let{callbackUrl:i}=(0,c.X)(t.useSecureCookies??"https:"===r.protocol),a=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(a&&!d(a,r.origin))return new l.me(`Invalid callback URL. Received: ${a}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:o}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof o||o?.url||(e="userinfo"):e="token":e="authorization",e)return new l.gs(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)f=!0;else if("email"===t.type)p=!0;else if("webauthn"===t.type){var b;if(h=!0,t.simpleWebAuthnBrowserVersion&&(b=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(b)))return new l.lR(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new l.s5("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new l.nd(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(f){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new l.Lx("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new l.u$("Must define an authorize() handler to use credentials authentication provider")}let{adapter:_,session:w}=t,v=[];if(p||w?.strategy==="database"||!w?.strategy&&_)if(p){if(!_)return new l.OZ("Email login requires an adapter");v.push(...y)}else{if(!_)return new l.OZ("Database session requires an adapter");v.push(...m)}if(h){if(!t.experimental?.enableWebAuthn)return new l.xm("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!_)return new l.OZ("WebAuthn requires an adapter");v.push(...g)}if(_){let e=v.filter(e=>!(e in _));if(e.length)return new l.i8(`Required adapter methods were missing: ${e.join(", ")}`)}return u||(u=!0),n}(n,t);if(Array.isArray(o))o.forEach(r.warn);else if(o){if(r.error(o),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,a=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||a)return a&&r.error(new l.CM(`The error page ${e?.error} should not require authentication`)),x(ez({theme:i}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),a=t.raw===I;try{let e=await nw(n,t);if(a)return e;let r=x(e),o=r.headers.get("Location");if(!i||!o)return r;return Response.json({url:o},{headers:r.headers})}catch(f){r.error(f);let o=f instanceof l.lR;if(o&&a&&!i)throw f;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:(0,l._2)(f)?f.type:"Configuration"});f instanceof l.xz&&s.set("code",f.code);let c=o&&f.kind||"error",u=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,d=`${n.url.origin}${u}?${s}`;if(i)return Response.json({url:d});return Response.redirect(d)}}var nS=r(87728);function nR(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:o}=e.nextUrl;return new nS.NextRequest(n.replace(o,r),e)}function nT(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||R(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),o=e[`AUTH_${n}_ID`],i=e[`AUTH_${n}_SECRET`],a=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:o,clientSecret:i,issuer:a,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=o),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=a)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var nA=r(11695),nE=r(33054);async function nx(e,t){return nk(new Request(nv("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function nO(e){return"function"==typeof e}function nP(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,nE.b)(),n=await e(void 0);return t?.(n),nx(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],o=r[1],i=await e(n);return t?.(i),nU([n,o],i)}if(nO(r[0])){let n=r[0];return async(...r)=>{let o=await e(r[0]);return t?.(o),nU(r,o,n)}}let n="req"in r[0]?r[0].req:r[0],o="res"in r[0]?r[0].res:r[1],i=await e(n);return t?.(i),nx(new Headers(n.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in o?o.headers.append("set-cookie",t):o.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,nE.b)()).then(t=>nx(t,e).then(e=>e.json()));if(t[0]instanceof Request)return nU([t[0],t[1]],e);if(nO(t[0])){let r=t[0];return async(...t)=>nU(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return nx(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function nU(e,t,r){let n=nR(e[0]),o=await nx(n.headers,t),i=await o.json(),a=!0;t.callbacks?.authorized&&(a=await t.callbacks.authorized({request:n,auth:i}));let s=nS.NextResponse.next?.();if(a instanceof Response){s=a;let e=a.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),o=Object.values(r.pages??{});return(nj.has(n)||o.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(a=!0)}else if(r)n.auth=i,s=await r(n,e[1])??nS.NextResponse.next();else if(!a){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=nS.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of o.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(27095);let nj=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var nC=r(52058);async function n$(e,t={},r,n){let o=new Headers(await (0,nE.b)()),{redirect:i=!0,redirectTo:a,...s}=t instanceof FormData?Object.fromEntries(t):t,c=a?.toString()??o.get("Referer")??"/",l=nv("signin",o.get("x-forwarded-proto"),o,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),i&&(0,nC.redirect)(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,o=r?.id??n.id;if(o===e){d={id:o,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,nC.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),o.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(u,{method:"POST",headers:o,body:new URLSearchParams({...s,callbackUrl:c})}),p=await nk(f,{...n,raw:I,skipCSRFCheck:D}),h=await (0,nA.U)();for(let e of p?.cookies??[])h.set(e.name,e.value,e.options);let y=(p instanceof Response?p.headers.get("Location"):p.redirect)??u;return i?(0,nC.redirect)(y):y}async function nD(e,t){let r=new Headers(await (0,nE.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=nv("signout",r.get("x-forwarded-proto"),r,process.env,t),o=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(n,{method:"POST",headers:r,body:o}),a=await nk(i,{...t,raw:I,skipCSRFCheck:D}),s=await (0,nA.U)();for(let e of a?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,nC.redirect)(a.redirect):a}async function nI(e,t){let r=new Headers(await (0,nE.b)());r.set("Content-Type","application/json");let n=new Request(nv("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),o=await nk(n,{...t,raw:I,skipCSRFCheck:D}),i=await (0,nA.U)();for(let e of o?.cookies??[])i.set(e.name,e.value,e.options);return o.body}function nL(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return nT(r),nk(nR(t),r)};return{handlers:{GET:t,POST:t},auth:nP(e,e=>nT(e)),signIn:async(t,r,n)=>{let o=await e(void 0);return nT(o),n$(t,r,n,o)},signOut:async t=>{let r=await e(void 0);return nT(r),nD(t,r)},unstable_update:async t=>{let r=await e(void 0);return nT(r),nI(t,r)}}}nT(e);let t=t=>nk(nR(t),e);return{handlers:{GET:t,POST:t},auth:nP(e),signIn:(t,r,n)=>n$(t,r,n,e),signOut:t=>nD(t,e),unstable_update:t=>nI(t,e)}}},76883:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let n=""+r(47630).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89886:(e,t)=>{t.y=void 0,t.y=function(e){return{createUser:t=>e.user.create({data:t}),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){var r;let n=await e.account.findUnique({where:{provider_providerAccountId:t},select:{user:!0}});return null!=(r=null==n?void 0:n.user)?r:null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},data:r}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...o}=r;return{user:n,session:o}},createSession:t=>e.session.create({data:t}),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},data:t}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create({data:t});return r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return r.id&&delete r.id,r}catch(e){if("P2025"===e.code)return null;throw e}}}}}};