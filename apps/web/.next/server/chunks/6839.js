"use strict";exports.id=6839,exports.ids=[6839],exports.modules={39832:(e,a,t)=>{t.d(a,{A0:()=>d,BF:()=>c,Hj:()=>o,XI:()=>n,nA:()=>u,nd:()=>i});var r=t(43197),l=t(14824),s=t(51001);let n=l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,s.cn)("w-full caption-bottom text-sm",e),...a})}));n.displayName="Table";let d=l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("thead",{ref:t,className:(0,s.cn)("[&_tr]:border-b",e),...a}));d.displayName="TableHeader";let c=l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("tbody",{ref:t,className:(0,s.cn)("[&_tr:last-child]:border-0",e),...a}));c.displayName="TableBody",l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("tfoot",{ref:t,className:(0,s.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let o=l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("tr",{ref:t,className:(0,s.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));o.displayName="TableRow";let i=l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("th",{ref:t,className:(0,s.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));i.displayName="TableHead";let u=l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("td",{ref:t,className:(0,s.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));u.displayName="TableCell",l.forwardRef(({className:e,...a},t)=>(0,r.jsx)("caption",{ref:t,className:(0,s.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},40017:(e,a,t)=>{t.d(a,{T:()=>r});function r(e){return e?parseFloat(e.toString().replace(/,/g,""))%1==0?e.toLocaleString("en-US"):e.toLocaleString("en-US",{minimumFractionDigits:3,maximumFractionDigits:3}).replace(/\.?0+$/,""):""}},43806:(e,a,t)=>{t.d(a,{V:()=>s,e:()=>n});var r=t(49068);t(77048);var l=t(68941);async function s(e){let a=await l.A.userProfile.findUnique({where:{user_id:e}});return a?{userId:a.user_id,firstName:a.first_name||"",lastName:a.last_name||"",createdAt:a.created_at,updatedAt:a.updated_at,deletedAt:a.deleted_at,settings:a.settings}:null}async function n(e,a){let t=await l.A.userProfile.update({where:{user_id:e},data:{first_name:a.firstName,last_name:a.lastName,settings:JSON.parse(JSON.stringify(a.settings)),updated_at:new Date}});return{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}}(0,t(84672).D)([s,n]),(0,r.A)(s,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,r.A)(n,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},51948:(e,a,t)=>{t.d(a,{U:()=>l});var r=t(14824);function l(e){let[a,t]=(0,r.useState)(!1);return a}},55751:(e,a,t)=>{t.d(a,{F:()=>d});var r=t(43197),l=t(14824),s=t(47990),n=t(51001);let d=l.forwardRef(({className:e,children:a,...t},l)=>(0,r.jsxs)(s.bL,{ref:l,className:(0,n.cn)("relative overflow-hidden",e),...t,children:[(0,r.jsx)(s.LM,{className:"h-full w-full rounded-[inherit]",children:a}),(0,r.jsx)(c,{}),(0,r.jsx)(s.OK,{})]}));d.displayName=s.bL.displayName;let c=l.forwardRef(({className:e,orientation:a="vertical",...t},l)=>(0,r.jsx)(s.VM,{ref:l,orientation:a,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:(0,r.jsx)(s.lr,{className:"relative flex-1 rounded-full bg-border"})}));c.displayName=s.VM.displayName},61013:(e,a,t)=>{t.d(a,{Bc:()=>d,ZI:()=>i,k$:()=>o,m_:()=>c});var r=t(43197),l=t(14824),s=t(6292),n=t(51001);let d=s.Kq,c=s.bL,o=s.l9,i=l.forwardRef(({className:e,sideOffset:a=4,...t},l)=>(0,r.jsx)(s.UC,{ref:l,sideOffset:a,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));i.displayName=s.UC.displayName},75241:(e,a,t)=>{t.d(a,{w:()=>n});var r=t(20349);t(71241);var l=t(90742),s=t(31595);async function n(e,a,t,r,n,d){console.log("placeOrder(): -> ",{userId:e,symbol:a,action:t,quantity:r,price:n});try{let c=await (0,s.V)(e);if(!c?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");console.log("User profile retrieved:",c);let o=new l.IBApiNext({host:c.settings.ibkrConnectionDetail.host,port:c.settings.ibkrConnectionDetail.port});return new Promise(async(e,s)=>{try{o.connect(c.settings.ibkrConnectionDetail.clientId??void 0);let s=await o.getNextValidOrderId();console.log("Next valid order ID:",s);let i={symbol:a,exchange:"SMART",currency:"USD",secType:l.SecType.STK},u={orderId:s,action:t,totalQuantity:r,orderType:l.OrderType.LMT,lmtPrice:n,account:d,transmit:!0};o.placeOrder(s,i,u),console.log("Order placed successfully"),e(!0)}catch(e){console.error("Error placing order:",e),s(e)}finally{o.disconnect(),console.log("Disconnected from IB API")}})}catch(e){return console.error("Error placing order:",e),!1}}(0,t(68785).D)([n]),(0,r.A)(n,"7eb434a6bda9ff01072b0388a3ec4a71531853042e",null)},93004:(e,a,t)=>{t.d(a,{A:()=>v});var r=t(43197),l=t(38042),s=t(89806),n=t(416),d=t(12444),c=t(98100),o=t(37534),i=t(20830),u=t(43182),m=t(32748),x=t(44311),f=t(24017);let p=(0,f.createServerReference)("7eb434a6bda9ff01072b0388a3ec4a71531853042e",f.callServer,void 0,f.findSourceMapURL,"placeOrder");var h=t(14824),b=t(45806),j=t(80519);let y=x.Ik({accountId:x.Yj().min(1,"Account is required"),symbol:x.Yj().min(1,"Symbol is required").max(5,"Symbol cannot exceed 5 characters"),action:x.k5(["BUY","SELL"]),quantity:x.au.number().min(1,"Quantity must be at least 1").max(1e6,"Quantity cannot exceed 1,000,000"),price:x.au.number().min(.01,"Price must be at least 0.01").max(1e6,"Price cannot exceed 1,000,000")});function g({userId:e,accounts:a,defaultSymbol:t="",defaultAction:l="BUY",defaultPrice:x=0,defaultQuantity:f=1,defaultAccountId:g="",onSuccess:N,onCancel:v}){let{toast:w}=(0,i.dj)(),[S,A]=(0,h.useState)(!1),I=(0,j.useRouter)(),R=(0,m.mN)({resolver:(0,u.u)(y),defaultValues:{accountId:g,symbol:t,action:l,quantity:f,price:x}});async function C(a){try{A(!0),await p(e,a.symbol,a.action,a.quantity,a.price,a.accountId)?(w({title:"Order placed successfully",description:`${a.action} ${a.quantity} ${a.symbol} @ $${a.price}`}),R.reset(),N?.(),I.refresh()):w({variant:"destructive",title:"Failed to place order",description:"Please check your connection details and try again."})}catch(e){console.error("Error placing order:",e),w({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"An unexpected error occurred."})}finally{A(!1)}}return(0,r.jsxs)(n.Zp,{className:"w-full max-w-md mx-auto",children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Place Order"}),(0,r.jsx)(n.BT,{children:"Enter the details below to place a stock order"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(d.lV,{...R,children:(0,r.jsxs)("form",{onSubmit:R.handleSubmit(C),className:"space-y-6",children:[1===a.length?(0,r.jsx)(d.zB,{control:R.control,name:"accountId",render:({field:e})=>(0,r.jsxs)(d.eI,{children:[(0,r.jsx)(d.lR,{children:"Account"}),(0,r.jsx)(d.MJ,{children:(0,r.jsx)("div",{className:"p-2 bg-muted rounded-md text-sm",children:a[0].accountId})})]})}):(0,r.jsx)(d.zB,{control:R.control,name:"accountId",render:({field:e})=>(0,r.jsxs)(d.eI,{children:[(0,r.jsx)(d.lR,{children:"Account"}),(0,r.jsxs)(o.l6,{onValueChange:e.onChange,defaultValue:e.value,disabled:S,children:[(0,r.jsx)(d.MJ,{children:(0,r.jsx)(o.bq,{children:(0,r.jsx)(o.yv,{placeholder:"Select account"})})}),(0,r.jsx)(o.gC,{children:a.map(e=>(0,r.jsx)(o.eb,{value:e.accountId,children:e.accountId},e.accountId))})]}),(0,r.jsx)(d.C5,{})]})}),(0,r.jsx)(d.zB,{control:R.control,name:"symbol",render:({field:e})=>(0,r.jsxs)(d.eI,{children:[(0,r.jsx)(d.lR,{children:"Symbol"}),(0,r.jsx)(d.MJ,{children:(0,r.jsx)(c.p,{placeholder:"AAPL",...e,className:"uppercase",disabled:S})}),(0,r.jsx)(d.Rr,{children:"Enter the stock symbol (e.g., AAPL for Apple Inc.)"}),(0,r.jsx)(d.C5,{})]})}),(0,r.jsx)(d.zB,{control:R.control,name:"action",render:({field:e})=>(0,r.jsxs)(d.eI,{children:[(0,r.jsx)(d.lR,{children:"Action"}),(0,r.jsxs)(o.l6,{onValueChange:e.onChange,defaultValue:e.value,disabled:S,children:[(0,r.jsx)(d.MJ,{children:(0,r.jsx)(o.bq,{children:(0,r.jsx)(o.yv,{placeholder:"Select action"})})}),(0,r.jsxs)(o.gC,{children:[(0,r.jsx)(o.eb,{value:"BUY",children:"Buy"}),(0,r.jsx)(o.eb,{value:"SELL",children:"Sell"})]})]}),(0,r.jsx)(d.C5,{})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(d.zB,{control:R.control,name:"quantity",render:({field:e})=>(0,r.jsxs)(d.eI,{children:[(0,r.jsx)(d.lR,{children:"Quantity"}),(0,r.jsx)(d.MJ,{children:(0,r.jsx)(c.p,{type:"number",placeholder:"100",...e,disabled:S})}),(0,r.jsx)(d.C5,{})]})}),(0,r.jsx)(d.zB,{control:R.control,name:"price",render:({field:e})=>(0,r.jsxs)(d.eI,{children:[(0,r.jsx)(d.lR,{children:"Price"}),(0,r.jsx)(d.MJ,{children:(0,r.jsx)(c.p,{type:"number",step:"0.01",placeholder:"150.00",...e,disabled:S})}),(0,r.jsx)(d.C5,{})]})})]}),(0,r.jsxs)("div",{className:"flex flex-row gap-4",children:[(0,r.jsx)(s.$,{type:"button",variant:"outline",className:"w-full",onClick:v,disabled:S,children:"Cancel"}),(0,r.jsxs)(s.$,{type:"submit",className:"w-full",disabled:S,variant:"BUY"===R.getValues("action")?"default":"destructive",children:[S&&(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),S?"Placing Order...":`Place ${R.getValues("action")} Order`]})]})]})})}),(0,r.jsx)(n.wL,{className:"text-xs text-muted-foreground",children:(0,r.jsx)("p",{children:"Make sure your IBKR connection details are properly configured in your profile settings."})})]})}var N=t(26191);function v({userId:e,symbol:a="",action:t="BUY",price:n=0,quantity:d=1,defaultAccountId:c}){let[o,i]=(0,h.useState)(!1),[u,m]=(0,h.useState)([]);return(0,r.jsxs)(l.lG,{open:o,onOpenChange:i,children:[(0,r.jsx)(l.zM,{asChild:!0,children:(0,r.jsxs)(s.$,{variant:"SELL"===t?"destructive":"ghost",size:"sm",className:"h-8 w-8 p-0",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"SELL"===t?"Sell position":"Place order"})]})}),(0,r.jsx)(l.Cf,{className:"sm:max-w-[425px] p-0",children:(0,r.jsx)(g,{userId:e,accounts:u,defaultSymbol:a,defaultAction:t,defaultPrice:n,defaultQuantity:d,defaultAccountId:c,onSuccess:()=>{i(!1)},onCancel:()=>{i(!1)}})})]})}t(33449)}};