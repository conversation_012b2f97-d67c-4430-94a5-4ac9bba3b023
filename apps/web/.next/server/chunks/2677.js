exports.id=2677,exports.ids=[2677],exports.modules={11611:(e,r,t)=>{"use strict";t.d(r,{p:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{children:t}=e,i=g(e,["children"]);return(0,n.jsxs)("head",d(m({},i),{ref:r,children:[(0,n.jsx)("meta",{content:"text/html; charset=UTF-8",httpEquiv:"Content-Type"}),(0,n.jsx)("meta",{name:"x-apple-disable-message-reformatting"}),t]}))});b.displayName="Head"},13317:(e,r,t)=>{"use strict";t.d(r,{j2:()=>c,Y9:()=>s,Jv:()=>f});var i=t(74723),n=t(89886),a=t(68941),o=t(30935);let l={...{secret:process.env.AUTH_SECRET,providers:[o.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,n.y)(a.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let t=await a.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!t)return e;let i=t.user_profiles?.settings;return{...e,user:{...e.user,id:t.id,role:i.role??"user"}}}}},{handlers:s,auth:c,signIn:f,signOut:p}=(0,i.Ay)(l)},57582:(e,r,t)=>{"use strict";t.d(r,{D:()=>y});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=e=>[u(e.m,["margin"]),u(e.mx,["marginLeft","marginRight"]),u(e.my,["marginTop","marginBottom"]),u(e.mt,["marginTop"]),u(e.mr,["marginRight"]),u(e.mb,["marginBottom"]),u(e.ml,["marginLeft"])].filter(e=>Object.keys(e).length).reduce((e,r)=>m(m({},e),r),{}),u=(e,r)=>r.reduce((r,t)=>isNaN(parseFloat(e))?r:d(m({},r),{[t]:`${e}px`}),{}),y=i.forwardRef((e,r)=>{var{as:t="h1",children:i,style:a,m:o,mx:l,my:s,mt:c,mr:f,mb:p,ml:u}=e,y=g(e,["as","children","style","m","mx","my","mt","mr","mb","ml"]);return(0,n.jsx)(t,d(m({},y),{ref:r,style:m(m({},b({m:o,mx:l,my:s,mt:c,mr:f,mb:p,ml:u})),a),children:i}))});y.displayName="Heading"},62892:(e,r,t)=>{"use strict";t.d(r,{E:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{style:t}=e,i=g(e,["style"]);let a={};(null==t?void 0:t.marginTop)===void 0&&(a.marginTop="16px"),(null==t?void 0:t.marginBottom)===void 0&&(a.marginBottom="16px");let o=function(e){let r={marginTop:void 0,marginRight:void 0,marginBottom:void 0,marginLeft:void 0};for(let[t,i]of Object.entries(e))"margin"===t?r=function(e){if("number"==typeof e)return{marginTop:e,marginBottom:e,marginLeft:e,marginRight:e};if("string"==typeof e){let r=e.toString().trim().split(/\s+/);if(1===r.length)return{marginTop:r[0],marginBottom:r[0],marginLeft:r[0],marginRight:r[0]};if(2===r.length)return{marginTop:r[0],marginRight:r[1],marginBottom:r[0],marginLeft:r[1]};if(3===r.length)return{marginTop:r[0],marginRight:r[1],marginBottom:r[2],marginLeft:r[1]};if(4===r.length)return{marginTop:r[0],marginRight:r[1],marginBottom:r[2],marginLeft:r[3]}}return{marginTop:void 0,marginBottom:void 0,marginLeft:void 0,marginRight:void 0}}(i):"marginTop"===t?r.marginTop=i:"marginRight"===t?r.marginRight=i:"marginBottom"===t?r.marginBottom=i:"marginLeft"===t&&(r.marginLeft=i);return r}(m(m({},a),t));return(0,n.jsx)("p",d(m({},i),{ref:r,style:m(m({fontSize:"14px",lineHeight:"24px"},t),o)}))});b.displayName="Text"},64214:(e,r,t)=>{"use strict";t.d(r,{n:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{children:t,style:i}=e,a=g(e,["children","style"]);return(0,n.jsx)("body",d(m({},a),{ref:r,style:i,children:t}))});b.displayName="Body"},64313:(e,r,t)=>{"use strict";t.d(r,{w:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{children:t,style:i}=e,a=g(e,["children","style"]);return(0,n.jsx)("table",d(m({align:"center",width:"100%",border:0,cellPadding:"0",cellSpacing:"0",role:"presentation"},a),{ref:r,style:i,children:(0,n.jsx)("tbody",{children:(0,n.jsx)("tr",{children:(0,n.jsx)("td",{children:t})})})}))});b.displayName="Section"},68941:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var i=t(67566);let n=globalThis.__prisma||new i.PrismaClient},78631:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var i=t(49068);t(77048);var n=t(98206);async function a({body:e,to:r,subject:t}){console.log("sendEmail()",{body:e,to:r,subject:t});let i=new n.u(process.env.RESEND_API_KEY);try{let{data:n,error:a}=await i.emails.send({from:`Aquarius Technologies <${process.env.RESEND_EMAIL_FROM}>`,to:r,subject:t,html:e});if(a)return!1;return!0}catch(e){return!1}}(0,t(84672).D)([a]),(0,i.A)(a,"4061815cf2a2c2868673e85dee1a5fab71cb0423aa",null)},82906:(e,r,t)=>{"use strict";t.d(r,{m:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{children:t,style:i}=e,a=g(e,["children","style"]);return(0,n.jsx)("table",d(m({align:"center",width:"100%"},a),{border:0,cellPadding:"0",cellSpacing:"0",ref:r,role:"presentation",style:m({maxWidth:"37.5em"},i),children:(0,n.jsx)("tbody",{children:(0,n.jsx)("tr",{style:{width:"100%"},children:(0,n.jsx)("td",{children:t})})})}))});b.displayName="Container"},83756:(e,r,t)=>{"use strict";t.d(r,{l:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{children:t=""}=e,i=g(e,["children"]);let a=(Array.isArray(t)?t.join(""):t).substring(0,150);return(0,n.jsxs)("div",d(m({style:{display:"none",overflow:"hidden",lineHeight:"1px",opacity:0,maxHeight:0,maxWidth:0},"data-skip-in-text":!0},i),{ref:r,children:[a,u(a)]}))});b.displayName="Preview";var u=e=>e.length>=150?null:(0,n.jsx)("div",{children:"\xa0‌​‍‎‏\uFEFF".repeat(150-e.length)})},86641:()=>{},87313:()=>{},95444:(e,r,t)=>{"use strict";t.d(r,{E:()=>b});var i=t(60154),n=t(52927),a=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,m=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&p(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&p(e,t,r[t]);return e},d=(e,r)=>o(e,l(r)),g=(e,r)=>{var t={};for(var i in e)c.call(e,i)&&0>r.indexOf(i)&&(t[i]=e[i]);if(null!=e&&s)for(var i of s(e))0>r.indexOf(i)&&f.call(e,i)&&(t[i]=e[i]);return t},b=i.forwardRef((e,r)=>{var{children:t,lang:i="en",dir:a="ltr"}=e,o=g(e,["children","lang","dir"]);return(0,n.jsx)("html",d(m({},o),{dir:a,lang:i,ref:r,children:t}))});b.displayName="Html"}};