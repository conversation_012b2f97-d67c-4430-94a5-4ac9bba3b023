exports.id=4515,exports.ids=[4515],exports.modules={12090:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,45634,23)),Promise.resolve().then(r.t.bind(r,89908,23)),Promise.resolve().then(r.t.bind(r,63528,23)),Promise.resolve().then(r.t.bind(r,10923,23)),Promise.resolve().then(r.t.bind(r,74795,23)),Promise.resolve().then(r.t.bind(r,39747,23)),Promise.resolve().then(r.t.bind(r,51603,23)),Promise.resolve().then(r.t.bind(r,2165,23))},20919:(e,t,r)=>{"use strict";r.d(t,{RootLayoutClient:()=>a});let a=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call RootLayoutClient() from the server but RootLayoutClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/root-layout-client.tsx","RootLayoutClient")},24302:(e,t,r)=>{Promise.resolve().then(r.bind(r,20919))},31138:()=>{},51001:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(40763),s=r(85047);function n(...e){return(0,s.QP)((0,a.$)(e))}},52258:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30436,23)),Promise.resolve().then(r.t.bind(r,19274,23)),Promise.resolve().then(r.t.bind(r,11578,23)),Promise.resolve().then(r.t.bind(r,58605,23)),Promise.resolve().then(r.t.bind(r,82809,23)),Promise.resolve().then(r.t.bind(r,46945,23)),Promise.resolve().then(r.t.bind(r,66041,23)),Promise.resolve().then(r.t.bind(r,8683,23))},64054:(e,t,r)=>{Promise.resolve().then(r.bind(r,81805))},67657:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>o,viewport:()=>l});var a=r(52927),s=r(94612),n=r.n(s);r(31138),r(15122);var i=r(20919);(0,r(91581).fR)();let l={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1},o={title:"Lunar Hedge",description:"",manifest:"/manifest.json",icons:{apple:[{url:"/web-app-manifest-512x512.png",sizes:"512x512"}]}};function c({children:e}){return(0,a.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,a.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,a.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,a.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Lunar Hedge"}),(0,a.jsx)("link",{rel:"apple-touch-icon",href:"/icon-512x512.png",sizes:"512x512"})]}),(0,a.jsx)("body",{className:n().className,children:(0,a.jsx)(i.RootLayoutClient,{children:e})})]})}},81805:(e,t,r)=>{"use strict";r.d(t,{RootLayoutClient:()=>H});var a=r(43197),s=r(23633),n=r(68238);function i({children:e}){return(0,a.jsx)(n.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e})}var l=r(48763);let o=({children:e})=>(0,a.jsx)(l.CP,{children:e});var c=r(1688),d=r(61789),u=r(83931),m=r(70201),h=r(45293),f=r(86613);let p={name:"Dashboard",description:"",mainNav:[{title:"Dashboard",href:"/",icon:c.A},{title:"Signals",href:"/risk-signals",icon:d.A},{title:"Portfolio",href:"/portfolio",icon:u.A},{title:"Orders",href:"/orders",icon:m.A},{title:"Watchlist",href:"/watchlist",icon:h.A},{title:"Analytics",href:"/dashboard",icon:f.A}],links:{}};var g=r(14824),x=r.n(g),b=r(65264),j=r(71001),v=r(7676),y=r(51001);let w=b.bL,k=b.l9;b.bm;let N=b.ZL,P=g.forwardRef(({className:e,...t},r)=>(0,a.jsx)(b.hJ,{className:(0,y.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));P.displayName=b.hJ.displayName;let M=(0,j.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),C=g.forwardRef(({side:e="right",className:t,children:r,...s},n)=>(0,a.jsxs)(N,{children:[(0,a.jsx)(P,{}),(0,a.jsxs)(b.UC,{ref:n,className:(0,y.cn)(M({side:e}),t),...s,children:[r,(0,a.jsxs)(b.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));C.displayName=b.UC.displayName;let A=g.forwardRef(({className:e,...t},r)=>(0,a.jsx)(b.hE,{ref:r,className:(0,y.cn)("text-lg font-semibold text-foreground",e),...t}));A.displayName=b.hE.displayName,g.forwardRef(({className:e,...t},r)=>(0,a.jsx)(b.VY,{ref:r,className:(0,y.cn)("text-sm text-muted-foreground",e),...t})).displayName=b.VY.displayName;var z=r(89806),T=r(74660);function D(){let[e,t]=(0,g.useState)({isOpen:!1,status:"closed"}),r=()=>{switch(e.status){case"open":return"bg-green-500";case"pre-market":return"bg-yellow-500";case"after-hours":return"bg-orange-500";case"closed":return"bg-red-500"}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-full border",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${r()}`}),(0,a.jsx)("span",{className:"text-size-sm font-weight-medium text-body",children:(()=>{switch(e.status){case"open":return"Market Open";case"pre-market":return"Pre-Market";case"after-hours":return"After Hours";case"closed":return"Market Closed"}})()})]}),(0,a.jsxs)("div",{className:"sm:hidden flex items-center gap-1 px-2 py-1 rounded-full border",children:[(0,a.jsx)("div",{className:`w-1.5 h-1.5 rounded-full ${r()}`}),(0,a.jsx)("span",{className:"text-size-xs font-weight-medium text-body",children:(()=>{switch(e.status){case"open":return"Open";case"pre-market":return"Pre";case"after-hours":return"After";case"closed":return"Closed"}})()})]})]})}let R=function(){let{theme:e,setTheme:t}=(0,n.D)();return(0,a.jsx)("header",{className:"bg-background/80 backdrop-blur-md sticky top-0 z-40 w-full border-b shadow-sm",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,a.jsxs)(w,{children:[(0,a.jsx)(k,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"ghost",size:"icon",children:[(0,a.jsx)(T.sKQ,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,a.jsxs)(C,{side:"left",className:"w-[240px] sm:w-[300px]",children:[(0,a.jsx)(A,{className:"sr-only",children:"Main Menu"}),(0,a.jsx)("nav",{className:"flex flex-col space-y-1 mt-6",children:p.mainNav.map((e,t)=>(0,a.jsxs)("a",{href:e.href,className:"group flex items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all",children:[(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[e.icon&&(0,a.jsx)("span",{className:"text-interactive",children:(0,a.jsx)(e.icon,{})}),(0,a.jsx)("span",{className:"text-interactive",children:e.title})]}),(0,a.jsx)("span",{className:"ml-auto opacity-0 transition-opacity group-hover:opacity-100","aria-hidden":"true",children:"→"})]},t))})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(D,{}),(0,a.jsxs)(z.$,{variant:"ghost",size:"icon",onClick:()=>{t("dark"===e?"light":"dark")},children:[(0,a.jsx)(T.gLX,{className:"h-6 w-6 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(T.rRK,{className:"absolute h-6 w-6 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),(0,a.jsxs)(w,{children:[(0,a.jsx)(k,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"ghost",size:"icon",children:[(0,a.jsx)(T.nXn,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"sr-only",children:"Open profile menu"})]})}),(0,a.jsxs)(C,{side:"right",className:"w-full sm:w-[400px]",children:[(0,a.jsx)(A,{className:"sr-only",children:"Profile Menu"}),(0,a.jsxs)("nav",{className:"flex flex-col space-y-4 mt-6",children:[(0,a.jsx)("div",{className:"text-size-lg font-weight-medium text-heading px-3 mb-2",children:"My Account"}),(0,a.jsx)("a",{href:"/profile",className:"group flex items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all hover:bg-accent",children:(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-interactive",children:(0,a.jsx)(T.nXn,{className:"h-5 w-5"})}),(0,a.jsx)("span",{className:"text-interactive",children:"Profile"})]})}),(0,a.jsx)("button",{onClick:()=>(0,l.CI)({callbackUrl:"/"}),className:"group flex w-full items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all hover:bg-accent",children:(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-interactive",children:(0,a.jsx)(T.Gq2,{className:"h-5 w-5"})}),(0,a.jsx)("span",{className:"text-interactive",children:"Logout"})]})})]})]})]})]})]})})};var S=r(60880),L=r.n(S),I=r(80519),$=r(21307),X=r(15582),E=r(90169);let _=({icon:e,label:t,href:r,isActive:s})=>(0,a.jsxs)(L(),{href:r,className:(0,y.cn)("flex flex-col items-center justify-center space-y-1 text-xs",s?"text-primary":"text-muted-foreground"),children:[e,(0,a.jsx)("span",{children:t})]});function O(){let e=(0,I.usePathname)(),t=[{icon:(0,a.jsx)(c.A,{size:20}),label:"Home",href:"/"},{icon:(0,a.jsx)($.A,{size:20}),label:"Portfolio",href:"/portfolio"},{icon:(0,a.jsx)(X.A,{size:20}),label:"Watchlist",href:"/watchlist"},{icon:(0,a.jsx)(E.A,{size:20}),label:"Analytics",href:"/dashboard"}];return(0,a.jsx)("div",{className:"sticky bottom-0 left-0 right-0 border-t bg-background md:hidden safe-area-pb",children:(0,a.jsx)("nav",{className:"flex w-full items-center justify-between px-4 py-2",children:t.map(t=>(0,a.jsx)(_,{...t,isActive:e===t.href},t.href))})})}function q({children:e}){let{resolvedTheme:t}=(0,n.D)(),[r,i]=x().useState(!1);return(0,a.jsx)(s.Sx,{appearance:r?t:"light",accentColor:"mint",radius:"large",scaling:"110%",suppressHydrationWarning:!0,children:e})}function H({children:e}){return(0,a.jsx)(i,{children:(0,a.jsx)(o,{children:(0,a.jsxs)(q,{children:[!1,(0,a.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,a.jsx)(R,{}),(0,a.jsx)("main",{className:"flex-1",children:e}),(0,a.jsx)(O,{})]})]})})})}(0,r(38094).default)(async()=>{},{loadableGenerated:{modules:["app/_components/root-layout-client.tsx -> ./eruda-debug"]},ssr:!1})},89806:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>o});var a=r(43197),s=r(14824),n=r(65443),i=r(71001),l=r(51001);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...i},c)=>{let d=s?n.DX:"button";return(0,a.jsx)(d,{className:(0,l.cn)(o({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},91581:(e,t,r)=>{"use strict";r.d(t,{TZ:()=>i,fR:()=>o,gO:()=>l});var a=r(91773),s=r(47524);s.A.suppressNotices(["yahooSurvey"]);let n={SPX:"^GSPC",VIX:"^VIX",DJI:"^DJI",IXIC:"^IXIC"};async function i(e,t=2){(0,a.unstable_noStore)();try{let r=n[e]||e;for(let a=0;a<=t;a++)try{a>0&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,a)));let e=await s.A.quote(r,{fields:["regularMarketPrice","regularMarketChange","regularMarketChangePercent","regularMarketTime"]});if(e&&"number"==typeof e.regularMarketPrice)return{regularMarketPrice:e.regularMarketPrice,regularMarketChange:e.regularMarketChange??0,regularMarketChangePercent:e.regularMarketChangePercent??0,regularMarketTime:new Date(e.regularMarketTime??Date.now())};throw Error("Invalid response format")}catch(r){if(console.warn(`Attempt ${a+1} failed for ${e}:`,r),a===t)throw r}throw Error("All retry attempts failed")}catch(t){return console.error(`Failed to fetch quote for ${e}:`,t),{regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date}}}async function l(e,t=2){(0,a.unstable_noStore)();try{let r=e.map(e=>n[e]||e);for(let a=0;a<=t;a++)try{let t=await s.A.quote(r,{fields:["regularMarketPrice","regularMarketChange","regularMarketChangePercent","regularMarketTime"]}),a={};return(Array.isArray(t)?t:[t]).forEach((t,r)=>{let s=e[r];t&&"number"==typeof t.regularMarketPrice?a[s]={regularMarketPrice:t.regularMarketPrice,regularMarketChange:t.regularMarketChange??0,regularMarketChangePercent:t.regularMarketChangePercent??0,regularMarketTime:new Date(t.regularMarketTime??Date.now())}:a[s]={regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date}}),a}catch(e){if(a===t)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("All retry attempts failed")}catch(t){return console.error(`Failed to fetch quotes for ${e.join(", ")}:`,t),e.reduce((e,t)=>(e[t]={regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date},e),{})}}function o(){s.A.setGlobalConfig({queue:{concurrency:2,timeout:5e3},validation:{logErrors:!0}})}}};