"use strict";exports.id=3349,exports.ids=[3349],exports.modules={109:(e,t,r)=>{function n(e){return global.__cachedSymbols.get(e)}function a(e,t){global.__cachedSymbols.set(e,{...t,lastUpdated:new Date})}function i(){return global.__cachedSymbols}r.d(t,{BK:()=>i,Tv:()=>n,bX:()=>a}),global.__cachedSymbols||(global.__cachedSymbols=new Map)},31972:(e,t,r)=>{r.d(t,{$:()=>d});var n=r(20349);r(71241);var a=r(88132),i=r(109),o=r(68785);async function l(e){try{let t=await fetch("https://api.openfigi.com/v3/mapping",{method:"POST",headers:{"Content-Type":"application/json","X-OPENFIGI-APIKEY":"563d9fd2-f93d-4d39-998d-5fbf54baae06"},body:JSON.stringify(e)}),r=await t.json();return JSON.stringify(r)}catch(e){return JSON.stringify({errorMessage:e})}}async function s(e){try{let t=await fetch("https://api.openfigi.com/v3/mapping",{method:"POST",headers:{"Content-Type":"application/json","X-OPENFIGI-APIKEY":"563d9fd2-f93d-4d39-998d-5fbf54baae06"},body:JSON.stringify([e])}),r=await t.json();return JSON.stringify(r)}catch(e){return JSON.stringify({errorMessage:e})}}async function d(e){if(console.log("getCachedInstrumentDetailBatch():symbols->",e),0===e.length){let t=await (0,a.u6)();e=JSON.parse(await (0,a.Jt)(new Date(t))).map(e=>e.index)}let t=e.filter(e=>!(0,i.Tv)(e));console.log("getCachedInstrumentDetailBatch():cachedSymbols->",t);let r=JSON.parse(await l(t.map(e=>({idValue:e,idType:"TICKER"}))));Array.isArray(r)&&r.forEach(e=>{if(e.data&&e.data.length>0){let t={ticker:e.data[0].ticker,name:e.data[0].name,securityType:e.data[0].securityType,securityType2:e.data[0].securityType2,figi:e.data[0].figi,exchange:e.data[0].exchCode};(0,i.bX)(t.ticker,t)}});let n=(0,i.BK)();return console.log("getCachedInstrumentDetailBatch():results->",n),n}(0,o.D)([l,s]),(0,n.A)(l,"7f5a3b429f9bf86298bbf02e1856c79e4e462a7d0e",null),(0,n.A)(s,"7f1ecaf8199bd1b8cdce70e54ad162711b6ade20b5",null),(0,o.D)([d]),(0,n.A)(d,"40396872244520ce764475f00a22baf0aa7a603940",null)},49068:(e,t,r)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=r(21601)},73788:(e,t,r)=>{let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return l},decrypt:function(){return c},encrypt:function(){return d},getActionEncryptionKey:function(){return p},getClientReferenceManifestForRsc:function(){return y},getServerModuleMap:function(){return g},setReferenceManifestsSingleton:function(){return f},stringToUint8Array:function(){return s}});let a=r(86715),i=r(64404),o=r(29294);function l(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let n="";for(let e=0;e<r;e++)n+=String.fromCharCode(t[e]);return n}function s(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r}function d(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function c(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}let u=Symbol.for("next.server.action-manifests");function f({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:n}){var a;let o=null==(a=globalThis[u])?void 0:a.clientReferenceManifestsPerPage;globalThis[u]={clientReferenceManifestsPerPage:{...o,[(0,i.normalizeAppPath)(e)]:t},serverActionsManifest:r,serverModuleMap:n}}function g(){let e=globalThis[u];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function y(){let e=globalThis[u];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,r=o.workAsyncStorage.getStore();if(!r){var n=t;let e=Object.values(n),r={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)r.clientModules={...r.clientModules,...t.clientModules},r.edgeRscModuleMapping={...r.edgeRscModuleMapping,...t.edgeRscModuleMapping},r.rscModuleMapping={...r.rscModuleMapping,...t.rscModuleMapping};return r}let i=t[r.route];if(!i)throw Object.defineProperty(new a.InvariantError(`Missing Client Reference Manifest for ${r.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return i}async function p(){if(n)return n;let e=globalThis[u];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new a.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",s(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},77048:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return p},encryptActionBoundArgs:function(){return y}}),r(40188);let n=r(21601),a=r(59175),i=r(95933),o=r(73788),l=r(63033),s=r(349),d=function(e){return e&&e.__esModule?e:{default:e}}(r(60154)),c=new TextEncoder,u=new TextDecoder;async function f(e,t){let r=await (0,o.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(t),a=n.slice(0,16),i=n.slice(16),l=u.decode(await (0,o.decrypt)(r,(0,o.stringToUint8Array)(a),(0,o.stringToUint8Array)(i)));if(!l.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return l.slice(e.length)}async function g(e,t){let r=await (0,o.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);l.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let a=(0,o.arrayBufferToString)(n.buffer),i=await (0,o.encrypt)(r,n,c.encode(e+t));return btoa(a+(0,o.arrayBufferToString)(i))}let y=d.default.cache(async function e(t,...r){let{clientModules:a}=(0,o.getClientReferenceManifestForRsc)(),d=Error();Error.captureStackTrace(d,e);let c=!1,u=l.workUnitAsyncStorage.getStore(),f=(null==u?void 0:u.type)==="prerender"?(0,s.createHangingInputAbortSignal)(u):void 0,y=await (0,i.streamToString)((0,n.renderToReadableStream)(r,a,{signal:f,onError(e){(null==f||!f.aborted)&&(c||(c=!0,d.message=e instanceof Error?e.message:String(e)))}}),f);if(c)throw d;if(!u)return g(t,y);let p=(0,l.getPrerenderResumeDataCache)(u),b=(0,l.getRenderResumeDataCache)(u),h=t+y,m=(null==p?void 0:p.encryptedBoundArgs.get(h))??(null==b?void 0:b.encryptedBoundArgs.get(h));if(m)return m;let v="prerender"===u.type?u.cacheSignal:void 0;null==v||v.beginRead();let S=await g(t,y);return null==v||v.endRead(),null==p||p.encryptedBoundArgs.set(h,S),S});async function p(e,t){let r,n=await t,i=l.workUnitAsyncStorage.getStore();if(i){let t="prerender"===i.type?i.cacheSignal:void 0,a=(0,l.getPrerenderResumeDataCache)(i),o=(0,l.getRenderResumeDataCache)(i);(r=(null==a?void 0:a.decryptedBoundArgs.get(n))??(null==o?void 0:o.decryptedBoundArgs.get(n)))||(null==t||t.beginRead(),r=await f(e,n),null==t||t.endRead(),null==a||a.decryptedBoundArgs.set(n,r))}else r=await f(e,n);let{edgeRscModuleMapping:s,rscModuleMapping:d}=(0,o.getClientReferenceManifestForRsc)();return await (0,a.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(c.encode(r)),(null==i?void 0:i.type)==="prerender"?i.renderSignal.aborted?e.close():i.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:d,serverModuleMap:(0,o.getServerModuleMap)()}})}},84672:(e,t)=>{function r(e){for(let t=0;t<e.length;t++){let r=e[t];if("function"!=typeof r)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof r}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return r}})},88132:(e,t,r)=>{r.d(t,{Jt:()=>l,KU:()=>s,Nd:()=>d,u6:()=>c,w4:()=>o});var n=r(20349);r(71241);var a=r(80336),i=r(68785);let o=async(e,t=!0)=>{if(console.info("1. Input targetDate:",e),isNaN(e.getTime()))return console.info("2. Invalid date detected:",e),[];let r=e.toISOString().split("T")[0];console.info("3. Converted dateString:",r);let n=await d();console.info("4. Retrieved archives:",{archiveDates:n.trendChanges.map(e=>e.toISOString().split("T")[0])});let i=n.trendChanges.some(e=>e.toISOString().split("T")[0]===r);console.info("5. Date exists in archives?",i),!i&&t&&(console.info("6. Target date not found, finding closest date..."),console.info("7. Found closest date:",r=n.trendChanges.reduce((t,r)=>{let n=Math.abs(r.getTime()-e.getTime()),a=Math.abs(t.getTime()-e.getTime()),i=n<a?r:t;return console.info("6a. Comparing dates:",{date:r.toISOString(),diff:n,isCloser:n<a}),i},n.trendChanges[0]).toISOString().split("T")[0])),console.info("8. Querying database with dateString:",r);let o=(await a.A.$queryRaw`SELECT id, original_index as "originalIndex", index, description, trend, buy_trade as "buyTrade", sell_trade as "sellTrade", previous_close as "previousClose", date FROM "trend_change" WHERE CAST(date AS DATE) = CAST(${r} AS DATE);`).map(e=>({...e,buyTrade:"object"==typeof e.buyTrade&&null!==e.buyTrade?Number(e.buyTrade.toString()):e.buyTrade,sellTrade:"object"==typeof e.sellTrade&&null!==e.sellTrade?Number(e.sellTrade.toString()):e.sellTrade,previousClose:"object"==typeof e.previousClose&&null!==e.previousClose?Number(e.previousClose.toString()):e.previousClose}));return console.info("9. Query results:",{count:o.length,firstResult:o[0]}),o};async function l(e){let t=e?new Date(e):new Date;t.setUTCHours(12,0,0,0);let r=await o(t);if(0===r.length){let e=await a.A.trendChange.findFirst({orderBy:{date:"desc"}});return JSON.stringify(await o(e?.date?new Date(e.date.setUTCHours(12,0,0,0)):new Date))}return JSON.stringify(r.map(e=>({...e,date:e.date.toISOString()})))}async function s(e){let t=await o(e[0].date,!1);if(console.info("existing.length ->",t.length),t&&t.length>0)return console.log("Existing trend change already in the database for this date: ",e[0].date),0;let r=e.map((e,t)=>({...e,originalIndex:e.originalIndex??t,buyTrade:"number"==typeof e.buyTrade?e.buyTrade:Number(e.buyTrade),sellTrade:"number"==typeof e.sellTrade?e.sellTrade:Number(e.sellTrade),previousClose:"number"==typeof e.previousClose?e.previousClose:Number(e.previousClose)}));return(await a.A.trendChange.createMany({data:r})).count}async function d(){try{let e=await a.A.trendChange.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[],t=await a.A.upsideDownsidePotential.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[];return{trendChanges:e.map(e=>e._max.date).filter(e=>null!==e),visualization:t.map(e=>e._max.date).filter(e=>null!==e)}}catch(e){return console.error("Error fetching archives of risk signals:",e),{trendChanges:[],visualization:[]}}}async function c(){return(await d()).trendChanges[0].toISOString()}(0,i.D)([o,l,s,d,c]),(0,n.A)(o,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a",null),(0,n.A)(l,"4082914884143dd2970140de0dd1f1be2975301463",null),(0,n.A)(s,"40e54e4c2e785198c3edb84e94b2846d18cc39479b",null),(0,n.A)(d,"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b",null),(0,n.A)(c,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520",null)}};