"use strict";exports.id=2452,exports.ids=[2452],exports.modules={16044:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(44736).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},47990:(e,r,t)=>{t.d(r,{LM:()=>q,OK:()=>Z,VM:()=>E,Ze:()=>$,bL:()=>G,lr:()=>z,zi:()=>K});var o=t(14824),n=t(2185),l=t(41661),i=t(6125),a=t(15167),s=t(21760),c=t(57536),d=t(36392),u=t(3188),p=t(57535),f=t(43197),h="ScrollArea",[w,v]=(0,i.A)(h),[m,g]=w(h),b=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:l="hover",dir:i,scrollHideDelay:s=600,...d}=e,[u,p]=o.useState(null),[h,w]=o.useState(null),[v,g]=o.useState(null),[b,S]=o.useState(null),[y,x]=o.useState(null),[E,C]=o.useState(0),[T,R]=o.useState(0),[L,P]=o.useState(!1),[j,A]=o.useState(!1),D=(0,a.s)(r,e=>p(e)),_=(0,c.jH)(i);return(0,f.jsx)(m,{scope:t,type:l,dir:_,scrollHideDelay:s,scrollArea:u,viewport:h,onViewportChange:w,content:v,onContentChange:g,scrollbarX:b,onScrollbarXChange:S,scrollbarXEnabled:L,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:x,scrollbarYEnabled:j,onScrollbarYEnabledChange:A,onCornerWidthChange:C,onCornerHeightChange:R,children:(0,f.jsx)(n.sG.div,{dir:_,...d,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});b.displayName=h;var S="ScrollAreaViewport",y=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:l,nonce:i,...s}=e,c=g(S,t),d=o.useRef(null),u=(0,a.s)(r,d,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,f.jsx)(n.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});y.displayName=S;var x="ScrollAreaScrollbar",E=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=g(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return o.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,f.jsx)(C,{...n,ref:r,forceMount:t}):"scroll"===l.type?(0,f.jsx)(T,{...n,ref:r,forceMount:t}):"auto"===l.type?(0,f.jsx)(R,{...n,ref:r,forceMount:t}):"always"===l.type?(0,f.jsx)(L,{...n,ref:r}):null});E.displayName=x;var C=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,i=g(x,e.__scopeScrollArea),[a,s]=o.useState(!1);return o.useEffect(()=>{let e=i.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},o=()=>{r=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[i.scrollArea,i.scrollHideDelay]),(0,f.jsx)(l.C,{present:t||a,children:(0,f.jsx)(R,{"data-state":a?"visible":"hidden",...n,ref:r})})}),T=o.forwardRef((e,r)=>{var t,n;let{forceMount:i,...a}=e,s=g(x,e.__scopeScrollArea),c="horizontal"===e.orientation,d=F(()=>h("SCROLL_END"),100),[u,h]=(t="hidden",n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>n[e][r]??e,t));return o.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,s.scrollHideDelay,h]),o.useEffect(()=>{let e=s.viewport,r=c?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(h("SCROLL"),d()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[s.viewport,c,h,d]),(0,f.jsx)(l.C,{present:i||"hidden"!==u,children:(0,f.jsx)(L,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:r,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),R=o.forwardRef((e,r)=>{let t=g(x,e.__scopeScrollArea),{forceMount:n,...i}=e,[a,s]=o.useState(!1),c="horizontal"===e.orientation,d=F(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(c?e:r)}},10);return B(t.viewport,d),B(t.content,d),(0,f.jsx)(l.C,{present:n||a,children:(0,f.jsx)(L,{"data-state":a?"visible":"hidden",...i,ref:r})})}),L=o.forwardRef((e,r)=>{let{orientation:t="vertical",...n}=e,l=g(x,e.__scopeScrollArea),i=o.useRef(null),a=o.useRef(0),[s,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=M(s.viewport,s.content),u={...n,sizes:s,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,r){return function(e,r,t,o="ltr"){let n=X(t),l=r||n/2,i=t.scrollbar.paddingStart+l,a=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return U([i,a],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,a.current,s,r)}return"horizontal"===t?(0,f.jsx)(P,{...u,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollLeft,s,l.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,f.jsx)(j,{...u,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollTop,s);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),P=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,i=g(x,e.__scopeScrollArea),[s,c]=o.useState(),d=o.useRef(null),u=(0,a.s)(r,d,i.onScrollbarXChange);return o.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(_,{"data-orientation":"horizontal",...l,ref:u,sizes:t,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&n({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:I(s.paddingLeft),paddingEnd:I(s.paddingRight)}})}})}),j=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,i=g(x,e.__scopeScrollArea),[s,c]=o.useState(),d=o.useRef(null),u=(0,a.s)(r,d,i.onScrollbarYChange);return o.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(_,{"data-orientation":"vertical",...l,ref:u,sizes:t,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&n({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:I(s.paddingTop),paddingEnd:I(s.paddingBottom)}})}})}),[A,D]=w(x),_=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:l,hasThumb:i,onThumbChange:c,onThumbPointerUp:d,onThumbPointerDown:u,onThumbPositionChange:h,onDragScroll:w,onWheelScroll:v,onResize:m,...b}=e,S=g(x,t),[y,E]=o.useState(null),C=(0,a.s)(r,e=>E(e)),T=o.useRef(null),R=o.useRef(""),L=S.viewport,P=l.content-l.viewport,j=(0,s.c)(v),D=(0,s.c)(h),_=F(m,10);function N(e){T.current&&w({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;y?.contains(r)&&j(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[L,y,P,j]),o.useEffect(D,[l,D]),B(y,_),B(S.content,_),(0,f.jsx)(A,{scope:t,scrollbar:y,hasThumb:i,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:D,onThumbPointerDown:(0,s.c)(u),children:(0,f.jsx)(n.sG.div,{...b,ref:C,style:{position:"absolute",...b.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=y.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),N(e))}),onPointerMove:(0,p.m)(e.onPointerMove,N),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,S.viewport&&(S.viewport.style.scrollBehavior=""),T.current=null})})})}),N="ScrollAreaThumb",z=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,n=D(N,e.__scopeScrollArea);return(0,f.jsx)(l.C,{present:t||n.hasThumb,children:(0,f.jsx)(H,{ref:r,...o})})}),H=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:l,...i}=e,s=g(N,t),c=D(N,t),{onThumbPositionChange:d}=c,u=(0,a.s)(r,e=>c.onThumbChange(e)),h=o.useRef(void 0),w=F(()=>{h.current&&(h.current(),h.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{w(),h.current||(h.current=V(e,d),d())};return d(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,w,d]),(0,f.jsx)(n.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;c.onThumbPointerDown({x:t,y:o})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});z.displayName=N;var k="ScrollAreaCorner",W=o.forwardRef((e,r)=>{let t=g(k,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,f.jsx)(O,{...e,ref:r}):null});W.displayName=k;var O=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...l}=e,i=g(k,t),[a,s]=o.useState(0),[c,d]=o.useState(0),u=!!(a&&c);return B(i.scrollbarX,()=>{let e=i.scrollbarX?.offsetHeight||0;i.onCornerHeightChange(e),d(e)}),B(i.scrollbarY,()=>{let e=i.scrollbarY?.offsetWidth||0;i.onCornerWidthChange(e),s(e)}),u?(0,f.jsx)(n.sG.div,{...l,ref:r,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function I(e){return e?parseInt(e,10):0}function M(e,r){let t=e/r;return isNaN(t)?0:t}function X(e){let r=M(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function Y(e,r,t="ltr"){let o=X(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,i=r.content-r.viewport,a=(0,u.q)(e,"ltr"===t?[0,i]:[-1*i,0]);return U([0,i],[0,l-o])(a)}function U(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var V=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},o=0;return!function n(){let l={left:e.scrollLeft,top:e.scrollTop},i=t.left!==l.left,a=t.top!==l.top;(i||a)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function F(e,r){let t=(0,s.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function B(e,r){let t=(0,s.c)(r);(0,d.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var G=b,q=y,$=E,K=z,Z=W},64020:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(44736).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])}};