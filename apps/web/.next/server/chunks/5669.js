"use strict";exports.id=5669,exports.ids=[5669],exports.modules={416:(e,t,a)=>{a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>d,aR:()=>i,wL:()=>f});var s=a(43197),r=a(14824),n=a(51001);let d=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));d.displayName="Card";let i=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let l=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let f=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));f.displayName="CardFooter"},12444:(e,t,a)=>{a.d(t,{C5:()=>y,MJ:()=>g,Rr:()=>b,eI:()=>p,lR:()=>x,lV:()=>l,zB:()=>f});var s=a(43197),r=a(14824),n=a(65443),d=a(32748),i=a(51001),o=a(26070);let l=d.Op,c=r.createContext({}),f=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(d.xI,{...e})}),m=()=>{let e=r.useContext(c),t=r.useContext(u),{getFieldState:a,formState:s}=(0,d.xW)(),n=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},u=r.createContext({}),p=r.forwardRef(({className:e,...t},a)=>{let n=r.useId();return(0,s.jsx)(u.Provider,{value:{id:n},children:(0,s.jsx)("div",{ref:a,className:(0,i.cn)("space-y-2",e),...t})})});p.displayName="FormItem";let x=r.forwardRef(({className:e,...t},a)=>{let{error:r,formItemId:n}=m();return(0,s.jsx)(o.J,{ref:a,className:(0,i.cn)(r&&"text-destructive",e),htmlFor:n,...t})});x.displayName="FormLabel";let g=r.forwardRef(({...e},t)=>{let{error:a,formItemId:r,formDescriptionId:d,formMessageId:i}=m();return(0,s.jsx)(n.DX,{ref:t,id:r,"aria-describedby":a?`${d} ${i}`:`${d}`,"aria-invalid":!!a,...e})});g.displayName="FormControl";let b=r.forwardRef(({className:e,...t},a)=>{let{formDescriptionId:r}=m();return(0,s.jsx)("p",{ref:a,id:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})});b.displayName="FormDescription";let y=r.forwardRef(({className:e,children:t,...a},r)=>{let{error:n,formMessageId:d}=m(),o=n?String(n?.message):t;return o?(0,s.jsx)("p",{ref:r,id:d,className:(0,i.cn)("text-sm font-medium text-destructive",e),...a,children:o}):null});y.displayName="FormMessage"},13317:(e,t,a)=>{a.d(t,{j2:()=>l,Y9:()=>o,Jv:()=>c});var s=a(74723),r=a(89886),n=a(68941),d=a(30935);let i={...{secret:process.env.AUTH_SECRET,providers:[d.A],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}},adapter:(0,r.y)(n.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:t}){let a=await n.A.user.findUnique({where:{email:t.email},include:{user_profiles:!0}});if(!a)return e;let s=a.user_profiles?.settings;return{...e,user:{...e.user,id:a.id,role:s.role??"user"}}}}},{handlers:o,auth:l,signIn:c,signOut:f}=(0,s.Ay)(i)},20830:(e,t,a)=>{a.d(t,{dj:()=>m,oR:()=>f});var s=a(14824);let r=0,n=new Map,d=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?d(a):e.toasts.forEach(e=>{d(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],l={toasts:[]};function c(e){l=i(l,e),o.forEach(e=>{e(l)})}function f({...e}){let t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=s.useState(l);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:f,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},26070:(e,t,a)=>{a.d(t,{J:()=>l});var s=a(43197),r=a(14824),n=a(81564),d=a(71001),i=a(51001);let o=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.b,{ref:a,className:(0,i.cn)(o(),e),...t}));l.displayName=n.b.displayName},31595:(e,t,a)=>{a.d(t,{V:()=>n,e:()=>d});var s=a(20349);a(71241);var r=a(80336);async function n(e){let t=await r.A.userProfile.findUnique({where:{user_id:e}});return t?{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}:null}async function d(e,t){let a=await r.A.userProfile.update({where:{user_id:e},data:{first_name:t.firstName,last_name:t.lastName,settings:JSON.parse(JSON.stringify(t.settings)),updated_at:new Date}});return{userId:a.user_id,firstName:a.first_name||"",lastName:a.last_name||"",createdAt:a.created_at,updatedAt:a.updated_at,deletedAt:a.deleted_at,settings:a.settings}}(0,a(68785).D)([n,d]),(0,s.A)(n,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,s.A)(d,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},33449:(e,t,a)=>{a.d(t,{I:()=>r});var s=a(24017);let r=(0,s.createServerReference)("40fea484d196ffc03250e6fc7effd806957ac9a456",s.callServer,void 0,s.findSourceMapURL,"getAllAccounts")},37534:(e,t,a)=>{a.d(t,{bq:()=>m,eb:()=>g,gC:()=>x,l6:()=>c,yv:()=>f});var s=a(43197),r=a(14824),n=a(25755),d=a(64168),i=a(15533),o=a(57124),l=a(51001);let c=n.bL;n.YJ;let f=n.WT,m=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(n.l9,{ref:r,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(d.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=n.l9.displayName;let u=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.PP,{ref:a,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=n.PP.displayName;let p=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.wn,{ref:a,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}));p.displayName=n.wn.displayName;let x=r.forwardRef(({className:e,children:t,position:a="popper",...r},d)=>(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{ref:d,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(u,{}),(0,s.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(p,{})]})}));x.displayName=n.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.JU,{ref:a,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName;let g=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(n.q7,{ref:r,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(n.p4,{children:t})]}));g.displayName=n.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.wv,{ref:a,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.wv.displayName},38042:(e,t,a)=>{a.d(t,{Cf:()=>u,HM:()=>f,L3:()=>x,c7:()=>p,lG:()=>o,zM:()=>l});var s=a(43197),r=a(14824),n=a(65264),d=a(7676),i=a(51001);let o=n.bL,l=n.l9,c=n.ZL,f=n.bm,m=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));m.displayName=n.hJ.displayName;let u=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(c,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(n.UC,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=n.UC.displayName;let p=({className:e,...t})=>(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=n.hE.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(n.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName=n.VY.displayName},43169:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var s=a(99292);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},64357:(e,t,a)=>{a.d(t,{E:()=>i});var s=a(43197);a(14824);var r=a(71001),n=a(51001);let d=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-500 hover:bg-green-500/20"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...a}){return(0,s.jsx)("div",{className:(0,n.cn)(d({variant:t}),e),...a})}},65629:(e,t,a)=>{a.d(t,{I:()=>d});var s=a(20349);a(71241);var r=a(90742),n=a(31595);async function d(e){try{let t=await (0,n.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let a=new r.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port});await a.connect(t.settings.ibkrConnectionDetail.clientId??void 0),console.log("Connected to IB API");let s=(await a.getManagedAccounts()).filter(e=>e&&""!==e.trim()).map(e=>({accountId:e,accountCode:e}));return console.log(s),a.disconnect(),console.log("Disconnected from IB API"),s}catch(e){return console.error("Error fetching all accounts:",e),[]}}(0,a(68785).D)([d]),(0,s.A)(d,"40fea484d196ffc03250e6fc7effd806957ac9a456",null)},68941:(e,t,a)=>{a.d(t,{A:()=>r});var s=a(67566);let r=globalThis.__prisma||new s.PrismaClient},80336:(e,t,a)=>{a.d(t,{A:()=>r});var s=a(51291);let r=globalThis.__prisma||new s.PrismaClient},91213:(e,t,a)=>{a.d(t,{A:()=>d,S:()=>r,SpinnerBasicWithText:()=>n});var s=a(43197);function r(){return(0,s.jsx)("div",{className:"fixed inset-0 flex items-center justify-center bg-white/50 dark:bg-black/50",children:(0,s.jsx)("div",{className:"inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",role:"status"})})}function n({text:e}){return(0,s.jsxs)("div",{className:"fixed inset-0 flex flex-col items-center justify-center bg-white/50 dark:bg-black/50",children:[(0,s.jsx)("div",{className:"inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",role:"status"}),(0,s.jsx)("div",{className:"mt-2",children:e})]})}let d=n},98100:(e,t,a)=>{a.d(t,{p:()=>d});var s=a(43197),r=a(14824),n=a(51001);let d=r.forwardRef(({className:e,type:t,...a},r)=>(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...a}));d.displayName="Input"}};