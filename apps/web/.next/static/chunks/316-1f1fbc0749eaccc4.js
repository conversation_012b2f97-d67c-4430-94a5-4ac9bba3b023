"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{4316:(e,t,r)=>{r.d(t,{Kq:()=>q,UC:()=>X,bL:()=>S,l9:()=>U});var n=r(8493),o=r(5389),l=r(3627),i=r(7709),a=r(6616),s=r(9463),u=r(1668),c=(r(9641),r(7431)),p=r(1929),d=r(9183),f=r(696),x=r(3252),h=r(1753),[v,g]=(0,i.A)("Tooltip",[u.Bk]),y=(0,u.Bk)(),b="TooltipProvider",m="tooltip.open",[w,C]=v(b),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(w,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};T.displayName=b;var E="Tooltip",[k,L]=v(E),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,p=C(E,e.__scopeTooltip),d=y(t),[x,v]=n.useState(null),g=(0,s.B)(),b=n.useRef(0),w=null!=a?a:p.disableHoverableContent,T=null!=c?c:p.delayDuration,L=n.useRef(!1),[R,j]=(0,f.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(p.onOpen(),document.dispatchEvent(new CustomEvent(m))):p.onClose(),null==i||i(e)},caller:E}),_=n.useMemo(()=>R?L.current?"delayed-open":"instant-open":"closed",[R]),P=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,L.current=!1,j(!0)},[j]),M=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,j(!1)},[j]),D=n.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{L.current=!0,j(!0),b.current=0},T)},[T,j]);return n.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),(0,h.jsx)(u.bL,{...d,children:(0,h.jsx)(k,{scope:t,contentId:g,open:R,stateAttribute:_,trigger:x,onTriggerChange:v,onTriggerEnter:n.useCallback(()=>{p.isOpenDelayedRef.current?D():P()},[p.isOpenDelayedRef,D,P]),onTriggerLeave:n.useCallback(()=>{w?M():(window.clearTimeout(b.current),b.current=0)},[M,w]),onOpen:P,onClose:M,disableHoverableContent:w,children:r})})};R.displayName=E;var j="TooltipTrigger",_=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=L(j,r),s=C(j,r),c=y(r),d=n.useRef(null),f=(0,l.s)(t,d,a.onTriggerChange),x=n.useRef(!1),v=n.useRef(!1),g=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,h.jsx)(u.Mz,{asChild:!0,...c,children:(0,h.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),x.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});_.displayName=j;var[P,M]=v("TooltipPortal",{forceMount:void 0}),D="TooltipContent",O=n.forwardRef((e,t)=>{let r=M(D,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=L(D,e.__scopeTooltip);return(0,h.jsx)(c.C,{present:n||i.open,children:i.disableHoverableContent?(0,h.jsx)(F,{side:o,...l,ref:t}):(0,h.jsx)(B,{side:o,...l,ref:t})})}),B=n.forwardRef((e,t)=>{let r=L(D,e.__scopeTooltip),o=C(D,e.__scopeTooltip),i=n.useRef(null),a=(0,l.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:p}=r,d=i.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{u(null),f(!1)},[f]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&d){let e=e=>v(e,d),t=e=>v(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,v,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==d?void 0:d.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,p=a.y;u>n!=p>n&&r<(c-s)*(n-u)/(p-u)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,s,p,x]),(0,h.jsx)(F,{...e,ref:a})}),[I,N]=v(E,{isInside:!1}),A=(0,d.Dc)("TooltipContent"),F=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,p=L(D,r),d=y(r),{onClose:f}=p;return n.useEffect(()=>(document.addEventListener(m,f),()=>document.removeEventListener(m,f)),[f]),n.useEffect(()=>{if(p.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(p.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[p.trigger,f]),(0,h.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,h.jsxs)(u.UC,{"data-state":p.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(A,{children:o}),(0,h.jsx)(I,{scope:r,isInside:!0,children:(0,h.jsx)(x.bL,{id:p.contentId,role:"tooltip",children:l||o})})]})})});O.displayName=D;var H="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=y(r);return N(H,r).isInside?null:(0,h.jsx)(u.i3,{...o,...n,ref:t})}).displayName=H;var q=T,S=R,U=_,X=O}}]);