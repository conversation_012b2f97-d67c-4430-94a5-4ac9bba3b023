(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{164:(e,r,t)=>{"use strict";t.d(r,{A0:()=>d,BF:()=>i,Hj:()=>c,XI:()=>n,nA:()=>m,nd:()=>o});var a=t(1753),s=t(8493),l=t(5783);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,l.cn)("w-full caption-bottom text-sm",t),...s})})});n.displayName="Table";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("thead",{ref:r,className:(0,l.cn)("[&_tr]:border-b",t),...s})});d.displayName="TableHeader";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tbody",{ref:r,className:(0,l.cn)("[&_tr:last-child]:border-0",t),...s})});i.displayName="TableBody",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tfoot",{ref:r,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tr",{ref:r,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})});c.displayName="TableRow";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("th",{ref:r,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})});o.displayName="TableHead";let m=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("td",{ref:r,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})});m.displayName="TableCell",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("caption",{ref:r,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption"},2588:(e,r,t)=>{"use strict";t.d(r,{AutoOrderPrefillList:()=>N});var a=t(1753),s=t(8493),l=t(9338),n=t(164),d=t(9305),i=t(3603),c=t(4336),o=t(8263),m=t(4578),f=t(9242),x=t(6052),u=t(2007);let h=(0,u.createServerReference)("409a303cc9c9a1946c183a09b48b42bcabb7822fd3",u.callServer,void 0,u.findSourceMapURL,"fetchAutoOrderPrefill");var p=t(6370),j=t(9613);function N(e){let{userId:r}=e,[t,u]=(0,s.useState)([]),[N,b]=(0,s.useState)(!0),v=(0,p.U)("(min-width: 768px)");return((0,s.useEffect)(()=>{(async()=>{try{let e=await h(r);u(e)}catch(e){console.error("Failed to load auto order prefills:",e)}finally{b(!1)}})()},[r]),N)?(0,a.jsx)(l.Zp,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin text-primary"})})}):t.length?v?(0,a.jsx)(l.Zp,{children:(0,a.jsx)(i.F,{className:"h-[calc(100vh-400px)]",children:(0,a.jsxs)(n.XI,{children:[(0,a.jsx)(n.A0,{children:(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nd,{children:"Symbol"}),(0,a.jsx)(n.nd,{children:"Action"}),(0,a.jsx)(n.nd,{children:"Quantity"}),(0,a.jsx)(n.nd,{children:"Price"}),(0,a.jsx)(n.nd,{children:"Status"}),(0,a.jsx)(n.nd,{children:"Created"})]})}),(0,a.jsx)(n.BF,{children:t.map(e=>(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nA,{className:"font-medium",children:e.ticker}),(0,a.jsx)(n.nA,{children:(0,a.jsx)(d.E,{variant:"BUY"===e.action?"default":"destructive",children:e.action})}),(0,a.jsx)(n.nA,{children:e.quantity}),(0,a.jsxs)(n.nA,{children:["$",e.price.toFixed(2)]}),(0,a.jsx)(n.nA,{children:(0,a.jsx)(d.E,{variant:e.deleted_at?"secondary":"success",children:e.deleted_at?"Deleted":"Pending"})}),(0,a.jsx)(n.nA,{children:new Date(e.created_at).toLocaleDateString()})]},e.id))})]})})}):(0,a.jsx)(i.F,{className:"h-[calc(100vh-400px)]",children:(0,a.jsx)("div",{className:"space-y-4 p-4",children:t.map(e=>(0,a.jsxs)(l.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-lg font-semibold",children:e.ticker}),(0,a.jsx)(d.E,{variant:"BUY"===e.action?"default":"destructive",children:e.action})]}),(0,a.jsx)(d.E,{variant:e.deleted_at?"secondary":"success",children:e.deleted_at?"Deleted":"Pending"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"})," Quantity"]}),(0,a.jsx)("div",{className:"font-medium",children:e.quantity})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(f.A,{className:"h-3 w-3"})," Price"]}),(0,a.jsxs)("div",{className:"font-medium",children:["$",e.price.toFixed(2)]})]})]}),(0,a.jsx)(j.w,{className:"my-4"}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]})]},e.id))})}):(0,a.jsx)(l.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center py-6 text-muted-foreground",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h3",{className:"font-medium mb-1",children:"No Auto Order Prefills"}),(0,a.jsx)("p",{className:"text-sm",children:"Enable auto order prefill in your account settings to use this feature"})]})})}},2650:(e,r,t)=>{Promise.resolve().then(t.bind(t,2588))},3603:(e,r,t)=>{"use strict";t.d(r,{F:()=>d});var a=t(1753),s=t(8493),l=t(6870),n=t(5783);let d=s.forwardRef((e,r)=>{let{className:t,children:s,...d}=e;return(0,a.jsxs)(l.bL,{ref:r,className:(0,n.cn)("relative overflow-hidden",t),...d,children:[(0,a.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(i,{}),(0,a.jsx)(l.OK,{})]})});d.displayName=l.bL.displayName;let i=s.forwardRef((e,r)=>{let{className:t,orientation:s="vertical",...d}=e;return(0,a.jsx)(l.VM,{ref:r,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...d,children:(0,a.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})})});i.displayName=l.VM.displayName},5783:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var a=t(3047),s=t(8171);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},6370:(e,r,t)=>{"use strict";t.d(r,{U:()=>s});var a=t(8493);function s(e){let[r,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let a=window.matchMedia(e);a.matches!==r&&t(a.matches);let s=()=>t(a.matches);return window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[r,e]),r}},9305:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var a=t(1753);t(8493);var s=t(5023),l=t(5783);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-500 hover:bg-green-500/20"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...s})}},9338:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>m});var a=t(1753),s=t(8493),l=t(5783);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});n.displayName="Card";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...s})});d.displayName="CardHeader";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});c.displayName="CardDescription";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...s})});o.displayName="CardContent";let m=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...s})});m.displayName="CardFooter"},9613:(e,r,t)=>{"use strict";t.d(r,{w:()=>d});var a=t(1753),s=t(8493),l=t(6621),n=t(5783);let d=s.forwardRef((e,r)=>{let{className:t,orientation:s="horizontal",decorative:d=!0,...i}=e;return(0,a.jsx)(l.b,{ref:r,decorative:d,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...i})});d.displayName=l.b.displayName}},e=>{var r=r=>e(e.s=r);e.O(0,[116,54,851,365,302,358],()=>r(2650)),_N_E=e.O()}]);