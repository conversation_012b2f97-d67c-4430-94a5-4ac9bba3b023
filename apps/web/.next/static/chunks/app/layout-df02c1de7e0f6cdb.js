(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1896:(e,s,t)=>{"use strict";t.d(s,{$:()=>c,r:()=>o});var a=t(1753),r=t(8493),n=t(9183),i=t(5023),l=t(5783);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,size:i,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(o({variant:r,size:i,className:t})),ref:s,...d})});c.displayName="Button"},2613:(e,s,t)=>{"use strict";t.d(s,{RootLayoutClient:()=>V});var a=t(1753),r=t(3333),n=t(7692);function i(e){let{children:s}=e;return(0,a.jsx)(n.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:s})}var l=t(9776);let o=e=>{let{children:s}=e;return(0,a.jsx)(l.CP,{children:s})};var c=t(174),d=t(8639),u=t(1931),h=t(835),m=t(8192),f=t(8199);let x={name:"Dashboard",description:"",mainNav:[{title:"Dashboard",href:"/",icon:c.A},{title:"Signals",href:"/risk-signals",icon:d.A},{title:"Portfolio",href:"/portfolio",icon:u.A},{title:"Orders",href:"/orders",icon:h.A},{title:"Watchlist",href:"/watchlist",icon:m.A},{title:"Analytics",href:"/dashboard",icon:f.A}],links:{}};var p=t(8493),g=t(4242),b=t(5023),j=t(5378),v=t(5783);let N=g.bL,y=g.l9;g.bm;let w=g.ZL,k=p.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(g.hJ,{className:(0,v.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:s})});k.displayName=g.hJ.displayName;let z=(0,b.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),A=p.forwardRef((e,s)=>{let{side:t="right",className:r,children:n,...i}=e;return(0,a.jsxs)(w,{children:[(0,a.jsx)(k,{}),(0,a.jsxs)(g.UC,{ref:s,className:(0,v.cn)(z({side:t}),r),...i,children:[n,(0,a.jsxs)(g.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});A.displayName=g.UC.displayName;let C=p.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(g.hE,{ref:s,className:(0,v.cn)("text-lg font-semibold text-foreground",t),...r})});C.displayName=g.hE.displayName,p.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(g.VY,{ref:s,className:(0,v.cn)("text-sm text-muted-foreground",t),...r})}).displayName=g.VY.displayName;var O=t(1896),P=t(9458);function _(){let[e,s]=(0,p.useState)({isOpen:!1,status:"closed"});(0,p.useEffect)(()=>{let e=()=>{s(function(){let e=new Date(new Date().toLocaleString("en-US",{timeZone:"America/New_York"})),s=e.getDay(),t=100*e.getHours()+e.getMinutes();return 0===s||6===s?{isOpen:!1,status:"closed"}:t>=400&&t<930?{isOpen:!1,status:"pre-market"}:t>=930&&t<1600?{isOpen:!0,status:"open"}:t>=1600&&t<2e3?{isOpen:!1,status:"after-hours"}:{isOpen:!1,status:"closed"}}())};e();let t=setInterval(e,6e4);return()=>clearInterval(t)},[]);let t=()=>{switch(e.status){case"open":return"bg-green-500";case"pre-market":return"bg-yellow-500";case"after-hours":return"bg-orange-500";case"closed":return"bg-red-500"}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-full border",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t())}),(0,a.jsx)("span",{className:"text-size-sm font-weight-medium text-body",children:(()=>{switch(e.status){case"open":return"Market Open";case"pre-market":return"Pre-Market";case"after-hours":return"After Hours";case"closed":return"Market Closed"}})()})]}),(0,a.jsxs)("div",{className:"sm:hidden flex items-center gap-1 px-2 py-1 rounded-full border",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full ".concat(t())}),(0,a.jsx)("span",{className:"text-size-xs font-weight-medium text-body",children:(()=>{switch(e.status){case"open":return"Open";case"pre-market":return"Pre";case"after-hours":return"After";case"closed":return"Closed"}})()})]})]})}let D=function(){let{theme:e,setTheme:s}=(0,n.D)();return(0,a.jsx)("header",{className:"bg-background/80 backdrop-blur-md sticky top-0 z-40 w-full border-b shadow-sm",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,a.jsxs)(N,{children:[(0,a.jsx)(y,{asChild:!0,children:(0,a.jsxs)(O.$,{variant:"ghost",size:"icon",children:[(0,a.jsx)(P.sKQ,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,a.jsxs)(A,{side:"left",className:"w-[240px] sm:w-[300px]",children:[(0,a.jsx)(C,{className:"sr-only",children:"Main Menu"}),(0,a.jsx)("nav",{className:"flex flex-col space-y-1 mt-6",children:x.mainNav.map((e,s)=>(0,a.jsxs)("a",{href:e.href,className:"group flex items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all",children:[(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[e.icon&&(0,a.jsx)("span",{className:"text-interactive",children:(0,a.jsx)(e.icon,{})}),(0,a.jsx)("span",{className:"text-interactive",children:e.title})]}),(0,a.jsx)("span",{className:"ml-auto opacity-0 transition-opacity group-hover:opacity-100","aria-hidden":"true",children:"→"})]},s))})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(_,{}),(0,a.jsxs)(O.$,{variant:"ghost",size:"icon",onClick:()=>{s("dark"===e?"light":"dark")},children:[(0,a.jsx)(P.gLX,{className:"h-6 w-6 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(P.rRK,{className:"absolute h-6 w-6 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),(0,a.jsxs)(N,{children:[(0,a.jsx)(y,{asChild:!0,children:(0,a.jsxs)(O.$,{variant:"ghost",size:"icon",children:[(0,a.jsx)(P.nXn,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"sr-only",children:"Open profile menu"})]})}),(0,a.jsxs)(A,{side:"right",className:"w-full sm:w-[400px]",children:[(0,a.jsx)(C,{className:"sr-only",children:"Profile Menu"}),(0,a.jsxs)("nav",{className:"flex flex-col space-y-4 mt-6",children:[(0,a.jsx)("div",{className:"text-size-lg font-weight-medium text-heading px-3 mb-2",children:"My Account"}),(0,a.jsx)("a",{href:"/profile",className:"group flex items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all hover:bg-accent",children:(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-interactive",children:(0,a.jsx)(P.nXn,{className:"h-5 w-5"})}),(0,a.jsx)("span",{className:"text-interactive",children:"Profile"})]})}),(0,a.jsx)("button",{onClick:()=>(0,l.CI)({callbackUrl:"/"}),className:"group flex w-full items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all hover:bg-accent",children:(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-interactive",children:(0,a.jsx)(P.Gq2,{className:"h-5 w-5"})}),(0,a.jsx)("span",{className:"text-interactive",children:"Logout"})]})})]})]})]})]})]})})};var E=t(7424),M=t.n(E),R=t(4213),S=t(8443),L=t(9034),$=t(5025);let H=e=>{let{icon:s,label:t,href:r,isActive:n}=e;return(0,a.jsxs)(M(),{href:r,className:(0,v.cn)("flex flex-col items-center justify-center space-y-1 text-xs",n?"text-primary":"text-muted-foreground"),children:[s,(0,a.jsx)("span",{children:t})]})};function T(){let e=(0,R.usePathname)(),s=[{icon:(0,a.jsx)(c.A,{size:20}),label:"Home",href:"/"},{icon:(0,a.jsx)(S.A,{size:20}),label:"Portfolio",href:"/portfolio"},{icon:(0,a.jsx)(L.A,{size:20}),label:"Watchlist",href:"/watchlist"},{icon:(0,a.jsx)($.A,{size:20}),label:"Analytics",href:"/dashboard"}];return(0,a.jsx)("div",{className:"sticky bottom-0 left-0 right-0 border-t bg-background md:hidden safe-area-pb",children:(0,a.jsx)("nav",{className:"flex w-full items-center justify-between px-4 py-2",children:s.map(s=>(0,a.jsx)(H,{...s,isActive:e===s.href},s.href))})})}function U(e){let{children:s}=e,{resolvedTheme:t}=(0,n.D)(),[i,l]=p.useState(!1);return p.useEffect(()=>{l(!0)},[]),(0,a.jsx)(r.Sx,{appearance:i?t:"light",accentColor:"mint",radius:"large",scaling:"110%",suppressHydrationWarning:!0,children:s})}function V(e){let{children:s}=e;return(0,a.jsx)(i,{children:(0,a.jsx)(o,{children:(0,a.jsxs)(U,{children:[!1,(0,a.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,a.jsx)(D,{}),(0,a.jsx)("main",{className:"flex-1",children:s}),(0,a.jsx)(T,{})]})]})})})}(0,t(1514).default)(()=>t.e(463).then(t.bind(t,4463)).then(e=>e.ErudaDebug),{loadableGenerated:{webpack:()=>[4463]},ssr:!1})},5746:(e,s,t)=>{Promise.resolve().then(t.bind(t,2613)),Promise.resolve().then(t.t.bind(t,2044,23)),Promise.resolve().then(t.t.bind(t,7020,23)),Promise.resolve().then(t.t.bind(t,6050,23))},5783:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var a=t(3047),r=t(8171);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},7020:()=>{}},e=>{var s=s=>e(e.s=s);e.O(0,[88,899,584,116,803,424,316,776,444,365,302,358],()=>s(5746)),_N_E=e.O()}]);