(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{367:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(790).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2614:(e,t,s)=>{"use strict";s.d(t,{UserRoleDisplay:()=>f});var a=s(1753),r=s(8493),i=s(9305),n=s(1896),l=s(7960),c=s(7920),o=s(2007);let d=(0,o.createServerReference)("7f2cceb82f3528bdce95a7fec1001f3e3f060119b1",o.callServer,void 0,o.findSourceMapURL,"upsertUserRole");var u=s(8640);function f(e){let{userId:t}=e,[s,o]=(0,r.useState)(!1),[f,h]=(0,r.useState)("user");(0,r.useEffect)(()=>{(async()=>{var e;let s=await (0,u.V)(t);h((null==s||null==(e=s.settings)?void 0:e.role)||"user")})()},[t]);let m=async e=>{try{await d({role:e},t),h(e),o(!1)}catch(e){console.error("Failed to update role:",e)}};return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.E,{variant:"admin"===f?"default":"secondary",className:"text-sm",children:f.charAt(0).toUpperCase()+f.slice(1)}),"admin"===f&&(0,a.jsxs)(l.lG,{open:s,onOpenChange:o,children:[(0,a.jsx)(l.zM,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:"Change Role"})}),(0,a.jsxs)(l.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsx)(l.c7,{children:(0,a.jsx)(l.L3,{children:"Change User Role"})}),(0,a.jsx)("div",{className:"grid gap-4 py-4",children:(0,a.jsxs)(c.l6,{value:f,onValueChange:e=>m(e),children:[(0,a.jsx)(c.bq,{className:"w-full",children:(0,a.jsx)(c.yv,{placeholder:"Select role"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"user",children:"User"}),(0,a.jsx)(c.eb,{value:"admin",children:"Admin"})]})]})})]})]})]})}},2944:(e,t,s)=>{"use strict";s.d(t,{NotificationPreferences:()=>x});var a=s(1753),r=s(8493),i=s(7568),n=s(790);let l=(0,n.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),c=(0,n.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),o=(0,n.A)("bell-off",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]]);var d=s(2007);let u=(0,d.createServerReference)("4051fed6790d5a5fb9d91fb6a5ad52ef1f5e8cc96c",d.callServer,void 0,d.findSourceMapURL,"saveSubscription");async function f(){try{if(!("serviceWorker"in navigator)||!("Notification"in window))throw Error("Push notifications are not supported");if("denied"===Notification.permission)throw Error("Notifications are blocked. Please enable them in your browser settings.");let e=await Notification.requestPermission();if("granted"!==e)throw Error("Notification permission was not granted.");let t=await navigator.serviceWorker.register("/service-worker.js",{scope:"/"});await navigator.serviceWorker.ready;let s=await t.pushManager.getSubscription();if(console.log("Existing subscription:",s),!s)try{console.log("Fetching VAPID key...");let e=await fetch("/api/push/vapid-key");if(!e.ok)throw Error("Failed to fetch VAPID key: ".concat(e.status));let a=await e.text();if(console.log("VAPID key received:",a),!a||0===a.length)throw Error("Invalid VAPID key received");let r=function(e){try{let t="=".repeat((4-e.length%4)%4),s=(e+t).replace(/\-/g,"+").replace(/_/g,"/"),a=window.atob(s),r=new Uint8Array(a.length);for(let e=0;e<a.length;++e)r[e]=a.charCodeAt(e);return r}catch(e){throw console.error("Error in urlBase64ToUint8Array:",e),e}}(a);console.log("Creating new subscription...");try{s=await t.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:r}),console.log("New subscription created:",s)}catch(e){if(console.error("Detailed subscription error:",e),e instanceof Error&&"AbortError"===e.name){let e=await t.pushManager.getSubscription();e&&(await e.unsubscribe(),s=await t.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:r}))}else throw e}}catch(e){throw console.error("Error during subscription creation:",e),e}if(!s)throw Error("Failed to create push subscription");try{if(!await u(s))throw Error("Failed to save subscription");return console.log("Subscription saved successfully"),s}catch(e){throw await s.unsubscribe(),e}}catch(e){throw console.error("Error in subscribeToPushNotifications:",e),e}}var h=s(8858),m=s(8355);function x(e){let{userId:t}=e,[s,n]=(0,r.useState)(!1),[d,u]=(0,r.useState)(!0),[x,p]=(0,r.useState)(null),[v,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{g(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://"))},[]);let b=async()=>{try{if(!("Notification"in window))return void p("Notifications are not supported in this browser");let e=Notification.permission;if("granted"===e){let e=await navigator.serviceWorker.ready,t=await e.pushManager.getSubscription();n(!!t)}else n(!1)}catch(e){p("Failed to check notification status"),console.error("Error checking notification status:",e)}finally{u(!1)}},j=async()=>{let e=await navigator.serviceWorker.ready,t=await e.pushManager.getSubscription();if(t)try{await fetch("/api/push/unsubscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({endpoint:t.endpoint})}),await t.unsubscribe(),n(!1)}catch(e){throw Error("Failed to unsubscribe from notifications")}},w=async()=>{try{if(u(!0),p(null),!v)throw Error("Please install Lunar Hedge as a PWA before enabling notifications");s?await j():await f().catch(e=>{throw Error(e instanceof Error?e.message:"Subscription failed")}),await b()}catch(e){p(e instanceof Error?e.message:"Failed to manage notification subscription")}finally{u(!1)}};return((0,r.useEffect)(()=>{b().finally(()=>{u(!1)})},[]),d)?(0,a.jsxs)("div",{className:"flex items-center justify-center p-4",children:[(0,a.jsx)(m.S,{}),(0,a.jsx)("span",{className:"ml-2",children:"Loading notification preferences..."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"rounded-lg border border-blue-100 bg-blue-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(l,{className:"h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900",children:"Setting Up Push Notifications"}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("p",{children:"Before enabling notifications, please ensure:"}),(0,a.jsxs)("ol",{className:"list-decimal ml-4 space-y-1",children:[(0,a.jsx)("li",{children:"You have installed Lunar Hedge as a PWA (Progressive Web App) on your device"}),(0,a.jsx)("li",{children:"Your browser permissions allow notifications for this website"}),(0,a.jsx)("li",{children:"You are using a supported browser (Chrome, Edge, Firefox, or Safari)"})]}),(0,a.jsx)("p",{className:"mt-2",children:"When you toggle notifications on, your browser will prompt you to allow notifications. You must accept this prompt to receive updates."})]}),"denied"===Notification.permission&&(0,a.jsx)("div",{className:"mt-2 text-sm text-red-600",children:"⚠️ Notifications are currently blocked. Please enable them in your browser settings to receive updates."}),!v&&(0,a.jsx)("div",{className:"mt-2 text-sm text-amber-600",children:"⚠️ Please install Lunar Hedge as a PWA to enable notifications. See installation instructions below."})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"text-size-lg font-weight-medium text-heading",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-size-sm text-muted",children:"Receive updates when risk signals data changes"})]}),(0,a.jsx)(i.d,{checked:s,onCheckedChange:w,disabled:d||!v})]}),x&&(0,a.jsx)(h.Fc,{variant:"destructive",children:(0,a.jsx)(h.TN,{children:x})}),(0,a.jsxs)("div",{className:"bg-muted rounded-lg p-4 space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c,{className:"h-5 w-5 text-green-500"}),(0,a.jsx)("span",{className:"text-size-sm text-body",children:"You will receive notifications when risk signals are updated"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o,{className:"h-5 w-5 text-muted"}),(0,a.jsx)("span",{className:"text-size-sm text-muted",children:"Enable notifications to stay updated with risk signal changes"})]})}),(0,a.jsx)("div",{className:"text-size-xs text-muted",children:s?(0,a.jsx)("p",{children:"You can disable notifications at any time by toggling the switch above or through your browser settings."}):(0,a.jsx)("p",{children:"You will need to allow notifications in your browser when prompted after enabling this setting."})})]}),!s&&(0,a.jsxs)("div",{className:"text-size-sm text-body bg-muted rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-weight-medium mb-2",children:"How to Install Lunar Hedge:"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"font-weight-medium",children:"On iOS Safari:"}),(0,a.jsxs)("ol",{className:"list-decimal ml-4 space-y-1",children:[(0,a.jsx)("li",{children:"Tap the Share button"}),(0,a.jsx)("li",{children:'Scroll down and tap "Add to Home Screen"'}),(0,a.jsx)("li",{children:'Tap "Add" to confirm'})]}),(0,a.jsx)("p",{className:"font-weight-medium mt-3",children:"On Android Chrome:"}),(0,a.jsxs)("ol",{className:"list-decimal ml-4 space-y-1",children:[(0,a.jsx)("li",{children:"Tap the menu (three dots)"}),(0,a.jsx)("li",{children:'Tap "Add to Home screen"'}),(0,a.jsx)("li",{children:'Tap "Install" to confirm'})]})]})]})]})}},3201:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>N,AvatarFallback:()=>k,AvatarImage:()=>S});var a=s(1753),r=s(8493),i=s(7709),n=s(2848),l=s(5760),c=s(1929),o=s(9570);function d(){return()=>{}}var u="Avatar",[f,h]=(0,i.A)(u),[m,x]=f(u),p=r.forwardRef((e,t)=>{let{__scopeAvatar:s,...i}=e,[n,l]=r.useState("idle");return(0,a.jsx)(m,{scope:s,imageLoadingStatus:n,onImageLoadingStatusChange:l,children:(0,a.jsx)(c.sG.span,{...i,ref:t})})});p.displayName=u;var v="AvatarImage",g=r.forwardRef((e,t)=>{let{__scopeAvatar:s,src:i,onLoadingStatusChange:u=()=>{},...f}=e,h=x(v,s),m=function(e,t){let{referrerPolicy:s,crossOrigin:a}=t,i=(0,o.useSyncExternalStore)(d,()=>!0,()=>!1),n=r.useRef(null),c=i?(n.current||(n.current=new window.Image),n.current):null,[u,f]=r.useState(()=>w(c,e));return(0,l.N)(()=>{f(w(c,e))},[c,e]),(0,l.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),s&&(c.referrerPolicy=s),"string"==typeof a&&(c.crossOrigin=a),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,a,s]),u}(i,f),p=(0,n.c)(e=>{u(e),h.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==m&&p(m)},[m,p]),"loaded"===m?(0,a.jsx)(c.sG.img,{...f,ref:t,src:i}):null});g.displayName=v;var b="AvatarFallback",j=r.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:i,...n}=e,l=x(b,s),[o,d]=r.useState(void 0===i);return r.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>d(!0),i);return()=>window.clearTimeout(e)}},[i]),o&&"loaded"!==l.imageLoadingStatus?(0,a.jsx)(c.sG.span,{...n,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}j.displayName=b;var y=s(5783);let N=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(p,{ref:t,className:(0,y.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});N.displayName=p.displayName;let S=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(g,{ref:t,className:(0,y.cn)("aspect-square h-full w-full",s),...r})});S.displayName=g.displayName;let k=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(j,{ref:t,className:(0,y.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});k.displayName=j.displayName},5227:(e,t,s)=>{"use strict";s.d(t,{IBAccountDetails:()=>y});var a=s(1753),r=s(8493),i=s(9806),n=s(9025),l=s(1896),c=s(4336);let o=(0,s(790).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var d=s(367),u=s(9338),f=s(8640),h=s(9776),m=s(8932),x=s(7568),p=s(8898),v=s(7920),g=s(3108),b=s(2007);let j=(0,b.createServerReference)("7fefb8e30cab7d78730e8f450e790bc532ceaea9ce",b.callServer,void 0,b.findSourceMapURL,"upsertUserProfileIBAccountSettings");function w(){var e;let{data:t}=(0,h.wV)(),[s,i]=(0,r.useState)(!1),[n,o]=(0,r.useState)([]),[d,u]=(0,r.useState)({defaultIBAccountId:"",enableAutoOrderPrefill:!1}),[b,w]=(0,r.useState)({defaultIBAccountId:"",enableAutoOrderPrefill:!1}),[y,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{var e,s,a;if(null==t||null==(e=t.user)?void 0:e.id){o((await (0,g.I)(t.user.id)).map(e=>({accountId:e.accountId,accountNumber:e.accountId})));let e=await (0,f.V)(t.user.id),r={defaultIBAccountId:(null==e||null==(s=e.settings)?void 0:s.defaultIBAccountId)||"",enableAutoOrderPrefill:(null==e||null==(a=e.settings)?void 0:a.enableAutoOrderPrefill)||!1};u(r),w(r)}})()},[null==t||null==(e=t.user)?void 0:e.id]),(0,r.useEffect)(()=>{N(d.defaultIBAccountId!==b.defaultIBAccountId||d.enableAutoOrderPrefill!==b.enableAutoOrderPrefill)},[d,b]);let S=async()=>{var e;if(null==t||null==(e=t.user)?void 0:e.id)try{i(!0),await j({defaultIBAccountId:d.defaultIBAccountId,enableAutoOrderPrefill:d.enableAutoOrderPrefill},t.user.id),w(d),(0,m.oR)({title:"Settings saved",description:"Your IBKR account settings have been updated."})}catch(e){(0,m.oR)({title:"Error",description:"Failed to save settings.",variant:"destructive"})}finally{i(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"defaultIBAccountId",children:"Default Account"}),(0,a.jsxs)(v.l6,{value:d.defaultIBAccountId,onValueChange:e=>u({...d,defaultIBAccountId:e}),children:[(0,a.jsx)(v.bq,{className:"w-[240px]",children:(0,a.jsx)(v.yv,{placeholder:"Select default account"})}),(0,a.jsx)(v.gC,{children:n.map(e=>(0,a.jsx)(v.eb,{value:e.accountNumber,children:e.accountNumber},e.accountNumber))})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"This account will be used as the default for all trading operations"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(p.J,{htmlFor:"autoOrderPrefill",children:"Auto Order Prefill"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically prefill order details based on risk signals"})]}),(0,a.jsx)(x.d,{id:"autoOrderPrefill",checked:d.enableAutoOrderPrefill,onCheckedChange:e=>u({...d,enableAutoOrderPrefill:e})})]})]}),(0,a.jsx)(l.$,{onClick:S,disabled:s||!y,className:"w-full sm:w-auto",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Settings"})]})}function y(){let{data:e}=(0,h.wV)(),[t,s]=(0,r.useState)(!1),[x,p]=(0,r.useState)("idle"),[v,g]=(0,r.useState)(null),[b,j]=(0,r.useState)(!1),y=async()=>{try{var t,a,r;s(!0),p("idle");let n=await (0,f.V)(null==e||null==(t=e.user)?void 0:t.id);if(!(null==n||null==(a=n.settings)?void 0:a.ibkrConnectionDetail))return void(0,m.oR)({title:"Error",description:"Please configure your IBKR connection details first.",variant:"destructive"});let l=await (0,i.F)(null==e||null==(r=e.user)?void 0:r.id);p(l?"success":"error")}catch(e){p("error"),console.error("Failed to test IBKR connection:",e)}finally{s(!1),j(!1)}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("div",{className:"text-size-base md:text-size-lg space-y-4",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)(l.$,{onClick:y,disabled:t,variant:"outline",className:"w-[200px]",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify IBKR Connection"}),"idle"!==x&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:"success"===x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"text-green-600 font-weight-medium",children:"IBKR connection available"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-600 font-weight-medium",children:"Unable to reach IBKR"})]})}),b&&(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsx)(n.E,{className:"h-[100px] w-full"})}),v&&(0,a.jsx)(u.Zp,{className:"w-full max-w-md p-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Account ID:"}),(0,a.jsx)("span",{className:"font-weight-medium",children:v.accountId})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Available Funds:"}),(0,a.jsxs)("span",{className:"font-weight-medium",children:["$",v.availableFunds.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Net Liquidation:"}),(0,a.jsxs)("span",{className:"font-weight-medium",children:["$",v.netLiquidation.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Cash Value:"}),(0,a.jsxs)("span",{className:"font-weight-medium",children:["$",v.totalCashValue.toLocaleString()]})]})]})})]})}),(0,a.jsx)("div",{className:"border-t"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-size-base md:text-size-lg font-weight-medium text-heading",children:"Account Settings"}),(0,a.jsx)(w,{})]}),v&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"border-t"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-size-base md:text-size-lg font-weight-medium text-heading",children:"Account Information"}),(0,a.jsx)(u.Zp,{className:"w-full p-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Account ID:"}),(0,a.jsx)("span",{className:"font-weight-medium",children:v.accountId})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Available Funds:"}),(0,a.jsxs)("span",{className:"font-weight-medium",children:["$",v.availableFunds.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Net Liquidation:"}),(0,a.jsxs)("span",{className:"font-weight-medium",children:["$",v.netLiquidation.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Cash Value:"}),(0,a.jsxs)("span",{className:"font-weight-medium",children:["$",v.totalCashValue.toLocaleString()]})]})]})})]})]})]})}},6575:(e,t,s)=>{Promise.resolve().then(s.bind(s,2614)),Promise.resolve().then(s.bind(s,5227)),Promise.resolve().then(s.bind(s,9796)),Promise.resolve().then(s.bind(s,2944)),Promise.resolve().then(s.bind(s,3201))},7568:(e,t,s)=>{"use strict";s.d(t,{d:()=>l});var a=s(1753),r=s(8493),i=s(8975),n=s(5783);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(i.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=i.bL.displayName},8640:(e,t,s)=>{"use strict";s.d(t,{V:()=>r});var a=s(2007);let r=(0,a.createServerReference)("40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",a.callServer,void 0,a.findSourceMapURL,"getUserProfile")},8858:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>c,TN:()=>d,XL:()=>o});var a=s(1753),r=s(8493),i=s(5023),n=s(5783);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,...i}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(l({variant:r}),s),...i})});c.displayName="Alert";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})});o.displayName="AlertTitle";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",s),...r})});d.displayName="AlertDescription"},8975:(e,t,s)=>{"use strict";s.d(t,{bL:()=>y,zi:()=>N});var a=s(8493),r=s(5389),i=s(3627),n=s(7709),l=s(696),c=s(8808),o=s(680),d=s(1929),u=s(1753),f="Switch",[h,m]=(0,n.A)(f),[x,p]=h(f),v=a.forwardRef((e,t)=>{let{__scopeSwitch:s,name:n,checked:c,defaultChecked:o,required:h,disabled:m,value:p="on",onCheckedChange:v,form:g,...b}=e,[y,N]=a.useState(null),S=(0,i.s)(t,e=>N(e)),k=a.useRef(!1),A=!y||g||!!y.closest("form"),[I,E]=(0,l.i)({prop:c,defaultProp:null!=o&&o,onChange:v,caller:f});return(0,u.jsxs)(x,{scope:s,checked:I,disabled:m,children:[(0,u.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":I,"aria-required":h,"data-state":w(I),"data-disabled":m?"":void 0,disabled:m,value:p,...b,ref:S,onClick:(0,r.m)(e.onClick,e=>{E(e=>!e),A&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),A&&(0,u.jsx)(j,{control:y,bubbles:!k.current,name:n,value:p,checked:I,required:h,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});v.displayName=f;var g="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:s,...a}=e,r=p(g,s);return(0,u.jsx)(d.sG.span,{"data-state":w(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});b.displayName=g;var j=a.forwardRef((e,t)=>{let{__scopeSwitch:s,control:r,checked:n,bubbles:l=!0,...d}=e,f=a.useRef(null),h=(0,i.s)(f,t),m=(0,c.Z)(n),x=(0,o.X)(r);return a.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==n&&t){let s=new Event("click",{bubbles:l});t.call(e,n),e.dispatchEvent(s)}},[m,n,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...d,tabIndex:-1,ref:h,style:{...d.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var y=v,N=b},9025:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(1753),r=s(5783);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",t),...s})}},9385:(e,t,s)=>{"use strict";var a=s(8493),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=a.useState,n=a.useEffect,l=a.useLayoutEffect,c=a.useDebugValue;function o(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!r(e,s)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),a=i({inst:{value:s,getSnapshot:t}}),r=a[0].inst,d=a[1];return l(function(){r.value=s,r.getSnapshot=t,o(r)&&d({inst:r})},[e,s,t]),n(function(){return o(r)&&d({inst:r}),e(function(){o(r)&&d({inst:r})})},[e]),c(s),s};t.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:d},9570:(e,t,s)=>{"use strict";e.exports=s(9385)},9796:(e,t,s)=>{"use strict";s.d(t,{IBKRConnectionForm:()=>p});var a=s(1753),r=s(8493),i=s(570),n=s(8622),l=s(9e3),c=s(1896),o=s(2846),d=s(5444),u=s(8932),f=s(2007);let h=(0,f.createServerReference)("7fa677cfc0582c3f00b593da75a535b6ed3714b542",f.callServer,void 0,f.findSourceMapURL,"upsertUserProfileIBKRConnectionDetail");var m=s(8640);let x=l.Ik({host:l.Yj().min(1,"Host is required"),port:l.ai().min(0,"Port must be a positive number"),clientId:l.ai().optional()});function p(e){let{userId:t}=e,[s,l]=(0,r.useState)(!1),[f,p]=(0,r.useState)(!0),v=(0,n.mN)({resolver:(0,i.u)(x),defaultValues:{host:"",port:8888,clientId:void 0}});async function g(e){l(!0);try{await h({ibkrConnectionDetail:e},t),(0,u.oR)({title:"Settings updated",description:"Your IBKR connection details have been saved."})}catch(e){(0,u.oR)({title:"Error",description:"Failed to update IBKR connection details.",variant:"destructive"})}finally{l(!1)}}return((0,r.useEffect)(()=>{!async function(){try{var e;let s=await (0,m.V)(t);if(null==s||null==(e=s.settings)?void 0:e.ibkrConnectionDetail){let{host:e,port:t,clientId:a}=s.settings.ibkrConnectionDetail;v.reset({host:e,port:t,clientId:a})}}catch(e){console.error("Error loading IBKR settings:",e),(0,u.oR)({title:"Error",description:"Failed to load saved connection details.",variant:"destructive"})}finally{p(!1)}}()},[t,v]),f)?(0,a.jsx)("div",{children:"Loading saved settings..."}):(0,a.jsx)(o.lV,{...v,children:(0,a.jsxs)("form",{onSubmit:v.handleSubmit(g),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsx)(o.zB,{control:v.control,name:"host",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{children:"Host"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(d.p,{placeholder:"localhost",...t})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:v.control,name:"port",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{children:"Port"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(d.p,{type:"number",placeholder:"7496",...t,onChange:e=>t.onChange(Number(e.target.value))})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:v.control,name:"clientId",render:e=>{var t;let{field:s}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{children:"Client ID (Optional)"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(d.p,{type:"number",placeholder:"1",...s,value:null!=(t=s.value)?t:"",onChange:e=>{let t=e.target.value;s.onChange(""===t?null:Number(t))}})}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsx)(c.$,{type:"submit",disabled:s,children:s?"Saving...":"Save Connection Details"})]})})}},9806:(e,t,s)=>{"use strict";s.d(t,{F:()=>r});var a=s(2007);let r=(0,a.createServerReference)("40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",a.callServer,void 0,a.findSourceMapURL,"testIBKRConnection")}},e=>{var t=t=>e(e.s=t);e.O(0,[116,803,239,776,669,365,302,358],()=>t(6575)),_N_E=e.O()}]);