(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[17],{164:(e,t,r)=>{"use strict";r.d(t,{A0:()=>d,BF:()=>c,Hj:()=>i,XI:()=>n,nA:()=>f,nd:()=>o});var a=r(1753),s=r(8493),l=r(5783);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",r),...s})})});n.displayName="Table";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",r),...s})});d.displayName="TableHeader";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",r),...s})});c.displayName="TableBody",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})});i.displayName="TableRow";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})});o.displayName="TableHead";let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})});f.displayName="TableCell",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",r),...s})}).displayName="TableCaption"},2007:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return a.callServer},createServerReference:function(){return l},findSourceMapURL:function(){return s.findSourceMapURL}});let a=r(6508),s=r(7456),l=r(877).createServerReference},2209:(e,t,r)=>{Promise.resolve().then(r.bind(r,3865))},3865:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var a=r(1753),s=r(9338),l=r(164),n=r(6370),d=r(2007);let c=(0,d.createServerReference)("60dd8e3768323d7f825691f04bb4127d9750fa06af",d.callServer,void 0,d.findSourceMapURL,"getAccountTransactions");var i=r(8493);class o extends Error{constructor(e){super(e),this.name="IBConnectionError"}}var f=r(4336),m=r(790);let u=(0,m.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),x=(0,m.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var p=r(8858),h=r(7424),j=r.n(h);let v=e=>{let{accountId:t,userId:r}=e,[d,m]=(0,i.useState)([]),[h,v]=(0,i.useState)(!0),[N,y]=(0,i.useState)(null),w=(0,n.U)("(min-width: 768px)");return((0,i.useEffect)(()=>{(async()=>{try{v(!0),y(null);let e=await c(t,r);m(e)}catch(t){let e="Failed to fetch transactions";t instanceof o?e="IB Connection Error: ".concat(t.message):t instanceof Error&&(e=t.message),y(e)}finally{v(!1)}})()},[t,r]),h)?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(f.A,{className:"h-8 w-8 animate-spin text-primary"})}):N?(0,a.jsxs)(p.Fc,{variant:"destructive",children:[(0,a.jsx)(u,{className:"h-4 w-4"}),(0,a.jsx)(p.XL,{children:"Error"}),(0,a.jsx)(p.TN,{children:N})]}):0===d.length?(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No transactions found for this account."}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(j(),{href:"/portfolio",className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-1"}),"Back to Portfolio"]})}),(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Transaction History"})]})}),w?(0,a.jsxs)(l.XI,{children:[(0,a.jsx)(l.A0,{children:(0,a.jsxs)(l.Hj,{children:[(0,a.jsx)(l.nd,{children:"Date"}),(0,a.jsx)(l.nd,{children:"Type"}),(0,a.jsx)(l.nd,{children:"Amount"}),(0,a.jsx)(l.nd,{children:"Currency"}),(0,a.jsx)(l.nd,{children:"Status"})]})}),(0,a.jsx)(l.BF,{children:d.map(e=>(0,a.jsxs)(l.Hj,{children:[(0,a.jsx)(l.nA,{children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)(l.nA,{className:"capitalize",children:e.type}),(0,a.jsx)(l.nA,{children:(0,a.jsxs)("span",{className:"withdrawal"===e.type?"text-red-500":"text-green-500",children:["withdrawal"===e.type?"-":"+","$",e.amount.toFixed(2)]})}),(0,a.jsx)(l.nA,{children:e.currency}),(0,a.jsx)(l.nA,{className:"capitalize",children:e.status})]},e.id))})]}):(0,a.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,a.jsxs)(s.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"capitalize font-medium",children:e.type}),(0,a.jsxs)("span",{className:"font-bold ".concat("withdrawal"===e.type?"text-red-500":"text-green-500"),children:["withdrawal"===e.type?"-":"+","$",e.amount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)("span",{children:e.currency}),(0,a.jsx)("span",{className:"capitalize",children:e.status})]})]},e.id))})]})}},4336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(790).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5783:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(3047),s=r(8171);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},6370:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(8493);function s(e){let[t,r]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let a=window.matchMedia(e);a.matches!==t&&r(a.matches);let s=()=>r(a.matches);return window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[t,e]),t}},8858:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>c,TN:()=>o,XL:()=>i});var a=r(1753),s=r(8493),l=r(5023),n=r(5783);let d=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,...l}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(d({variant:s}),r),...l})});c.displayName="Alert";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",r),...s})});i.displayName="AlertTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",r),...s})});o.displayName="AlertDescription"},9338:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>d,wL:()=>f});var a=r(1753),s=r(8493),l=r(5783);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});d.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});c.displayName="CardTitle";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});i.displayName="CardDescription";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});o.displayName="CardContent";let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})});f.displayName="CardFooter"}},e=>{var t=t=>e(e.s=t);e.O(0,[116,424,365,302,358],()=>t(2209)),_N_E=e.O()}]);