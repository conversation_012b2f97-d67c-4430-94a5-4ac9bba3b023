"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[803],{680:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(8493),o=n(5760);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},696:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(8493),i=n(5760),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},830:(e,t,n)=>{n.d(t,{A:()=>G});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(8493)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var f="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=i({async:!0,ssr:!1},e),l}(),h=function(){},v=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,A=e.noRelative,N=e.noIsolation,S=e.inert,O=e.allowPinchZoom,T=e.as,L=e.gapMode,P=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,f(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(u,n)},[n]),u),M=i(i({},P),v);return a.createElement(a.Fragment,null,E&&a.createElement(C,{sideCar:m,removeScrollBar:x,shards:R,noRelative:A,noIsolation:N,inert:S,setCallbacks:g,allowPinchZoom:!!O,lockRef:c,gapMode:L}),y?a.cloneElement(a.Children.only(w),i(i({},M),{ref:D})):a.createElement(void 0===T?"div":T,i({},M,{className:b,ref:D}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=b(),N="data-scroll-locked",S=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(N,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(N,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(N)||"0",10);return isFinite(e)?e:0},T=function(){a.useEffect(function(){return document.body.setAttribute(N,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(N):document.body.setAttribute(N,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(A,{styles:S(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){P=!1}var M=!!P&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,f=a>0,d=0,p=0;do{if(!u)break;var m=F(e,u),h=m[0],v=m[1]-m[2]-l*h;(h||v)&&I(e,u)&&(d+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},z=0,$=[];let V=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=_(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=j(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=j(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if($.length&&$[$.length-1]===i){var n="deltaY"in e?B(e):_(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=a.useCallback(function(e){n.current=_(e),r.current=void 0},[]),d=a.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,_(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return $.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,M),document.addEventListener("touchmove",c,M),document.addEventListener("touchstart",f,M),function(){$=$.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,M),document.removeEventListener("touchmove",c,M),document.removeEventListener("touchstart",f,M)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(r),g);var U=a.forwardRef(function(e,t){return a.createElement(v,i({},e,{ref:t,sideCar:V}))});U.classNames=v.classNames;let G=U},1668:(e,t,n)=>{n.d(t,{Mz:()=>eK,i3:()=>eJ,UC:()=>eZ,bL:()=>eq,Bk:()=>eM});var r=n(8493);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=h(g(t)),u=v(a),c=p(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(c,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=b(m),v=a[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},R=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-R.top+h.top)/E.y,bottom:(R.bottom-g.bottom+h.bottom)/E.y,left:(g.left-R.left+h.left)/E.x,right:(R.right-g.right+h.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function N(e){return o.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=m(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,f=d(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function O(){return"undefined"!=typeof window}function T(e){return D(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t;return null==(t=(D(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function D(e){return!!O()&&(e instanceof Node||e instanceof L(e).Node)}function M(e){return!!O()&&(e instanceof Element||e instanceof L(e).Element)}function k(e){return!!O()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function j(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=H(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(e){let t=_(),n=M(e)?H(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function _(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(T(e))}function H(e){return L(e).getComputedStyle(e)}function z(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $(e){if("html"===T(e))return e;let t=e.assignedSlot||e.parentNode||j(e)&&e.host||P(e);return j(t)?t.host:t}function V(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=$(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:k(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=L(o);if(i){let e=U(l);return t.concat(l,l.visualViewport||[],I(o)?o:[],e&&n?V(e):[])}return t.concat(o,V(o,[],n))}function U(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function G(e){let t=H(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=k(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function X(e){return M(e)?e:e.contextElement}function Y(e){let t=X(e);if(!k(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=G(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let q=c(0);function K(e){let t=L(e);return _()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:q}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=X(e),a=c(1);t&&(r?M(r)&&(a=Y(r)):a=Y(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===L(l))&&o)?K(l):c(0),s=(i.left+u.x)/a.x,f=(i.top+u.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=L(l),t=r&&M(r)?L(r):r,n=e,o=U(n);for(;o&&r&&t!==n;){let e=Y(o),t=o.getBoundingClientRect(),r=H(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=U(n=L(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=z(e).scrollLeft;return t?t.left+n:Z(P(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=L(e),r=P(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=_();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=P(e),n=z(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===H(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(P(e));else if(M(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=k(e)?Y(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=K(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===H(e).position}function en(e,t){if(!k(e)||"fixed"===H(e).position)return null;if(t)return t(e);let n=e.offsetParent;return P(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=L(e);if(F(e))return n;if(!k(e)){let t=$(e);for(;t&&!B(t);){if(M(t)&&!et(t))return t;t=$(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(T(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!W(r)?n:r||function(e){let t=$(e);for(;k(t)&&!B(t);){if(W(t))return t;if(F(t))break;t=$(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=k(t),o=P(t),i="fixed"===n,l=Z(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==T(t)||I(o))&&(a=z(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o));i&&!r&&o&&(u.x=J(o));let s=!o||r||i?c(0):Q(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=P(r),a=!!t&&F(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=k(r);if((d||!d&&!i)&&(("body"!==T(r)||I(l))&&(u=z(r)),k(r))){let e=Z(r);s=Y(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?c(0):Q(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=V(e,[],!1).filter(e=>M(e)&&"body"!==T(e)),o=null,i="fixed"===H(e).position,l=i?$(e):e;for(;M(l)&&!B(l);){let t=H(l),n=W(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(l)&&!n&&function e(t,n){let r=$(t);return!(r===n||!M(r)||B(r))&&("fixed"===H(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=$(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=G(e);return{width:t,height:n}},getScale:Y,isElement:M,isRTL:function(e){return"rtl"===H(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=h(g(o)),E=v(x),R=await u.getDimensions(f),C="y"===x,A=C?"clientHeight":"clientWidth",N=a.reference[E]+a.reference[x]-w[x]-a.floating[E],S=w[x]-a.reference[x],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),T=O?O[A]:0;T&&await (null==u.isElement?void 0:u.isElement(O))||(T=c.floating[A]||a.floating[E]);let L=T/2-R[E]/2-1,P=i(y[C?"top":"left"],L),D=i(y[C?"bottom":"right"],L),M=T-R[E]-D,k=T/2-R[E]/2+(N/2-S/2),j=l(P,i(k,M)),I=!s.arrow&&null!=m(o)&&k!==j&&a.reference[E]/2-(k<P?P:D)-R[E]/2<0,F=I?k<P?k-P:k-M:0;return{[x]:w[x]+F,data:{[x]:j,centerOffset:k-j-F,...I&&{alignmentOffset:F}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return R(e,t,{...o,platform:i})};var ec=n(9280),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function em(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await S(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},m=await C(t,s),v=g(p(o)),y=h(v),w=f[y],b=f[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+m[e],r=w-m[t];w=l(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=l(n,i(b,r))}let x=c.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=g(o),m=h(f),v=s[m],y=s[f],w=d(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[f]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:N="none",flipAlignment:S=!0,...O}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let T=p(a),L=g(s),P=p(s)===s,D=await (null==f.isRTL?void 0:f.isRTL(b.floating)),M=R||(P||!S?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),k="none"!==N;!R&&k&&M.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,S,N,D));let j=[s,...M],I=await C(t,O),F=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&F.push(I[T]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(g(e)),i=v(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,c,D);F.push(I[e[0]],I[e[1]])}if(W=[...W,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&("alignment"!==E||L===g(t)||W.every(e=>e.overflows[0]>0&&g(e.placement)===L)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{let e=null==(l=W.filter(e=>{if(k){let t=g(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:u,rects:c,platform:s,elements:f}=t,{apply:h=()=>{},...v}=d(e,t),y=await C(t,v),w=p(u),b=m(u),x="y"===g(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let A=R-y.top-y.bottom,N=E-y.left-y.right,S=i(R-y[o],A),O=i(E-y[a],N),T=!t.middlewareData.shift,L=S,P=O;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=N),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=A),T&&!b){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?P=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):L=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await h({...t,availableWidth:P,availableHeight:L});let D=await s.getDimensions(f.floating);return E!==D.width||R!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=A(await C(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:N(e)}}}case"escaped":{let e=A(await C(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:N(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eR=n(1929),eC=n(1753),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eC.jsx)(eR.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eN=n(3627),eS=n(7709),eO=n(2848),eT=n(5760),eL=n(680),eP="Popper",[eD,eM]=(0,eS.A)(eP),[ek,ej]=eD(eP),eI=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eC.jsx)(ek,{scope:t,anchor:o,onAnchorChange:i,children:n})};eI.displayName=eP;var eF="PopperAnchor",eW=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=ej(eF,n),a=r.useRef(null),u=(0,eN.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eC.jsx)(eR.sG.div,{...i,ref:u})});eW.displayName=eF;var e_="PopperContent",[eB,eH]=eD(e_),ez=r.forwardRef((e,t)=>{var n,o,a,c,s,f,d,p;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:A="optimized",onPlaced:N,...S}=e,O=ej(e_,m),[T,L]=r.useState(null),D=(0,eN.s)(t,e=>L(e)),[M,k]=r.useState(null),j=(0,eL.X)(M),I=null!=(d=null==j?void 0:j.width)?d:0,F=null!=(p=null==j?void 0:j.height)?p:0,W="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},_=Array.isArray(x)?x:[x],B=_.length>0,H={padding:W,boundary:_.filter(eG),altBoundary:B},{refs:z,floatingStyles:$,placement:U,isPositioned:G,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);ef(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=l||h,E=a||g,R=r.useRef(null),C=r.useRef(null),A=r.useRef(f),N=null!=c,S=em(c),O=em(i),T=em(s),L=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};O.current&&(e.platform=O.current),eu(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!ef(A.current,t)&&(A.current=t,ec.flushSync(()=>{d(t)}))})},[p,t,n,O,T]);es(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let P=r.useRef(!1);es(()=>(P.current=!0,()=>{P.current=!1}),[]),es(()=>{if(x&&(R.current=x),E&&(C.current=E),x&&E){if(S.current)return S.current(x,E,L);L()}},[x,E,L,S,N]);let D=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:b}),[w,b]),M=r.useMemo(()=>({reference:x,floating:E}),[x,E]),k=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,f.x),r=ep(M.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,M.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:L,refs:D,elements:M,floatingStyles:k}),[f,L,D,M,k])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=X(e),m=a||c?[...p?V(p):[],...V(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,o=P(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=d;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:l(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?Z(e):null;return d&&function t(){let r=Z(e);y&&!el(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:O.anchor},middleware:[ev({mainAxis:v+F,alignmentAxis:y}),b&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ey():void 0,...H}),b&&ew({...H}),eb({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),M&&eE({element:M,padding:w}),eX({arrowWidth:I,arrowHeight:F}),C&&ex({strategy:"referenceHidden",...H})]}),[q,K]=eY(U),J=(0,eO.c)(N);(0,eT.N)(()=>{G&&(null==J||J())},[G,J]);let Q=null==(n=Y.arrow)?void 0:n.x,ee=null==(o=Y.arrow)?void 0:o.y,et=(null==(a=Y.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eT.N)(()=>{T&&er(window.getComputedStyle(T).zIndex)},[T]),(0,eC.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:G?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(c=Y.transformOrigin)?void 0:c.x,null==(s=Y.transformOrigin)?void 0:s.y].join(" "),...(null==(f=Y.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eB,{scope:m,placedSide:q,onArrowChange:k,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eR.sG.div,{"data-side":q,"data-align":K,...S,ref:D,style:{...S.style,animation:G?void 0:"none"}})})})});ez.displayName=e_;var e$="PopperArrow",eV={top:"bottom",right:"left",bottom:"top",left:"right"},eU=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eH(e$,n),i=eV[o.placedSide];return(0,eC.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eG(e){return null!==e}eU.displayName=e$;var eX=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,m]=eY(a),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+f/2,g=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+d/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=s?h:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function eY(e){let[t,n="center"]=e.split("-");return[t,n]}var eq=eI,eK=eW,eZ=ez,eJ=eU},1929:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(8493),o=n(9280),i=n(9183),l=n(1753),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},2848:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(8493);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3252:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>u});var r=n(8493),o=n(1929),i=n(1753),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var u=a},3627:(e,t,n)=>{n.d(t,{s:()=>l,t:()=>i});var r=n(8493);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function l(...e){return r.useCallback(i(...e),e)}},4242:(e,t,n)=>{n.d(t,{G$:()=>Y,Hs:()=>x,UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(8493),o=n(5389),i=n(3627),l=n(7709),a=n(9463),u=n(696),c=n(6616),s=n(5534),f=n(9641),d=n(7431),p=n(1929),m=n(4499),h=n(830),v=n(8844),g=n(9183),y=n(1753),w="Dialog",[b,x]=(0,l.A)(w),[E,R]=b(w),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,s=r.useRef(null),f=r.useRef(null),[d,p]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:s,contentRef:f,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};C.displayName=w;var A="DialogTrigger",N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=R(A,n),a=(0,i.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":G(l.open),...r,ref:a,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});N.displayName=A;var S="DialogPortal",[O,T]=b(S,{forceMount:void 0}),L=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,l=R(S,t);return(0,y.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(d.C,{present:n||l.open,children:(0,y.jsx)(f.Z,{asChild:!0,container:i,children:e})}))})};L.displayName=S;var P="DialogOverlay",D=r.forwardRef((e,t)=>{let n=T(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=R(P,e.__scopeDialog);return i.modal?(0,y.jsx)(d.C,{present:r||i.open,children:(0,y.jsx)(k,{...o,ref:t})}):null});D.displayName=P;var M=(0,g.TL)("DialogOverlay.RemoveScroll"),k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(P,n);return(0,y.jsx)(h.A,{as:M,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),j="DialogContent",I=r.forwardRef((e,t)=>{let n=T(j,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=R(j,e.__scopeDialog);return(0,y.jsx)(d.C,{present:r||i.open,children:i.modal?(0,y.jsx)(F,{...o,ref:t}):(0,y.jsx)(W,{...o,ref:t})})});I.displayName=j;var F=r.forwardRef((e,t)=>{let n=R(j,e.__scopeDialog),l=r.useRef(null),a=(0,i.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(_,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),W=r.forwardRef((e,t)=>{let n=R(j,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...u}=e,f=R(j,n),d=r.useRef(null),p=(0,i.s)(t,d);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,y.jsx)(c.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":G(f.open),...u,ref:p,onDismiss:()=>f.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(K,{titleId:f.titleId}),(0,y.jsx)(Z,{contentRef:d,descriptionId:f.descriptionId})]})]})}),B="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(B,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});H.displayName=B;var z="DialogDescription",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(z,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});$.displayName=z;var V="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=R(V,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function G(e){return e?"open":"closed"}U.displayName=V;var X="DialogTitleWarning",[Y,q]=(0,l.q)(X,{contentName:j,titleName:B,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,n=q(X),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Z=e=>{let{contentRef:t,descriptionId:n}=e,o=q("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},J=C,Q=N,ee=L,et=D,en=I,er=H,eo=$,ei=U},4499:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(8493),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},5378:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(790).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5389:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5534:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(8493),o=n(3627),i=n(1929),l=n(2848),a=n(1753),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(v),E=(0,l.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||m(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,A.paused]),r.useEffect(()=>{if(w){h.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),h.remove(A)},0)}}},[w,x,E,A]);let N=r.useCallback(e=>{if(!n&&!f||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,A.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:N})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},5760:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(8493),o=globalThis?.document?r.useLayoutEffect:()=>{}},6616:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(8493),i=n(5389),l=n(1929),a=n(3627),u=n(2848),c=n(1753),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(f),[R,C]=o.useState(null),A=null!=(d=null==R?void 0:R.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,N]=o.useState({}),S=(0,a.s)(t,e=>C(e)),O=Array.from(E.layers),[T]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),L=O.indexOf(T),P=R?O.indexOf(R):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,M=P>=L,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));M&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},A),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},A);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},A),o.useEffect(()=>{if(R)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[R,A,h,E]),o.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,E]),o.useEffect(()=>{let e=()=>N({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...x,ref:S,style:{pointerEvents:D?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},7431:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(8493),o=n(3627),i=n(5760),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},7709:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(8493),o=n(1753);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},8844:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],f=[],d=new Set,p=new Set(c),m=function(e){!e||d.has(e)||(d.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),f.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),d.clear(),a++,function(){f.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},9183:(e,t,n)=>{n.d(t,{DX:()=>a,Dc:()=>c,TL:()=>l,bL:()=>a,xV:()=>s});var r=n(8493),o=n(3627),i=n(1753);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var l;let e,a,u=(l=n,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),u=a.find(f);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=l("Slot"),u=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}var s=c("Slottable");function f(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},9463:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(8493),i=n(5760),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},9641:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(8493),o=n(9280),i=n(1929),l=n(5760),a=n(1753),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[f,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=c||f&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"}}]);