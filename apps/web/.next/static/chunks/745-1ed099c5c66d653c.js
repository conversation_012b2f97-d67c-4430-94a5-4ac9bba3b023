"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{367:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},1071:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},1287:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]])},1964:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},3090:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},3340:(e,t,r)=>{r.d(t,{UC:()=>G,VY:()=>H,ZD:()=>z,ZL:()=>T,bL:()=>N,hE:()=>K,hJ:()=>L,l9:()=>E,rc:()=>S});var a=r(8493),n=r(7709),o=r(3627),i=r(4242),l=r(5389),s=r(9183),c=r(1753),d="AlertDialog",[u,f]=(0,n.A)(d,[i.Hs]),p=(0,i.Hs)(),h=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,c.jsx)(i.bL,{...a,...r,modal:!0})};h.displayName=d;var y=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,c.jsx)(i.l9,{...n,...a,ref:t})});y.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,c.jsx)(i.ZL,{...a,...r})};v.displayName="AlertDialogPortal";var m=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,c.jsx)(i.hJ,{...n,...a,ref:t})});m.displayName="AlertDialogOverlay";var b="AlertDialogContent",[g,x]=u(b),A=(0,s.Dc)("AlertDialogContent"),w=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...s}=e,d=p(r),u=a.useRef(null),f=(0,o.s)(t,u),h=a.useRef(null);return(0,c.jsx)(i.G$,{contentName:b,titleName:k,docsSlug:"alert-dialog",children:(0,c.jsx)(g,{scope:r,cancelRef:h,children:(0,c.jsxs)(i.UC,{role:"alertdialog",...d,...s,ref:f,onOpenAutoFocus:(0,l.m)(s.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=h.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,c.jsx)(A,{children:n}),(0,c.jsx)(I,{contentRef:u})]})})})});w.displayName=b;var k="AlertDialogTitle",D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,c.jsx)(i.hE,{...n,...a,ref:t})});D.displayName=k;var j="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,c.jsx)(i.VY,{...n,...a,ref:t})});R.displayName=j;var M=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=p(r);return(0,c.jsx)(i.bm,{...n,...a,ref:t})});M.displayName="AlertDialogAction";var C="AlertDialogCancel",F=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=x(C,r),l=p(r),s=(0,o.s)(t,n);return(0,c.jsx)(i.bm,{...l,...a,ref:s})});F.displayName=C;var I=e=>{let{contentRef:t}=e,r="`".concat(b,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(b,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(b,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},N=h,E=y,T=v,L=m,G=w,S=M,z=F,K=D,H=R},3885:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4041:(e,t,r)=>{r.d(t,{UC:()=>J,B8:()=>Y,bL:()=>U,l9:()=>Z});var a=r(8493),n=r(5389),o=r(7709),i=r(5857),l=r(3627),s=r(9463),c=r(1929),d=r(2848),u=r(696),f=r(8400),p=r(1753),h="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,g]=(0,i.N)(v),[x,A]=(0,o.A)(v,[g]),[w,k]=x(v),D=a.forwardRef((e,t)=>(0,p.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:t})})}));D.displayName=v;var j=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:i=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:A,preventScrollOnEntryFocus:k=!1,...D}=e,j=a.useRef(null),R=(0,l.s)(t,j),M=(0,f.jH)(s),[C,I]=(0,u.i)({prop:m,defaultProp:null!=g?g:null,onChange:x,caller:v}),[N,E]=a.useState(!1),T=(0,d.c)(A),L=b(r),G=a.useRef(!1),[S,z]=a.useState(0);return a.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(h,T),()=>e.removeEventListener(h,T)},[T]),(0,p.jsx)(w,{scope:r,orientation:o,dir:M,loop:i,currentTabStopId:C,onItemFocus:a.useCallback(e=>I(e),[I]),onItemShiftTab:a.useCallback(()=>E(!0),[]),onFocusableItemAdd:a.useCallback(()=>z(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>z(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:N||0===S?-1:0,"data-orientation":o,...D,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{G.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!G.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(h,y);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),k)}}G.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>E(!1))})})}),R="RovingFocusGroupItem",M=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:l,children:d,...u}=e,f=(0,s.B)(),h=l||f,y=k(R,r),v=y.currentTabStopId===h,g=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:A,currentTabStopId:w}=y;return a.useEffect(()=>{if(o)return x(),()=>A()},[o,x,A]),(0,p.jsx)(m.ItemSlot,{scope:r,id:h,focusable:o,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...u,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?y.onItemFocus(h):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>y.onItemFocus(h)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void y.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return C[n]}(e,y.orientation,y.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=y.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>F(r))}}),children:"function"==typeof d?d({isCurrentTabStop:v,hasTabStop:null!=w}):d})})});M.displayName=R;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var I=r(7431),N="Tabs",[E,T]=(0,o.A)(N,[A]),L=A(),[G,S]=E(N),z=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:d="automatic",...h}=e,y=(0,f.jH)(l),[v,m]=(0,u.i)({prop:a,onChange:n,defaultProp:null!=o?o:"",caller:N});return(0,p.jsx)(G,{scope:r,baseId:(0,s.B)(),value:v,onValueChange:m,orientation:i,dir:y,activationMode:d,children:(0,p.jsx)(c.sG.div,{dir:y,"data-orientation":i,...h,ref:t})})});z.displayName=N;var K="TabsList",H=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=S(K,r),i=L(r);return(0,p.jsx)(D,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:a,children:(0,p.jsx)(c.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});H.displayName=K;var P="TabsTrigger",_=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...i}=e,l=S(P,r),s=L(r),d=V(l.baseId,a),u=O(l.baseId,a),f=a===l.value;return(0,p.jsx)(M,{asChild:!0,...s,focusable:!o,active:f,children:(0,p.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;f||o||!e||l.onValueChange(a)})})})});_.displayName=P;var q="TabsContent",B=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:i,...l}=e,s=S(q,r),d=V(s.baseId,n),u=O(s.baseId,n),f=n===s.value,h=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(I.C,{present:o||f,children:r=>{let{present:a}=r;return(0,p.jsx)(c.sG.div,{"data-state":f?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":d,hidden:!a,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&i})}})});function V(e,t){return"".concat(e,"-trigger-").concat(t)}function O(e,t){return"".concat(e,"-content-").concat(t)}B.displayName=q;var U=z,Y=H,Z=_,J=B},4722:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},5025:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]])},6621:(e,t,r)=>{r.d(t,{b:()=>c});var a=r(8493),n=r(1929),o=r(1753),i="horizontal",l=["horizontal","vertical"],s=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:s=i,...c}=e,d=(r=s,l.includes(r))?s:i;return(0,o.jsx)(n.sG.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});s.displayName="Separator";var c=s},8263:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("list-filter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},8443:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("chart-line",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},8639:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},9242:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(790).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])}}]);