"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[776],{9776:(e,t,n)=>{n.d(t,{CP:()=>el,CI:()=>ei,wV:()=>ea});var s,a,r,o,i,l=n(1753),c=n(8493),d=n.t(c,2);class u extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let n=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${n}`}}class p extends u{}p.kind="signIn";class v extends u{}v.type="AdapterError";class h extends u{}h.type="AccessDenied";class y extends u{}y.type="CallbackRouteError";class f extends u{}f.type="ErrorPageLoop";class g extends u{}g.type="EventError";class w extends u{}w.type="InvalidCallbackUrl";class E extends p{constructor(){super(...arguments),this.code="credentials"}}E.type="CredentialsSignin";class x extends u{}x.type="InvalidEndpoints";class S extends u{}S.type="InvalidCheck";class _ extends u{}_.type="JWTSessionError";class b extends u{}b.type="MissingAdapter";class m extends u{}m.type="MissingAdapterMethods";class L extends u{}L.type="MissingAuthorize";class U extends u{}U.type="MissingSecret";class k extends p{}k.type="OAuthAccountNotLinked";class A extends p{}A.type="OAuthCallbackError";class C extends u{}C.type="OAuthProfileParseError";class R extends u{}R.type="SessionTokenError";class T extends p{}T.type="OAuthSignInError";class N extends p{}N.type="EmailSignInError";class P extends u{}P.type="SignOutError";class I extends u{}I.type="UnknownAction";class M extends u{}M.type="UnsupportedStrategy";class O extends u{}O.type="InvalidProvider";class H extends u{}H.type="UntrustedHost";class X extends u{}X.type="Verification";class j extends p{}j.type="MissingCSRF";class V extends u{}V.type="DuplicateConditionalUI";class W extends u{}W.type="MissingWebAuthnAutocomplete";class $ extends u{}$.type="WebAuthnVerificationError";class D extends p{}D.type="AccountNotLinked";class F extends u{}F.type="ExperimentalFeatureNotEnabled";class B extends u{}class J extends u{}async function q(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a="".concat(z(t),"/").concat(e);try{var r;let e={headers:{"Content-Type":"application/json",...(null==s||null==(r=s.headers)?void 0:r.cookie)?{cookie:s.headers.cookie}:{}}};(null==s?void 0:s.body)&&(e.body=JSON.stringify(s.body),e.method="POST");let t=await fetch(a,e),n=await t.json();if(!t.ok)throw n;return n}catch(e){return n.error(new B(e.message,e)),null}}function z(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function G(){return Math.floor(Date.now()/1e3)}function K(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let n=new URL(e||t),s=("/"===n.pathname?t.pathname:n.pathname).replace(/\/$/,""),a="".concat(n.origin).concat(s);return{origin:n.origin,host:n.host,path:s,base:a,toString:()=>a}}var Q=n(8499);let Y={baseUrl:K(null!=(a=Q.env.NEXTAUTH_URL)?a:Q.env.VERCEL_URL).origin,basePath:K(Q.env.NEXTAUTH_URL).path,baseUrlServer:K(null!=(o=null!=(r=Q.env.NEXTAUTH_URL_INTERNAL)?r:Q.env.NEXTAUTH_URL)?o:Q.env.VERCEL_URL).origin,basePathServer:K(null!=(i=Q.env.NEXTAUTH_URL_INTERNAL)?i:Q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},Z=null;function ee(){return new BroadcastChannel("next-auth")}function et(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{}}:(null===Z&&(Z=ee()),Z)}let en={debug:console.debug,error:console.error,warn:console.warn},es=null==(s=c.createContext)?void 0:s.call(d,void 0);function ea(e){if(!es)throw Error("React Context is unavailable in Server Components");let t=c.useContext(es),{required:n,onUnauthenticated:s}=null!=e?e:{},a=n&&"unauthenticated"===t.status;return(c.useEffect(()=>{if(a){let e="".concat(Y.basePath,"/signin?").concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));s?s():window.location.href=e}},[a,s]),a)?{data:t.data,update:t.update,status:"loading"}:t}async function er(e){var t;let n=await q("session",Y,en,e);return(null==(t=null==e?void 0:e.broadcast)||t)&&ee().postMessage({event:"session",data:{trigger:"getSession"}}),n}async function eo(){var e;let t=await q("csrf",Y,en);return null!=(e=null==t?void 0:t.csrfToken)?e:""}async function ei(e){var t,n,s,a;let r=null!=(n=null!=(t=null==e?void 0:e.redirectTo)?t:null==e?void 0:e.callbackUrl)?n:window.location.href,o=z(Y),i=await eo(),l=await fetch("".concat(o,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:i,callbackUrl:r})}),c=await l.json();if(et().postMessage({event:"session",data:{trigger:"signout"}}),null==(s=null==e?void 0:e.redirect)||s){let e=null!=(a=c.url)?a:r;window.location.href=e,e.includes("#")&&window.location.reload();return}return await Y._getSession({event:"storage"}),c}function el(e){if(!es)throw Error("React Context is unavailable in Server Components");let{children:t,basePath:n,refetchInterval:s,refetchWhenOffline:a}=e;n&&(Y.basePath=n);let r=void 0!==e.session;Y._lastSync=r?G():0;let[o,i]=c.useState(()=>(r&&(Y._session=e.session),e.session)),[d,u]=c.useState(!r);c.useEffect(()=>(Y._getSession=async function(){let{event:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t="storage"===e;if(t||void 0===Y._session){Y._lastSync=G(),Y._session=await er({broadcast:!t}),i(Y._session);return}if(!e||null===Y._session||G()<Y._lastSync)return;Y._lastSync=G(),Y._session=await er(),i(Y._session)}catch(e){en.error(new J(e.message,e))}finally{u(!1)}},Y._getSession(),()=>{Y._lastSync=0,Y._session=void 0,Y._getSession=()=>{}}),[]),c.useEffect(()=>{let e=()=>Y._getSession({event:"storage"});return et().addEventListener("message",e),()=>et().removeEventListener("message",e)},[]),c.useEffect(()=>{let{refetchOnWindowFocus:t=!0}=e,n=()=>{t&&"visible"===document.visibilityState&&Y._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",n,!1),()=>document.removeEventListener("visibilitychange",n,!1)},[e.refetchOnWindowFocus]);let p=function(){let[e,t]=c.useState("undefined"!=typeof navigator&&navigator.onLine),n=()=>t(!0),s=()=>t(!1);return c.useEffect(()=>(window.addEventListener("online",n),window.addEventListener("offline",s),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",s)}),[]),e}(),v=!1!==a||p;c.useEffect(()=>{if(s&&v){let e=setInterval(()=>{Y._session&&Y._getSession({event:"poll"})},1e3*s);return()=>clearInterval(e)}},[s,v]);let h=c.useMemo(()=>({data:o,status:d?"loading":o?"authenticated":"unauthenticated",async update(e){if(d)return;u(!0);let t=await q("session",Y,en,void 0===e?void 0:{body:{csrfToken:await eo(),data:e}});return u(!1),t&&(i(t),et().postMessage({event:"session",data:{trigger:"getSession"}})),t}}),[o,d]);return(0,l.jsx)(es.Provider,{value:h,children:t})}}}]);