FROM node:20-alpine

WORKDIR /usr/app

# Copy only what's needed, excluding scripts/mcp-puppeteer
COPY ./.next ./.next
COPY ./public ./public
COPY ./package.json ./
COPY ./pnpm-lock.yaml ./
COPY ./next.config.js ./
COPY ./prisma/schema.prisma ./prisma/
# Don't copy scripts/mcp-puppeteer

RUN corepack enable && corepack prepare pnpm@latest --activate

RUN pnpm --version && pnpm install --prod --ignore-scripts
RUN npx prisma generate

EXPOSE 3000
CMD [ "pnpm", "start" ]
