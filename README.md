# HEDGEYE SESSION COOKIE

To retrieve daily signals from <PERSON><PERSON><PERSON>, we rely on the session cookie.

- Login to https://app.hedgeye.com
- In navigation, click `Risk Range Signal`
- Go to inpect -> application -> cookies. Select `_hedgeye_session` then right click to `Show Requests With This Cookie`
- Select the request with `all?page=...`. Inspect the `Headers` and look for `Set-Cookie`. Copy the value - this is the session cookie.

Notes:
@stoqey/ib seems to be the best library for IBKR API since this one is also using TypeScript.
To connect to IBKR, we can use our self-hosted IBKR docker instance in Azure.
The docker instance is hosted in Azure Container Apps is just basic pull of the ibkr-docker image. Just provided IBKR username and password.
As extrange/ibkr-docker instructed, we need to use 8888 when connecting to API. The 4002 is for the TWS.

We use this https://github.com/extrange/ibkr-docker

IB_PORT=8888
IB_HOST=ibkr.graybeach-bc564721.southeastasia.azurecontainerapps.io

## Core Features

### 1. Auto Order System

The system automatically creates order prefills based on risk signal changes:

- **Trigger Points**:

  - Risk signal changes
  - Manual triggers
  - Scheduled checks

- **Process Flow**:

  ```
  Risk Signal Change
  → OrderService.autoOrder()
  → /api/order/auto-order endpoint
  → autoOrderPrefill()
  → createAutoOrderPrefill()
  → Database
  ```

- **Security Measures**:
  - User authentication
  - API key validation
  - Settings verification
  - Input validation

### 2. IBKR Integration

The system connects to Interactive Brokers for trade execution:

- **Connection Details**:

  ```
  IB_PORT=8888
  IB_HOST=ibkr.graybeach-bc564721.southeastasia.azurecontainerapps.io
  ```

- **Docker Implementation**:
  - Uses extrange/ibkr-docker
  - Hosted on Azure Container Apps
  - TWS accessible on port 4002
  - API accessible on port 8888

### 3. Risk Signal Monitoring

Hedgeye integration for risk signals:

- **Session Management**:
  1. Login to https://app.hedgeye.com
  2. Navigate to Risk Range Signal
  3. Retrieve session cookie from browser
  4. Use cookie for API authentication

## Technical Implementation

### Key Components

1. **Order Service (`order.service.ts`)**

   - Handles order creation and execution
   - Manages auto-order prefill logic
   - Integrates with IBKR API

2. **API Routes**

   - `/api/order/auto-order`: Handles auto order requests
   - `/api/hedgeye/risk-range-signals`: Fetches risk signals
   - `/api/push/notify-trend-changes`: Manages notifications

3. **Database Schema**
   ```prisma
   model AutoOrderPrefill {
     id          String    @id @default(cuid())
     user_id     String
     account_id  String
     ticker      String
     action      String
     quantity    Int
     price       Float
     order_type  String
     created_at  DateTime  @default(now())
     deleted_at  DateTime?
   }
   ```

### Security Implementation

1. **Authentication**

   - NextAuth.js for user authentication
   - API key validation for server-to-server communication
   - Session-based security for Hedgeye integration

2. **Authorization**
   - Role-based access control
   - User-specific settings validation
   - Account-level permissions

## Configuration

### Environment Variables

## Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Configure IBKR connection
5. Start development server: `npm run dev`

## Production Deployment

1. Build the application: `npm run build`
2. Deploy to hosting platform
3. Configure environment variables
4. Set up IBKR docker container
5. Configure Hedgeye integration

## Notes

- @stoqey/ib is used for IBKR API integration
- IBKR docker instance runs in Azure Container Apps
- Hedgeye session cookie required for risk signals
- System requires proper configuration of all components for full functionality

## Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Create pull request

## License

[Add your license information here]
