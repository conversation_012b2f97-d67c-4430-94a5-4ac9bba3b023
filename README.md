# Lunar Hedge Trading Platform - Monorepo

A comprehensive trading platform built with Next.js frontend and Fastify backend, featuring risk signal monitoring, portfolio management, and automated trading capabilities through IBKR integration.

## 🏗️ Monorepo Structure

```
lunar-hedge/
├── apps/
│   ├── web/                    # Next.js frontend application
│   └── api/                    # Fastify backend API
├── packages/
│   ├── shared/                 # Shared types, utilities, constants
│   ├── database/               # Prisma schema and database utilities
│   └── ui/                     # Shared UI components (future)
├── tools/
│   └── eslint-config/          # Shared ESLint configuration
├── package.json                # Root package.json with workspace config
├── pnpm-workspace.yaml         # PNPM workspace configuration
├── turbo.json                  # Turborepo configuration
└── docker-compose.yml          # Docker services orchestration
```

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- PNPM 9+
- Docker & Docker Compose (optional)

### Development Setup

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd lunar-hedge
   pnpm install
   ```

2. **Set up environment variables:**
   ```bash
   cp apps/web/.env.example apps/web/.env
   cp apps/api/.env.example apps/api/.env
   # Edit the .env files with your configuration
   ```

3. **Start development servers:**
   ```bash
   # Start all services
   pnpm dev

   # Or start individual services
   pnpm web:dev    # Frontend on http://localhost:3000
   pnpm api:dev    # Backend on http://localhost:3001
   ```

4. **Database setup:**
   ```bash
   pnpm db:generate  # Generate Prisma client
   pnpm db:migrate   # Run database migrations
   ```

### Docker Development

```bash
# Start all services with Docker
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📦 Package Scripts

### Root Level Commands
- `pnpm dev` - Start all applications in development mode
- `pnpm build` - Build all applications
- `pnpm lint` - Lint all packages
- `pnpm test` - Run tests across all packages
- `pnpm clean` - Clean all build artifacts

### Application-Specific Commands
- `pnpm web:dev` - Start web app only
- `pnpm web:build` - Build web app
- `pnpm api:dev` - Start API server only
- `pnpm api:build` - Build API server

### Database Commands
- `pnpm db:generate` - Generate Prisma client
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Prisma Studio

## Core Features

### 1. Auto Order System

The system automatically creates order prefills based on risk signal changes:

- **Trigger Points**:

  - Risk signal changes
  - Manual triggers
  - Scheduled checks

- **Process Flow**:

  ```
  Risk Signal Change
  → OrderService.autoOrder()
  → /api/order/auto-order endpoint
  → autoOrderPrefill()
  → createAutoOrderPrefill()
  → Database
  ```

- **Security Measures**:
  - User authentication
  - API key validation
  - Settings verification
  - Input validation

### 2. IBKR Integration

The system connects to Interactive Brokers for trade execution:

- **Connection Details**:

  ```
  IB_PORT=8888
  IB_HOST=ibkr.graybeach-bc564721.southeastasia.azurecontainerapps.io
  ```

- **Docker Implementation**:
  - Uses extrange/ibkr-docker
  - Hosted on Azure Container Apps
  - TWS accessible on port 4002
  - API accessible on port 8888

### 3. Risk Signal Monitoring

Hedgeye integration for risk signals:

- **Session Management**:
  1. Login to https://app.hedgeye.com
  2. Navigate to Risk Range Signal
  3. Retrieve session cookie from browser
  4. Use cookie for API authentication

## Technical Implementation

### Key Components

1. **Order Service (`order.service.ts`)**

   - Handles order creation and execution
   - Manages auto-order prefill logic
   - Integrates with IBKR API

2. **API Routes**

   - `/api/order/auto-order`: Handles auto order requests
   - `/api/hedgeye/risk-range-signals`: Fetches risk signals
   - `/api/push/notify-trend-changes`: Manages notifications

3. **Database Schema**
   ```prisma
   model AutoOrderPrefill {
     id          String    @id @default(cuid())
     user_id     String
     account_id  String
     ticker      String
     action      String
     quantity    Int
     price       Float
     order_type  String
     created_at  DateTime  @default(now())
     deleted_at  DateTime?
   }
   ```

### Security Implementation

1. **Authentication**

   - NextAuth.js for user authentication
   - API key validation for server-to-server communication
   - Session-based security for Hedgeye integration

2. **Authorization**
   - Role-based access control
   - User-specific settings validation
   - Account-level permissions

## Configuration

### Environment Variables

## 🏭 Production Deployment

### Building for Production
```bash
# Build all applications
pnpm build

# Build specific application
pnpm web:build
pnpm api:build
```

### Docker Production Build
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 IBKR Integration Setup

The system connects to Interactive Brokers for trade execution:

- **Connection Details**:
  ```
  IB_PORT=8888
  IB_HOST=ibkr.graybeach-bc564721.southeastasia.azurecontainerapps.io
  ```

- **Docker Implementation**:
  - Uses extrange/ibkr-docker
  - Hosted on Azure Container Apps
  - TWS accessible on port 4002
  - API accessible on port 8888

## 📊 Hedgeye Session Cookie Setup

To retrieve daily signals from Hedgeye:

1. Login to https://app.hedgeye.com
2. Navigate to Risk Range Signal
3. Go to inspect -> application -> cookies
4. Select `_hedgeye_session` then right click to `Show Requests With This Cookie`
5. Select the request with `all?page=...`
6. Inspect the `Headers` and look for `Set-Cookie`
7. Copy the value - this is the session cookie

## 🧪 Testing the Setup

### API Health Check
```bash
# Start the API server
pnpm api:dev

# Test health endpoint
curl http://localhost:3001/api/health

# Test detailed health endpoint
curl http://localhost:3001/api/health/detailed

# View API documentation
open http://localhost:3001/docs
```

### Web Application
```bash
# Start the web application
pnpm web:dev

# Open in browser
open http://localhost:3000
```

## 📁 Package Structure

### `apps/web` - Next.js Frontend
- **Framework**: Next.js 15 with React 19
- **Styling**: TailwindCSS v4 with Radix UI components
- **Authentication**: NextAuth with Google OAuth
- **Features**: Trading dashboard, portfolio management, risk signals

### `apps/api` - Fastify Backend
- **Framework**: Fastify with TypeScript
- **Features**: RESTful API, health checks, Swagger documentation
- **Endpoints**: `/api/health`, `/api/health/detailed`
- **Documentation**: Available at `/docs`

### `packages/shared` - Shared Utilities
- **Types**: Common TypeScript interfaces
- **Constants**: API endpoints, pagination defaults
- **Utils**: Validation schemas, formatting functions

### `packages/database` - Database Layer
- **ORM**: Prisma with PostgreSQL
- **Migrations**: Database schema management
- **Client**: Singleton Prisma client instance

## 🔧 Configuration

### Environment Variables

**Web App (`apps/web/.env`)**:
```env
AUTH_URL=http://localhost:3000
AUTH_SECRET=your-auth-secret
AUTH_GOOGLE_ID=your-google-oauth-id
AUTH_GOOGLE_SECRET=your-google-oauth-secret
PG_HEDGE_PRISMA_URL=postgresql://...
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

**API (`apps/api/.env`)**:
```env
PORT=3001
NODE_ENV=development
DATABASE_URL=postgresql://...
ALLOWED_ORIGINS=http://localhost:3000
```

## 📝 Notes

- @stoqey/ib is used for IBKR API integration
- IBKR docker instance runs in Azure Container Apps
- Hedgeye session cookie required for risk signals
- System requires proper configuration of all components for full functionality
- Uses PNPM workspaces for efficient dependency management
- Turborepo for optimized build and development workflows

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Create pull request

## 📄 License

[Add your license information here]

---

## ✅ Monorepo Conversion Complete!

The project has been successfully converted to a monorepo structure with:

✅ **Frontend**: Next.js app in `apps/web/`
✅ **Backend**: Fastify API in `apps/api/` with `/health` endpoint
✅ **Shared Packages**: Types, utilities, and database layer
✅ **Build System**: Turborepo for optimized builds
✅ **Package Management**: PNPM workspaces
✅ **Docker Support**: Multi-stage builds and docker-compose
✅ **Development Ready**: Hot reload and development scripts

**Next Steps**:
1. Set up your environment variables
2. Configure your database connection
3. Start developing with `pnpm dev`
4. Add more API endpoints as needed
5. Implement shared UI components in `packages/ui/`
