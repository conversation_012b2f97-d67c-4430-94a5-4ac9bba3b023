"use client";

import { siteConfig } from "@/config/site";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import {
  HamburgerMenuIcon,
  PersonIcon,
  ExitIcon,
  SunIcon,
  MoonIcon,
} from "@radix-ui/react-icons";
import { NavItem } from "@/types/nav";
import { signOut } from "next-auth/react";
import { MarketStatusIndicator } from "@/app/_components/market-status-indicator";
import { useTheme } from "next-themes";

function Header() {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <header className="bg-background/80 backdrop-blur-md sticky top-0 z-40 w-full border-b shadow-sm">
      <div className="flex h-16 items-center justify-between px-4">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <HamburgerMenuIcon className="h-6 w-6" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[240px] sm:w-[300px]">
            <SheetTitle className="sr-only">Main Menu</SheetTitle>
            <nav className="flex flex-col space-y-1 mt-6">
              {siteConfig.mainNav.map((item: NavItem, index) => (
                <a
                  key={index}
                  href={item.href}
                  className="group flex items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all"
                >
                  <span className="flex items-center gap-3">
                    {item.icon && (
                      <span className="text-interactive">
                        <item.icon />
                      </span>
                    )}
                    <span className="text-interactive">{item.title}</span>
                  </span>
                  <span
                    className="ml-auto opacity-0 transition-opacity group-hover:opacity-100"
                    aria-hidden="true"
                  >
                    &rarr;
                  </span>
                </a>
              ))}
            </nav>
          </SheetContent>
        </Sheet>

        <div className="flex items-center gap-4">
          <MarketStatusIndicator />
          <Button variant="ghost" size="icon" onClick={toggleTheme}>
            <SunIcon className="h-6 w-6 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <MoonIcon className="absolute h-6 w-6 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <PersonIcon className="h-6 w-6" />
                <span className="sr-only">Open profile menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full sm:w-[400px]">
              <SheetTitle className="sr-only">Profile Menu</SheetTitle>
              <nav className="flex flex-col space-y-4 mt-6">
                <div className="text-size-lg font-weight-medium text-heading px-3 mb-2">
                  My Account
                </div>
                <a
                  href="/profile"
                  className="group flex items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all hover:bg-accent"
                >
                  <span className="flex items-center gap-3">
                    <span className="text-interactive">
                      <PersonIcon className="h-5 w-5" />
                    </span>
                    <span className="text-interactive">Profile</span>
                  </span>
                </a>
                <button
                  onClick={() => signOut({ callbackUrl: "/" })}
                  className="group flex w-full items-center rounded-lg px-3 py-2.5 text-size-sm font-weight-medium transition-all hover:bg-accent"
                >
                  <span className="flex items-center gap-3">
                    <span className="text-interactive">
                      <ExitIcon className="h-5 w-5" />
                    </span>
                    <span className="text-interactive">Logout</span>
                  </span>
                </button>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}

export default Header;
